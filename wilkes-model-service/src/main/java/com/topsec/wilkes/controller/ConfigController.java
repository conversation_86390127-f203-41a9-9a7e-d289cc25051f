package com.topsec.wilkes.controller;

import com.topsec.wilkes.config.AggConfig;
import com.topsec.wilkes.config.ThreatKnowledgeConfig;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping
public class ConfigController {
  @Autowired
  private AggConfig aggConfig;
  @Autowired
  private ThreatKnowledgeConfig threatKnowledgeConfig;

  /**
   * 用来提供给计算引擎任务使用的参数
   */
  @GetMapping(value = "/aggConfig")
  public AggConfig aggConfig() {
    return aggConfig;
  }

  /**
   * 给威胁情报赋来源设置匹配值
   * @return
   */
  @GetMapping(value = "/threatKnowledgeSource")
  public Map<String, String[]> knowledgeSourceFieldMapping() {
    return threatKnowledgeConfig.getKnowledgeSourceFieldMapping();
  }
}
