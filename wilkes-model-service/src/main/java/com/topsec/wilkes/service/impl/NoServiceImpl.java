package com.topsec.wilkes.service.impl;

import static com.topsec.wilkes.common.dto.SourceType.*;

import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.repository.ConfigRepository;
import com.topsec.wilkes.repository.entity.ConfigEntity;
import com.topsec.wilkes.service.ModelService;
import com.topsec.wilkes.service.NoService;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class NoServiceImpl implements NoService {
  private static final Pattern WITH_PREFIX_PATTERN = Pattern.compile("[A-Z](\\d+)");
  private static final Pattern WITHOUT_PREFIX_PATTERN = Pattern.compile("(\\d+)");
  private static final String MAX_NO_NAME = "max.no";
  private static final String MAX_INNER_NO_NAME = "max.inner.no";
  private static final String MAX_INNER_AI_NO_NAME = "max.inner.ai.no";
  private static final String MAX_SAMPLE_NO_NAME = "max.sample.no";
  private final static Object syncLock = new Object();

  @Autowired
  private ModelService modelService;

  @Autowired
  private ConfigRepository configRepository;

  public void init() {
    //模型编号初始化值0
    initNo();

    // 兼容之前的模型，把之前的模型的前缀去掉
    List<Model> modelList = modelService.getModels().stream().filter(model -> model.getModelType() != ModelType.AI).collect(Collectors.toList());
    if (modelList.size() > 0) {
      for (Model model : modelList) {
        //获取所有模型编号最大值
        int maxNo = getMaxNo(Pattern.compile("[^0-9]").matcher(model.getNo()).replaceAll(""));
        //保存最大值
        switch (model.getSource()) {
          case BUILT_IN:
            doSaveMaxNo(maxNo, MAX_INNER_NO_NAME);
            break;
          case SELF_DEFINING:
            doSaveMaxNo(maxNo, MAX_NO_NAME);
            break;
          case SAMPLE:
            doSaveMaxNo(maxNo, MAX_SAMPLE_NO_NAME);
            break;
          default:
            break;
        }
      }
    }
  }

  private void initNo() {
    Optional<ConfigEntity> maxNoOptional = configRepository.findById(MAX_NO_NAME);
    if (!maxNoOptional.isPresent()) {
      configRepository.saveAndFlush(new ConfigEntity(MAX_NO_NAME, "0"));
    }

    Optional<ConfigEntity> maxInnerNoOptional = configRepository.findById(MAX_INNER_NO_NAME);
    if (!maxInnerNoOptional.isPresent()) {
      configRepository.saveAndFlush(new ConfigEntity(MAX_INNER_NO_NAME, "0"));
    }

    Optional<ConfigEntity> maxExampleNoOptional = configRepository.findById(MAX_SAMPLE_NO_NAME);
    if (!maxExampleNoOptional.isPresent()) {
      configRepository.saveAndFlush(new ConfigEntity(MAX_SAMPLE_NO_NAME, "0"));
    }

    Optional<ConfigEntity> maxAIInnerNoOptional = configRepository.findById(MAX_INNER_AI_NO_NAME);
    if (!maxAIInnerNoOptional.isPresent()) {
      configRepository.saveAndFlush(new ConfigEntity(MAX_INNER_AI_NO_NAME, "90061"));
    }
  }

  private int getMaxNo(String modelNo) {
    int maxNo = 0;
    Matcher withPrefixMatcher = WITH_PREFIX_PATTERN.matcher(modelNo);
    Matcher withoutPrefixMatcher = WITHOUT_PREFIX_PATTERN.matcher(modelNo);
    Integer no = 0;
    if (withPrefixMatcher.matches()) {
      no = Integer.valueOf(withPrefixMatcher.group(1));
    }
    if (withoutPrefixMatcher.matches()) {
      no = Integer.valueOf(withoutPrefixMatcher.group(1));
    }
    if (maxNo < no) {
      maxNo = no;
    }
    return maxNo;
  }

  private void doSaveMaxNo(int maxNo, String configId) {
    ConfigEntity configEntity = new ConfigEntity();
    Optional<ConfigEntity> configEntityOptional = configRepository.findById(configId);
    if (configEntityOptional.isPresent()) {
      configEntity = configEntityOptional.get();
      int no = Integer.valueOf(configEntity.getValue());
      if (maxNo < no) {
        maxNo = no;
      }
    } else {
      configEntity.setName(configId);
    }
    configEntity.setValue(String.valueOf(maxNo));
    configRepository.saveAndFlush(configEntity);
  }

  @Transactional
  @Override
  public String next(String value) {

    init();

    Optional<ConfigEntity> configEntity = null;
    String prefix = "";
    switch (value) {
      case SELF_DEFINING:
        configEntity = configRepository.findById(MAX_NO_NAME);
        break;
      case BUILT_IN:
        configEntity = configRepository.findById(MAX_INNER_NO_NAME);
        prefix = "B-";
        break;
      case BUILT_IN_AI:
        configEntity = configRepository.findById(MAX_INNER_AI_NO_NAME);
        prefix = "B-";
        break;
      case SAMPLE:
        configEntity = configRepository.findById(MAX_SAMPLE_NO_NAME);
        prefix = "E-";
        break;
      default:
        break;
    }
    int maxNo = Integer.valueOf(configEntity.get().getValue());
    int nextNo = maxNo + 1;
    configEntity.get().setValue(String.valueOf(nextNo));
    configRepository.saveAndFlush(configEntity.get());
    if (Strings.isNullOrEmpty(prefix)) {
      return String.format("%05d", nextNo);
    } else {
      return String.format("%s%05d", prefix, nextNo);
    }
  }

  @Transactional
  @Override
  public String current(String value) {

    init();

    Optional<ConfigEntity> configEntity = null;
    String prefix = "";
    switch (value) {
      case SELF_DEFINING:
        configEntity = configRepository.findById(MAX_NO_NAME);
        break;
      case BUILT_IN:
        configEntity = configRepository.findById(MAX_INNER_NO_NAME);
        prefix = "B-";
        break;
      case BUILT_IN_AI:
        configEntity = configRepository.findById(MAX_INNER_AI_NO_NAME);
        prefix = "B-";
        break;
      case SAMPLE:
        configEntity = configRepository.findById(MAX_SAMPLE_NO_NAME);
        prefix = "E-";
        break;
      default:
        break;
    }
    int maxNo = Integer.valueOf(configEntity.get().getValue());
    if (Strings.isNullOrEmpty(prefix)) {
      return String.format("%05d", maxNo);
    } else {
      return String.format("%s%05d", prefix, maxNo);
    }
  }

  @Override
  public synchronized String syncNext(String value) {
    synchronized (syncLock) {
      return ((NoServiceImpl) AopContext.currentProxy()).next(value);
    }
  }

}
