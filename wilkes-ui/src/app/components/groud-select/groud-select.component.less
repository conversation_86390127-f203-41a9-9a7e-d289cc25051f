:host {
  .bg-cover {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 9;
  }

  .groud-select-con {
    position: relative;
    z-index: 99;
    box-sizing: border-box;
    width: 100%;
    background-color: #fff;

    .add-tage {
      width: 100%;
      cursor: pointer;
    }

    ::ng-deep
      .g-hidden-checkbox
      .ui-tree-container
      .ui-treenode
      .ui-treenode-content
      .ui-chkbox {
      display: none !important;
    }

    i {
      position: absolute;
      top: 50%;
      right: 9px;
      margin-top: -8px;
      font-size: 16px;
    }

    div.f-con {
      display: flex;
      align-items: center;
    }

    .from-name-con {
      position: relative;

      input {
        padding-right: 30px;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      i {
        position: absolute;
        right: 20px;
        z-index: 200;
        cursor: pointer;
      }

      .icon-type {
        right: 5px;
        margin-top: -7px;
        font-size: 14px;
      }

      .over-c {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 99;
      }

      label {
        display: inline-block;
        width: 75px;
        text-align: center;
      }
    }

    .list-btn-con {
      position: absolute;
      top: 30px;
      z-index: 9;
      box-sizing: border-box;
      width: 100%;
      min-width: 240px;
      overflow: hidden;
      background: #fff;
      border: 1px solid #eee;
      border-radius: 5px;

      & > div {
        display: flex;
        align-items: center;
        width: 100%;
      }

      .search-con {
        position: relative;
        padding: 0 6px;
        margin-top: 2px;
      }

      .radioButton-btn {
        box-sizing: border-box;
        width: calc(100% - 12px) !important;
        height: 35px;
        margin: 0 6px;
        overflow: hidden;
        background-color: #f5f5f5;
        border-radius: 0 0 5px 5px;

        t-radiobutton {
          width: 50%;
          padding: 0 7px;
        }
      }

      .list-con {
        display: block;
        height: 230px;
        overflow: hidden;

        .type-con {
          height: 100%;
        }

        .type-two {
          box-sizing: border-box;
          float: right;
          width: 50%;
          border-left: 1px solid #eee;
        }
      }

      li {
        box-sizing: border-box;
        width: 100%;
        padding: 0 5px;
        overflow: hidden;
        font-size: 14px;
        line-height: 26px;
        text-overflow: ellipsis;
        white-space: nowrap;
        cursor: pointer;
      }
    }
  }
}
