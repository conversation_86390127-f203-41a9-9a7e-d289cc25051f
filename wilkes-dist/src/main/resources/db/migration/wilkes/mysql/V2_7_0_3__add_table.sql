create table if not exists `model_distribute`(
  `id` varchar(255) not null,
  `model_no` varchar(255) not null,
  `platform_id` varchar(255) not null,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;

create table if not exists `model_distribute_operation`(
  `id` varchar(255) not null,
  `model_no` varchar(255) not null,
  `platform_id` varchar(255) not null,
  `operation_time` datetime default null ,
  `operation_num` varchar(255) default null,
  `operation` varchar(255) default null,
  `status` varchar(255) default null,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET=utf8mb4;