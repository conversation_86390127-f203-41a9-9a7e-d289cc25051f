package com.topsec.wilkes.behaviour.service.impl.portrait;

import com.topsec.wilkes.operator.search.bean.Range;
import com.topsec.wilkes.operator.search.bean.Sort;
import com.topsec.wilkes.operator.search.util.ElasticSearch;
import com.topsec.wilkes.operator.type.RangeStateType;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.Test;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class BehaviourPortraitServiceImplTest {
  @Test
  public void thresholdDeviationRangeQuery() throws URISyntaxException {
    String url = "es://elasticsearch:23200/threshold_deviation_*/_doc";
    ElasticSearch elasticSearch = new ElasticSearch(new URI(url));
    Map timeMap = Maps.newHashMap();
    String[] indecis = new String[]{"threshold_deviation_*"};

    Map conditionMap = Maps.newHashMap();
    //conditionMap.put("stage_type", "statistic");
    conditionMap.put("subject_value", Lists.newArrayList("10001"));
    conditionMap.put("object_value", Lists.newArrayList("10.10.127.11", "BeiJing"));
    conditionMap.put("model_no", "00025");

    Range range = new Range("2020-01-02 00:00:00", "2020-01-03 00:00:00", "begin_time", RangeStateType.LeftClose_RightClose);
    Sort sort = new Sort("begin_time", SortOrder.ASC);
    Optional<List<Map<String, Object>>> optionalList = elasticSearch.rangeQuery(indecis, range, Lists.newArrayList("r_v"), conditionMap, 20, sort);
    optionalList.get();
    LoggerFactory.getLogger(BehaviourPortraitServiceImplTest.class).info(optionalList.toString());
  }


  @Test
  public void setDeviationRangeQuery() throws URISyntaxException {
    String url = "es://elasticsearch:23200/set_deviation_*/_doc";
    ElasticSearch elasticSearch = new ElasticSearch(new URI(url));
    Map timeMap = Maps.newHashMap();
    String[] indecis = new String[]{"set_deviation_*"};

    Map conditionMap = Maps.newHashMap();
    //conditionMap.put("stage_type", "statistic");
    conditionMap.put("subject_value", Lists.newArrayList("127.0.0.1"));
    conditionMap.put("object_value", Lists.newArrayList("OA"));
    conditionMap.put("model_no", "00002");

    Range range = new Range("2020-01-02 00:00:00", "2020-01-03 00:00:00", "begin_time", RangeStateType.LeftClose_RightClose);
    Sort sort = new Sort("begin_time", SortOrder.ASC);
    Optional<List<Map<String, Object>>> optionalList = elasticSearch.rangeQuery(indecis, range, Lists.newArrayList(), conditionMap, 10, sort);
    optionalList.get();
  }
}