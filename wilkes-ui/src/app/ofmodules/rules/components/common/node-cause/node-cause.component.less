.cause-con {
    margin-top: 20px;

    .t-con {
        font-size: 16px;
    }

    .cause-node-item {
        margin: 10px;

        ::ng-deep .ui-title .ui-titext {
            font-size: 12px;
        }

        .table-item-con {
            border: 1px #d7d7d7 solid;
            margin-left: 16px;
            // width: 533px;

            .table-item {
                overflow: hidden;
                border-bottom: 1px #d7d7d7 solid;

                span {
                    float: left;
                    padding: 5px 15px;
                }

                .table-name{
                    width: 50%;
                    background: #f2f2f2;
                    border-right: 1px #d7d7d7 solid;
                }

                .table-value {
                    width: 50%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }

            .table-item:last-child{
                border-bottom: none;
            }
        }

        .table-cause{
            width: auto;
            padding-bottom: 10px;
        }
    }

    .cause-node-item::before {
        content: "";
        display: block;
        height: 0;
        margin-top: 10px; /* 调整此处数值以达到所需效果 */
      }

      .icon-incident-investigation{
        cursor: pointer;
      }

      .table-cause ::ng-deep td {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
}