package com.topsec.wilkes.repository;

import com.topsec.wilkes.repository.entity.OfflineEntity;
import com.topsec.wilkes.repository.entity.OfflineTaskEntity;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface OfflineTaskRepository extends JpaRepository<OfflineTaskEntity, String>, JpaSpecificationExecutor<OfflineTaskEntity> {

  List<OfflineTaskEntity> findAllByOfflineEntity(OfflineEntity offlineEntity);

  void deleteAllByOfflineEntity(OfflineEntity offlineEntity);

  OfflineTaskEntity findByTaskId(String taskId);

  void deleteAllByTaskId(String taskId);
}
