import {
  AfterViewInit,
  Component,
  HostListener,
  Input,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from "@angular/core";
import { BChartBase, PortraitInfo } from "../../models/model";
import * as echarts from 'echarts/core' ;
import { tap } from "rxjs/operators";
import { RelatedRuleInfoComponent } from "./components/related-rule-info/related-rule-info.component";
import { DomSanitizer } from "@angular/platform-browser";
import { GetGenerateTime } from "../../utils/getGenerateTIme";
import { BehaviourService } from "../../service/behaviour.service";

@Component({
  selector: "app-related-rule",
  templateUrl: "./related-rule.component.html",
  styleUrls: ["./related-rule.component.less"],
  entryComponents: [RelatedRuleInfoComponent],
})
export class RelatedRuleComponent implements OnInit, BChartBase {
  @ViewChild("chartBox", { read: ViewContainerRef }) chartBox: ViewContainerRef;

  // 模型编号
  @Input() modelNo: string;

  // 模型数据对象
  @Input() model: PortraitInfo;

  // 暂无数据标识
  isNodata = true;
  // 加载等待
  isLoading = false;
  // 分组数据集
  groupList = [];

  // 分组
  groupValue: string;
  // echart实例
  chartObj: any;

  // 当前画像时间
  currentTime: string;
  // 下次检测时间
  forecastTime: string;

  // 详情显示/隐藏
  showDialog = false;
  // 详情参数
  pointParams: object;

  // 提示信息label集合
  tipsLabel: Array<string>;
  // 提示信息数据集
  tipsValue: object;

  // 加载群体
  loadGroup = false;

  // 画像首次生成时间
  generateTime:string = '';

  constructor(
    private domSanitizer: DomSanitizer,
    private ht: BehaviourService) {}

  ngOnInit() {

    if (this.model.groupField) {
      this.loadGroup = true;
      // 获取分组数据
      this.ht.getGroupList(this.modelNo).subscribe(
        (data) => {
          const flatMap = data
            .toString()
            .split(",")
            .filter((e: any) => e);
          this.groupList = flatMap.map((e: any) => {
            return { label: e, value: e };
          });
        },
        null,
        () => {
          this.loadGroup = false;
        }
      );
    }

    if(this.model.scope){
      this.generateTime = GetGenerateTime(this.model.scope)
    }
  }

  /**
   * 下拉选择改变事件
   * @param e event
   * @param name 类型
   */
  selectChange(e: { value: any }) {
    if (e.value == null) {
      this.groupValue = "";
      this.isNodata = true;
    } else {
      this.fetchData();
    }
  }

  fetchData() {
    this.isLoading = true;
    this.ht
      .getBehaviourorData(
        this.modelNo,
        {
          conditionMap: {
            groupValue: this.groupValue ? [this.groupValue] : null,
            subjectValue: null,
            objectValue: null,
          },
        },
        this.model.scope
      )
      .pipe(tap(() => (this.isLoading = false)))
      .subscribe((data) => {
        if (data.dataList && data.dataList.length) {
          this.isNodata = false;
          const series = this.serializeDate(data);
          const option = this.getOption(series);
          this.charOption = option;
        } else {
          this.isNodata = true;
        }
      });
  }

  serializeDate(d: any): any {
    // 返回图表数据对象集合
    const series = [];
    const chartData = d.dataList;

    this.currentTime = d.extendConfig.nowTime || "";
    this.forecastTime = d.extendConfig.nextTime || "";

    this.tipsLabel = [];
    this.tipsValue = {};

    // 图表散点数据集
    const pointData = [];

    function normalizeMap(index: any) {
      const idx = index;

      return (vals: any[]) => {
        vals.map((cur: any, i: number) => {
          const result = (pointData[i] = pointData[i] || Array(2));
          result[idx] = cur * 100;
          return pointData;
        });
      };
    }

    chartData.map((item: { name: string; data: any }) => {
      const vals = item.data;
      if (item.name === "重合度") {
        normalizeMap(0)(vals);
      } else if (item.name === "差异度") {
        normalizeMap(1)(vals);
      } else if (item.data.length) {
        this.tipsLabel.push(item.name);
        this.tipsValue[item.name] = item.data;
      }
    });

    let limitX = 0;
    let limitY = 0;
    Object.keys(d.extendConfig.mark).map((key) => {
      let data = {};
      switch (key) {
        case "重合阈值":
          data = { xAxis: d.extendConfig.mark[key] };
          limitX = d.extendConfig.mark[key];
          break;

        case "差异阈值":
          data = { yAxis: d.extendConfig.mark[key] };
          limitY = d.extendConfig.mark[key];
          break;
      }

      series.push({
        type: "line",
        zlevel: 0,
        markLine: {
          symbol: [],
          label: {
            show: true,
            formatter: (params: any) => {
              return key;
            },
          },
          tooltip: {
            show: false,
          },
          itemStyle: {
            normal: {
              borderWidth: 1,
              lineStyle: {
                type: "dashed",
              },
            },
          },
          data: [data],
        },
      });
    });

    series.push({
      type: "scatter",
      symbolSize: 10,
      zlevel: 1,
      tooltip: {
        trigger: "item",
        formatter: (params) => {
          return this.formatTips(params);
        },
      },
      itemStyle: {
        color: (params: { value: number[] }) => {
          return params.value[0] >= limitX && params.value[1] <= limitY
            ? "#009F53"
            : "#D06D6A";
        },
      },
      data: pointData,
    });

    return series;
  }

  getOption(data: any) {
    return {
      backgroundColor: "#ffffff",
      color: ["#30B7F5"],
      tooltip: {
        trigger: "axis",
        triggerOn: "click",
        enterable: true,
        axisPointer: {
          type: "cross",
        },
        textStyle: {
          color: "#666",
        },
        padding: 10,
        extraCssText:
          "max-width: 150px;box-shadow: rgb(0 0 0 / 20%) 1px 2px 10px",
        backgroundColor: "rgba(255, 255, 255, 0.8)",
      },
      axisPointer: {
        link: { xAxisIndex: "all" },
        label: {
          backgroundColor: "#777",
        },
      },
      grid: {
        left: 20,
        right: 60,
        top: 40,
        bottom: 10,
        containLabel: true,
      },
      xAxis: {
        nameLocation: "end",
        name: "重合度",
        boundaryGap: false,
        max: 100,
        axisLine: {
          show: true,
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: true,
        },
      },
      yAxis: {
        type: "value",
        nameLocation: "end",
        name: "差异度",
        max: 100,
        axisTick: {
          show: true,
        },
        minInterval: 10,
        maxInterval: 20,
        axisLine: {
          show: true,
        },
        splitLine: {
          show: false,
        },
      },
      series: data,
    };
  }

  set charOption(options) {
    if (!this.chartObj) {
      this.chartObj = echarts.init(this.chartBox.element.nativeElement);
    }
    this.chartObj.setOption(options);
  }

  formatTips(params) {
    let html = "";
    html +=
      '<div><div style="float:left">重合度</div><div style="margin-left: 65px; font-weight: 900;word-break: break-all">' +
      params.value[0] +
      "%</div></div>";
    html +=
      '<div><div style="float:left">差异度</div><div style="margin-left: 65px; font-weight: 900;word-break: break-all">' +
      params.value[1] +
      "%</div></div>";
    this.tipsLabel.map((val, index) => {
      if (this.tipsValue[val][params.dataIndex].length) {
        const value = this.tipsValue[val][params.dataIndex];
        html +=
          '<div><div style="float:left">' +
          val +
          '</div><div style="margin-left: 65px; font-weight: 900;word-break: break-all;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;" title="' +
          value +
          '">' +
          value +
          "</div></div>";
      }
    });
    html += '<div><a id="detailTips" class="link">查看详情</a></div>';
    setTimeout(() => {
      const div = document.getElementById("detailTips");
      div.addEventListener("click", (event) => {
        const myEvent = new CustomEvent("showDetail", {
          detail: {
            overlapValue: params.value[0],
            diffValue: params.value[1],
            // tslint:disable-next-line:no-string-literal
            subjectValue: this.tipsValue["主体值"][params.dataIndex],
            // tslint:disable-next-line:no-string-literal
            objectValue: this.tipsValue["客体值"][params.dataIndex],
          },
          bubbles: true,
        });
        event.target.dispatchEvent(myEvent);
      });
    }, 200);
    return html;
  }

  /**
   * 查看详情
   */
  @HostListener("showDetail", ["$event"])
  openDialog(params) {
    this.pointParams = params.detail;

    // 打开详情弹窗
    this.showDialog = true;
  }

  closeDialog() {
    this.showDialog = false;
  }
}
