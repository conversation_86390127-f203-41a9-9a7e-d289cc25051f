package com.topsec.wilkes.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.repository.ModelHistoryRepository;
import com.topsec.wilkes.repository.entity.ModelHistoryEntity;
import com.topsec.wilkes.service.ModelHistoryService;
import com.topsec.wilkes.service.ModelService;
import com.topsec.wilkes.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ModelHistoryServiceImpl implements ModelHistoryService {

  @Autowired
  private ModelHistoryRepository modelHistoryRepository;

  @Autowired
  private ModelService modelService;

  @Override
  public List<Model> getModelHistorys(String no) {
    return modelHistoryRepository.findAllByNo(no).stream().map(ModelHistoryEntity::convertToBaseModel).collect(Collectors.toList());
  }

  @Override
  public Model getModelHistory(String no, String time) {
    Model model;
    List<ModelHistoryEntity> modelHistoryEntities = modelHistoryRepository.findByNoAndLastUpdatedTimeBefore(no, DateUtil.toDate(time));
    if (!CollectionUtils.isEmpty(modelHistoryEntities)) {
      model = modelHistoryEntities.get(0).convertToModel();
    } else {
      model = modelService.getModel(no);
    }
    return model;
  }

  @Override
  public Model createModelHistory(Model model) {
    ModelHistoryEntity modelHistoryEntity = ModelHistoryEntity.toEntity(model);
    modelHistoryEntity.setId(UUID.randomUUID().toString());
    try {
      modelHistoryEntity.setAction(ToolUtils.OBJECT_MAPPER.writeValueAsString(model.getActions()));
    } catch (JsonProcessingException e) {
      log.error("", e);
    }
    return modelHistoryRepository.saveAndFlush(modelHistoryEntity).convertToBaseModel();
  }

}


