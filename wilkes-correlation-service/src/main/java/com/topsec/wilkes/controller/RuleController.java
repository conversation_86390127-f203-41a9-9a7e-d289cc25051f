package com.topsec.wilkes.controller;

import com.topsec.minsky.dto.Result;
import com.topsec.pangu.portal.audit.annotation.AuditOperation;
import com.topsec.pangu.portal.common.enums.OperationType;
import com.topsec.wilkes.annotation.SecurityParameter;
import com.topsec.wilkes.common.dto.Rule;
import com.topsec.wilkes.dto.BusinessInfo;
import com.topsec.wilkes.service.RuleService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/rules")
@Api("模型管理")
@Slf4j
public class RuleController {

  @Autowired
  private RuleService ruleService;

  @SecurityParameter(outEncode = false)
  @RequestMapping(method = RequestMethod.POST)
  @ResponseBody
  @AuditOperation(description = "新增一条模型", operationType = OperationType.CREATE)
  public Rule createRule(@RequestBody Rule rule) {
    return ruleService.createRule(rule);
  }

  @SecurityParameter(outEncode = false)
  @RequestMapping(value = "/{no}", method = RequestMethod.PUT)
  @ResponseBody
  @AuditOperation(description = "根据模型no修改一条模型", operationType = OperationType.UPDATE)
  public Rule editRule(@PathVariable("no") String no, @RequestBody Rule rule) {
    return ruleService.editRule(no, rule);
  }

  @SecurityParameter(inDecode = false)
  @ResponseBody
  @RequestMapping(value = "/{no}", method = RequestMethod.GET)
  public Rule getRule(@PathVariable("no") String no) {
    return ruleService.getRule(no);
  }

  @SecurityParameter(inDecode = false)
  @ResponseBody
  @RequestMapping(value = "/{no}/history", method = RequestMethod.GET)
  public Rule getRuleHistory(@PathVariable("no") String no, @RequestParam("time") String time) {
    return ruleService.getRuleHistory(no, time);
  }

  @ResponseBody
  @RequestMapping(value = "/jobInfo/{id}", method = RequestMethod.GET)
  public void getJobInfo(@PathVariable("id") String id,
                         HttpServletRequest request,
                         HttpServletResponse response) {
    try {
      request.getRequestDispatcher("/models/jobInfo/" + id).forward(request, response);
    } catch (Exception e) {
      log.error("", e);
    }
  }

  @ResponseBody
  @RequestMapping(value = "/sources", method = RequestMethod.GET)
  public Set<String> getRuleSources() {
    return ruleService.getRuleSources();
  }

  @SecurityParameter(outEncode = false)
  @ResponseBody
  @RequestMapping(value = "/test", method = RequestMethod.POST)
  public Result testRule(@RequestBody Rule rule) {
    return rule != null ? ruleService.testRule(rule) : Result.failed();
  }

  @ResponseBody
  @RequestMapping(value = "/business-info", method = RequestMethod.POST)
  public BusinessInfo getEventFieldInfos(@RequestBody String process) {
    return ruleService.getBusinessInfo(process);
  }

  @SecurityParameter(inDecode = false)
  @ResponseBody
  @RequestMapping(value = "/{no}/original", method = RequestMethod.GET)
  public Map<String, Object> getRuleOriginal(@PathVariable("no") String no) {
    return ruleService.getRuleOriginal(no);
  }

  @SecurityParameter(outEncode = false)
  @ResponseBody
  @RequestMapping(value = "/{no}/customize", method = RequestMethod.PUT)
  @AuditOperation(description = "关联分析定制模型参数复原", operationType = OperationType.UPDATE)
  public Rule customizeRule(@PathVariable("no") String no,
                            @RequestBody Rule rule) {
    return ruleService.customizeRule(no, rule);
  }
}
