<div class="base-event even-detial-con">
  <form nz-form [formGroup]="baseInfoData" class="from">
    <div class="form-item" *ngIf="isDebug">
      <div class="form-label" style="text-align: left; width: 80px">
        回溯时间 <i class="icon-tps icon-con" tTooltip="仅支持7天内的数据" tooltipPosition="bottom"></i>
      </div>
      <div class="form-control" style="
          float: right;
          width: calc(100% - 80px);
          line-height: 25px !important;
        ">
        <div class="err-con">
          <div [ngClass]="{
              'in-error-con':
                baseInfoData.get('time')?.dirty &&
                baseInfoData.get('time')?.errors
            }">
            <t-double-calendar [minDate]="minDate" [initEmpty]="true" [maxDate]="maxDate" [shortcutKeys]="shortcutKeys"
              dateFormat="yyyy-MM-dd HH:mm:ss" [appendTo]="'body'" (onOkClick)="getRangeTime($event)">
            </t-double-calendar>
          </div>
        </div>
      </div>
    </div>
    <div class="form-item" *ngIf="!isDebug">
      <div class="form-label" style="text-align: left; width: 80px">
        是否启用
      </div>
      <div class="form-control" style="
          float: right;
          width: calc(100% - 80px);
          line-height: 25px !important;
        ">
        <t-radioButton name="group1" formControlName="isOpen" value="enabled" label="是" inputId="ny"
          [disabled]="isOpen">
        </t-radioButton>
        <t-radioButton name="group1" formControlName="isOpen" value="disabled" label="否" inputId="sf"
          [disabled]="isOpen">
        </t-radioButton>
        <span class="form-explain" *ngIf="
            baseInfoData.get('isOpen')?.dirty &&
            baseInfoData.get('isOpen')?.errors
          ">
          *请选择是否启用
        </span>
      </div>
    </div>
    <div class="form-item" *ngIf="!isDebug && isHistory" aas [uri]="'/wilkes/cause'">
      <div class="form-label" style="text-align: left; width: 80px">
        是否启用历史
      </div>
      <div class="form-control" style="
          float: right;
          width: calc(100% - 80px);
          line-height: 25px !important;
        ">
        <t-radioButton name="group2" formControlName="history" value="enabled" label="是" inputId="ny1"
          [disabled]="isOpen">
        </t-radioButton>
        <t-radioButton name="group2" formControlName="history" value="disabled" label="否" inputId="sf1"
          [disabled]="isOpen">
        </t-radioButton>
        <span class="form-explain" *ngIf="
            baseInfoData.get('history')?.dirty &&
            baseInfoData.get('history')?.errors
          ">
          *请选择是否启用
        </span>
      </div>
    </div>
    <div class="form-item" *ngIf="isUnion">
      <div class="form-label" style="text-align: left; width: 80px">
        数据节点
      </div>
      <div class="form-control" style="float: right; width: calc(100% - 80px)">
        <t-multiSelect [style]="{ width: '100%' }" [options]="platLists"
          appendTo="body" [disabled]="isOpen" placeholder="请选择数据节点" scrollHeight="250px" display="chip"
          formControlName="platforms">
        </t-multiSelect>
      </div>
    </div>
    <div class="form-item"  *ngIf="isConfig.subject">
      <div class="form-label" style="text-align: left; width: 80px">
        模型专题
      </div>
      <div class="form-control" style="float: right; width: calc(100% - 80px)">
        <t-multiSelect [style]="{ width: '100%' }" [options]="subjectLists" [layoutModel]="'group-horizontal'"
          appendTo="body" [disabled]="isOpen" placeholder="请选择模型专题" scrollHeight="250px" display="chip"
          formControlName="subjects">
        </t-multiSelect>
      </div>
    </div>
    <div class="form-item" *ngIf="isConfig.modelType">
      <div class="form-label" style="text-align: left; width: 80px">
        模型分类
      </div>
      <div class="form-control" style="float: right; width: calc(100% - 80px)">
        <t-cascadedSelect placeholder="请选择模型分类" [options]="modelTypes" appendTo="body" formControlName="categories"
          [disabled]="isOpen" [style]="{width: '100%'}"></t-cascadedSelect>
      </div>
    </div>
    <div class="form-item" [ngStyle]="{'display': isConfig.att ? 'block' : 'none'}">
      <div class="form-label" style="text-align: left; width: 80px">
        ATT&CK
        <i class="file-name from-list-icon icon-help" [tTooltip]="'ATT&CK受事件类型联动，事件类型改变时ATT&CK的值也会改变'"
              tooltipPosition="right"></i>
      </div>
      <div class="form-control" class="base-select-input" style="float: right; width: calc(100% - 80px)">
        <div class="over-con" *ngIf="isOpen"></div>
        <attck-selector #attck [layout]="'list'"  [layout]="'mutlist'" (onChangeHandler)="onHandleChange($event)" formControlName="attckTag"></attck-selector>
        <!-- <t-dropdown [options]="attLists" scrollHeight="250px" [style]="{ 'width': '100%'}"
          [layoutModel]="'group-horizontal'" formControlName="attckTag" placeholder="请选择ATT&CK" [showClear]="true"
          [filter]="true"></t-dropdown> -->
        <!-- <attck-selector [isOpen]="isOpen" (onChangeHandler)="onHandleChange($event)"></attck-selector> -->
      </div>
    </div>
    <div class="form-item vertical">
      <div class="form-label" style="
          text-align: left;
          width: 100%;
          line-height: 14px !important;
          margin-bottom: 10px;
        ">
        模型描述
      </div>
      <div class="form-control" style="width: 100%">
        <div class="dis" *ngIf="isOpen"></div>
        <textarea tInputTextarea [ngClass]="{ 'disable-con': isOpen }" [disabled]="isOpen" nz-input
          placeholder="请输入模型描述" formControlName="desc" style="height: 56px" class="ruleDesctex"></textarea>
        <!-- 适配盘古修改 -->
        <span class="form-explain" *ngIf="
            baseInfoData.get('desc')?.dirty && baseInfoData.get('desc')?.errors
          ">
          *请输入模型描述
        </span>
      </div>
    </div>
  </form>
</div>