import { Routes } from '@angular/router';
import { BehaviorDetailsComponent } from './components/behavior-edit/behavior-details.component';
import { BehaviorResolver } from './behavior.resolver';

export const BehaviorRoutes: Routes = [
  {
    path: 'new',
    component: BehaviorDetailsComponent,
    data: { label: 'models/behaviour/edit' },
  },
  {
    path: ':id',
    component: BehaviorDetailsComponent,
    resolve: {
      model: BehaviorResolver,
    },
    data: { label: 'models/behaviour/edit' },
  },
];
