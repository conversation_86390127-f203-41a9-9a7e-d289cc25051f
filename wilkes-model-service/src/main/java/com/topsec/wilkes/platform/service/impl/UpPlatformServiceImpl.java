package com.topsec.wilkes.platform.service.impl;

import com.topsec.wilkes.common.dto.*;
import com.topsec.wilkes.dto.platformlevel.UpPlatformCondition;
import com.topsec.wilkes.dto.Platform;
import com.topsec.wilkes.dto.RestPage;
import com.topsec.wilkes.platform.feign.ProbeService;
import com.topsec.wilkes.platform.service.PlatformService;
import com.topsec.wilkes.service.ModelService;
import com.topsec.wilkes.service.NoService;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Conditional(UpPlatformCondition.class)
public class UpPlatformServiceImpl implements PlatformService {
  @Autowired
  ProbeService probeService;

  @Autowired
  ModelService modelService;

  @Autowired
  NoService noService;

  @Override
  public Map<String, List<Platform>> getPlatforms() {
    List<Platform> platforms = probeService.getPlatforms("UP", "1", String.valueOf(Integer.MAX_VALUE), "ONLINE").getContent();
    HashMap<String, List<Platform>> platformMap = new HashMap<>();
    platformMap.put("DOWN", platforms);
    platformMap.put("UP", Collections.emptyList());
    platformMap.put("CURRENT", Lists.newArrayList());
    return platformMap;
  }

  @Override
  public Model createModel(String platformIdUrl, Model model, String platformIdHeader) {
    return probeService.createModel(platformIdUrl, model, platformIdHeader);
  }

  @Override
  public RestPage getModelsByCondition(String platformIdUrl, String source, String name, Status status, String[] categoryId, String[] killChainId,
                                       String[] subjectId, String[] dataTypeId, String[] modelNos, int pageNo, int pageSize, String sort,
                                       Sort.Direction sortType, ModelType modelType, String creator, String platformIdHeader) {
    return probeService.getModelsByCondition(platformIdUrl, source, name, status, categoryId, killChainId, subjectId, dataTypeId, modelNos, pageNo, pageSize, sort,
        sortType, modelType,creator,platformIdHeader);
  }

  @Override
  public int deleteModels(String platformIdUrl, List<String> nos, String platformIdHeader) {
    return probeService.deleteModels(platformIdUrl, nos, platformIdHeader);
  }

  @Override
  public Model editModel(String platformIdUrl, String no, Model model) {
    return probeService.editModel(platformIdUrl, no, model);
  }

  @Override
  public Map<String, Object> editModelStatus(String platformIdUrl, List<String> nos, Status status) {
    return probeService.editModelStatus(platformIdUrl, nos, status);
  }

  @Override
  public Object getModelWithRule(String platformIdUrl, String modelNo) {
    return probeService.getModelWithRules(platformIdUrl, modelNo);
  }

  @Override
  public Object getModelWithBehaviour(String platformIdUrl, String modelNo) {
    return probeService.getModelWithBehaviours(platformIdUrl, modelNo);
  }

  @Override
  public List<Tag> getTags(String platformIdUrl, String parentId, GroupId groupId, ModelType modelType, List<String> modelNos, String platformHeader) {
    return probeService.getTags(platformIdUrl, parentId, groupId, modelType, modelNos, platformHeader);
  }

  @Override
  public List<Tag> getTagsWithParentId(String platformIdUrl, String parentId, GroupId groupId, ModelType modelType, List<String> modelNos, String platformHeader) {
    return probeService.getTagsWithParentId(platformIdUrl, parentId, groupId, modelType, modelNos, platformHeader);
  }
}
