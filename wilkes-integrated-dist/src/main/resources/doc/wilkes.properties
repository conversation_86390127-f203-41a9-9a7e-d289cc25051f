aasResource=[
{"uri":"/wilkes/models/byTags","name":"æ ¹æ®æ ç­¾æ£ç´¢æ¨¡å","method":"GET"},\
{"uri":"/wilkes/models/byTags/count","name":"è·åæ¯ä¸ªæ ç­¾ä¸æ¨¡åæ°é","method":"GET"},\
{"uri":"/wilkes/models/byTags/*/*","name":"ä¿®æ¹æ ç­¾","method":"PUT"},\
{"uri":"/wilkes/models","name":"ç­éæ¨¡å","method":"GET"},\
{"uri":"/wilkes/models/*","name":"ä¿®æ¹æ¨¡å","method":"PUT"},\
{"uri":"/wilkes/models/*","name":"å é¤æ¨¡å","method":"DELETE"},\
{"uri":"/wilkes/models/*/copy","name":"æ¨¡ååå»ºå¯æ¬","method":"POST"},\
{"uri":"/wilkes/models/*/to-built-in","name":"æ¨¡åè½¬åç½®","method":"PUT"},\
{"uri":"/wilkes/models/*/*","name":"ä¿®æ¹æ¨¡å","method":"PUT"},\
{"uri":"/wilkes/models/jobInfo/*","name":"è·åæ¨¡åä»»å¡è¿è¡ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/models/model_security_source","name":"è·åæ¨¡åæè¿°","method":"GET"},\
{"uri":"/wilkes/models/export","name":"æ¨¡åå¯¼åº","method":"GET"},\
{"uri":"/wilkes/models/detection","name":"æ¨¡ååæ£æµ","method":"POST"},\
{"uri":"/wilkes/models/import/*","name":"æ¨¡åå¯¼å¥","method":"POST"},\
{"uri":"/wilkes/models/import/*","name":"åæ¶æ¨¡åå¯¼å¥","method":"DELETE"},\
{"uri":"/wilkes/models/counts","name":"æ¨¡åæ°é","method":"GET"},\
{"uri":"/wilkes/models/*/extension","name":"è·åæ¨¡åå¶å®æè¿°ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/jobs/info/*","name":"è·åJobè¿è¡ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/tags/*","name":"è·åæ ç­¾","method":"GET"},\
{"uri":"/wilkes/tags","name":"åå»ºæ ç­¾","method":"POST"},\
{"uri":"/wilkes/tags/list","name":"è·åä¸»ä¸é¢/æä¼¤é¾æ¨¡åæ°ä»¥åè¯¦æ","method":"GET"},\
{"uri":"/wilkes/tags/*","name":"å é¤æ ç­¾","method":"DELETE"},\
{"uri":"/wilkes/tags/*","name":"ä¿®æ¹æ ç­¾","method":"PUT"},\
{"uri":"/wilkes/tags","name":"è·åæ ç­¾ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/behaviour/tags","name":"è·åè¡ä¸ºåææ ç­¾","method":"GET"},\
{"uri":"/wilkes/behaviours/*/portrait/subject","name":"æ ¹æ®è¡ä¸ºå®¢ä½è¯·æ±è¡ä¸ºä¸»ä½åè¡¨","method":"POST"},\
{"uri":"/wilkes/behaviours/*/portrait/object","name":"è¯·æ±å¯¹åºæ¨¡åçè¡ä¸ºå®¢ä½åè¡¨","method":"GET"},\
{"uri":"/wilkes/behaviours/*/portrait/object","name":"æ ¹æ®è¡ä¸ºä¸»ä½è¯·æ±è¡ä¸ºå®¢ä½åè¡¨","method":"POST"},\
{"uri":"/wilkes/behaviours/*/portrait/data","name":"è¯·æ±ç»åæ°æ®æ¥å£","method":"POST"},\
{"uri":"/wilkes/behaviours/*/portrait","name":"è¯·æ±å¯¹åºæ¨¡åçè¡ä¸ºç»åä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/behaviours/*/portrait/subject","name":"è¯·æ±å¯¹åºæ¨¡åçè¡ä¸ºä¸»ä½åè¡¨","method":"GET"},\
{"uri":"/wilkes/behaviour/algorithm","name":"æ ¹æ®è¡ä¸ºå®ä¹è·åç®æ³","method":"POST"},\
{"uri":"/wilkes/behaviour/algorithm","name":"è·åææç®æ³","method":"GET"},\
{"uri":"/wilkes/behaviour/algorithm/parameter","name":"è·åç®æ³åºç¡åæ°","method":"GET"},\
{"uri":"/wilkes/behaviours/*","name":"è·åè¡ä¸ºåææ¨¡å","method":"GET"},\
{"uri":"/wilkes/behaviours/test","name":"æµè¯è¡ä¸ºåææ¨¡å","method":"POST"},\
{"uri":"/wilkes/behaviours","name":"æ°å¢è¡ä¸ºåææ¨¡å","method":"POST"},\
{"uri":"/wilkes/behaviours/parameter","name":"è·åè¡ä¸ºåææ¨¡åçé¢åæ°","method":"GET"},\
{"uri":"/wilkes/behaviours/*","name":"ç¼è¾è¡ä¸ºåææ¨¡å","method":"PUT"},\
{"uri":"/wilkes/aiAlgorithmBase/*/download","name":"ä¸è½½AIç®æ³å","method":"GET"},\
{"uri":"/wilkes/operators","name":"è·åç®å­åè¡¨","method":"GET"},\
{"uri":"/wilkes/rules/*","name":"ä¿®æ¹å³èåææ¨¡å","method":"PUT"},\
{"uri":"/wilkes/rules/jobInfo/*","name":"è·åå³èåææ¨¡åä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/rules","name":"åå»ºå³èåææ¨¡å","method":"POST"},\
{"uri":"/wilkes/rules/*","name":"è·åå³èåæä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/rules/sources","name":"è·åå³èåææ¨¡åæ°æ®æº","method":"GET"},\
{"uri":"/wilkes/rules/test","name":"å³èåææ¨¡åæµè¯","method":"POST"},\
{"uri":"/wilkes/rules/business-info","name":"è·åä¸å¡å­æ®µ","method":"POST"},\
{"uri":"/wilkes/admin/rules","name":"å³èåæç®¡çé¡µé¢åè¡¨","method":"GET"},\
{"uri":"/wilkes/upgrade/eventInfo","name":"æ´æ°äºä»¶åç±»ä¿¡æ¯","method":"POST"},\
{"uri":"/wilkes/upgrade/sourceUrl","name":"æ´æ°æ°æ®æºä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/ruleTags","name":"è·åå³èåææ ç­¾","method":"GET"},\
{"uri":"/wilkes/aiModel/*/*","name":"ä¿®æ¹æ·±åº¦åææ¨¡åç¶æ","method":"PUT"},\
{"uri":"/wilkes/aiModel/*","name":"ä¿®æ¹æ·±åº¦åææ¨¡å","method":"PUT"},\
{"uri":"/wilkes/aiModel/*","name":"è·åæ·±åº¦åææ¨¡å","method":"GET"},\
{"uri":"/wilkes/aiModel","name":"è·åæ·±åº¦åææ¨¡ååè¡¨","method":"GET"},\
{"uri":"/wilkes/aiModel/test/*","name":"æµè¯æ·±åº¦åææ¨¡å","method":"POST"},\
{"uri":"/wilkes/aiModelTags","name":"è·åæ·±åº¦åææ¨¡åæ ç­¾","method":"GET"},\
{"uri":"/wilkes/soar/playbook/list","name":"è·åå§æ¬åè¡¨","method":"GET"},\
{"uri":"/wilkes/intelligence","name":"è·åææ¥åè¡¨","method":"GET"},\
{"uri":"/wilkes/intelligence/*","name":"è·åææ¥ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/intelligence/address","name":"è·åææ¥å°å","method":"GET"},\
{"uri":"/wilkes/intelligence/threatIntelligenceTypes/*","name":"è·åå¨èææ¥ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/intelligence/list","name":"è·åç¥è¯ææ¥åºä¸å¨èææ¥åºåè¡¨","method":"GET"},\
{"uri":"/wilkes/fields/tactics","name":"è·åæææ¯æ ç­¾","method":"GET"},\
{"uri":"/wilkes/fields/eventType","name":"è·åäºä»¶åç±»","method":"GET"},\
{"uri":"/wilkes/fields/event-type-regex","name":"æ ¹æ®äºä»¶åç§°è·åäºä»¶åç±»","method":"GET"},\
{"uri":"/wilkes/fields/account-domain","name":"è·åå®å¨åä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/fields/k_c","name":"è·åæä¼¤é¾é¶æ®µ","method":"GET"},\
{"uri":"/wilkes/fields/*","name":"è·åæå®æ°æ®æºå­æ®µä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/fields/*/*","name":"è·åæå®å­æ®µä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/data-model/functions","name":"è·åå­æ®µå¯åå½æ°åè¡¨","method":"GET"},\
{"uri":"/wilkes/data-model","name":"è·åæ°æ®æ¨¡åä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/data-model/groups/tree","name":"è·åæ°æ®æ¨¡ååè¡¨æ ","method":"GET"},\
{"uri":"/wilkes/data-model","name":"åå»ºæ°æ®æ¨¡å","method":"POST"},\
{"uri":"/wilkes/data-model/*/schema","name":"è·åæ°æ®æºSchema","method":"GET"},\
{"uri":"/wilkes/data-model/*","name":"å é¤æ°æ®æ¨¡å","method":"DELETE"},\
{"uri":"/wilkes/data-model/edit/*","name":"ç¼è¾æ°æ®æ¨¡å","method":"PUT"},\
{"uri":"/wilkes/knowledgeBase","name":"è·åç¥è¯åºæ³ªé£","method":"GET"},\
{"uri":"/wilkes/knowledgeBase/threatIntelligenceTypes/*","name":"è·åå¨èææ¥ç±»å","method":"GET"},\
{"uri":"/wilkes/knowledgeBase/contents","name":"è·åå¨èææ¥ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/knowledgeBase/*","name":"è·åæå®å¨èææ¥ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/knowledgeBase/address","name":"è·åç¥è¯åºå°å","method":"GET"},\
{"uri":"/wilkes/fact-table/all-datetime-field","name":"è·åææäºä»¶ç±»åå­æ®µ","method":"GET"},\
{"uri":"/wilkes/fact-table/*/event-time-field","name":"è·åæå®æ°æ®æºçæ¶é´ç±»åå­æ®µ","method":"GET"},\
{"uri":"/wilkes/fact-table","name":"è·åäºå®è¡¨åè¡¨","method":"GET"},\
{"uri":"/wilkes/ruleSource","name":"è·åæ°æ®æº","method":"GET"},\
{"uri":"/wilkes/ruleSource","name":"è´§ç©æ°æ®æº","method":"GET"},\
{"uri":"/wilkes/models/import/lock","name":"æ¨¡åå¯¼å¥é","method":"websocket"},\
{"uri":"/wilkes/docs/*","name":"æ¨¡åå¸®å©ææ¡£","method":"GET"},\
{"uri":"/wilkes/filters/*/*","name":"è·åæ¥å¿è¿æ»¤å¨ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes-ai-model/ai_model/*/iframe/*","name":"è·åAIæ¨¡åå¨ç»","method":"GET"},\
{"uri":"/wilkes/proxy/Wilkes-Session/*","name":"è·åflinkSessionä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/jobs/info/*","name":"è·åflinkä»»å¡ä¿¡æ¯","method":"GET"},\
{"uri":"/wilkes/fields/group/*","name":"å¤ä¸ªæ°æ®æºæç»è·åç®å­å³èå­æ®µ","method":"GET"}]