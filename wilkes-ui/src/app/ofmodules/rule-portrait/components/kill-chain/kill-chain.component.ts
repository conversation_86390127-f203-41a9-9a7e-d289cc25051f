
import { AfterViewInit, Component, Input, OnChanges, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { RulePortraitService } from '../../rule-portrait.service';

@Component({
    selector: 'app-kill-chain',
    templateUrl: './kill-chain.component.html',
    styleUrls: ['./kill-chain.component.less'],
})
export class AppKillChainComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
    @Input() set modelType(d) {
        if(d) {
            this._modelType = d;
            this.event();
        }
    };
    get modelType() {
        return this._modelType;
    }
    @ViewChild('killChainBoxChart') killChainBoxChart: any;
    _modelType: any[];
    // 杀伤力链图表数据
    killChainData: any[] = [
        { name: '侦察跟踪', value: 0 },
        { name: '武器构建', value: 0 },
        { name: '载荷投递', value: 0 },
        { name: '漏洞利用', value: 0 },
        { name: '安装植入', value: 0 },
        { name: '命令与控制', value: 0 },
        { name: '目标达成', value: 0 },
        { name: '痕迹清理', value: 0 },
    ];
    constructor(
        private ht: RulePortraitService,
    ) { }

    renderKillChainBoxChart(data: any[]) {
        setTimeout(() => {
            this.killChainBoxChart.isDefault = false;
            let config = this.killChainBoxChart.tcGetConfig();
            config.dataConfig.data = data;
            this.killChainBoxChart.tcSetOption(config);
        }, 0)
    }

    ngOnInit(): void {
    }

    // 事件类型
    event() {
        const modelType = this.modelType.find(e => e.isSelect).value;
        this.ht.tagTop({ modelType,size: 10, groupId: 'KILL_CHAIN' }).subscribe(d => {
            if(d) {
                this.killChainData.forEach(e => e.value = d.find(k => k.key == e.name).value);
                this.renderKillChainBoxChart(this.killChainData);
            }
        })
    }

    ngAfterViewInit(): void {
        //this.event();
    }
    ngOnChanges() {
    }
    ngOnDestroy(): void {
       
    }
}