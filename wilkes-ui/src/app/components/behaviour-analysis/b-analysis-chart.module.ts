import { NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule  } from '@angular/forms';
import { BAnalysisChartComponent } from './b-analysis-chart.component';
import { DialogModule, DropdownModule, LoadingModule, NoDataModule, PaginatorModule, TableModule, TitleModule, TooltipModule, ScrollPanelModule } from '@tui/component-library';
import { AssembleProfileComponent } from './components/assemble-profile/assemble-profile.component';
import { ThresholdDeviationComponent } from './components/threshold-deviation/threshold-deviation.component';
import { GroupProfileComponent } from './components/group-profile/group-profile.component';
import { RelatedRuleComponent } from './components/related-rule/related-rule.component';
import { RelatedRuleInfoComponent } from './components/related-rule/components/related-rule-info/related-rule-info.component';
import { CollectionDeviationComponent } from './components/collection-deviation/collection-deviation.component';
import {BarChartModule, TChartsDirectiveModule} from '@tui/charts-library';
import { WDropdownComponent } from './components/w-dropdown/w-dropdown.component';
// import { EarthChartModule } from '../../charts/threejs/earth-chart/earth-chart.module';

@NgModule({
  declarations: [
    BAnalysisChartComponent,
    AssembleProfileComponent,
    ThresholdDeviationComponent,
    GroupProfileComponent,
    RelatedRuleComponent,
    RelatedRuleInfoComponent,
    CollectionDeviationComponent,
    WDropdownComponent
  ],
  imports: [
    NoDataModule,
    FormsModule,
    CommonModule,
    TableModule,
    DialogModule,
    PaginatorModule,
    DropdownModule,
    LoadingModule,
    TooltipModule,
    TitleModule,
    // EarthChartModule,
    ReactiveFormsModule,
    ScrollPanelModule,
    BarChartModule,
    TChartsDirectiveModule
  ],
  entryComponents: [
    BAnalysisChartComponent,
    AssembleProfileComponent,
    ThresholdDeviationComponent,
    GroupProfileComponent,
    RelatedRuleComponent
  ],
  exports: [
    BAnalysisChartComponent
  ],
  schemas: [NO_ERRORS_SCHEMA]
})
export class BAnalysisChartModule { }
