{"index_patterns": ["statistic_outlier_*"], "settings": {"index": {"number_of_shards": "1", "number_of_replicas": "0", "refresh_interval": "6s", "max_result_window": 2147483647}}, "mappings": {"properties": {"m_n": {"type": "keyword"}, "b_t": {"format": "yyyy-MM-dd HH:mm:ss", "type": "date"}, "e_t": {"format": "yyyy-MM-dd HH:mm:ss", "type": "date"}, "s_v": {"type": "keyword", "doc_values": false}, "o_v": {"type": "keyword", "doc_values": false}, "g_v": {"type": "keyword", "doc_values": true}, "a_v": {"type": "keyword", "doc_values": false}, "o_s": {"type": "double", "doc_values": false}, "model_no": {"path": "m_n", "type": "alias"}, "begin_time": {"path": "b_t", "type": "alias"}, "end_time": {"path": "e_t", "type": "alias"}, "subject_value": {"path": "s_v", "type": "alias"}, "object_value": {"path": "o_v", "type": "alias"}, "group_value": {"path": "g_v", "type": "alias"}, "agg_value": {"path": "a_v", "type": "alias"}, "outlier_score": {"path": "o_s", "type": "alias"}}}, "aliases": {}}