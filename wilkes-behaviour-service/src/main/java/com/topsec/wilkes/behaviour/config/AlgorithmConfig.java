package com.topsec.wilkes.behaviour.config;

/**
 * <AUTHOR>
 */
public interface AlgorithmConfig {
  // 学习范围
  String LEARN_SCOPE = "learn_scope";
  // 旧的学习间隔，更名为检测间隔
  String LEARN_INTERVAL = "learn_interval";
  // 旧的检测间隔
  String DETECTION_INTERVAL = "learn_interval";
  // 敏感度
  String SENITIVITY = "detection_sensitivity";
  // 群体划分
  String GROUP_FIELDS = "group_fields";
  // 群体阈值
  String AGG_THRESHOLD = "agg_threshold";
  // 重合阈值
  String COINCIDENCE_THRESHOLD = "coincidence_threshold";
  // 差异阈值
  String DEVIATION_THRESHOLD = "deviation_threshold";
}
