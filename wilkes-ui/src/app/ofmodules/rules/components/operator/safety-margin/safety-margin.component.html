<section class="safety-margin">
  <div class="bottom-con">
    <t-radioButton name="type1" label="通用" value="1" inputId="opt3" [(ngModel)]="state" (onClick)="changeState()"
      [disabled]="disabled"></t-radioButton>
    <t-radioButton name="type1" label="高级" value="2" inputId="opt4" [(ngModel)]="state" (onClick)="changeState()"
      [disabled]="disabled">
    </t-radioButton>
  </div>
  <div *ngIf="state == '1'; else select">
    <div class="bottom-con" style="margin-top: 10px;">
      <t-dropdown [options]="_options" [(ngModel)]="resultO.security_domain_direction" placeholder="请选择"
        [disabled]="disabled" [style]="{'width': '100%'}"></t-dropdown>
    </div>
  </div>
</section>
<ng-template #select>
  <div class="bottom-con" [ngClass]="{'in-error-con' : isError}">
    <label class="from_name">源安全域</label>
    <!-- 适配盘古修改 -->
    <!-- <t-tree-select  [disabled]="disabled"  ngDefaultcontrol [propagateSelectionUp]="false" (selectedFilesChange)="onNodeSelect($event, 'src')" [propagateSelectionDown]="false" [options]="selectList[1]"
        [(ngModel)]="treeModel.src"></t-tree-select> -->

    <t-multiSelect [options]="selectList[1]" [(ngModel)]="treeModel.src" placeholder="请选择源安全域 " [filter]="true"
      [showClear]="true" [disabled]="disabled" [style]="{'width': '150px'}" [maxSelectedLabels]="20"
      (onChange)="onNodeSelect($event, 'src')">
    </t-multiSelect>
  </div>
  <div class="bottom-con" [ngClass]="{'in-error-con' : isError}">
    <label class="from_name">目的安全域</label>
    <!-- 适配盘古修改 -->
    <!-- <t-tree-select  [disabled]="disabled" ngDefaultcontrol [propagateSelectionUp]="false" (selectedFilesChange)="onNodeSelect($event, 'dst')" [propagateSelectionDown]="false" [options]="selectList[1]"
        [(ngModel)]="treeModel.dst"></t-tree-select>-->
    <t-multiSelect [options]="selectList[1]" [(ngModel)]="treeModel.dst" placeholder="请选择目的安全域" [filter]="true"
      [showClear]="true" [disabled]="disabled" [style]="{'width': '150px'}" [maxSelectedLabels]="20"
      (onChange)="onNodeSelect($event, 'dst')">
    </t-multiSelect>
  </div>
</ng-template>