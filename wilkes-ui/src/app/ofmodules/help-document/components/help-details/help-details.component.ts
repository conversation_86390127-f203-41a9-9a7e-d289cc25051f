import { Component, OnInit } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { TuiAppService, TuiUserService } from '@tui/frame';

@Component({
  selector: 'app-help-details',
  templateUrl: './help-details.component.html',
  styleUrls: ['./help-details.component.css'],
})
export class HelpDetailsComponent implements OnInit {
  src: any;
  constructor(private tuiAppService: TuiAppService, private tuiUserService: TuiUserService, private routerParams: ActivatedRoute, private sanitizer: DomSanitizer) {}

  ngOnInit() {
    this.init();
  }

  init() {
    const name = this.routerParams.data['value']['label'];
    this.src = `${this.tuiAppService.getAppViewSettById('wilkes', 'wilkes').helpDocument}/${name}.pdf?principal=${this.tuiUserService.curUser.principal}`;
    this.src = this.sanitizer.bypassSecurityTrustResourceUrl(this.src);
  }
}
