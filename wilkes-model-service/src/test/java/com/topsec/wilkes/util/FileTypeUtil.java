package com.topsec.wilkes.util;

import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;


/***********************************************************************
 *
 * Description: 主要用于判断文件的类型
 *
 ***********************************************************************/

public class FileTypeUtil {

    public final static Map<String, String> FILE_TYPE_MAP = new HashMap();
    static{
        getAllFileType(); // 初始化文件类型信息
    }
    /**
     * getAllFileType,常见文件头信息
     */
    private static void getAllFileType(){
//        FILE_TYPE_MAP.put("js", "696b2e71623d696b2e71"); // js
//        FILE_TYPE_MAP.put("ps", "252150532D41646F6265");
//        FILE_TYPE_MAP.put("gz", "1f8b0800000000000000");// gz文件
//        FILE_TYPE_MAP.put("mf", "4d616e69666573742d56");// MF文件
        FILE_TYPE_MAP.put("jpg", "ffd8ffe000104a464946"); // JPEG (jpg)
        FILE_TYPE_MAP.put("png", "89504e470d0a1a0a0000"); // PNG (png)
        FILE_TYPE_MAP.put("gif", "47494638"); // GIF (gif)47494638396126026f01
//        FILE_TYPE_MAP.put("tif", "49492a00227105008037"); // TIFF (tif)
//        FILE_TYPE_MAP.put("bmp", "424d"); //图(bmp)
//        FILE_TYPE_MAP.put("dwg", "41433130313500000000"); // CAD (dwg)
//        FILE_TYPE_MAP.put("htm", "3c21646f637479706520"); // HTM (htm)
//        FILE_TYPE_MAP.put("css", "48544d4c207b0d0a0942"); // css
//        FILE_TYPE_MAP.put("rtf", "7b5c727466315c616e73"); // Rich Text Format (rtf)
//        FILE_TYPE_MAP.put("psd", "38425053000100000000"); // Photoshop (psd)
//        FILE_TYPE_MAP.put("eml", "46726f6d3a203d3f6762"); // Email [Outlook Express 6] (eml)
//        FILE_TYPE_MAP.put("doc", "d0cf11e0a1b11ae10000"); // MS Excel 注意：word、msi 和 excel的文件头一样
//        FILE_TYPE_MAP.put("xls", "d0cf11e0a1b11ae10000"); // MS Excel 注意：word、msi 和 excel的文件头一样
//        FILE_TYPE_MAP.put("vsd", "d0cf11e0a1b11ae10000"); // Visio 绘图
//        FILE_TYPE_MAP.put("mdb", "5374616E64617264204A"); // MS Access (mdb)
//        // FILE_TYPE_MAP.put("pdf", "255044462d312e350d0a"); // Adobe Acrobat (pdf)
//        FILE_TYPE_MAP.put("pdf", "255044462d312e");
//        FILE_TYPE_MAP.put("flv", "464c56010500"); // flv与f4v相同
        FILE_TYPE_MAP.put("mp4", "000000206674");
        FILE_TYPE_MAP.put("mp3", "494433030000");
        FILE_TYPE_MAP.put("mpg", "000001ba2100"); //
        FILE_TYPE_MAP.put("wmv", "3026b2758e66"); // wmv与asf相同
        FILE_TYPE_MAP.put("wav", "52494646e278"); // Wave (wav)
        FILE_TYPE_MAP.put("avi", "52494646d07d");
//        FILE_TYPE_MAP.put("mid", "4d5468640000"); // MIDI (mid)
        FILE_TYPE_MAP.put("zip", "504b03041400");
//        FILE_TYPE_MAP.put("rar", "526172211a07");
//        FILE_TYPE_MAP.put("ini", "235468697320");
//        FILE_TYPE_MAP.put("jar", "504b03040a00");
//        FILE_TYPE_MAP.put("exe", "4d5a90000300");// 可执行文件
//        FILE_TYPE_MAP.put("jsp", "3c2540207061");// jsp文件
//        FILE_TYPE_MAP.put("xml", "3c3f786d6c2076657273");// xml文件
//        FILE_TYPE_MAP.put("sql", "494e5345525420494e54");// xml文件
//        FILE_TYPE_MAP.put("bat", "406563686f206f66660d");// bat文件
//        FILE_TYPE_MAP.put("chm", "49545346030000006000");// bat文件
//        FILE_TYPE_MAP.put("mxp", "04000000010000001300");// bat文件
//        FILE_TYPE_MAP.put("wps", "d0cf11e0a1b11ae10000");// WPS文字wps、表格et、演示dps都是一样的
//        FILE_TYPE_MAP.put("html", "3c21444f435459504520"); // HTML (html)
//        FILE_TYPE_MAP.put("rmvb", "2e524d46000000120001"); // rmvb/rm相同
//        FILE_TYPE_MAP.put("java", "7061636b616765207765");// java文件
//        FILE_TYPE_MAP.put("docx", "504b0304140006000800");// docx文件
//        FILE_TYPE_MAP.put("xlsx", "504b0304140006000800");// xlsx文件
//        FILE_TYPE_MAP.put("class", "cafebabe0000002e0041");// bat文件
//        FILE_TYPE_MAP.put("torrent", "6431303a637265617465");
//        FILE_TYPE_MAP.put("properties", "6c6f67346a2e726f6f74");// bat文件
//        FILE_TYPE_MAP.put("dbx", "CFAD12FEC5FD746F"); // Outlook Express (dbx)
//        FILE_TYPE_MAP.put("mov", "6D6F6F76"); // Quicktime (mov)
//        FILE_TYPE_MAP.put("wpd", "FF575043"); // WordPerfect (wpd)
//        FILE_TYPE_MAP.put("pst", "2142444E"); // Outlook (pst)
//        FILE_TYPE_MAP.put("qdf", "AC9EBD8F"); // Quicken (qdf)
//        FILE_TYPE_MAP.put("pwl", "E3828596"); // Windows pd (pwl)
//        FILE_TYPE_MAP.put("ram", "2E7261FD"); // Real Audio (ram)
    }
    /**
     * 得到上传文件的文件头
     *
     * @param src
     * @return
     */
    public static String bytesToHexString(byte[] src){
        StringBuilder stringBuilder = new StringBuilder();
        if(src == null || src.length <= 0){
            return null;
        }
        int tempLength=100;
        if(src.length<100){
            tempLength=src.length;
        }
        for(int i = 0; i < tempLength; i++){
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if(hv.length() < 2){
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    /**
     * 根据制定文件的文件头判断其文件类型
     *
     * @param
     * @return
     */
    public static boolean getFileType(String fileType, byte[] fileBytes){
        boolean result = false;
        String fileCode = bytesToHexString(fileBytes).toLowerCase(Locale.ENGLISH);
        System.out.println(fileCode);
        String fileSourceCode = FILE_TYPE_MAP.get(fileType.toLowerCase(Locale.ENGLISH));
        if(fileCode.equals("")  || fileSourceCode.equals("")){
            return false;
        }
        System.out.println(fileSourceCode);
        System.out.println();
        if(fileSourceCode != null){
            if(fileSourceCode.toLowerCase(Locale.ENGLISH).startsWith(fileCode.toLowerCase())
                    || fileCode.toLowerCase(Locale.ENGLISH).startsWith(fileSourceCode.toLowerCase())){
                result = true;
            }
        }
        return result;
    }

    /**
     * 调用案例
     * @param args
     */
    public static void main(String[] args) {
        File file = new File("C:\\Users\\<USER>\\Desktop\\关联分析模型-2022-09-06.zip");
        FileInputStream is;
        try {
            String fileName=file.getName();
            String suffix = fileName.substring(fileName.indexOf(".") + 1);
            is = new FileInputStream(file);
            byte[] b = new byte[16];
            is.read(b, 0, b.length);
            System.out.println(FileTypeUtil.getFileType(suffix, b));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
}

