@import url("~@tui/component-library/src/style/mixin/mixin"); //  引入统一方法与变量
.rule-portrait-con {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;

 .title-iocn {
    font-size: 65px;
    position: absolute;
    left: 15px;
    top: 13px;
    z-index: 99;
    color: var(--primary-3, #E8675D) !important;
  }

  .back-img {
    height: 93px;
    width: 95px;
    max-width: 95px;
    animation: turn 8s linear infinite;
  }



  .w-switch-btn {
    position: absolute;
    top: 0;
    right: 10px;
    width: auto !important;
    margin: 0 !important;

    ::ng-deep .ui-button-text {
      display: inline-block !important;
      font-size: 14px !important;
      font-weight: 400 !important;
      color: var(--primary-color, #ae1d22) !important;
    }

    ::ng-deep .ui-button-text:hover {
      color: var(--text-color-inverse, #fff) !important;
    }


    ::ng-deep .ui-color_but .ui-button-text {
      color: var(--text-color-inverse, #fff) !important;
    }

    ::ng-deep .ui-togglebutton {
      font-size: 14px !important;
    }
  }

  .chart-title {
    position: absolute;
    display: inline-block;
    width: 200px;
    height: 26px;
    z-index: 999;
    top: 40%;
    left: 34%;
    cursor: pointer;
  }

  .sel-con {
    box-sizing: border-box;
    width: calc(100% - 48px);
    padding: 20px;
    margin: 20px 24px 0 24px;
    font-size: 14px;
    background-color: #fff;
    border: 1px #e6e6e8 solid;

    .flex-ai {
      display: flex;
      align-items: center;
    }

    .tag-con {
      position: relative;
      overflow: hidden;

      label {
        float: left;
        line-height: 26px;
      }

      ul {
        float: left;
        width: calc(100% - 137px);
        height: 28px;
        margin-left: 6px;
        overflow: hidden;

        li {
          float: left;
          min-width: 66px;
          height: 22px;
          padding: 0 5px;
          margin: 3px;
          font-size: 12px;
          line-height: 21px;
          color: #4dabca;
          text-align: center;
          cursor: pointer;
          border: 1px solid #95d8ee;
          border-radius: 6px;
        }
      }

      .more-btn {
        position: absolute;
        right: 15px;
        height: 28px;
        padding: 0 20px 0 10px;
        line-height: 27px;
        color: #6c99b6;
        text-align: center;
        cursor: pointer;
        border: 1px #6c99b6 solid;

        &::before {
          position: absolute;
          top: 2px;
          right: 5px;
        }
      }
    }

    .name-con {
      input {
        width: 460px;
        height: 34px;
        margin: 0 10px 0 7px;
        line-height: 34px;
      }

      button.btn {
        width: 90px;
        height: 34px;
      }
    }
  }
}

@keyframes turn {
  0% {
    -webkit-transform: rotate(0deg);
  }


  25% {
    -webkit-transform: rotate(90deg);
  }

  50% {
    -webkit-transform: rotate(180deg);
  }

  75% {
    -webkit-transform: rotate(270deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}