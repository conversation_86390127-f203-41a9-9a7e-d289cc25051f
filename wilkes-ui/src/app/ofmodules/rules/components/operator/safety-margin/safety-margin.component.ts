import { Component, OnInit, Input, forwardRef, AfterViewInit, Output, EventEmitter } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { RuleService } from '../../../services/rule.service';
import { ComToComService } from '../../../services/com-to-com.service';

export const DROPDOWN_VALUE_ACCESSOR: any = {
  provide: NG_VALUE_ACCESSOR,
  useExisting: forwardRef(() => SafetyMarginComponent),
  multi: true,
};

@Component({
  selector: 'app-safety-margin',
  templateUrl: './safety-margin.component.html',
  styleUrls: ['./safety-margin.component.scss'],
  providers: [DROPDOWN_VALUE_ACCESSOR],
})
export class SafetyMarginComponent implements OnInit, AfterViewInit, ControlValueAccessor {
  @Input() id = '';
  @Input() disabled = false;
  @Input() set options(v) {
    this._options = this.selectList[0] = v;
  }
  _options: any;
  @Output() change = new EventEmitter<any>();
  state = '1';
  selectList = [];
  resultO: any = { security_domain_direction: 'in2any', security_domain_prediction: { src: [], dst: [] } };
  treeModel = { src: [], dst: [] };
  isError = false;
  onModelChange: Function = () => {};
  onModelTouched: Function = () => {};

  constructor(private rs: RuleService, private comToComService: ComToComService) {}
  ngOnInit() {
    this.selectList[1] = [];
  }

  ngAfterViewInit(): void {
    this.rs.getsafetys().subscribe((res) => {
      this.selectList[1] = this.getTreeData(res);
    });
  }

  set _resultO(d) {
    this.resultO = d;
  }
  get _resultO(): any {
    return this.resultO;
  }

  writeValue(obj: any): void {
    this._resultO = obj || { security_domain_direction: 'in2any' };
    if (this.resultO.security_domain_direction) {
      this.resultO.security_domain_prediction = { src: [], dst: [] };
    } else {
      this.state = '2';
    }
    this.isError = this.comToComService.postData.includes(this.id);
  }

  save() {
    let s = '';
    if (this.state == '2') {
      s = this.treeModel.src.map((e) => e.label).toString() + '->' + this.treeModel.dst.map((e) => e.label).toString();
      s = s == '->' ? '' : s;
    } else s = this._options.find((e) => e.value === this.resultO.security_domain_direction).label;
    this.change.emit(s);
    this.onModelChange(this.resultO);
  }

  registerOnChange(fn: any): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onModelTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {}

  changeState() {
    if (this.state == '1') {
      this.resultO['security_domain_direction'] = this.selectList[0][0].value;
      this.resultO['security_domain_prediction'] = null;
    } else {
      this.resultO['security_domain_prediction'] = { src: [], dst: [] };
      this.resultO['security_domain_direction'] = '';
    }
  }

  // 公共的生成树形选择
  getTreeData(d: any[]) {
    const arr1 = [];
    const itemsFun = (label, data, value) => ({ label, data, value });
    const fun = (arr: any[], fildeObj) => {
      if (fildeObj instanceof Array && fildeObj.length > 0) {
        fildeObj.forEach((e) => {
          e.expanded = true;
          fun(arr, e);
        });
      } else {
        const o = { ...fildeObj, ...itemsFun(fildeObj.safeName, fildeObj.id, fildeObj.id) };
        this.resultO['security_domain_prediction'].src.includes(o.id) && this.treeModel.src.push(o.id);
        this.resultO['security_domain_prediction'].dst.includes(o.id) && this.treeModel.dst.push(o.id);
        arr.push(o);
        // if (fildeObj.children && fildeObj.children.length > 0) {
        //   o.children = [];
        //   fun(o.children, fildeObj.children);
        // } else {
        //   o.expanded = true;
        // }
      }
    };
    fun(arr1, d);
    return arr1;
  }

  onNodeSelect(e, k) {
    this.resultO.security_domain_prediction[k] = e.value;
  }
}
