import { Injectable, EventEmitter } from '@angular/core';
import { RuleService } from './rule.service';
import { Util } from 'src/app/utils/util';
import { catchError, forkJoin, of } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class ComToComService {
  // 模型id
  modelId = 'new';
  // 过滤展示
  sourceFilter = {
    data: [],
    header: [],
  };
  nodeMonitor: any[];
  fieldsByModel: any;
  fildesBase: any;
  allFields: any;
  nodeId = '';
  sourceData: any;
  fildeObj: any;
  fildes: any[] | undefined;
  // 1参数设置后告诉外边
  tellFormValue: any;
  // 跳转保存页面
  saveFromList: any;
  // 2告诉隐藏header
  tellHideHeader: any;
  // 3告诉外边flowchart里添加了元素
  tellOutAddEle: any;
  // 销毁
  myOnDestroy: any;
  saveDestroy: any;
  // 情报列表
  public intelligenceList!: any[];
  node!: {
    node: any;
    toolId: any;
    type: any;
    groupName: any;
    operatorName: any;
    titleName: any;
    anchor: any;
  };
  model: any = null;
  postData: string[] = [];
  portData = [];
  constructor(private ruleService: RuleService) {
    this.tellFormValue = new EventEmitter();
    this.tellHideHeader = new EventEmitter();
    this.tellOutAddEle = new EventEmitter();
    this.saveFromList = new EventEmitter();
    //@ts-ignore
    this.nodeMonitor = null;
  }

  // 触发发送formvalue的方法
  passFormValue(v: { id: any; descTaxt: any; descHtml: any; param: null }) {
    this.tellFormValue.emit(v);
  }
  // 触发保存
  saveBtnClick() {
    this.saveFromList.emit();
  }
  // 触发发送隐藏header的方法
  passHideHeader(v: any) {
    this.tellHideHeader.emit(v);
  }
  // 触发发送添加元素的方法
  passAddEle(v: any) {
    this.tellOutAddEle.emit(v);
  }

  getIntelligenceList(d: any[]) {
    this.ruleService.getIntelligenceList(d.toString()).subscribe((data) => {
      if (data) {
        const arr1 = [];
        // tslint:disable-next-line:forin
        for (const resKey in data) {
          arr1.push({ label: data[resKey], value: resKey });
        }
        this.intelligenceList = arr1;
      }
    });
  }

  // cb是否单独使用
  getFiles(
    d: string | any[],
    cb?:
      | {
          (data: any): void;
          (fildes: any): void;
          (fildes: any): void;
          (fildes: any): void;
          (data: any): void;
          (fildes: any): void;
          (arg0: { label: string; items: any[]; nodes: any[] }[] | { label: string; items: any[]; nodes: any[] }[][]): void;
        }
      | undefined,
    is?: boolean | undefined,
  ) {
    const itemsFun = (bItems: any[]) =>
      bItems
        ? bItems.map((em: { value: any; name: any }) => ({
            value: em.value,
            label: em.name,
          }))
        : '';
    const fun = (fildeObj: any[], fildSpecials?, bItems?) => {
      const arr = [];
      // tslint:disable-next-line: forin
      for (const k in fildeObj) {
        const v = fildeObj[k];
        // 条件表达式根据btype,扩展字段根据type
        const map = {};
        bItems &&
          fildSpecials &&
          bItems.forEach((bT) => {
            fildSpecials.forEach((s) => {
              const vId = s == 'kill_chain' ? 'k_c' : s;
              map[s] ? !map[s].find((m) => m.value == bT[vId + '_id']) && map[s].push({ label: bT[vId], value: bT[vId + '_id'] }) : (map[s] = [{ label: bT[vId], value: bT[vId + '_id'] }]);
            });
          });
        const items = [
          ...v.map((em: { nameEn: any; name: any; nameCn: any; bType: any; type: any; bItems: any }) => ({
            nameEn: em.nameEn,
            nameCn: em.nameCn,
            value: em.name,
            label: `${em.nameCn}(${em.nameEn})`,
            type: em.bType,
            bType: em.type,
            entity: map[em.name] ? 'specialField' : '',
            items: map[em.name] ? map[em.name] : itemsFun(em.bItems),
          })),
        ];
        this.allFields.push(...items);
        arr.push({ label: k, items, nodes: items });
      }
      return arr;
    };
    forkJoin([
      this.ruleService.getFiles(d.toString(), is).pipe(catchError((err) => of(cb ? [] : {}))),
      this.ruleService.getBitems().pipe(catchError((err) => of([]))),
      this.ruleService.getKnowledgeBase().pipe(catchError((err) => of({data: {datas: null}})))]).subscribe(
      (data: any) => {
        data = data || [];
        this.allFields = [];
        this.fildesBase = JSON.parse(JSON.stringify(data[0]));
        if (cb) {
          cb(is ? data[0].map((e: any) => fun(e)) : fun(data[0], data[1], (data[2].data || {datas: []}).datas));
        } else {
          this.fildes = fun(data[0], data[1], data[2].data.datas);
          Util.operationAnd(this.fieldsByModel, this.fieldsByModel);
        }
      },
      () => {
        cb && cb([]);
      },
    );
    // @ts-ignore
    // this.ruleService.getFiles(d.toString(), is).subscribe(
    //   (data: any) => {
    //     data = data || [];
    //     this.allFields = [];
    //     this.fildesBase = JSON.parse(JSON.stringify(data));
    //     if (cb) {
    //       cb(is ? data.map((e: any) => fun(e)) : fun(data));
    //     } else {
    //       this.fildes = fun(data);
    //       Util.operationAnd(this.fieldsByModel, this.fieldsByModel);
    //     }
    //   },
    //   () => {
    //     cb && cb([]);
    //   },
    // );
  }

  getSourceFilter(d: { toString: () => any }) {
    this.ruleService.getSourceFilter(this.modelId, d.toString()).subscribe((data) => {
      this.sourceFilter = {
        data: [],
        header: [],
      };
      for (const k in data) {
        if (data[k] && data[k].length > 0) {
          // @ts-ignore
          this.sourceFilter.header.push({
            // @ts-ignore
            field: k,
            // @ts-ignore
            header: k === 'private' ? '私有' : '全局',
          });
          if (this.sourceFilter.data.length > 0) {
            data[k].forEach(
              (e: { name: any }, i: string | number) =>
                // @ts-ignore
                (this.sourceFilter.data[i] = {
                  // @ts-ignore
                  ...(this.sourceFilter.data[i] || {}),
                  ...{ [k]: e.name },
                }),
            );
          } else {
            this.sourceFilter.data = data[k] && data[k] instanceof Array ? data[k].map((e: { name: any }) => ({ [k]: e.name })) : [];
          }
        }
      }
    });
  }

  set _sourceData(d: string[]) {
    d = Array.from(new Set(d));
    d = (d || []).filter((e: any) => e);
    this.sourceData = d;
    if (d && d.length > 0) {
      this.getFiles(d);
      // 过滤数据源展示
      this.getSourceFilter(d);
      this.getIntelligenceList(d);
    } else {
      this.fildesBase = this.fildes = this.allFields = [];
      this.sourceFilter = {
        data: [],
        header: [],
      };
      this.intelligenceList = [];
    }
  }
}
