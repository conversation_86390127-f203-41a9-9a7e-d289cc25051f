package com.topsec.wilkes.ai.service.impl.stage;

import com.topsec.wilkes.ai.config.AIConfig;
import com.topsec.wilkes.ai.dto.AIModelTask;
import com.topsec.wilkes.ai.dto.DataModelFilterWrapper;
import com.topsec.wilkes.ai.service.common.ExecutorParam;
import com.topsec.wilkes.ai.service.common.PredictionInfo;
import com.topsec.wilkes.ai.transform.CronTransform;
import com.topsec.scheduler.dto.SchedulerInfo;
import com.topsec.wilkes.config.WilkesConfig;
import com.topsec.wilkes.dto.task.StageType;
import com.topsec.wilkes.dto.task.info.BaseInfo;
import com.topsec.wilkes.service.impl.DependentServiceManager;
import com.topsec.wilkes.task.BatchSchedulerInfoGenerateService;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PredictionSchedulerInfoGenerateImpl implements BatchSchedulerInfoGenerateService {

  @Autowired
  DependentServiceManager dependentServiceManager;
  @Autowired
  AIConfig aiConfig;

  @Override
  public SchedulerInfo generateSchedulerInfo(BaseInfo baseInfo) {
    PredictionInfo predictionInfo = (PredictionInfo) baseInfo;
    ExecutorParam executorParam = new ExecutorParam();

    ExecutorParam.DataModel dataModel = new ExecutorParam.DataModel();
    DataModelFilterWrapper modelDataModel = predictionInfo.getDataModelFilterWrapper();
    DataModelFilterWrapper.Filter filter = modelDataModel.getFieldFilter();
    if (Strings.isNullOrEmpty(filter.getPredication())) {
//      dataModel.setFilter(ExpressionTransform.GenerateSQL(filter.getExpression()));
    } else {
      dataModel.setFilter(filter.getPredication());
    }
    dataModel.setData_ids(modelDataModel.getData_ids());

    AIModelTask aiModelTask = predictionInfo.getAiModelTask();

    Map<String, Object> parameterMap = new HashMap<>();
    if (aiModelTask.getParameters() != null) {
      for (AIModelTask.Parameter parameter : aiModelTask.getParameters()) {
        parameterMap.put(parameter.getName(), parameter.getValue());
      }
    }

    executorParam.setTriggerTime("${trigger_time}");
    executorParam.setNo(predictionInfo.getModel().getNo());
    executorParam.setDetail(aiModelTask.getDetail());
    executorParam.setDataModel(dataModel);
    executorParam.setParameter(parameterMap);
    executorParam.setRequest_check(Boolean.getBoolean(predictionInfo.getCommonMap().getOrDefault("request_check", "false").toString()));

    SchedulerInfo schedulerTaskInfo = new SchedulerInfo();
    schedulerTaskInfo.setId(predictionInfo.getCommonMap().containsKey("id") ? predictionInfo.getCommonMap().get("id").toString() : null);
    schedulerTaskInfo.setJobGroup(1);
    schedulerTaskInfo.setJobCron(CronTransform.generateCron(aiModelTask.getCron()));
    if (!Strings.isNullOrEmpty(aiModelTask.getDetail().getContext())) {
      schedulerTaskInfo.setJobDesc(aiModelTask.getDetail().getContext());
    } else {
      schedulerTaskInfo.setJobDesc(aiModelTask.getDetail().getProcess().stream().map(detail -> detail.getContext()).collect(Collectors.joining(",")));
    }
    schedulerTaskInfo.setAuthor("wilkes-ai");
    schedulerTaskInfo.setAlarmEmail("");
    schedulerTaskInfo.setGlueType("BEAN");
    schedulerTaskInfo.setExecutorTimeout(0);
    schedulerTaskInfo.setExecutorFailRetryCount(0);
    schedulerTaskInfo.setExecutorHandler(aiModelTask.getDetail().getType().getHandler());
    schedulerTaskInfo.setExecutorRouteStrategy("FIRST");
    schedulerTaskInfo.setExecutorBlockStrategy("SERIAL_EXECUTION");

    String minskyUrl = dependentServiceManager.getServiceUrl(WilkesConfig.MINSKY);
    String processPath = aiConfig.getAiModelPath() + "/" + predictionInfo.getModel().getNo() + "/" + aiModelTask.getDetail().getContext() + "/process.json";
    schedulerTaskInfo.setExecutorParam(String.format("POST http://%s/minsky/namespaces/ai/add %s", minskyUrl, processPath));
    return schedulerTaskInfo;
  }

  public StageType accept() {
    return StageType.PREDICTION;
  }
}
