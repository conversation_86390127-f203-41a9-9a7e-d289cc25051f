<div class="choose-playbook">
  <ng-container *ngIf="showNum == false">
    <p class="playbookDtos-icon" *ngFor="let item of selectplaybook" [ngClass]="{
        success: item.status === 'valid',
        deleted: item.status === 'deleted',
        invalid: item.status === 'invalid'
      }">
      <i [ngClass]="{
          'icon-addReduces': item.status === 'invalid',
          'icon-success': item.status === 'valid',
          'icon-error1': item.status === 'deleted'
        }" style="margin-right: 3px"></i><span [title]="item.name" style="
          display: inline-block;
          max-width: 200px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        ">{{ item.name }}</span><span class="icon-closebg icon-playbook"
        (click)="deleteChooseplaybooks(item.id, selectplaybook)"></span>
    </p>
  </ng-container>
  <p *ngIf="showNum == true" style="display: inline-block; margin-right: 10px">
    已选{{ choosePlaybook.selectedRows.length }}个
  </p>
  <button tButton [label]="
      ((choosePlaybook.selectedRows &&
        choosePlaybook.selectedRows.length > 0 &&
        '重新') ||
        '') + '选择剧本'
    " icon="icon-plus" class="ui-button-secondary" (click)="choosePlaybook.display = true"
    [disabled]="disabled"></button>
</div>
<t-dialog header="剧本列表" [(visible)]="choosePlaybook.display" styleClass="choosePlaybook-dialog"
  (onShow)="playbookListPopup()">
  <div class="choosePlaybook">
    <div class="choosePlaybook-search">
      <span style="margin-right: 10px">编号/名称</span>
      <input style="width: 300px" type="text" tInputText [(ngModel)]="choosePlaybook.table.page.condition" />
      <button tButton type="button" icon="icon-search2" label="查询" class="ui-width keep" (click)="search()"
        style="margin-right: 0px"></button>
      <button tButton type="button" icon="icon-loop" label="重置" class="ui-width keep" (click)="reset()"></button>
    </div>
    <div class="choosePlaybook-table">
      <div class="choosePlaybook-table-content">
        <t-table [columns]="choosePlaybook.table.header" [data]="choosePlaybook.table.list" selectionMode="multiple"
          [scrollable]="true" scrollHeight="100%">
          <ng-template tTemplate="header" let-columns>
            <tr class="tr-th">
              <th *ngFor="let col of columns">
                {{ col.header }}
              </th>
              <th style="width: 80px">操作</th>
            </tr>
          </ng-template>
          <ng-template tTemplate="body" let-rowData let-columns="columns">
            <tr>
              <td *ngFor="let col of columns; let i = index">
                {{ rowData[col.key] }}
              </td>
              <td style="width: 80px">
                <t-checkbox [(ngModel)]="rowData.selected" [binary]="true" (onChange)="getChange($event, rowData)">
                </t-checkbox>
              </td>
            </tr>
          </ng-template>
        </t-table>
      </div>
      <div class="choosePlaybook-table-page">
        <t-paginator *ngIf="choosePlaybook.display" [pageIndex]="choosePlaybook.table.page.backOne"
          [rows]="choosePlaybook.table.page.pageSize" [totalRecords]="choosePlaybook.table.page.total"
          [displayRecords]="true" [displayPages]="true" [jumpToPage]="true" dropdownAppendTo="body"
          [rowsPerPageOptions]="[10, 20, 30, 50]" (onPageChange)="choosePlaybookpageChange($event)"></t-paginator>
      </div>
    </div>
    <div class="has-choose-playbook">
      <p class="has-choose-playbook-block has-choose-playbook-block-tit">
        已选剧本：
      </p>
      <ul class="has-choose-playbook-block has-choose-playbook-block-list">
        <li *ngFor="let item of choosePlaybook.selectedRows">
          <span [ngClass]="{
              success: item.status === 'valid',
              deleted: item.status === 'deleted',
              invalid: item.status === 'invalid'
            }"><i [ngClass]="{
                'icon-addReduces': item.status === 'invalid',
                'icon-success': item.status === 'valid',
                'icon-error1': item.status === 'deleted'
              }" style="margin-right: 3px; vertical-align: baseline"></i>{{ item.name }}</span>
        </li>
      </ul>
    </div>
  </div>
  <t-footer>
    <button class="ui-button-primary" type="button" tButton (click)="addPlaybook()" label="确定"></button>
    <button type="button" tButton (click)="clearPlaybook()" label="取消" class="ui-button-secondary"></button>
  </t-footer>
</t-dialog>