package com.topsec.wilkes.service.impl;

import com.google.common.collect.Lists;
import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.config.WilkesConfig;
import com.topsec.wilkes.dto.OperatorTree;
import com.topsec.wilkes.service.AIAlgorithmBaseService;
import com.topsec.wilkes.service.OperatorService;
import com.topsec.wilkes.util.AIAlgorithm;
import com.topsec.wilkes.util.OperatorDefinition;
import com.topsec.wilkes.common.dto.ModelType;


import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Service
@Slf4j
public class OperatorServiceImpl implements OperatorService {

  @Autowired
  private WilkesConfig wilkesConfig;

  @Autowired
  private AIAlgorithmBaseService aiAlgorithmBaseService;

  @Override
  public Collection<OperatorTree> getOperatorsByGroup(ModelType modelType) {
    Map<OperatorDefinition.Group, OperatorTree> treeMap = new LinkedHashMap<>();
    List<OperatorDefinition> operatorDefinitions = OperatorDefinition.getVisibleOperatorDefinition();

    for (OperatorDefinition.Group group : OperatorDefinition.Group.values()) {
      if (group.visible()) {
        OperatorTree operatorTree = new OperatorTree();
        operatorTree.setGroupName(group.getName());
        List<Object> children = new ArrayList<Object>();
        operatorTree.setChildren(children);
        treeMap.put(group, operatorTree);
      }
    }
   List<String> exclude = new ArrayList<>();
    exclude.addAll(wilkesConfig.getExclude());
    if (ModelType.CORRELATION_TAG == modelType) {
      exclude.addAll(wilkesConfig.getTagExclude());
    }
    for (OperatorDefinition operatorDef : operatorDefinitions) {
      if (exclude.contains(operatorDef.getId())) {
        continue;
      }

      if (!operatorDef.getGroup().visible()) {
        continue;
      }

      if (!wilkesConfig.isViewAI() && operatorDef.getGroup() == OperatorDefinition.Group.AI) {
        continue;
      }

      OperatorTree parentGroup = treeMap.get(operatorDef.getGroup());
      if (operatorDef.getGroup() == OperatorDefinition.Group.AI) {
        Collection<OperatorDefinition> aiOperatorDefinitions = getAIOperatorDefinitions(operatorDef);
        if (aiOperatorDefinitions.size() == 0) {
          treeMap.remove(OperatorDefinition.Group.AI);
        } else {
          parentGroup.getChildren().addAll(aiOperatorDefinitions);
        }
      } else {
        if (!wilkesConfig.isRelationChooseOutput()){ //默认不支持选择relation类算子的输出字段
          if (operatorDef.getGroup()==OperatorDefinition.Group.RELATION) {
            List<Object> params = operatorDef.getParams();
            List<Object> paramResult = Lists.newArrayList();
            for (Object param : params) {
              Map map = ToolUtils.OBJECT_MAPPER.convertValue(param, Map.class);
              if ("primary_output".equals(map.get("name"))){
                continue;
              }
              paramResult.add(map);
            }
            operatorDef.setParams(paramResult);
          }
        }
        parentGroup.getChildren().add(operatorDef);
      }
    }
    List<OperatorTree> treeList = treeMap.values().stream().filter(value -> value.getChildren().size() > 0).collect(Collectors.toList());
    treeList.stream().forEach(value -> value.getChildren().sort((a, b) -> ((OperatorDefinition) a).getOrder() - ((OperatorDefinition) b).getOrder()));
    return treeList;
  }

  private Collection<OperatorDefinition> getAIOperatorDefinitions(OperatorDefinition aiOperatorDefinition) {
    List<AIAlgorithm> aiAlgorithms = aiAlgorithmBaseService.getAIAlgorithms();

    try {
      String originalDefinition = ToolUtils.OBJECT_MAPPER.writeValueAsString(aiOperatorDefinition);
      return aiAlgorithms.stream().map(aiAlgorithm -> {
        OperatorDefinition operatorDefinition = null;
        try {
          operatorDefinition = ToolUtils.OBJECT_MAPPER.readValue(originalDefinition, OperatorDefinition.class);
        } catch (IOException e) {
          log.error("", e);
        }
        operatorDefinition.setId(String.format("%s%s", operatorDefinition.getId(), aiAlgorithm.getName()));
        operatorDefinition.setName(aiAlgorithm.getDesc());
        operatorDefinition.setDesc(aiAlgorithm.getDesc());
        return operatorDefinition;
      }).collect(Collectors.toList());

    } catch (JsonProcessingException e) {
      log.warn("get modelBase-apply definition failed");
      return Collections.EMPTY_LIST;
    }
  }
}
