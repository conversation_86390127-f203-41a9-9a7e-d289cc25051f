v13_en_name,v20_en_name
DVC_T,dev_type
DVC_ID,agt_id
EVT_T,log_type
DVC_A,dev_ip
AGT_A,agt_ip
RECORDER,recorder
RECV_TIME,recv_time
AGT_ID,dev_id
CAT1,cat1
CAT2,cat2
NAME,name
S_SEC_DOM,src_sec_dom
D_SEC_DOM,dst_sec_dom
E_ID,log_id
RAW,raw
TIME,time
D_IP,dst_ip
D_PORT,dst_port
D_MAC,dst_mac
D_NATP,nat_dst_port
D_NATIP,nat_dst_ip
D_CPE,dst_cpe
D_ASSID,dst_asset_id
D_ASSN,dst_asset_name
D_ASSCAT,dst_asset_cat
D_ASSOS,dst_asset_os
D_UNIT,dst_unit
D_UNTCAT,dst_unit_cat
D_APPID,dst_infosys_id
D_SCOPE,dst_infosys_name
D_BCAT,dst_infosys_cat
D_ACOUNT,dst_account
D_UID,dst_uid
D_UNAME,dst_user
D_COUNTRY,dst_country
D_PROVINCE,dst_province
D_CITY,dst_city
D_DISTRICT,dst_district
D_ISP,dst_isp
D_LONG,dst_long
D_LATI,dst_lati
D_DESC,dst_desc
S_IP,src_ip
S_PORT,src_port
S_MAC,src_mac
S_NATP,nat_src_port
S_NATIP,nat_src_ip
S_CPE,src_cpe
S_ASSID,src_asset_id
S_ASSN,src_asset_name
S_ASSCAT,src_asset_cat
S_ASSOS,src_asset_os
S_UNIT,src_unit
S_UNITCAT,src_unit_cat
S_APPID,src_infosys_id
S_SCOPE,src_infosys_name
S_BCAT,src_infosys_cat
S_ACOUNT,src_account
S_UID,src_uid
S_UNAME,src_user
S_COUNTRY,src_country
S_PROVINCE,src_province
S_CITY,src_city
S_DISTRICT,src_district
S_ISP,src_isp
S_LONG,src_long
S_LATI,src_lati
S_DESC,src_desc
S_IPV6,src_ip6
D_IPV6,dst_ip6
CUSTOM1,custom1
CUSTOM2,custom2
CUSTOM3,custom3
CUSTOM4,custom4
CUSTOM5,custom5
DVC_V,dev_ver
VENDOR,vendor
PRO_T,proto_4
PRO_A,proto_7
FLE_P,src_file_path
FLE_N,src_file_name
FLE_S,src_file_size
TAGS,log_tags
VL_ID,vlan_id
I_BYTES,recv_bytes
O_BYTES,send_bytes
URL,url
URL_CAT,url_cat
DOMAIN,domain
PATH,http_path
PARAM,http_param
REFERER,http_referer
REQ_HEADER,http_req_hdr
REQ_METHOD,http_method
REQ_COOKIES,http_cookies
REQ_UA,http_ua
REQ_TYPE,http_req_type
REQ_BODY,http_req_body
RES_STATUS,http_res_code
RES_HEADER,http_res_hdr
RES_TYPE,http_res_type
RES_BODY,http_res_body
RES_PT,http_res_wait_time
PRO_C_IP,http_pro_cip
PRO_INT,pro_int
ENCODE,http_ac_encode
CHARSET,http_charset
SERVER,dst_app_info
PKG_T,session_total_pkts
HTTP_VER,http_ver
REQ_PAUZ,http_proxy_auz
REQ_AUZ,http_req_auz
REQ_LOCAT,http_location
REQ_ETAG,http_req_etag
RES_TIME,http_res_send_time
RES_TRAILE,http_res_traile
RES_TENCODE,http_trans_encode
RES_VIA,http_res_via
RES_PRG,http_res_prg
RES_CONNECT,http_res_conn
RES_CENCODE,http_cont_encode
RES_LAGE,http_res_lang
RES_CLOCAT,http_cont_locat
RES_CRAN,http_cont_range
RES_CLEN,http_res_len
REQ_RANG,http_range
FLE_TM,http_last_mod_time
PRO_CONN,http_proxy_conn
FLE_T,src_file_type
SD_LABEL,sd_label
SETCOOKIE,http_set_cookie
REQ_HTTPV,http_req_ver
RES_HTTPV,http_res_ver
REQ_LABNUM,http_req_labnum
RES_LABNUM,http_res_labnum
FLE_DIR,direction
URL_MD5,url_md5
HOST_MD5,domain_md5
RE_CONTT,re_contt
FLE_PN,stor_file_name
DNS_IPS,dns_req_type
DNS_STATUS,dns_query_status
OPT_DNS_MX,dns_cname
OPT_DNS_TXT,dns_mx
CNAME,dns_ttl
DNS_COU,dns_ptr
DNS_ID,direction
Q_CLASS,dns_query_type
Q_TYPE,dns_query_status
DIR_TYPE,session_total_pkts
COUNT,dns_head_info
DNS_AA,dns_trans_id
DNS_TC,dns_aa
DNS_RD,dns_tc
DNS_RA,dns_rd
RES_CODE,dns_ra
QUE_COU,dns_res_code
AN_COU,dns_ques_count
AU_COU,dns_ansr_count
AD_COU,dns_authority_count
AN_CON,dns_ansr_type
AU_CON,dns_ansr_cont
AD_CON,dns_authority_cont
RR_TTL,dns_aaaa
REQ_LEN,dns_repeat_count
REP_LEN,dns_req_len
DNS_RRRT,dns_ansr_domain
DNS_RENAME,dns_add_count
DNS_IPV6S,dns_a
DNS_ADD,dns_auth
DB_N,db_instance
DB_T,db_type
USR_NE,slave_account
DB_SQL,command
DB_VER,db_ver
DB_T_N,db_table_name
R_ID,rule_id
R_NAME,rule_name
RESULT,sql_ret_result
C_PRG,db_client_prog
C_IP,c_ip
C_USER,account
C_HOST,src_host_name
S_PRG,db_server_prog_ver
RET_RESULT,oper_result
OP_T,oper_type
DB_T_F,db_table_field
VER,netflow_ver
U_SEC,unix_sec
PK_SEQ,pkt_seq
I_PKTS,recv_pkts
FLOWS,traffic
S_TOS,src_tos
T_FLAGS,tcp_session_flag
S_MASK,src_mask
I_PUT,input_snmp
D_MASK,dst_mask
O_PUT,output_snmp
IP_NP,next_hop_ip
S_AS,src_as
D_AS,dst_as
NEXT_HOP,bnext_hop_ip
MD_PKTS,mul_dst_pkts
MD_BYTES,mul_dst_bytes
LAST,last
FIRST,first
O_PKTS,send_pkts
MIN_PKT,min_pkt_len
MAX_PKT,max_pkt_len
S_MASK6,src_mask6
D_MASK6,dst_mask6
FLOW_L6,ip6_flow_label
I_TYPE,icmp_type
MI_TYPE,igmp_type
S_INIT,samp_inter
S_ALGO,samp_algo
FAC_TO,fac_to
FINAC_TO,finac_to
EG_TYPE,engine_type
EG_ID,engine_id
TB_EXP,tb_exp
TP_EXP,tp_exp
TF_EXP,tf_exp
S_IPPR,src_ippr
D_IPPR,dst_ippr
MTL_TYPE,mtl_type
MTLIP_ADDR,mtlip_addr
FSAM_ID,fsam_id
FSAM_MODE,fsam_mode
FSR_INT,fsr_int
MIN_TTL,min_ttl
MAX_TTL,max_ttl
IP_ID,ip_id
DST_TOS,dst_tos
I_SMAC,in_src_mac
O_DMAC,out_dst_mac
S_VLAN,src_vlan
D_VLAN,dst_vlan
IPRPO_VER,iprpo_ver
DIR,direction
NEXT_HOP6,next_hop_ip6
BNEXT_HOP6,bnext_hop_ip6
O_HEADER6,ip6_option_hdr
I_DMAC,in_dst_mac
O_SMAC,out_src_mac
IF_N,if_name
IF_D,if_desc
SR_NAME,sr_name
IN_P_BYTE,in_p_byte
IN_P_PKTS,in_p_pkts
BYTES_T,session_total_bytes
TOS,ip_tos
TCP_END,tcp_end_type
HEAD_HG,head_hist
PAYLOAD_HG,payload_hist
TCPFLAG_HG,tcp_flag_hist
TCPKT_LEN,tcp_pkt_len
TCPKT_TIME,time_inr
TCPL_BD,tcp_char_count
ODF_TOLEN,odf_tolen
FIRST_P,first_pkt_time
LAST_P,last_pkt_time
S_USER,mail_sender_alias
D_USER,mail_rcvr_alias（recv）
M_PRO,m_pro
M_FROM,mail_sender
M_TO,mail_rcvr
M_CC,mail_cc
M_BCC,mail_bcc
M_SUBJECT,mail_subject
M_CONTENT,mail_cont
M_TIME,mail_send_time
ATT_N,src_file_name
RET_PATH,mail_ret_path
OP,oper
M_METH,mail_client_type
PW,password
M_CLIENT,src_app_info
FLE_TE,mail_cont_encode
M_ID,mail_id
REFERENCE,mail_reply_id
RETURN_P,mail_ret_path_hdr
RECEIVED,mail_rcvd_hdr
ATT_H,src_file_md5
MIME_TYPE,mail_mime_type
FTP_SEQ,ftp_oper_seq
MODE ,ftp_mode
PASV_PORT,ftp_pasv_port
MSG,install_action_info
PRS_NE,proc_name
S_USERG,account_type
HST_N,src_host_name
E_T_ID,event_id
BEHA_T,beha_type
SYS_N,sys_account
LG_RT,login_result
LG_T,login_type
FBI,is_forbid
GRP_IN,is_group
CHME_D,home_dir
SER_SATT,service_boot_type
CUR_STAT,service_cur_state
CUR_SATM,service_modify_state
SER_STTM,service_modify_boottype
SER_N,src_app_info
ACTSTAT,is_auto_boot
SER_STAT,service_state
SER_DESC,ser_desc
START_TIME,start_time
END_TIME,end_time
DW_T,hardware_type
SFT_T,software_type
DIS_N,display_name
INST_D,install_dir
INST_T,install_time
CG_TE,update_time
SFT_N,software_name
OLDIP,orig_ip
NWIP,new_ip
SHAR_N,share_name
LOCAL_P,local_path
BROW_AB,is_brow
D_MED_T,dst_media_type
S_MED_T,src_media_type
OBJ_T,obj_type
FLE_H,src_file_hash
PRS_PH,proc_path
PRS_PT,proc_port
PRS_PL,proc_port_proto
DEL_YN,is_delete
CLR_YN,is_clear
PRS_CD,command
PDT_VN,pkg_ver
RLE_TM,rule_time
RLE_VN,rule_ver
MLE_NE,module
RLE_TE,rule_type
LEK_NE,vul_name
LEK_ID,vul
RPR_RT,repair_result
PCH_NE,patch_name
PCH_ID,patch_id
PCH_RT,patch_install_result
OP_NAME,oper_name
OBJECT_T,obj_class
REQ_CODE,icmp_req_code
DATA,icmp_req_data
VERSION,nfs_ver
NFS_OP,nfs_oper
NFS_PROC,nfs_proc
COMMAND,command
DEAL_SIZE,nfs_trans_file_size
UNAME,account
E_T,e_t
SEVERITY,severity
K_C,kill_chain
CO_ID_LIST,co_log
REASON,reason
SOLU,solu
DUR,duration
CON_T,con_type
PKG_A,att_packets
BYTES_A,att_bytes
REQ_AGENT,http_ua
REQ_BODY   ,http_req_body
VUL,vul
ATK_STATUS,att_status
ATK_T,oper_type
ATK_DET,att_det
CUR_VALUE,thr_value
UNIT,thr_unit
THREATSCORE,threat_score
SECURITY_ID,security
