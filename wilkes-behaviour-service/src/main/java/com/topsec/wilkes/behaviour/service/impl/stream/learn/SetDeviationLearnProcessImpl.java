package com.topsec.wilkes.behaviour.service.impl.stream.learn;

import com.google.common.collect.Lists;
import com.topsec.common.utils.ToolUtils;
import com.topsec.minsky.domain.Operator;
import com.topsec.minsky.domain.Process;
import com.topsec.wilkes.behaviour.component.BaseProcessComponent;
import com.topsec.wilkes.behaviour.component.util.ConnectionUtil;
import com.topsec.wilkes.behaviour.component.util.OperatorGenerate;
import com.topsec.wilkes.behaviour.config.BehaviourAlgorithmIDConfig;
import com.topsec.wilkes.behaviour.config.BehaviourModelConfig;
import com.topsec.wilkes.behaviour.config.OperatorIDConfig;
import com.topsec.wilkes.behaviour.dto.definition.BehaviourDefinition;
import com.topsec.wilkes.behaviour.service.LearnProcessService;
import com.topsec.wilkes.dto.task.info.BaseInfo;
import com.topsec.wilkes.transform.ProcessGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (<EMAIL>)
 * @Description 首次出现行为检测算法学习Process生成实现层
 */
@Slf4j
@Service
@Deprecated
public class SetDeviationLearnProcessImpl implements LearnProcessService {
    @Autowired
    private OperatorGenerate operatorDesGenerate;

    @Autowired
    private BaseProcessComponent baseProcessComponent;

    @Autowired
    private ProcessGenerator processGenerator;

    @Override
    public Process generateLearnProcess(BaseInfo baseInfo) {
        Map<String, Object> commonMap = baseInfo.getCommonMap();
        // 获取基础数据过滤模型
        Process baseProcess = baseProcessComponent.getBaseProcess((BehaviourDefinition) commonMap.get(BehaviourModelConfig.BEHAVIOUR_DEFINITION));
        List<Operator> operators = Lists.newArrayList();
        List<Process.Connection> connections = Lists.newArrayList();
        operators.addAll(baseProcess.getOperators());
        connections.addAll(baseProcess.getConnections());

        // 连接 统计算子[统计信息输出]+学习算子+画像输出算子
        Operator learnDesc = operatorDesGenerate.getOperatorFromBaseInfo(commonMap.get(BehaviourModelConfig.OPERATOR_ID).toString(), baseInfo);
        Operator learnSinkDesc = operatorDesGenerate.getOperatorFromBaseInfo(OperatorIDConfig.PORTRAINT_SINK, baseInfo);
        Operator lastBaseDesc = operators.get(operators.size() - 1);
        operators.add(learnDesc);
        connections.add(ConnectionUtil.getConnection(lastBaseDesc, learnDesc));
        operators.add(learnSinkDesc);
        connections.add(ConnectionUtil.getConnection(learnDesc, learnSinkDesc));
        Map<String, Boolean> configMap = new HashMap<>();
        configMap.put(OperatorIDConfig.WHITE_LIST_ENABLED, false);
        configMap.put(OperatorIDConfig.SECURITY_EVENT_ENABLED, false);
        try {
            return processGenerator.getCompleteProcess(ToolUtils.OBJECT_MAPPER.writer().writeValueAsString(new Process(operators, connections)), baseInfo.getModel(), configMap);
        } catch (Exception e) {
            log.error("", e);
            return new Process();
        }
    }

    @Override
    public String accept() {
        return BehaviourAlgorithmIDConfig.SET_DEVIATION;
    }
}
