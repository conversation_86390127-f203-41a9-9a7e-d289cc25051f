package com.topsec.wilkes.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Slf4j
public class RuleBaseInfo {
  /**
   * 模型名称
   * 是否必填： 是
   * 示例： 模型名称
   */
  private String name;

  /**
   * 模型描述
   * 是否必填： 是
   * 示例： 模型描述
   */
  private String desc;

  /**
   * 攻击阶段
   * 是否必填： 否
   * 示例：
   */
  private String attckTag;

  /**
   * 模型专题
   * 是否必填： 否
   * 示例：
   */
  private List<String> subjects;

  /**
   * 模型分类
   * 是否必填： 否
   * 示例：
   */
  private List<String> categories;

  /**
   * 模型ID
   * 是否必填： 是
   * 示例："{\"operators\":[{\"id\":\"source\",\"name\":\"选择事件数据\",\"params\":{\"url\":[{\"id\":\"dns_log\"}]},\"instanceId\":\"43f212cd-294c-4216-98f3-f67e9557602d\",\"typeId\":\"1——source\",\"operatorName\":\"选择<span class=\\\"aaa file-name\\\"><span class=\\\"aaa file-name\\\">DNS日志</span></span>数据<span class=\\\"errorNode\\\">(设置异常)</span>\",\"PositionX\":200,\"PositionY\":40,\"pointArr\":[[0.5,1,0,1,0,0,\"outport1\"]]},{\"id\":\"field-filter\",\"name\":\"过滤条件筛选\",\"params\":{\"type\":\"include\",\"predication\":\"\",\"expression\":{\"node\":{\"operator\":\"and\",\"children\":[{\"leaf_node\":{\"left\":\"DVC_T\",\"operator\":\"!=\",\"right\":\"1\"}}]}}},\"instanceId\":\"0a693493-4e83-49e8-a805-dfa767a2a009\",\"typeId\":\"2——field-filter\",\"operatorName\":\"<span class=\\\"aaa file-name\\\"><span class=\\\"aaa file-name\\\">保留</span></span>符合<span class=\\\"aaa file-name\\\">过滤条件</span>的事件<span class=\\\"errorNode\\\">(设置异常)</span>\",\"PositionX\":204,\"PositionY\":196,\"pointArr\":[[0.5,0,0,-1,0,0,\"inport1\"],[0.5,1,0,1,0,0,\"outport1\"]]}],\"connections\":[{\"ConId\":\"con_14\",\"from\":\"43f212cd-294c-4216-98f3-f67e9557602d.outport1\",\"to\":\"0a693493-4e83-49e8-a805-dfa767a2a009.inport1\"}]}"
   */
  private String process;

  /**
   * 模型动作
   * 是否必填： 是
   * 示例：[
   *     {
   *         "id":"generate-security-event",
   *         "params":{
   *             "mappings":[
   *                 {
   *                     "name":"THREATSCORE",
   *                     "value":5,
   *                     "func":"constant"
   *                 },
   *                 {
   *                     "name":"TAGS",
   *                     "value":[
   *                         "域控制器"
   *                     ],
   *                     "func":"constant"
   *                 },
   *                 {
   *                     "name":"SEVERITY",
   *                     "value":"中",
   *                     "func":"constant"
   *                 },
   *                 {
   *                     "name":"NAME",
   *                     "value":"事件名称"
   *                 },
   *                 {
   *                     "name":"DESC",
   *                     "value":"事件描述"
   *                 },
   *                 {
   *                     "name":"SOLU",
   *                     "value":"处置建议"
   *                 },
   *                 {
   *                     "name":"EVENT_SUBJECT",
   *                     "value":"SGROUP"
   *                 },
   *                 {
   *                     "name":"CAT1",
   *                     "value":"僵尸",
   *                     "func":"constant"
   *                 },
   *                 {
   *                     "name":"CAT1_ID",
   *                     "value":1,
   *                     "func":"constant"
   *                 },
   *                 {
   *                     "name":"CAT2",
   *                     "value":"IRC僵尸软件",
   *                     "func":"constant"
   *                 },
   *                 {
   *                     "name":"CAT2_ID",
   *                     "value":1,
   *                     "func":"constant"
   *                 },
   *                 {
   *                     "name":"K_C",
   *                     "value":"命令与控制",
   *                     "func":"constant"
   *                 },
   *                 {
   *                     "name":"K_C_ID",
   *                     "value":6,
   *                     "func":"constant"
   *                 },
   *                 {
   *                     "name":"ATTEN",
   *                     "value":"100"
   *                 },
   *                 {
   *                     "name":"SEVERITY_ID",
   *                     "value":2,
   *                     "func":"constant"
   *                 }
   *             ],
   *             "reason_mappings":[
   *
   *             ]
   *         }
   *     },
   *     {
   *         "id":"generate-alarm-event",
   *         "params":{
   *             "mappings":[
   *                 {
   *                     "name":"GENERATE_ALARM_EVENT",
   *                     "value":true
   *                 },
   *                 {
   *                     "name":"INDEXING_FIELD",
   *                     "value":"CAT1"
   *                 },
   *                 {
   *                     "name":"INTELLIGENCE",
   *                     "value":"BLANK"
   *                 },
   *                 {
   *                     "name":"KNOWLEDGE",
   *                     "value":"{"id":"ee809c7a911bef77f82c2edfd949bc5c","knowledgeFields":"CITY,DISTRICT","fields":"DVC_T,EVT_T"}"
   *                 },
   *                 {
   *                     "name":"DELETE_KNOWLEDGE",
   *                     "value":"{"id":"47967f1d75ac9e40e2341ffab7d67753","knowledgeFields":"URL","fields":"DVC_T"}"
   *                 }
   *             ],
   *             "reason_mappings":[
   *
   *             ]
   *         }
   *     }
   * ]
   */
  private List<Action> actions;

  /**
   * 模型创建人
   * 是否必填： 是
   * 示例： system
   */
  private String creator;

  /**
   * 模型状态
   * 是否必填： 是
   * 示例： ENABLED
   */
  private Status status;

}
