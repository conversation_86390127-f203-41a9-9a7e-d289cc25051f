package com.topsec.wilkes.platform.service;

import com.topsec.wilkes.dto.ModelDistributeOperation;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface ModelDistributeOperationService {
  List<ModelDistributeOperation> create(List<ModelDistributeOperation> modelDistributeOperationList);

  ModelDistributeOperation create(ModelDistributeOperation modelDistributeOperation);

  void delete(String id);

  void deleteByModelNoAndPlatformId(String modelNo, String platformId);

  List<ModelDistributeOperation> getAll();

  ModelDistributeOperation update(ModelDistributeOperation modelDistributeOperation);

  List<ModelDistributeOperation> findByPlatformIdAndOperationNumGreaterThan(String platformId, Integer operationNum);

  List<ModelDistributeOperation> getFail(String platformId, Integer operationNum);
}
