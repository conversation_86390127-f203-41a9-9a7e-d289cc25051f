package com.topsec.wilkes.behaviour.dto;

import com.topsec.wilkes.common.dto.Action;
import com.topsec.wilkes.behaviour.dto.definition.BehaviourAlgorithm;
import com.topsec.wilkes.behaviour.dto.definition.BehaviourDefinition;
import com.topsec.wilkes.common.dto.Status;
import com.topsec.wilkes.common.dto.Event;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.common.dto.Tag;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BehaviourModel {
  public BehaviourModel() {
  }

  private String id;
  private String no;
  private String name;
  private String creator;
  private String source;
  private Date createTime;
  private Date lastUpdatedTime;
  private List<Tag> tags;
  private String desc;
  private Status status;
  private ModelType modelType;
  private List<Event> events;

  private String attckTag;
  private List<String> subjects;
  private List<String> categories;

  private List<Action> actions;
  private BehaviourDefinition behaviourDefinition;
  private BehaviourAlgorithm behaviourAlgorithm;
  private String abnormal;

  private String forceFlag;
  /**
   * 规则ID
   */
  private String ruleId;
}
