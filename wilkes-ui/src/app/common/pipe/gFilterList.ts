import { Pipe, PipeTransform } from '@angular/core';
import { FilterListPipe } from './filterList';

@Pipe({
  name: 'gFilterList',
  pure: true,
})
export class GFilterListPipe implements PipeTransform {
  transform(arr: any[], name, status): any {
    if (!arr) {
      return [];
    }
    arr = arr.filter((e) => !(e.groupName === '业务字段' && !e.isRemove));
    return new FilterListPipe().transform(arr, name, status);
  }
}
