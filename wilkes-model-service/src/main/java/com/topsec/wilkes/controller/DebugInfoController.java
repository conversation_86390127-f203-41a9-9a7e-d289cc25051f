package com.topsec.wilkes.controller;

import static com.topsec.common.utils.ToolUtils.OBJECT_MAPPER;

import com.topsec.wilkes.dto.Constants;
import com.topsec.wilkes.service.DebugInfoService;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Controller
@RequestMapping(value = "/debug")
@Slf4j
public class DebugInfoController {

  @Autowired
  private DebugInfoService debugInfoService;

  @RequestMapping(value = "/info/{no}", method = RequestMethod.GET)
  public void getResult(@PathVariable(value = "no") String no,
                        @RequestParam(value = "type") String type,
                        @RequestParam(value = "jid", required = false) String jid,
                        HttpServletResponse response, HttpServletRequest request) throws Exception {
    if (Strings.isNullOrEmpty(type)) return;
    switch (type) {
      case Constants.PROCESS:
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().println(OBJECT_MAPPER.writeValueAsString(debugInfoService.getProcess(no)));
        break;
      case Constants.GROUPED_PROCESS:
        response.setContentType("application/json;charset=UTF-8");
        setResponse(debugInfoService.getGroupedProcess(no), response);
        break;
      case Constants.LOG:
        response.setContentType("text/plain;charset=UTF-8");
        setResponse(debugInfoService.getServiceLog(), response);
        break;
      case Constants.ENGINE_LOG:
        response.setContentType("text/plain;charset=UTF-8");
        setResponse(debugInfoService.getEngineLog(), response);
        break;
      case Constants.FLINK_CONF:
        response.setContentType("text/plain;charset=UTF-8");
        setResponse(debugInfoService.getFlinkConf(no), response);
        break;
      case Constants.STDERR:
        response.setContentType("text/plain;charset=UTF-8");
        setResponse(debugInfoService.getTaskManagerStderr(no), response);
        break;
      case Constants.STDOUT:
        response.setContentType("text/plain;charset=UTF-8");
        setResponse(debugInfoService.getTaskManagerStdout(no), response);
        break;
      case Constants.SESSION_STATUS:
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().println(JSON.toJSON(debugInfoService.getSessionStatus(no)));
        break;
      case Constants.ENV:
        response.setContentType("text/html;charset=UTF-8");
        setResponse(debugInfoService.getEnv(), response);
        break;
      case Constants.JOB_LIST:
      case Constants.JOB_DETAIL:
      case Constants.JOB_EXCEPTION:
        String jobUrl;
        if (type.equals(Constants.JOB_DETAIL) || type.equals(Constants.JOB_EXCEPTION)) {
          jobUrl = debugInfoService.getJobDetailUrl(no, jid);
        } else {
          jobUrl = debugInfoService.getJobListUrl(no);
        }
        if (Strings.isNullOrEmpty(jobUrl)) {
          response.setContentType("text/html;charset=UTF-8");
          response.getWriter().print("任务不存在！");
          return;
        }
        if (type.equals(Constants.JOB_EXCEPTION)) {
          jobUrl = jobUrl + "/exceptions";
        }
        response.sendRedirect("../../../minsky" + jobUrl);
        break;
      case Constants.CONFIG:
        response.setContentType("text/plain;charset=UTF-8");
        setResponse(debugInfoService.getConfig(), response);
        break;
      case Constants.BANNER:
        response.setContentType("text/plain;charset=UTF-8");
        setResponse(debugInfoService.getBanner(), response);
        break;
      default:
        break;
    }
  }

  @RequestMapping(value = "/info/offline", method = RequestMethod.GET)
  public void getOfflineJobList(@RequestParam(value = "type") String type,
                                @RequestParam(value = "jid", required = false) String jid,
                                HttpServletResponse response,
                                HttpServletRequest request) throws Exception {
    if (Strings.isNullOrEmpty(type)) return;
    switch (type) {
      case Constants.JOB_LIST:
      case Constants.JOB_DETAIL:
      case Constants.JOB_EXCEPTION:
        String jobUrl = debugInfoService.getOfflineUrl(jid);
        if (Strings.isNullOrEmpty(jobUrl)) {
          response.setContentType("text/html;charset=UTF-8");
          response.getWriter().print("任务不存在！");
          return;
        }
        if (type.equals(Constants.JOB_EXCEPTION)) {
          jobUrl = jobUrl + "/exceptions";
        }
        response.sendRedirect("../../../minsky" + jobUrl);
        break;
      default:
        break;
    }
  }

  @RequestMapping(value = "/request", method = RequestMethod.POST)
  public void httpRequest(@RequestBody Map<String, Object> request, HttpServletResponse servletResponse) {
    String url = request.get("url").toString();
    RequestMethod method = RequestMethod.valueOf(request.get("method").toString());
    CloseableHttpClient httpClient = HttpClientBuilder.create().build();
    CloseableHttpResponse response = null;
    try {
      Map body = (Map<String, Object>) request.get("body");
      JSONObject param = new JSONObject();
      for (Object key : body.keySet()) {
        param.put((String) key, body.get(key));
      }
      StringEntity stringEntity = new StringEntity(param.toString());
      stringEntity.setContentType(ContentType.APPLICATION_JSON.toString());
      switch (method) {
        case GET:
          response = httpClient.execute(new HttpGet(url));
          break;
        case DELETE:
          response = httpClient.execute(new HttpDelete(url));
          break;
        case POST:
          HttpPost post = new HttpPost(url);
          post.setEntity(stringEntity);
          response = httpClient.execute(post);
          break;
        case PUT:
          HttpPut put = new HttpPut(url);
          put.setEntity(stringEntity);
          response = httpClient.execute(put);
        default:
          break;
      }
      servletResponse.setContentType("application/json;charset=UTF-8");
      if (response != null) {
        setResponse(response.getEntity().getContent(), servletResponse);
      }
    } catch (Exception e) {
      log.error("", e);
    } finally {
      try {
        if (httpClient != null) {
          httpClient.close();
        }
      } catch (IOException e) {
        log.error("", e);
      }
    }
  }

  @RequestMapping(value = "/config/update", method = RequestMethod.POST)
  public void updateConfig(HttpServletRequest request, HttpServletResponse response) {
    debugInfoService.updateConfig(getRequestStr(request));
    List<String> changeList = refreshConfig(response);
    debugInfoService.updateNamespace(changeList);
  }

  @RequestMapping(value = "/flink-conf/add", method = RequestMethod.POST)
  public void updateFlinkConf(@RequestParam(value = "no") String no, HttpServletRequest request, HttpServletResponse response) {
    String result = debugInfoService.addFlinkConf(no, getRequestStr(request));
    response.setContentType("text/plain;charset=UTF-8");
    try {
      response.getOutputStream().print(Strings.isNullOrEmpty(result) ? "ok" : result);
    } catch (IOException e) {
      log.error("", e);
    }

  }

  private String getRequestStr(HttpServletRequest request) {
    BufferedReader br = null;
    StringBuilder sb = new StringBuilder("");
    try {
      br = request.getReader();
      String line;
      int i = 0;
      while ((line = br.readLine()) != null) {
        if (i > 0) {
          sb.append("\n");
        }
        sb.append(line);
        i++;
      }
      br.close();
    } catch (IOException e) {
      log.error("", e);
    } finally {
      if (null != br) {
        try {
          br.close();
        } catch (IOException e) {
          log.error("", e);
        }
      }
    }
    return sb.toString();
  }

  private void setResponse(InputStream input, HttpServletResponse response) throws Exception {
    if (input == null) return;
    OutputStream osOut = response.getOutputStream();
    try {
      byte[] buf = new byte[1024];
      int bytesRead;
      while ((bytesRead = input.read(buf)) > 0) {
        osOut.write(buf, 0, bytesRead);
      }
    } finally {
      input.close();
      osOut.close();
    }
  }

  private List<String> refreshConfig(HttpServletResponse servletResponse) {
    List<String> refreshList = new LinkedList<>();
    String url = "http://localhost:22010/wilkes/actuator/refresh";
    CloseableHttpClient httpClient = HttpClientBuilder.create().build();
    HttpPost post = new HttpPost(url);
    try {
      CloseableHttpResponse response = httpClient.execute(post);
      if (response != null) {
        String result = new BufferedReader(new InputStreamReader(response.getEntity().getContent()))
            .lines().parallel().collect(Collectors.joining(System.lineSeparator()));
        refreshList.addAll(OBJECT_MAPPER.readValue(result, ArrayList.class));
        setResponse(response.getEntity().getContent(), servletResponse);
      }
      servletResponse.setContentType("application/json;charset=UTF-8");
    } catch (Exception e) {
      e.printStackTrace();
    }
    return refreshList;
  }

}
