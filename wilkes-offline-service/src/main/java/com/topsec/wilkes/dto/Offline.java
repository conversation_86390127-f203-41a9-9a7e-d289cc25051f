package com.topsec.wilkes.dto;

import com.topsec.wilkes.common.dto.Action;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.common.dto.Rule;
import com.topsec.wilkes.common.dto.Tag;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class Offline {
  private String id;
  private String modelNo;
  private String modelName;
  private ModelType modelType;
  private OfflineStatus offlineStatus;
  private Date startTime;
  private Date endTime;
  private boolean triggerResponse;
  private String creator;
  private Date createTime;
  private String content;
  private List<Action> actions;
  private OfflineType offlineType;
  private List<Tag> tags;

  private Rule rule;
}
