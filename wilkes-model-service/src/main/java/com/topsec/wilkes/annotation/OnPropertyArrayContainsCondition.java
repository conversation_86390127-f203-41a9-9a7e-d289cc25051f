package com.topsec.wilkes.annotation;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class OnPropertyArrayContainsCondition implements Condition {

  @Override
  public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
    Environment env = context.getEnvironment();
    Map<String, Object> attributes = metadata.getAnnotationAttributes(ConditionalOnPropertyArrayContains.class.getName());
    String propertyName = (String) attributes.get("propertyName");
    String expectedValue = (String) attributes.get("expectedValue");
    String defaultValue = (String) attributes.get("defaultValue");

    String[] propertyValues = env.getProperty(propertyName, String[].class, defaultValue.split(","));
    if (propertyValues != null) {
      for (String value : propertyValues) {
        if (expectedValue.equalsIgnoreCase(value)) {
          return true;
        }
      }
    }
    return false;
  }
}
