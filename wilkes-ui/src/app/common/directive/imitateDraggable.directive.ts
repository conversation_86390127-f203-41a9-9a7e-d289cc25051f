/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Directive, EventEmitter, Output, AfterViewInit, ViewContainerRef, OnInit, HostListener, Renderer2, NgZone } from '@angular/core';
@Directive({
  selector: '[imitateDraggable]',
  inputs: ['dragItem', 'dragScope'],
})
export class ImitateDraggable implements OnInit, AfterViewInit {
  bodyclassName = '';
  dragItem: any; //拖拽绑定数据
  dragScope: any; //拖拽创建区域
  overEl: any; // 移动覆盖元素用于做鼠标拖拽等动作
  mouseDownElement: any;
  mouseEvents = {
    mouseup: () => {},
    mousemove: () => {},
    scopeMouseup: () => {},
  };
  position = {
    x: 0,
    y: 0,
  };
  @Output() mouseUp: EventEmitter<any> = new EventEmitter();
  constructor(private v: ViewContainerRef, private renderer: Renderer2, private zone: Ng<PERSON>one) {}
  ngOnInit() {}

  ngAfterViewInit() {}

  @HostListener('mousedown', ['$event'])
  @HostListener('touchstart', ['$event'])
  mousedown(e) {
    this.position.x = e.clientX;
    this.position.y = e.clientY;
    this.mouseDownElement = e.target;
    this.creatOverElement();
    this.creatdragElement();
    this.bodyclassName = document.body.className;
    document.body.className = document.body.className + ' wilkes';
    setTimeout(() => {
      this.creatdragScopeElement();
    }, 0);
  }

  // 创建平台元素
  creatOverElement() {
    // 创建模拟移动父元素
    this.overEl = this.renderer.createElement('div');
    this.renderer.addClass(this.overEl, 'wilke-drag-over');
    this.renderer.appendChild(document.body, this.overEl);
    this.zone.runOutsideAngular(() => {
      for (const k in this.mouseEvents) {
        this.mouseEvents[k] = this.renderer.listen(this.overEl, k, (e) => {
          this[k + 'Over'](e);
        });
      }
    });
  }

  // 创建模拟画布元素
  creatdragScopeElement() {
    // 获取真实画布元素
    const scope = document.querySelector(this.dragScope);
    const xy = this.getPos(scope);
    // 创建模拟画布
    const dragScope = this.renderer.createElement('div');
    this.renderer.addClass(dragScope, 'wilke-drag-scope');
    this.renderer.setStyle(dragScope, 'left', xy.x + 'px');
    this.renderer.setStyle(dragScope, 'top', xy.y + 'px');
    this.renderer.setStyle(dragScope, 'height', scope['offsetHeight'] + 'px');
    this.renderer.setStyle(dragScope, 'width', scope['offsetWidth'] + 'px');
    this.renderer.appendChild(this.overEl, dragScope);
    // 算子拖拽进入区域事件
    this.zone.runOutsideAngular(() => {
      this.mouseEvents.scopeMouseup = this.renderer.listen(dragScope, 'mouseup', (e) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { parent, ...extra } = this.dragItem;
        // 当鼠标松开时发射事件
        this.mouseUp && this.mouseUp.emit({ drag: e, item: JSON.parse(JSON.stringify(extra)) });
      });
    });
  }

  // 创建模拟拖拽元素
  creatdragElement() {
    const scope = this.mouseDownElement;
    const xy = this.getPos(scope);
    const drag = this.renderer.createElement('div');
    drag.innerText = this.dragItem.name;
    this.renderer.addClass(drag, 'wilke-drag');
    this.renderer.setStyle(drag, 'left', xy.x + 'px');
    this.renderer.setStyle(drag, 'top', xy.y + 'px');
    this.renderer.setStyle(drag, 'height', scope['offsetHeight'] + 'px');
    this.renderer.setStyle(drag, 'width', scope['offsetWidth'] + 20 + 'px');
    this.renderer.appendChild(this.overEl, drag);
  }

  mouseupOver(e) {
    e.stopPropagation();
    e.preventDefault();
    document.body.className = this.bodyclassName;
    // 当鼠标松开时消除所有添加时间
    this.destroy();
  }

  mousemoveOver(e) {
    const s = document.querySelector('.wilke-drag');
    const l = parseInt(s['style']['left']);
    const t = parseInt(s['style']['top']);
    s['style']['top'] = Math.min(Math.max(0, t + (e.clientY - this.position.y))) + 'px';
    s['style']['left'] = Math.min(Math.max(0, l + (e.clientX - this.position.x))) + 'px';
    this.position.x = e.clientX;
    this.position.y = e.clientY;
  }

  destroy() {
    for (const k in this.mouseEvents) {
      this.mouseEvents[k]();
    }
    this.overEl.remove();
  }

  getPos(e) {
    // left and top value
    let x = 0,
      y = 0,
      top = 0; // offsetParent返回一个指向最近的定位元素,标准模式如果没有就返回html/body,表示迁移量都是相对其中来计算.
    while (e.offsetParent) {
      // 计算偏移量
      x += e.offsetLeft + (e.currentStyle ? this.intval(e.currentStyle.borderLeftWidth) : 0);
      y += e.offsetTop + (e.currentStyle ? this.intval(e.currentStyle.borderTopWidth) : 0); // 最近的定位元素或者body
      top += e.scrollTop || 0;
      e = e.offsetParent;
    }

    x += e.offsetLeft + (e.currentStyle ? this.intval(e.currentStyle.borderLeftWidth) : 0);
    y += e.offsetTop + (e.currentStyle ? this.intval(e.currentStyle.borderTopWidth) : 0);
    top += e.scrollTop || 0;
    return {
      x,
      y,
      top,
    };
  }

  intval(v) {
    v = parseInt(v);
    return isNaN(v) ? 0 : v;
  }
}
