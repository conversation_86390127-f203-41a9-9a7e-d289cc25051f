package com.topsec.wilkes.ai.util;

import com.topsec.wilkes.ai.service.AIModelService;
import com.topsec.wilkes.runner.ModelImportRunner;

import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> (<EMAIL>)
 */
@Component
public class AIApplicationEventListener implements ApplicationListener<ApplicationReadyEvent> {

  @Autowired
  private ModelImportRunner modelImportRunner;

  @Value("${wilkes.run.module:correlation,behaviour,ai}")
  public List<String> runModule;

  @Override
  public void onApplicationEvent(ApplicationReadyEvent event) {
    new Thread() {
      @SneakyThrows
      @Override
      public void run() {
        Thread.sleep(30 * 1000);
        if (runModule.contains("ai")) {
          AIModelService aiModelService = event.getApplicationContext().getBean(AIModelService.class);
          aiModelService.initAIModelBase();
        }
        modelImportRunner.run();
      }
    }.start();
  }
}
