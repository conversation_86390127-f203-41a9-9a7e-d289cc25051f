package com.topsec.wilkes.platform.service.impl;

import com.topsec.wilkes.dto.ModelDistribute;
import com.topsec.wilkes.dto.ModelDistributeOperation;
import com.topsec.wilkes.platform.service.ModelDistributeOperationService;
import com.topsec.wilkes.repository.ModelDistributeOperationRespository;
import com.topsec.wilkes.repository.entity.ModelDistributeOperationEntity;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.base.Strings;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ModelDistributeOperationServiceImpl implements ModelDistributeOperationService {

  private static final Integer OPERATIONNUM = 3;
  @Autowired
  ModelDistributeOperationRespository modelDistributeOperationRespository;

  //create 方法 处理了保存和创建情况
  @Override
  public List<ModelDistributeOperation> create(List<ModelDistributeOperation> modelDistributeOperationList) {
    List<ModelDistributeOperationEntity> modelDistributeOperationEntityList = modelDistributeOperationRespository.saveAll(modelDistributeOperationList.stream()
        .map(modelDistributeOperation -> {
          String modelDistributeOperationId = Strings.isNullOrEmpty(modelDistributeOperation.getId()) ?
              UUID.randomUUID().toString() : modelDistributeOperation.getId();
          modelDistributeOperation.setId(modelDistributeOperationId);
          if (null == modelDistributeOperation.getOperationNum()) {  //尚未初始化，给初始化值5
            modelDistributeOperation.setOperationNum(OPERATIONNUM);
          }
          return modelDistributeOperation.convertToModelDistributeEntity();
        }).collect(Collectors.toList()));
    return modelDistributeOperationEntityList.stream().map(modelDistributeOperationEntity -> modelDistributeOperationEntity.convertToModelDistributeOperation())
        .collect(Collectors.toList());
  }

  @Override
  public ModelDistributeOperation create(ModelDistributeOperation modelDistributeOperation) {
    String modelDistributeOperationId = Strings.isNullOrEmpty(modelDistributeOperation.getId()) ?
        UUID.randomUUID().toString() : modelDistributeOperation.getId();
    modelDistributeOperation.setId(modelDistributeOperationId);
    if (null == modelDistributeOperation.getOperationNum()) {
      modelDistributeOperation.setOperationNum(OPERATIONNUM);
    }
    return modelDistributeOperationRespository.saveAndFlush(modelDistributeOperation.convertToModelDistributeEntity()).convertToModelDistributeOperation();
  }

  @Override
  public void delete(String id) {
    modelDistributeOperationRespository.deleteById(id);
    modelDistributeOperationRespository.flush();
  }

  @Override
  public void deleteByModelNoAndPlatformId(String modelNo, String platformId) {
    modelDistributeOperationRespository.deleteByModelNoAndPlatformId(modelNo, platformId);
  }

  @Override
  public List<ModelDistributeOperation> getAll() {
    return modelDistributeOperationRespository.findModelDistributeOperationEntitiesByOrderByOperationTimeAsc().stream().map(modelDistributeOperationEntity ->
        modelDistributeOperationEntity.convertToModelDistributeOperation()).collect(Collectors.toList());
  }

  @Override
  public ModelDistributeOperation update(ModelDistributeOperation modelDistributeOperation) {
    return modelDistributeOperationRespository.saveAndFlush(modelDistributeOperation.convertToModelDistributeEntity()).convertToModelDistributeOperation();
  }

  @Override
  public List<ModelDistributeOperation> findByPlatformIdAndOperationNumGreaterThan(String platformId, Integer operationNum) {
    return modelDistributeOperationRespository.findByPlatformIdAndOperationNumGreaterThan(platformId, operationNum)
        .stream().map(modelDistributeOperationEntity -> modelDistributeOperationEntity.convertToModelDistributeOperation())
        .collect(Collectors.toList());
  }

  @Override
  public List<ModelDistributeOperation> getFail(String platformId, Integer operationNum) {
    return modelDistributeOperationRespository.findByPlatformIdAndOperationNum(platformId, operationNum)
        .stream().map(modelDistributeOperationEntity -> modelDistributeOperationEntity.convertToModelDistributeOperation().exceptionMessageDispose()).collect(Collectors.toList());
  }
}
