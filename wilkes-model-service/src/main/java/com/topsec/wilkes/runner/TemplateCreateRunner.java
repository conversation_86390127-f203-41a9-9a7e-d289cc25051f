package com.topsec.wilkes.runner;

import com.google.common.collect.Lists;

import com.topsec.dm.domain.FactTableStorageInfo;
import com.topsec.dm.domain.StorageType;
import com.topsec.dm.service.FactTableService;
import com.topsec.wilkes.annotation.EnabledBehaviour;
import com.topsec.wilkes.config.YarnModelConfig;
import com.topsec.wilkes.operator.search.util.ElasticSearch;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2022/9/28
 */
@Slf4j
@Component
@Order(3)
@EnabledBehaviour
public class TemplateCreateRunner implements ApplicationRunner {

  private final String catalinaBase = System.getProperty("catalina.base", "");
  private final String templateFilePath = String.format("%s/%s", catalinaBase, "conf/template");
  private String esUrl;

  @Autowired
  private FactTableService factTableService;
  @Autowired
  private YarnModelConfig yarnModelConfig;

  public String getEsUrl() {
    if (StringUtils.isEmpty(esUrl)) {
      initEsUrl();
    }
    return esUrl;
  }

  public void setEsUrl(String esUrl) {
    this.esUrl = esUrl;
  }

//  @PostConstruct  //todo 大禹版本下，pensieve等资源获取信息滞后，此处不能加载过早，需等checkAPP通过后再启动
  public void initEsUrl() {
    Map<String, Object> paramsMap = new HashMap<>();
    try {
      FactTableStorageInfo storageInfo = factTableService.getFactTableStorageInfo(null, StorageType.DATABASE_ES);
      Map<String, String> properties = storageInfo.getProperties();
      paramsMap.keySet().stream().forEach(key -> properties.put(key, paramsMap.get(key).toString()));
      String url = storageInfo.toURL(yarnModelConfig.getYarnMode());
      setEsUrl(url);
    } catch (Exception e) {
      log.error("无法从数据管理服务获取ES连接信息异常,可能会影响行为分析模板初始化");
    }
  }

  @Override
  public void run(ApplicationArguments args) throws Exception {
    log.info("------行为分析模板路径:" + templateFilePath);
    File templateFile = new File(templateFilePath);
    if (!templateFile.exists()) {
      return;
    }
    String esUrl = getEsUrl();
    if (StringUtils.isEmpty(esUrl)) {
      log.info("------行为分析模板初始化失败------");
      return;
    }
    log.info("------行为分析模板初始化开始------");
    for (File file : Objects.requireNonNull(templateFile.listFiles())) {
      String templateName = file.getName().split("\\.")[0];
      URI uri = new URI(esUrl.replace("null/null", templateName));
      try {
        ElasticSearch elasticSearch = new ElasticSearch(uri);
        if (!elasticSearch.templateExists(templateName)) {
          elasticSearch.createTemplate(templateName, Lists.newArrayList(templateName + "_*"), templateFilePath);
        } else {
          log.info("------行为分析模板,{}已存在------", templateName);
        }
        elasticSearch.close();
      } catch (Exception e) {
        log.error("Elasticsearch节点连接失败，创建模板异常!", e);
      }
    }
    log.info("------行为分析模板初始化结束------");
  }
}
