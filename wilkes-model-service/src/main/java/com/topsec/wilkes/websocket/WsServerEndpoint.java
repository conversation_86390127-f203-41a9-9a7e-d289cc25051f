package com.topsec.wilkes.websocket;


import static javax.websocket.CloseReason.CloseCodes.CANNOT_ACCEPT;
import static javax.websocket.CloseReason.CloseCodes.GOING_AWAY;

import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.config.TimeoutConfig;
import com.topsec.wilkes.service.ModelImportCacheService;
import com.topsec.wilkes.websocket.dto.Message;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import javax.websocket.CloseReason;
import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/models/import/lock")
public class WsServerEndpoint {
  private volatile static long lastHeartbeat;
  private static long loginTime;
  private static Session currentSession;
  private static String lastMessage = "";
  private static TimeoutConfig timeoutConfig;
  private static ModelImportCacheService modelImportCacheService;
  private static ScheduledExecutorService scheduledExecutorService;

  @OnOpen
  public void onOpen(Session session) {
    Message message = new Message(true, "");
    if (currentSession != null || !modelImportCacheService.tryLock()) {
      message.setLabel(false);
      message.setMessage(lastMessage + " 请稍后再试！");
    }
    try {
      if (session.isOpen()) {
        session.getBasicRemote().sendText(ToolUtils.OBJECT_MAPPER.writeValueAsString(message));
      }
    } catch (IOException e) {
      log.info("", e);
    }
    if (!message.isLabel() && session.isOpen()) {
      try {
        session.close(new CloseReason(CANNOT_ACCEPT, message.getMessage()));
        return;
      } catch (IOException e) {
        log.error("", e);
      }
    }
    currentSession = session;
    lastHeartbeat = System.currentTimeMillis();
    loginTime = System.currentTimeMillis();
    startHeatBeat();
  }

  @OnClose
  public void onClose(Session session) {
    if (session.getId().equals(getCurrentSessionId())) {
      modelImportCacheService.clearCache();
      stopHeatBeat();
    }
  }

  @OnMessage
  public void onMessage(String message, Session session) {
    if (session.getId().equals(getCurrentSessionId())) {
      lastHeartbeat = System.currentTimeMillis();
      lastMessage = message;
    }
  }

  @Autowired
  public void setModelImportCacheService(ModelImportCacheService modelImportCacheService) {
    WsServerEndpoint.modelImportCacheService = modelImportCacheService;
  }

  @Autowired
  public void setTimeoutConfig(TimeoutConfig timeoutConfig) {
    WsServerEndpoint.timeoutConfig = timeoutConfig;
  }

  private String getCurrentSessionId() {
    return currentSession == null ? "" : currentSession.getId();
  }

  public void startHeatBeat() {
    scheduledExecutorService = Executors.newScheduledThreadPool(1);
    scheduledExecutorService.scheduleAtFixedRate(new Runnable() {
      @Override
      public void run() {
        if ((System.currentTimeMillis() - lastHeartbeat > timeoutConfig.getHeartbeatTimeout()) ||
            (System.currentTimeMillis() - loginTime > timeoutConfig.getLoginTimeout())) {
          try {
            currentSession.close(new CloseReason(GOING_AWAY, "连接超时！"));
            onClose(currentSession);
          } catch (IOException e) {
            log.error("", e);
          }
        }
      }
    }, 0, 5, TimeUnit.SECONDS);
  }

  public void stopHeatBeat() {
    currentSession = null;
    lastHeartbeat = 0L;
    loginTime = 0L;
    lastMessage = "";
    scheduledExecutorService.shutdownNow();
  }
}
