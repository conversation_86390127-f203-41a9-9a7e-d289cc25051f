package com.topsec.minsky.transform;

import com.google.common.base.Joiner;
import com.topsec.dm.util.HttpHeaderUtil;
import com.topsec.wilkes.config.WilkesConfig;
import com.topsec.wilkes.common.dto.Status;
import com.topsec.wilkes.common.dto.Event;
import com.topsec.wilkes.common.dto.GroupId;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.common.dto.Rule;
import com.topsec.wilkes.common.dto.Tag;
import com.topsec.wilkes.util.EventUtil;
import com.topsec.wilkes.util.ProcessUtil;
import com.topsec.wilkes.util.TagUtil;

import com.google.common.base.Strings;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ModelRuleTransform {

  @Autowired
  private TagUtil tagUtil;

  @Autowired
  private WilkesConfig wilkesConfig;

  public Model convertToModel(Rule rule) {
    Model model = new Model();
    model.setActions(rule.getActions());
    model.setContent(rule.getProcess());
    if (rule.getModelType() != null) {
      model.setModelType(rule.getModelType());
    } else {
      model.setModelType(ModelType.CORRELATION);
    }
    List<Tag> tags = new LinkedList<>();
    if (rule.getSubjects() != null && rule.getSubjects().size() > 0) {
      for (String subject : rule.getSubjects()) {
        tags.add(new Tag(subject, GroupId.SUBJECT));
      }
    }
    if (rule.getCategories() != null && rule.getCategories().size() > 0) {
      for (String category : rule.getCategories()) {
        tags.add(new Tag(category, GroupId.CATEGORY));
      }
    }
    if (!Strings.isNullOrEmpty(rule.getAttckTag())) {
      String[] split = rule.getAttckTag().split(",");
      Arrays.stream(split).forEach(tagId -> tags.add(new Tag(tagId, GroupId.ATTCK_TAG)));
    }
    tags.addAll(tagUtil.getTags(ProcessUtil.toProcess(rule.getProcess())));
    tags.addAll(tagUtil.getTags(rule.getActions()));
    model.setTags(tags);
    model.setId(UUID.randomUUID().toString());
    model.setNo(rule.getNo());
    model.setName(rule.getName());
    model.setDesc(rule.getDesc());
    model.setDomainDesc(HttpHeaderUtil.getTenantConditions());
    model.setTenantId(HttpHeaderUtil.getTenantId());
    model.setReason(rule.getReason());
    model.setHistory(rule.getHistory());
    model.setPlatforms(rule.getPlatforms());
    model.setRuleId(rule.getRuleId());
    if (Strings.isNullOrEmpty(rule.getSource())) {
      model.setSource("自定义");
    } else {
      model.setSource(rule.getSource());
    }

    model.setStatus(rule.getStatus() == Status.ENABLED ? Status.ENABLED : Status.DISABLED);
    return model;
  }

  public Rule convertFromModel(Model model) {
    Rule rule = new Rule();
    rule.setId(model.getId());
    rule.setName(model.getName());
    rule.setNo(model.getNo());
    rule.setDesc(model.getDesc());
    List<String> subjects = new LinkedList<>();
    List<String> categories = new LinkedList<>();
    List<String> attack = new LinkedList<>();
    if (!CollectionUtils.isEmpty(model.getTags())) {
      for (Tag tag : model.getTags()) {
        if (tag.getGroupId() == GroupId.SUBJECT) {
          subjects.add(tag.getId());
          continue;
        }
        if (tag.getGroupId() == GroupId.CATEGORY) {
          categories.add(tag.getId());
          continue;
        }
        if (tag.getGroupId() == GroupId.ATTCK_TAG) {
          attack.add(tag.getId());
          continue;
        }
      }
    }
    rule.setAttckTag(Joiner.on(",").join(attack));
    rule.setSubjects(subjects);
    rule.setCategories(categories);
    rule.setTags(model.getTags());
    rule.setSource(model.getSource());
    rule.setProcess(model.getContent());
    rule.setActions(model.getActions());
    rule.setCreator(model.getCreator());
    rule.setCreateTime(model.getCreateTime());
    rule.setLastUpdatedTime(model.getLastUpdatedTime());
    rule.setStatus(model.getStatus());
    rule.setModelType(model.getModelType());
    List<Event> events = EventUtil.getEvents(model.getActions()).stream()
        .filter(event -> !wilkesConfig.getExcludeViewEvents().contains(event.getId()))
        .collect(Collectors.toList());
    rule.setEvents(events);
    rule.setAbnormal(model.getAbnormal());
    rule.setReason(model.getReason());
    rule.setHistory(model.getHistory());
    rule.setPlatforms(model.getPlatforms());
    rule.setRuleId(model.getRuleId());
    return rule;
  }
}
