package com.topsec.wilkes.service.impl;


import org.junit.Test;

/**
 * <AUTHOR>
 */
class MonitorInfoServiceImplTest {

  @Test
  void uploadMonitorRateInfo() {
    String str="{\n" +
        "  \"00002\": [\n" +
        "    {\n" +
        "      \"modelNo\": \"00002\",\n" +
        "      \"instanceId\": \"df0decfb-b4a3-4b83-b549-cb7e7ddb46ab-action1\",\n" +
        "      \"rate\": 86,\n" +
        "      \"eventType\": \"security_log\",\n" +
        "      \"group\": \"\",\n" +
        "      \"sinkFrom\": true\n" +
        "    },\n" +
        "    {\n" +
        "      \"modelNo\": \"00002\",\n" +
        "      \"instanceId\": \"2d15661c-e12b-4856-aa6f-a2cb2f06c5a5\",\n" +
        "      \"rate\": 2,\n" +
        "      \"eventType\": \"\",\n" +
        "      \"group\": \"continuous-happen-cep\",\n" +
        "      \"sinkFrom\": false\n" +
        "    },\n" +
        "    {\n" +
        "      \"modelNo\": \"00002\",\n" +
        "      \"instanceId\": \"d64ed433-9854-430e-851a-f2e9ffc3f7ed-http_log\",\n" +
        "      \"rate\": 30,\n" +
        "      \"eventType\": \"http_log\",\n" +
        "      \"group\": \"source\",\n" +
        "      \"sinkFrom\": false\n" +
        "    },\n" +
        "    {\n" +
        "      \"modelNo\": \"00002\",\n" +
        "      \"instanceId\": \"27d83459-e00e-4ce0-8f0a-fcd551a9e476\",\n" +
        "      \"rate\": 1,\n" +
        "      \"eventType\": \"\",\n" +
        "      \"group\": \"model-triggered-timer\",\n" +
        "      \"sinkFrom\": false\n" +
        "    },\n" +
        "    {\n" +
        "      \"modelNo\": \"00002\",\n" +
        "      \"instanceId\": \"d64ed433-9854-430e-851a-f2e9ffc3f7ed-http_log_filter-adapter\",\n" +
        "      \"rate\": 66,\n" +
        "      \"eventType\": \"\",\n" +
        "      \"group\": \"filter-adapter\",\n" +
        "      \"sinkFrom\": false\n" +
        "    }\n" +
        "  ]\n" +
        "}\n";
    MonitorInfoServiceImpl monitorInfoService = new MonitorInfoServiceImpl();
    monitorInfoService.uploadMonitorRateInfo(str);
  }
}