package com.topsec.wilkes.behaviour.service.impl.util;


import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.topsec.wilkes.behaviour.config.AlgorithmConfig;
import com.topsec.wilkes.behaviour.dto.BehaviourModel;
import com.topsec.wilkes.behaviour.dto.definition.BehaviourAlgorithm;
import com.topsec.wilkes.behaviour.dto.definition.BehaviourDefinition;
import com.topsec.wilkes.operator.search.SearchConfig;
import com.topsec.wilkes.operator.type.TimePeriod;
import lombok.SneakyThrows;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.topsec.wilkes.behaviour.constants.AlgorithmCatalog.INDIVIDUAL_COLLECTION;
import static com.topsec.wilkes.behaviour.constants.AlgorithmCatalog.INDIVIDUAL_GROUP;

/**
 * <AUTHOR>
 */
public class StatisticTimeUtil {
  public static final ThreadLocal<DateFormat> DATE_FORMAT = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
  private static final long ONE_MINUTE = 60 * 1000L;
  private static final long ONE_HOUR = 60 * ONE_MINUTE;
  private static final long ONE_DAY = 24 * ONE_HOUR;

  public static String getScope(BehaviourModel behaviourModel) {
    BehaviourAlgorithm behaviourAlgorithm = behaviourModel.getBehaviourAlgorithm();
    BehaviourDefinition behaviourDefinition = behaviourModel.getBehaviourDefinition();
    String periodic = behaviourDefinition.getPeriodic();
    if (!Strings.isNullOrEmpty(periodic) && !"none".equals(periodic)) {
      int statisticSize = Integer.parseInt(periodic.split(" +")[0]);
      String statisticUnit = periodic.split(" +")[1];
      return String.format("%s %s", 7 * statisticSize, statisticUnit);
    } else {
      Object detectionIntervalObject = behaviourAlgorithm.getParams().get(AlgorithmConfig.DETECTION_INTERVAL);
      String statisticInterval;
      if (detectionIntervalObject == null) {
        statisticInterval = behaviourAlgorithm.getParams().get(AlgorithmConfig.LEARN_INTERVAL).toString();
      } else {
        statisticInterval = detectionIntervalObject.toString();
      }
      if (INDIVIDUAL_GROUP.contains(behaviourAlgorithm.getId())) {
        return statisticInterval;
      }
      int scopeSize;
      int statisticSize = Integer.parseInt(statisticInterval.split(" +")[0]);
      String statisticUnit = statisticInterval.split(" +")[1];
      //集合偏离多查一时数据往前推补0
      if (INDIVIDUAL_COLLECTION.contains(behaviourAlgorithm.getId())) {
        switch (TimeUnit.valueOf(String.format("%s%s", statisticUnit.toUpperCase(), "S"))) {
          case MINUTES:
            //例如统计间隔5分钟，展示最近一个小时的数据
            scopeSize = statisticSize * 13;
            break;
          case HOURS:
            //例如统计间隔为1小时，展示最近一天的数据
            scopeSize = statisticSize * 25;
            break;
          case DAYS:
            //例如统计间隔为1天，展示最近七天的数据
            scopeSize = statisticSize * 8;
            break;
          default:
            throw new RuntimeException("GetScope error with " + statisticUnit.toUpperCase());
        }
      } else {
        switch (TimeUnit.valueOf(String.format("%s%s", statisticUnit.toUpperCase(), "S"))) {
          case MINUTES:
            //例如统计间隔5分钟，展示最近一个小时的数据
            scopeSize = statisticSize * 12;
            break;
          case HOURS:
            //例如统计间隔为1小时，展示最近一天的数据
            scopeSize = statisticSize * 24;
            break;
          case DAYS:
            //例如统计间隔为1天，展示最近七天的数据
            scopeSize = statisticSize * 7;
            break;
          default:
            throw new RuntimeException("GetScope error with " + statisticUnit.toUpperCase());
        }
      }

      return String.format("%s %s", scopeSize, statisticUnit);
    }
  }

  @SneakyThrows
  public static List<String> getTimeRange(String scope, String currentTime) {
    Date date = Strings.isNullOrEmpty(currentTime) ? new Date() : new Date(DATE_FORMAT.get().parse(currentTime).getTime());
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    int scopeSize = Integer.parseInt(scope.split(" +")[0]);
    String scopeUnit = scope.split(" +")[1];

    switch (TimeUnit.valueOf(String.format("%s%s", scopeUnit.toUpperCase(), "S"))) {
      case MINUTES:
        calendar.add(Calendar.MINUTE, -1 * scopeSize);
        break;
      case HOURS:
        calendar.add(Calendar.HOUR, -1 * scopeSize);
      break;
      case DAYS:
        calendar.add(Calendar.DATE, -1 * scopeSize);
        break;
      default:
        throw new RuntimeException("GetScope error with " + scopeUnit.toUpperCase());
    }
    return Lists.newArrayList(DATE_FORMAT.get().format(calendar.getTime()), DATE_FORMAT.get().format(date));
  }

  @SneakyThrows
  public static String getNextTimeStr(String scope, String nowTime) {
    long curTime = DATE_FORMAT.get().parse(nowTime).getTime();

    Calendar calendar = Calendar.getInstance();
    int scopeSize = Integer.parseInt(scope.split(" +")[0]);
    String scopeUnit = scope.split(" +")[1];

    switch (TimeUnit.valueOf(String.format("%s%s", scopeUnit.toUpperCase(), "S"))) {
      case MINUTES:
        curTime = curTime - (curTime % ONE_MINUTE);
        calendar.setTime(new Date(curTime));
        calendar.add(Calendar.MINUTE, scopeSize);
        break;
      case HOURS:
        curTime = curTime - (curTime % ONE_HOUR);
        calendar.setTime(new Date(curTime));
        calendar.add(Calendar.HOUR, scopeSize);
        break;
      case DAYS:
        curTime = curTime - (curTime % ONE_DAY);
        calendar.setTime(new Date(curTime));
        calendar.add(Calendar.DATE, scopeSize);
        break;
      default:
        throw new RuntimeException("GetScope error with " + scopeUnit.toUpperCase());
    }
    return DATE_FORMAT.get().format(calendar.getTime());
  }

  public static Map<String, String> getScopeByAddTime(long time, String interval, int spanOfInterval) {
    String[] scopeArr = interval.split(" +");
    int scopeValue = Integer.parseInt(scopeArr[0]);
    String scopeTimeUnit = scopeArr[1].toUpperCase();

    Date date = new Date();
    date.setTime(time);
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    switch (TimePeriod.valueOf(scopeTimeUnit)) {
      case YEAR:
        calendar.add(Calendar.YEAR, spanOfInterval * scopeValue);
        break;
      case MONTH:
        calendar.add(Calendar.MONTH, spanOfInterval * scopeValue);
        break;
      case DAY:
        calendar.add(Calendar.DATE, spanOfInterval * scopeValue);
        break;
      case HOUR:
        calendar.add(Calendar.HOUR_OF_DAY, spanOfInterval * scopeValue);
        break;
      case MINUTE:
        calendar.add(Calendar.MINUTE, spanOfInterval * scopeValue);
        break;
      default:
    }

    Map<String, String> timeMap = Maps.newHashMap();
    if (calendar.getTime().getTime() > date.getTime()) {
      timeMap.put(SearchConfig.FROM, DATE_FORMAT.get().format(date));
      timeMap.put(SearchConfig.TO, DATE_FORMAT.get().format(calendar.getTime()));
    } else {
      timeMap.put(SearchConfig.FROM, DATE_FORMAT.get().format(calendar.getTime()));
      timeMap.put(SearchConfig.TO, DATE_FORMAT.get().format(date));
    }

    return timeMap;
  }

  public static String getCurrentTimeStr() {
    return DATE_FORMAT.get().format(new Date());
  }
}
