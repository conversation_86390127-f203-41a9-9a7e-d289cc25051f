{"operators": [{"id": "source", "instanceId": "e068b573-049b-44e0-9871-cdab4b4cfdb6", "params": {"url": "kafka://************:9092/wilkes_test"}}, {"id": "field-filter", "instanceId": "05e5ecd8-3d0b-4198-a7f9-aa4c905f7266", "params": {"type": "include", "predication": "srcIP='*********'"}}, {"id": "action", "instanceId": "f29032f1-dbb7-4245-88af-00c9fe8855c9", "params": {"actions": []}}, {"id": "sink", "instanceId": "sink_05e5ecd8-3d0b-4198-a7f9-aa4c905f7266", "params": {"url": "kafka://************:9092/wikes_test_out"}}], "connections": [{"from": "e068b573-049b-44e0-9871-cdab4b4cfdb6.outport1", "to": "05e5ecd8-3d0b-4198-a7f9-aa4c905f7266.inport1"}, {"from": "05e5ecd8-3d0b-4198-a7f9-aa4c905f7266.outport1", "to": "f29032f1-dbb7-4245-88af-00c9fe8855c9.inport1"}, {"from": "f29032f1-dbb7-4245-88af-00c9fe8855c9.outport1", "to": "sink_05e5ecd8-3d0b-4198-a7f9-aa4c905f7266.inport1"}]}