@import url("~@tui/component-library/src/style/mixin/mixin"); //  引入统一方法与变量

:host {
  .table-operatebox t-splitbutton {
    margin-right: 5px;

    ::ng-deep .ui-button,
    ::ng-deep .ui-button .ui-buttonset {
      margin-right: 1px !important;
      color: #fff;
      background-color: #efb73f !important;
      border-color: #efb73f !important;

      &:enabled:hover {
        color: #fff;
      }
    }

    ::ng-deep .ui-button .ui-button-text {
      display: none;
    }
  }

  .recall-list {
    position: relative;
    display: flex;
    flex-direction: column;
    width: calc(100%);
    height: 100%;
    padding: 10px;
    background: #fff;
    border: 1px solid #e2e2e2;
    border-radius: 3px;

    .loading-mask {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 3;
    }

    .loading-mask-zindex {
      position: relative;
      z-index: 2;
    }

    .recall-search {
      padding: 18px 0;
      margin-bottom: 10px;
      border-bottom: 1px solid #dadbdd;

      .input-group {
        display: inline-block;
        margin-right: 20px;

        label {
          margin-right: 10px;
        }

        .reset-button {
          background-color: #595959 !important;
          border-color: #595959 !important;
        }
      }
    }

    .recall-add {
      z-index: 9;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      height: 30px;
      margin-bottom: 10px;

      .common-tips {
        margin-left: 20px;
        line-height: 30px;

        span {
          vertical-align: bottom !important;
        }

        .tips-icon {
          padding-right: 6px;
          font-size: 18px;
          // color: #848cf1fa;
          color: #ffba01;
          vertical-align: -20% !important;
        }

        .tips-content {
          display: inline;
          padding: 3px 6px;
          color: #58606e;
          border: 1px solid #ffba01;
          border-radius: 4px;

          .t-c-logo {
            font-weight: 900;
          }
        }
      }
    }

    .recall-content {
      flex: 1;

      .status-color {
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 6px;
        background-color: #dadbdd;
        border-radius: 50%;

        &.running {
          background-color: #1a78cf;
        }

        &.finished {
          background-color: #1ea45b;
        }
      }
    }

    .recall-footer {
      height: 50px;
      padding-top: 18px;
    }
  }

  ::ng-deep a.tablea {
    float: left !important;
    margin: 0 6px;
    cursor: pointer;
  }
}

.exp-paginator-demo {
  position: absolute;
  right: 5px;
  bottom: 10px;
  left: 0;
  height: 30px;
  background: #fff;
}

.exp-playbook-table-box {
  position: absolute;
  top: 48px;
  right: 0;
  bottom: 10px;
  left: -5px;
  width: calc(100% - 10px) !important;
  margin-left: 15px;
  overflow: hidden !important;
}

.container-box {
  height: calc(100% - 50px);
  overflow: auto;

  .exp-dataview-box {
    height: calc(100% - 8px);

    .tableview-bigbox {
      height: 100%;
    }
  }
}

.table-inputgroup {
  margin-left: 10px;

  .ui-inputtext {
    padding-right: 26px;
    border-right: 1px solid #cfcfcf;
    border-radius: 2px;
  }

  .ui-button {
    position: relative;
    padding: 0;
    margin-left: -15px;
    background: transparent;
    border: none;

    &:enabled:focus {
      box-shadow: none;
    }
  }
}

// 查询信息
.ainputgroup {
  width: 200px;
  transition: all 0.3s;
}

.binputgroup {
  width: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.ainputgroup:hover,
.ainputgroup:focus {
  width: 200px;
  color: #444;
  border-color: #fff;
}
