{"id": "set-deviation", "name": "首次出现行为检测算法", "desc": "根据过去指定范围内的数据，判定集合是否为首次出现的行为", "order": 2, "type": "individual-comparison", "isModelOutputSupport": true, "storageInfo": {"statistic": {"type": "DATABASE_ES", "location": "set_deviation_(B_T)/_doc", "format": "yyyy-MM"}, "learn": {"type": "DATABASE_ES", "location": "set_deviation_(B_T)/_doc", "format": "yyyy-MM"}}, "taskInfo": [{"taskType": "PROCESS", "stageType": "DETECTION", "params": {"operatorId": "set-deviation-detection"}}], "params": [{"name": "learn_scope", "alias": "学习范围", "type": "interval_time", "configurable": "required", "display": "senior", "value": [{"periodic": "none", "value": "1 month"}, {"periodic": "day", "value": "1 month"}, {"periodic": "week", "value": "1 year"}, {"periodic": "month", "value": "1 year"}], "option": [{"min": 1, "max": null, "items": null, "label": "月", "value": "month"}, {"min": 30, "max": null, "items": null, "label": "天", "value": "day"}, {"min": 1, "max": null, "items": null, "label": "年", "value": "year"}], "desc": "指定学习需要的历史数据的时间范围，默认训练一个月的数据"}, {"name": "learn_interval", "alias": "检测间隔", "type": "interval_time", "configurable": "required", "display": "senior", "value": [{"periodic": "none", "value": "1 hour"}, {"periodic": "day", "value": "1 day"}, {"periodic": "week", "value": "1 day"}, {"periodic": "month", "value": "1 day"}], "option": [{"items": [5, 10, 20], "label": "分钟", "value": "minute"}, {"items": [1, 2, 3, 4, 6, 12], "label": "小时", "value": "hour"}, {"items": [1], "label": "天", "value": "day"}], "desc": "指定每次学习时间之间的间隔，默认一天学习一次"}]}