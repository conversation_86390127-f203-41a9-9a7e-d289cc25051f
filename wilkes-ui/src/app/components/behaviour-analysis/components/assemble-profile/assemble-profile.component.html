

<t-loading [position]="'absolute'" [visible]="isLoading"
[ngStyle]="{'zIndex': isLoading ? '2' : '-2'}">
</t-loading>

<div class="tui-exp">
  <div class="tui-exp-search" *ngIf="model.subjectField">
    <div class="tui-exp-lione clear clearfix">
      <div class="tui-exp-list clearfix clear">
        <span class="tui-exp-lable">行为主体</span>
        <span class="tui-exp-input">
          <!-- <t-dropdown  [options]="subjectList" [(ngModel)]="subjectValue" [filter]="true" [loading]="loadingSubject" [numberMoreLayout]="false"
            [ischeckbox]="false" placeholder="请选择行为主体" [style]="{'width':'170px'}"
            (onChange)="selectChange($event, 'subjectValue')" [showClear]="true"></t-dropdown> -->
            <app-w-dropdown placeholder="请选择行为主体" [(ngModel)]="subjectValue" [options]="subjectList" (onChange)="selectChange($event, 'subjectValue')"></app-w-dropdown>
        </span>
      </div>
      <div class="tui-exp-list clearfix clear" *ngIf="model.objectField">
        <span class="tui-exp-lable">行为客体</span>
        <span class="tui-exp-input">
          <!-- <t-dropdown [options]="objectList" [(ngModel)]="objectValue" [filter]="true" [loading]="loadingObject" [numberMoreLayout]="false"
            [style]="{'width':'170px'}" placeholder="请选择行为客体" (onChange)="selectChange($event, 'objectValue')"
            [showClear]="true"></t-dropdown> -->
            <app-w-dropdown placeholder="请选择行为客体" [(ngModel)]="objectValue" [options]="objectList" (onChange)="selectChange($event, 'objectValue')"></app-w-dropdown>
        </span>
      </div>

    </div>
  </div>
  <!--表格-->
  <div class="tui-exp-table clear clearfix">
    <div class="assemble-content">
      <t-table [columns]="columns" [data]="listData" [scrollable]="true" [scrollHeight]="'300px'">
        <ng-template tTemplate="header">
          <tr>
            <th [colSpan]="cols" class="txt-center">
              {{title? (title + '时刻的预测值'): ''}}
            </th>
          </tr>
        </ng-template>
        <ng-template tTemplate="colgroup" let-columns>
          <colgroup>
            <col *ngFor="let col of columns" [ngStyle]="{width:  'calc(100% / '+ cols + ')'}">
          </colgroup>
        </ng-template>
        <ng-template tTemplate="body" let-data>
          <tr>
            <td class="text-overflow" *ngFor="let col of columns"  >
              <span *ngIf="data[col]" [tTooltip]="data[col]">
                {{data[col]}}
              </span>
            </td>
          </tr>
        </ng-template>
      </t-table>
    </div>
    <div class="assemble-pager">
      <t-paginator [rows]="pageParams.pageSize" [totalRecords]="total"
      (onPageChange)="onPageChange($event)" [displayRecords]="true"
      [jumpToPage]="true"></t-paginator>
    </div>

    <ng-container *ngIf="isNodata">
      <div class="no-data">
        <div class="ui-data ng-star-inserted">
          <div class="ui-data-contentimg"></div>
          <div class="ui-data-text">暂无数据</div>
          <div class="ui-data-text">预计画像首次生成时间：{{ generateTime }}</div>
        </div>
      </div>
    </ng-container>
  </div>
</div>
