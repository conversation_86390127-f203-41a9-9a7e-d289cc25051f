<div class="kita-box wilkes-cause-con">
    <div class="handle-bar">
        <t-handle-bar title="安全日志" [handleSett]="handleSett" (onHandleChange)="onHandleChange($event)">
        </t-handle-bar>
    </div>
    <div class="container-box">
        <!-- 4、列表查询条件编辑部分-->
        <div class="searchBox-box query-box clearfix clear" *ngIf="handleSett.actionButton[0].selected">
            <div class="query-searchBox">
                <div class="search-box">
                    <span class="search-label">日志编号</span>
                    <div class="search-inner">
                        <input [(ngModel)]="queryViewSett.modelNo" tInputText placeholder="请输入日志编号" />
                    </div>
                </div>
                <div class="search-box">
                    <span class="search-label">时间范围</span>
                    <div class="search-inner">
                        <t-double-calendar #doubleC dateFormat="yyyy-MM-dd HH:mm:ss" [absolutTimeBase]="absolutTimeBase"
                            (onOkClick)="getRangeTime($event)" [initEmpty]="true" [startDate]='startDate' [endDate]="endDate">
                        </t-double-calendar>
                    </div>
                </div>
            </div>
            <div class="search-operation">
                <button tButton label="查询" icon="icon-search2" class="ui-button-primary" (click)="query(true)"></button>
                <button tButton icon="icon-loop" class="ui-button-color-grey ml8" (click)="reset(doubleC)"></button>
            </div>
            <!--图标-->
            <!-- <a *ngIf="queriesNumber>4" class="query-btn {{showQueryMore ? 'icon-retract':'icon-retractno' }}"
                (click)="queryMore()"></a> -->
        </div>

        <div class="dataview-box">
            <!--自定义模块-->
            <div class="view-btn {{ fieldViewSett.show ? 'icon-caret-left':'icon-caret-right'}}"
                (click)="onToggleFieldsPickHandler()" tTooltip="{{fieldViewSett.show? '关闭左侧内容':'展示左侧内容'}}"
                tooltipPosition="left">
            </div>
            <div class="tableview-bigbox">
                <!--字段选择-->
                <div class="pickList-box" [style.width]="(fieldViewSett.show ? 240 : 0 )+'px'"
                    [style.border]="(fieldViewSett.show ? '' : 'none' )">
                    <p class="pickList-box-title" [style.border]="(fieldViewSett.show ? '' : 'none' )">自定义字段 </p>
                    <t-pickList [direction]="'vertical'" [showSourceControls]="false" [showTargetControls]="false"
                        [target]="fieldViewSett.selFields" [source]="fieldViewSett.fields" [sourceHeader]="'可选字段 '"
                        [targetHeader]="'选中字段'" [responsive]="true" filterBy="filed,label" [dragdrop]="true"
                        [sourceFilterPlaceholder]="'输入名称过滤'" [targetFilterPlaceholder]="'输入名称过滤'"
                        [sourceStyle]="{'height':'200px'}" [targetStyle]="{'height':'200px'}"
                        (onMoveToTarget)="targetFn($event)" (onMoveToSource)="sourceFn($event)">
                        <ng-template let-xxxVo tTemplate="item">
                            <div class="ui-helper-clearfix">
                                <div class="ml8">{{xxxVo.label}} </div>
                            </div>
                        </ng-template>
                    </t-pickList>
                </div>

                <!--表格列表-->
                <div class="tableview-box clear clearfix {{fieldViewSett.show? 'tableview-smallbox':'tableview-box'}}">
                    <div class="table-operate"></div>
                    <!--表格数据列表-->
                    <div class="box-table">
                        <div *ngIf="fieldViewSett.selFields.length || fieldViewSett.list.length">
                            <t-table [data]="fieldViewSett.list" [scrollable]="true" [scrollHeight]="fieldViewSett.tableH"
                                [resizableColumns]="true"  [loading]="fieldViewSett.loading">
                                <ng-template tTemplate="colgroup">
                                    <colgroup>
                                        <col width="50">
                                        <col *ngFor=" let item of fieldViewSett.selFields">
                                        <col width="100">
                                    </colgroup>
                                </ng-template>
                                <ng-template tTemplate="header">
                                    <tr style="height: 42px;">
                                        <th>
                                            序号
                                        </th>
                                        <ng-container *ngFor="let col of fieldViewSett.selFields">
                                            <th *ngIf="col.isSelect" [tSortableColumn]="col.field" tResizableColumn>
                                                {{col.label}}
                                                <t-sortIcon [field]="col.field"></t-sortIcon>
                                            </th>
                                        </ng-container>
                                        <th tResizableColumn>操作</th>
                                    </tr>
                                </ng-template>
                                <ng-template tTemplate="body" let-rowVo let-rowIndex="rowIndex">
                                    <tr style="height: 36px;" [tSelectableRow]="rowVo">
                                        <td>
                                            {{rowIndex + 1}}
                                        </td>
                                        <ng-container *ngFor="let col of fieldViewSett.selFields"
                                            [ngSwitch]="col.field">
                                            <td *ngSwitchCase="'name'" class="td-name-cause">
                                                <a class="table-start"  tTooltip="{{rowVo[col.field]}}" tooltipPosition="bottom" (click)="lookCause(rowVo)">{{rowVo.name}}</a>
                                            </td>
                                            <td *ngSwitchDefault class="td-name-cause">
                                              <span tTooltip="{{rowVo[col.field]}}" tooltipPosition="left">{{rowVo[col.field]}}</span> 
                                            </td>
                                        </ng-container>

                                        <td>
                                            <a tTooltip="查看" tooltipPosition="top" class="tablea" (click)="lookCause(rowVo)">
                                              <span class="icon-eye1"></span>
                                            </a>
                                          </td>
                                    </tr>
                                </ng-template>
                            </t-table>
                            <div class="mm-r-page" style="margin-top: 8px;" *ngIf="!!fieldViewSett.list?.length">
                                <t-paginator [rows]="queryViewSett.pageSize" [totalRecords]="fieldViewSett.total"
                                  (onPageChange)="onPageChange($event)" [displayPages]="true" [rowsPerPageOptions]="[20, 30, 50]"
                                  [jumpToPage]="true" #paginator></t-paginator>
                              </div>
                        </div>
                        <t-nodata *ngIf="!fieldViewSett.selFields.length || !fieldViewSett.list.length" [noDataType]="'noData'"
                            [noDatatext]="'无数据'"></t-nodata>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<t-toast key="wilkes-cause" position="top-center" [style]="{'marginTop': '80px', 'width': '200px'}"></t-toast>
<t-loading [bgColor]="'transparent'" [zindex]="100000000" [position]="'absolute'"
[visible]="fieldViewSett.loading"></t-loading>