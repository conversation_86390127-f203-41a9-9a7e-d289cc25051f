import { Injectable } from '@angular/core';
import { HttpdatetoEncryotService } from 'src/app/services/httpdatetoEncryot.service';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { TuiUserService } from '@tui/frame';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class AiHttpService {
  constructor(private http: HttpClient, private ht: HttpdatetoEncryotService, public tuiUserService: TuiUserService) {}

  // ai标签
  getAitageList(): Observable<any> {
    return this.http.get(`${environment.root}/aiModelTags`);
  }

  // ai模型列表
  getAIModules(p: string): Observable<any> {
    return this.http.get(`${environment.root}/aiModel${p || ''}`);
  }

  // 启用禁用
  openOrClose(p: any): Observable<any> {
    return this.http.put(`${environment.root}/aiModel/${p.id}/${p.isNo ? 'ENABLED' : 'DISABLED'}`, null);
  }

  // ai模型详情信息
  getAIModulesDatails(id: string): Observable<any> {
    return this.http.get(`${environment.root}/aiModel/${id}`);
  }

  // ai详情保存
  setAIModulesDatails(id: string, data: any): Observable<any> {
    return this.http.put(`${environment.root}/aiModel/${id}`, this.ht.setData(JSON.stringify(data)), {
      headers: {
        'Content-Type': 'application/json',
      },
      responseType: 'text',
    });
  }

  // ai详情-数据模型数据
  getAiModelData(): Observable<any> {
    return this.http.get(`${environment.root}/ruleSource`);
  }

  // ai详情-获取fields
  getAiFields(ids: any[]): Observable<any> {
    ids = (ids || []).filter((e) => e);
    return this.http.get(`${environment.root}/fields/${ids.join(',')}`);
  }

  // 获得参数
  getReasons(p?: any, vs?: any): Observable<any> {
    return p ? this.http.post(`${environment.root}/rules/business-info`, p) : this.http.get(`${environment.root}/fields/event`);
  }

  // 根据模型内容获取参数
  getReasonsForModel(data: any): Observable<any> {
    return this.http.post(`${environment.root}/rules/business-info`, data);
  }

  // ai详情测试测
  getTestData(id: string, data: any): Observable<any> {
    return this.http.post(`${environment.root}/aiModel/test/${id}`, this.ht.setData(JSON.stringify(data)), {
      headers: {
        'Content-Type': 'application/json',
      },
      responseType: 'text',
    });
  }

  // 告警管理情报列表
  getIntelligenceList(dataModelIds: any): Observable<any> {
    return this.http.get(`${environment.root}/knowledgeBase/threatIntelligenceTypes/${dataModelIds}`);
  }
}
