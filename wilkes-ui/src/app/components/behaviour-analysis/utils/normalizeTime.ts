/**
 * 标准化时间
 * @param scope 周期
 * @param time 时间
 * @returns string
 */
export function NormalizeTime(scope, time): string {
    if (new RegExp('minute', 'i').test(scope)) {
        return time.split(' ')[0] + '\n' + time.split(' ')[1];
    } else if (new RegExp('hour', 'i').test(scope)) {
        return time.split(' ')[0] + ' ' + time.split(' ')[1].slice(0, 2);
    } else if (new RegExp('day', 'i').test(scope)) {
        return time.split(' ')[0];
    } else {
        return time.split(' ')[0];
    }
}
