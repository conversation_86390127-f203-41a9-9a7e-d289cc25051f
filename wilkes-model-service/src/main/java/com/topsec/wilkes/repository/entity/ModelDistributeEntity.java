package com.topsec.wilkes.repository.entity;

import com.topsec.wilkes.dto.ModelDistribute;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "model_distribute")
public class ModelDistributeEntity {
  @Id
  private String id;

  @Column
  private String modelNo;

  @Column
  private String platformId;

  public ModelDistribute convertToModelDistribute() {
    return new ModelDistribute(this.id, this.modelNo, this.platformId);
  }
}
