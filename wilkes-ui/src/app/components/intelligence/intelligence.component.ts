import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  forwardRef,
  AfterViewInit,
  Input,
  OnDestroy,
} from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { TeditService } from 'src/app/services/t-edit.service';
import { ComToComService } from 'src/app/ofmodules/rules/services/com-to-com.service';
import { Rule, RuleSet } from '@tui/component-library';
import { BHHttpService } from 'src/app/ofmodules/behavior/services/bhHttp.service';

@Component({
  selector: 'app-intelligence',
  templateUrl: './intelligence.component.html',
  styleUrls: ['./intelligence.component.less'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => IntelligenceComponent),
      multi: true,
    },
  ],
})
export class IntelligenceComponent
  implements OnInit, AfterViewInit, ControlValueAccessor, On<PERSON><PERSON>roy
{
  @Output() saveClick = new EventEmitter<any>();
  @Output() selChange = new EventEmitter<any>();
  @Output() setROptions = new EventEmitter<any>(); // 附加字段下拉列表
  @Input() fildes: any[] = [];
  @Input() typeNum = '2';
  @Input() disabled = false;
  @Input() showRadioButton = true;
  @Input() toolId: string | undefined;
  // 错误提示
  errObg = {
    isErrFile: false,
    isEdit: false,
  };
  value: any = { type: 'include' };
  // 情报库类型types sel情报库
  intelligenceO = {
    types: [],
    sel: [],
    typeValue: '0',
    knowledgeBase2: [],
    knowledgeBase1: [],
  };
  // 编译起关联条件对象
  aConditionO = { fildes: [], operator: [], relation: [], sel: [] };
  allStyle = {
    dataIputStyle: {
      width: '190px',
      btnStyle: { marginRight: '5px' },
    },
    iconStyle: {},
    selStyle: {
      width: '146px',
      borderRadius: '1px',
      fontSize: '13px',
      marginRight: '5px',
    },
    btnStyle: {
      fontSize: '12px',
      margin: '2px 5px 0 5px',
    },
    inputStyle: {
      width: '146px',
      height: '32px',
      marginRight: '5px',
      borderRadius: '1px',
    },
    selStyleAndOR: { width: '200px' },
    selStyleCount: { width: '100px' },
    inputStyleCount: { width: '100px', verticalAlign: 'initial' },
    selStyleLast: {},
  };
  ruleSets = {
    a: new RuleSet(), // 情报库过滤
    b: new RuleSet(), // 关联条件
  };
  public onModelChange: Function = () => {};
  public onModelTouched: Function = () => {};
  constructor(
    private bh: BHHttpService,
    private teditService: TeditService,
    private comToComService: ComToComService
  ) {}

  ngAfterViewInit(): void {
    this.getIntelligenceO();
    if (this.value.filter_condition) {
      this.setRuleSet(this.value.filter_condition, 'a');
    }
    if (this.value.predication) {
      this.setRuleSet(this.value.predication, 'b');
    }
  }

  ngOnDestroy(): void {
    this.save();
  }

  // 请求知识库信息
  getIntelligenceO() {
    const fun = (label: any, value: any, items: any, dataType = 'string') => ({
      label,
      value,
      items,
      dataType,
    });
    this.bh.intelligenceList('operator').subscribe(
      (d: {
        map: (
          arg0: (e: any) => { label: any; value: any; items: any }
        ) => never[];
      }) => {
        if (d) {
          this.intelligenceO.types = d.map((e: any) => {
            const o = fun(
              e.baseTypeName,
              e.baseTypeNo,
              (e.dataInfos || []).map((ee: any) =>
                fun(
                  ee.name,
                  ee.id,
                  ee.datas.map((item: any) => {
                    if (
                      this.value.knowledge_base_url &&
                      item.id === this.value.knowledge_base_url
                    ) {
                      this.intelligenceO.typeValue = e.baseTypeNo;
                    }
                    return fun(item.name, item.id, null);
                  })
                )
              )
            );
            if (this.intelligenceO.typeValue == o.value) {
              this.intelligenceO.sel = o.items;
            }
            return o;
          });
        }
      },
      (e: any) => {}
    );
    // 1代表情报库2代表知识库
    [2, 1].forEach((e) => {
      // 获取知识库下拉数据
      this.bh.getKnowledgeBase(e + '').subscribe((res: any) => {
        // @ts-ignore
        this.intelligenceO['knowledgeBase' + e] = res.map((item) => {
          const items = (item.schemas || []).map((e: any) =>
            fun(e.name, e.column, null, e.dataType)
          );
          const o = fun(item.name, item.id, items);
          if (
            this.value.knowledge_base_url &&
            item.id === this.value.knowledge_base_url
          ) {
            this.value.kname = item.name;
            this.aConditionO.fildes = JSON.parse(JSON.stringify(items));
            this.aConditionO.sel = o.items;
            this.setROptions && this.setROptions.emit(o.items);
          }
          return o;
        });
      });
    });
  }
  writeValue(obj: any): void {
    this._value = obj;
  }

  registerOnChange(fn: any): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onModelTouched = fn;
  }

  setDisabledState?(isDisabled: boolean): void {}

  ngOnInit() {
    this.typeNum = (this.typeNum || '0') == '0' ? '2' : this.typeNum || '0';
    this.intelligenceO.typeValue = this.typeNum;
    //@ts-ignore
    this.aConditionO.operator = this.teditService.getFunList(1);
    this.aConditionO.relation = this.aConditionO.operator.filter(
      //@ts-ignore
      (e) => e.label !== '不等于'
    );
    // tslint:disable-next-line:no-unused-expression
    this.errObg = {
      isErrFile:
        (
          this.comToComService.postData.find((e) =>
            //@ts-ignore
            new RegExp(this.toolId).test(e)
          ) || ''
        ).split(',')[1] == 'true',
      isEdit:
        (
          this.comToComService.postData.find((e) =>
            //@ts-ignore
            new RegExp(this.toolId).test(e)
          ) || ''
        ).split(',')[2] == 'true',
    };
  }

  save() {
    this.dataChange();
    this.onModelChange(this._value);
  }

  dataChange(e?: any, is?: any) {
    if (is && e && e.field) {
      //@ts-ignore
      const l = this.aConditionO.fildes.find(
        //@ts-ignore
        (item) => item.value === e.field
      ).value;
      if (['IP', 'DOMAIN'].some((item) => item === l)) {
        e.operators = [
          {
            value: 'equal',
            label: '等值匹配',
            implementsD: 'string,int,number,enum,array,datetime,hour,double',
          },
        ];
        e._operator = e.operator = 'equal';
      } else {
        e.operators = null;
        e._operator = e.operator = '';
      }
    }
    this.saveClick.emit();
  }

  get _value(): any {
    this.errObg.isErrFile = !this.value.knowledge_base_url;
    this.value.filter_condition = this.getParamEntery(this.ruleSets.a, true);
    this.value.predication = this.getParamEntery(this.ruleSets.b);
    return this.value;
  }

  set _value(d) {
    try {
      this.value = {
        type: d.type || 'include',
        knowledge_base_url: d.knowledge_base_url || '',
        filter_condition: d.filter_condition || null,
        predication: d.predication || null,
      };
      this.setRuleSet(d.filter_condition, 'a');
      this.setRuleSet(d.predication, 'b');
    } catch (error) {
      this.value = {
        type: 'include',
        knowledge_base_url: '',
        filter_condition: null,
        predication: null,
      };
    }
  }

  // 情报威胁库类型选择
  typeChange(e: { option: { items: never[] } }) {
    this.intelligenceO.sel = e.option.items;
    this.value.knowledge_base_url = '';
    this.dataChange();
  }

  selsVals(e: { [s: string]: unknown } | ArrayLike<unknown>) {
    // tslint:disable-next-line:forin
    for (const k in e) {
      //@ts-ignore
      if (new RegExp('MD5', 'i').test(e[k].option.label)) {
        //@ts-ignore
        e[k].rule._operator = e[k].rule.operator = 'equal';
        //@ts-ignore
        e[k].rule.operators = [
          {
            value: 'equal',
            label: '等值匹配',
            implementsD: 'string,int,number,enum,array,datetime,hour,double',
          },
        ];
      } else {
        //@ts-ignore
        e[k].rule.operators = null;
      }
    }
    //@ts-ignore
    this.selChange.emit(Object.values(e).map((item) => item['rule']['value']));
  }

  // 情报知识编辑器条件同步方式
  change(e: string, p: string | number) {
    let operator = '';
    //@ts-ignore
    this.ruleSets[p].rules.forEach(
      (
        em: { operator: string; _operator: string; value: string },
        i: number
      ) => {
        if (i === 0) {
          operator = em.operator;
        }
        if (e === 'add') {
          em._operator = operator;
        } else {
          em._operator = e;
          em.value = '';
        }
      }
    );
  }

  // 情报威胁值选择
  valueChange(e: { value: any; option: { label: any } }) {
    this.ruleSets.b.rules = [new Rule()];
    if (!e.value) {
      this.value.kname = '';
      this.value.knowledge_base_url = '';
      this.aConditionO.fildes = this.aConditionO.sel = [];
      this.setROptions && this.setROptions.emit([]);
      return;
    }
    const p = 'knowledgeBase' + this.intelligenceO.typeValue;
    !this.showRadioButton && (this.value.kname = e.option.label);
    //@ts-ignore
    const s = this.intelligenceO[p].find(
      (ee: { value: any }) => ee.value === e.value
    );
    this.aConditionO.fildes = s.items;
    this.aConditionO.sel = s.items;
    this.setROptions && this.setROptions.emit(s.items);
    this.dataChange();
  }

  // 获得规则编译器的业务结构
  getParamEntery(d: RuleSet, is?: boolean | undefined) {
    const dataS = JSON.parse(JSON.stringify(d));
    // if (!dataS.rules.every(e => e instanceof Rule ? e.field && e.operator && e.value : true)) return null;
    const isArr: boolean[] = [];
    let isT = true;
    const isFun = (d: string | null | undefined) =>
      d != null && d !== undefined && d !== '';
    // 保留/排除符合条件的事件', '知识库关联', '保留/排除符合知识库条件的事件'
    const contionFun = (d: {
      field: any;
      operator: string;
      value: any;
      value1: any;
      func: any;
      attr: any;
    }) => {
      const obj = {
        left: d.field,
        operator: d.operator,
        right: (d.operator === 'between'
          ? [d.value, d.value1].filter(
              (item) => item !== '' || item !== undefined
            )
          : d.value
        ).toString(),
      };
      if (d.func || d.attr) {
        //@ts-ignore
        obj['func'] = d.func || d.attr;
      }
      return { leaf_node: obj };
    };
    // 递归函数
    const repeatFun = (d: any[], sData?: any) => {
      let data: any;
      d.forEach((item) => {
        // 判断是否有确实的编译器参数
        if (item.relation) {
          isArr.push(item.rules.length > 0);
          data = {
            node: {
              operator: item.relation.toLocaleLowerCase(),
              children: [],
            },
          };
          repeatFun(item.rules, data.node.children);
        } else {
          isT = isFun(item.field) && isFun(item.operator) && isFun(item.value);
          isArr.push(isT);
          data = contionFun(item);
        }
        if (is) {
          isT && sData && sData.push(data);
        } else {
          sData && sData.push(data);
        }
      });
      return data;
    };
    let da = repeatFun([dataS]);
    // 满足条件去掉，不满足添加入+
    this.errObg.isEdit = !isArr.every((e) => e);
    if (is && da.node.children.length === 0) da = null;
    return da;
  }

  // 模型设置-过滤条件默认值（中间值）
  setRuleSet(paramEntery: any, d: string) {
    if (!paramEntery) {
      //@ts-ignore
      this.ruleSets[d] = new RuleSet();
      return;
    }
    const dataS = [JSON.parse(JSON.stringify(paramEntery))];
    const getRuleSet = () => {
      const d = new RuleSet();
      d.rules = [];
      return d;
    };
    const ruleSet = getRuleSet();
    // 递归函数
    const repeatFun = (d: any[], ruleSetData: RuleSet, isNo?: boolean) => {
      let ang: any;
      let p: any;
      d.forEach((item) => {
        let child: any;
        if (item.node) {
          child = isNo ? ruleSetData : getRuleSet();
          // tslint:disable-next-line: no-unused-expression
          !isNo && (child.parentId = ruleSetData.id);
          child.relation = item.node.operator.toLocaleUpperCase();
          repeatFun(item.node.children, child);
        } else {
          ang = item.leaf_node;
          const isNO = ang.operator === 'between';
          p = isNO ? ang.right.toString().split(',') : ang.right;
          child = new Rule(
            ang.left || '',
            ang.operator || '',
            (isNO ? p[0] : p) || ''
          );
          child._operator = ang.operator || '';
          child.attr = ang.func || '';
          p && p[1] && (child.value1 = p[1]);
          // ang.aggType && (child.attr = ang.aggType);
        }
        !isNo && ruleSetData.rules.push(child);
      });
    };
    repeatFun(dataS, ruleSet, true);
    if (ruleSet.rules.length === 0) {
      ruleSet.rules.push(new Rule());
    }
    //@ts-ignore
    this.ruleSets[d] = ruleSet;
  }
}
