package wilkes;

import static com.topsec.wilkes.util.ModelUtil.upgradeAction2_5;

import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.common.dto.Action;

import com.google.common.io.ByteStreams;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class V2_6_0__wilkes_upgrade_test {

  @Test
  public void upgradeAction() {
    try {
      String action = new String(ByteStreams.toByteArray(V2_6_0__wilkes_upgrade_test.class.getResourceAsStream("/action/v_2_5_0_action.json")));
      List<Action> actions = new LinkedList<>();
      actions = Arrays.asList(ToolUtils.OBJECT_MAPPER.readValue(action, Action[].class));
      List<Action> actions1 = upgradeAction2_5(actions);
      log.info(ToolUtils.OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(actions1));
    } catch (IOException e) {
      e.printStackTrace();
    }
  }
}
