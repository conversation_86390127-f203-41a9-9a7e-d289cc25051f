<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <property name="LOG_PATH" value="${LOG_PATH:-${catalina.base:-./}/logs}"/>
  <property name="LOG_FILE" value="${LOG_PATH}/wilkes/service/wilkes.log"/>
  <property name="LOG_FILE_MAX_HISTORY" value="20"/>
  <property name="LOG_FILE_MAX_SIZE" value="100MB"/>
  <property name="LOG_FILE_TOTAL_SIZE_CAP" value="2GB"/>
  <property name="LOG_FILE_CLEAN_HISTORY_ON_START" value="true"/>

  <jmxConfigurator/>
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
  <include resource="org/springframework/boot/logging/logback/file-appender.xml"/>
  <root level="INFO">
    <appender-ref ref="FILE"/>
    <appender-ref ref="CONSOLE"/>
  </root>
  <logger name="org.apache.flink">
    <appender-ref ref="CONSOLE"/>
  </logger>
  <logger name="com.topsec.flink">
    <appender-ref ref="CONSOLE"/>
  </logger>
  <logger name="com.topsec.wilkes.operator">
    <appender-ref ref="CONSOLE"/>
  </logger>
  <logger name="org.apache.flink.api.java.typeutils" level="ERROR">
  </logger>
  <logger name="org.apache.flink.table.module.ModuleManager" level="ERROR">
  </logger>
  <logger name="org.apache.kafka" level="ERROR">
  </logger>
  <logger name="org.elasticsearch.client.RestClient" level="ERROR">
  </logger>
  <logger name="com.topsec.ti.audit.core.aspect.AuditExecutor" level="ERROR">
  </logger>
</configuration>