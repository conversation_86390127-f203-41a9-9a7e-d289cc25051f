import { Component, OnInit, Input, forwardRef, Output, EventEmitter } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'w-radio',
  templateUrl: './w-radio.component.html',
  styles: [
    `
      i {
        font-size: 15px;
        margin-left: 10px;
        color: #83de12;
      }
    `,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => WRadioComponent),
      multi: true,
    },
  ],
})
export class WRadioComponent implements OnInit, ControlValueAccessor {
  @Input() disabled = false;
  @Input() options = [
    { label: '保留', value: 'include' },
    { label: '排除', value: 'exclude' },
  ];
  @Input() implement = 'string';
  @Input() desc = '';
  @Output() change = new EventEmitter<any>();
  rId = '';
  value: any = 'include';

  public onModelChange: Function = () => {};
  public onModelTouched: Function = () => {};

  constructor() {}

  ngOnInit() {
    this.rId = new Date().getTime() + 'wRadioCon';
    this.implement = this.implement || 'string';
  }

  writeValue(v): void {
    if (v != undefined) {
      this.value = v + '';
      this.radioButtonClick();
    }
  }

  registerOnChange(fn: any): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onModelTouched = fn;
  }

  radioButtonClick() {
    this.onModelChange(this.implement == 'string' ? this.value + '' : 'true' == this.value);
    this.change.emit(this.options.find((e) => e.value === this.value));
  }
}
