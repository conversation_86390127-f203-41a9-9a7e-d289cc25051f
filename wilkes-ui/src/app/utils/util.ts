export class Util {
  // 算子id
  static nodeId: any = null;
  // 算子调试页面数据
  static inputStr: any = null;
  static searchObj: any = null;
  static listOfDisplayData: any[] = [];
  static rules: any = null;
  // manage页面的规则名称等打开关闭按钮
  static isopen = true;
  static baseInfo: any = null;
  static routerObserved: any = null;
  // 背景图
  static imgBase64: any = null;
  // 算则验证不通过算子
  static postData: any[] = ['1——source'];
  // 路由根
  static locationUrl = '';
  constructor() {}

  // 查找数组中的某一项
  static findNodeByList(list: any[], pName: string, value: any, insurance?: any): any {
    return list.find((e) => e[pName] == value) || { [insurance || pName]: '' };
  }

  // 用于使用二维运算符&
  static operationAnd(a: any, b: any, p?: any) {
    // eslint-disable-next-line no-unused-expressions, @typescript-eslint/no-unused-expressions
    a && b();
  }

  // 获取屏幕可视区域高度宽度
  static getScreenDetail(): { width: string; height: string } {
    const width = document.documentElement.clientWidth;
    const height = document.documentElement.clientHeight;
    //@ts-ignore
    return { width, height };
  }

  // 获取元素相对于浏览器位置
  static getElementByScreen(element: any): { left: string; top: string } {
    const o = { left: 0, top: 0 };
    o.left = element.offsetLeft;
    o.top = element.offsetTop;
    let current = element.offsetParent;
    while (current !== null) {
      o.left += current.offsetLeft;
      o.top += current.offsetTop;
      current = current.offsetParent;
    }
    // @ts-ignore
    return o;
  }

  // 连接多维数组
  static concat(arr1: any[], arr2: any[], p?: string): any[] {
    p && (arr2 = arr2.map((d) => (d[p] ? d[p] : d)));
    return arr1.concat.apply(arr1, arr2);
  }

  // 扩展
  static arrPush(arr1: any[], arr2: any[]) {
    arr1.push.apply(arr1, arr2);
  }

  static svgToCanvg(dom: any, fun?: any) {
    // dom = dom.cloneNode(true);
    // const cloneDom = document.querySelector('.clone-con');
    // cloneDom.append(dom);
    // const svgs = dom.querySelectorAll('svg');
    // const nodesToRecover = [];
    // const nodesToRemove = [];
    // svgs.forEach((node, index) => {
    //   const parentNode = node.parentNode;
    //   const svg = node.outerHTML.trim();
    //   const canvas = document.createElement('canvas');
    //   canvg(canvas, svg);
    //   if (node.style.position) {
    //     canvas.style.position += node.style.position;
    //     canvas.style.left += node.style.left;
    //     canvas.style.top += node.style.top;
    //   }
    //   nodesToRecover.push({
    //     parent: parentNode,
    //     child: node
    //   });
    //   parentNode.removeChild(node);
    //   nodesToRemove.push({
    //     parent: parentNode,
    //     child: canvas
    //   });
    //   parentNode.appendChild(canvas);
    // });
    // CommonService.domToImg(dom, 0, 0, (canvas) => {
    //   cloneDom.removeChild(dom);
    //   CommonService.imgBase64 = canvas.toDataURL('image/png');
    // });
  }

  // // dom转化成图片
  static domToImg(dom: any, w: any, h: any, callback: any) {
    // html2canvas(dom, { logging: true, allowTaint: false, useCORS: true }).then((canvas1) => {
    //   if (callback) {
    //     callback(canvas1);
    //   }
    // });
  }

  // 用于异步加载时数据之间验证的中间件
  static asyncM(data: any, fun?: any) {
    let set: any = null;
    const f = () => {
      if (
        //@ts-ignore
        !(this[data] && this[data] instanceof Array && this[data].length > 0)
      ) {
        clearInterval(set);
        set = setInterval(() => {
          f();
        }, 200);
      } else {
        clearInterval(set);
        fun && fun(data);
      }
    };
    f();
  }

  // 鼠标拖动 dropDom拖拽元素,dropChangeDom拖拽要改变的元素,dropChangeLDom跟随变换的元素
  static bindResize(dropDom: any, dropChangeDom: any, setType = 'width', maxMinO: any = { max: 800, min: 610 }, dropChangeLDom?: any) {
    // 鼠标的 X 和 Y 轴坐标
    let x = 0,
      dropChangeDomW = 0,
      dL = 0;
    // 移动事件
    const mouseMove = (mDom: any) => {
      if (dropDom && dropChangeDom) {
        // 计算拖拽距离
        let w = setType === 'left' ? mDom.clientX - x : x - mDom.clientX;
        let width = dropChangeDomW + w;
        const pW = dropChangeDom.parentNode.style.left || 0;
        const maxW = setType === 'left' ? -1 * parseFloat(pW) : maxMinO.max;
        const minW = setType === 'left' ? 0 : maxMinO.min;
        // if (width > maxW || width < minW || (setType === 'left' && pW === 0)) { return; }
        width = Math.max(Math.min(maxW, width), minW);
        w = width - dropChangeDomW;
        dropChangeDom.style[setType] = width + 'px';
        if (dropChangeLDom) {
          dropChangeLDom.style.width = dL - w + 'px';
        }
      }
    };

    // 停止事件
    const mouseUp = () => {
      dropChangeDomW = setType === 'width' ? dropChangeDom.offsetWidth : parseFloat(dropChangeDom.style.left || 0);
      dropChangeLDom && (dL = parseFloat(dropChangeLDom.style.left));
      // releaseCapture
      dropDom.releaseCapture
        ? // 释放焦点
          (dropDom.releaseCapture(),
          // 移除事件
          (dropDom.onmousemove = dropDom.onmouseup = null))
        : // 卸载事件
          (document.onmousemove = document.onmouseup = null);
    };

    dropDom.onmousedown = (e: any) => {
      dropChangeDomW = setType === 'width' ? dropChangeDom.offsetWidth : parseFloat(dropChangeDom.style.left || 0);
      dL = parseFloat(dropChangeLDom ? dropChangeLDom.offsetWidth || 0 : 0);
      // 按下元素后，计算当前鼠标与对象计算后的坐标
      x = e.clientX;
      // 支持 setCapture
      dropDom.setCapture
        ? // 捕捉焦点
          (dropDom.setCapture(),
          // 设置事件
          (dropDom.onmousemove = (ev: any) => {
            mouseMove(ev || event);
          }),
          (dropDom.onmouseup = mouseUp))
        : // 绑定事件
          ((document.onmousemove = mouseMove), (document.onmouseup = mouseUp));
      // 防止默认事件发生
      e.preventDefault();
    };
  }

  // 对象的copey和扩展  修改
  static copyObj(sourceObj: object, tarObj?: object): object {
    let { ...o } = sourceObj;
    if (tarObj) {
      o = { ...o, ...tarObj };
    }
    return o;
  }

  // arr1为目标数组arr2为改变
  static setArray(arr1: any[], arr2: any[], fun?: any): void {
    const arr3 = [...arr1];
    const arr4: any[] = [];
    arr2.forEach((item) => {
      const i = arr1.indexOf(item);
      if (i === -1) {
        arr1.push(item);
        arr4.push(item);
      }
    });

    arr3.forEach((item, index) => {
      const i = arr2.indexOf(item);
      if (i === -1) {
        arr1.splice(index, 1);
      }
    });
    if (fun) {
      fun(arr1, arr4, arr3);
    }
  }

  // 对象转换成数组
  static objToArr(o: any, fun?: any): any {
    let keys: any[] = [];
    let values: any[] = [];
    if (Object.prototype.toString.call(o) === '[object Object]') {
      keys = Object.keys(o);
      values = Object.values(o);
    }
    if (fun && keys.length > 0 && values.length > 0) {
      return fun(keys, values);
    }
    return { k: keys, v: values };
  }

  // 遍历树形结构
  // eslint-disable-next-line
  static iterator<T, U>(_arg: T[], obj?: U): any[] {
    const arr: any[] = [];
    const iterator = _arg[Symbol.iterator]();
    //@ts-ignore
    let d: { value: any; done: boolean } = iterator.next();
    while (!d.done) {
      const v = d.value;
      if (obj) {
        // eslint-disable-next-line guard-for-in
        for (const k in obj) {
          v[k] = v.hasOwnProperty(obj[k]) ? v[obj[k]] : obj[k];
        }
      }
      if ((v.children || []).length > 0) {
        v.children = this.iterator<T, U>(v.children, obj);
      }
      arr.push(v);
      //@ts-ignore
      d = iterator.next();
    }
    return arr;
  }

  // 处理字符串长度
  static strLength(str: string): number {
    const charArr = str.split('');
    let len = 0;
    charArr.forEach((item) => {
      const c = item.charCodeAt(0);
      if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
        len++;
      } else {
        len += 2;
      }
    });
    return len;
  }

  // 格式化时间的方法推荐用下面的
  static formatTime(time: any) {
    time = new Date(time);
    return (
      time.getFullYear() +
      '-' +
      (time.getMonth() + 1 < 10 ? '0' + (time.getMonth() + 1) : time.getMonth() + 1) +
      '-' +
      (time.getDate() < 10 ? '0' + time.getDate() : time.getDate()) +
      ' ' +
      (time.getHours() < 10 ? '0' + time.getHours() : time.getHours()) +
      ':' +
      (time.getMinutes() < 10 ? '0' + time.getMinutes() : time.getMinutes()) +
      ':' +
      (time.getSeconds() < 10 ? '0' + time.getSeconds() : time.getSeconds())
    );
  }

  //处理流程图<<>> 去设置参数
  static replaceAll(originStr: string, reg: string, str: string) {
    return originStr.replace(new RegExp(reg, 'g'), str);
  }

  // 正则操作
  static regExpMatch(matchName = 'test', a: any, b: any, c?: any) {
    let value: any = '';
    switch (matchName) {
      case 'test':
        value = new RegExp(a).test(b);
        break;
      case 'replaceAll':
        value = a.replace(new RegExp(b, 'g'), c);
        break;
      case 'indexOf':
        value = a.indexOf(b) !== -1;
        break;
      default:
        break;
    }
    return value;
  }

  // 格式化时间
  static dateFormat(date: Date, format = 'yyyy-MM-dd HH:mm:ss') {
    if (!date) return '';
    const o = {
      'M+': date.getMonth() + 1,
      'd+': date.getDate(),
      'H+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      'q+': Math.floor((date.getMonth() + 3) / 3),
      S: date.getMilliseconds(),
    };
    if (/(y+)/.test(format)) {
      format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (const k in o) {
      if (new RegExp('(' + k + ')').test(format)) {
        format = format.replace(
          RegExp.$1,
          RegExp.$1.length == 1
            ? // @ts-ignore
              o[k]
            : // @ts-ignore
              ('00' + o[k]).substr(('' + o[k]).length),
        );
      }
    }
    return format;
  }

  static getPreviousDay(dayNum = 1) {
    var currentDate = new Date(); // 获取当前日期
    currentDate.setDate(currentDate.getDate() - dayNum); // 将当前日期减去n天
    return currentDate.toLocaleDateString(); // 返回前n天的日期
  }

  static querySelctNode(treeNodes: any[], node: string, childName: string, key: string) {
    let o = null;
    const fun = (arr: any[]) => {
      let is = false;
      if (arr instanceof Array) {
        arr.forEach((e) => {
          if (e[key] === node) {
            o = e;
            is = true;
          } else {
            // tslint:disable-next-line:no-unused-expression
            const isNo = e[childName] && e[childName].length > 0 && fun(e[childName]);
            e.expanded = e.expanded ? true : isNo;
          }
        });
      }
      return is;
    };
    fun(treeNodes);
    return o;
  }

  // 合并相同对象的参数把对象b参数和对象a比较，相同的赋值给a, isSome是否是正则匹配属性
  static setKeyFromOther(aObj: any, bObj: any, isSome = false) {
    // eslint-disable-next-line guard-for-in
    for (const k in aObj) {
      const arr = [Object.prototype.hasOwnProperty.call(bObj, k.toLocaleLowerCase()), Object.prototype.hasOwnProperty.call(bObj, k.toLocaleUpperCase())];
      if (Object.prototype.hasOwnProperty.call(aObj, k) && arr.some((e) => e)) {
        const is = [undefined, null, ''].some((e) => e === bObj[k.toLocaleLowerCase()]);
        aObj[k] = is ? bObj[k.toLocaleUpperCase()] : bObj[k.toLocaleLowerCase()];
      }
    }
  }

  // 深度克隆
  static copy(o1: any) {
    return JSON.parse(JSON.stringify(o1));
  }

  // 数组去重
  static removeHasNode(arr: any[]) {
    return Array.from(new Set(arr));
  }

  static arraySum(arr: number[]) {
    return eval(arr.join('+'));
  }
}
