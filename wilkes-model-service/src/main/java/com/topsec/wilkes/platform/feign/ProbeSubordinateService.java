package com.topsec.wilkes.platform.feign;

import static com.topsec.wilkes.config.WilkesConfig.PROBESUBORDINATE;

import com.topsec.wilkes.dto.Platform;
import com.topsec.wilkes.platform.feign.fallbackFactory.ProbeSubordinateFallbackFactory;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 */
@FeignClient(name = PROBESUBORDINATE, fallbackFactory = ProbeSubordinateFallbackFactory.class, url = "${feign.probesubordinate.url:}")
public interface ProbeSubordinateService {
  @GetMapping(value = "probesubordinate/subordinate/getSelfInfo")
  Platform getSelfInfo();
}
