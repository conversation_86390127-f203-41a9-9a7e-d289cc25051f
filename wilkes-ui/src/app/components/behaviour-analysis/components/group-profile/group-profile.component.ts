import {
  Component,
  Input,
  OnInit,
  ViewChild,
  ViewContainerRef,
} from "@angular/core";
import * as echarts from 'echarts/core' ;
import { BChartBase, PortraitInfo } from "../../models/model";
import { tap } from "rxjs/operators";
import { NormalizeTime } from "../../utils/normalizeTime";
import { GetGenerateTime } from "../../utils/getGenerateTIme";
import { BehaviourService } from "../../service/behaviour.service";

@Component({
  selector: "app-group-profile",
  templateUrl: "./group-profile.component.html",
  styleUrls: ["./group-profile.component.less"],
})
export class GroupProfileComponent implements OnInit, BChartBase {
  @ViewChild("chartBox", { read: ViewContainerRef }) chartBox: ViewContainerRef;

  // 模型编号
  @Input() modelNo: string;

  // 模型数据对象
  @Input() model: PortraitInfo;

  // 暂无数据标识
  isNodata = true;
  // 加载等待
  isLoading = false;

  // 分组
  groupValue: string;

  // 分组数据集
  groupList = [];

  // 当前画像时间
  currentTime: string;
  // 下次检测时间
  forecastTime: string;
  // echart实例
  chartObj: any;

  // 加载群体
  loadGroup = false;

  // 画像首次生成时间
  generateTime:string = '';

  constructor(private ht: BehaviourService) {}

  ngOnInit() {
    if (this.model.groupField) {
      this.loadGroup = true;
      // 获取分组数据
      this.ht.getGroupList(this.modelNo).subscribe(
        (data) => {
          const flatMap = data
            .toString()
            .split(",")
            .filter((e) => e);
          this.groupList = flatMap.map((e) => {
            return { label: e, value: e };
          });
        },
        null,
        () => {
          this.loadGroup = false;
        }
      );
    } else {
      this.fetchData();
    }

    if(this.model.scope){
      this.generateTime = GetGenerateTime(this.model.scope)
    }
  }

  /**
   * 下拉选择改变事件
   * @param e event
   * @param name 类型
   */
  selectChange(e) {
    if (e.value == null) {
      this.groupValue = "";

      this.isNodata = true;
    } else {
      this.fetchData();
    }
  }

  fetchData() {
    this.isLoading = true;
    this.ht
      .getBehaviourorData(
        this.modelNo,
        {
          conditionMap: {
            groupValue: this.groupValue ? [this.groupValue] : null,
            subjectValue: null,
            objectValue: null,
          },
        },
        this.model.scope
      )
      .pipe(tap(() => (this.isLoading = false)))
      .subscribe((data) => {
        if (data && data.dataList && data.dataList.length) {
          this.isNodata = false;
          const series = this.serializeDate(data);
          const option = this.getOption(series);
          this.charOption = option;
        } else {
          this.isNodata = true;
        }
      });
  }

  serializeDate(d: any): any {
    let result: any[] = [];
    const da = d.dataList;
    const subject = da.find((e) => e.name === "主体值").data;
    const object = da.find((e) => e.name === "客体值").data;
    da.scores = da.find((e) => e.name === "分数").data;
    da.statistic = da.find((e) => e.name === "统计值").data;
    da.scores = da.find((e) => e.name === "分数").data;
    this.currentTime = d.extendConfig.nowTime || "";
    this.forecastTime = d.extendConfig.nextTime || "";

    const data = [],
      sAndO = [];
    let min = Math.min.apply([], da.scores),
      max = Math.max.apply([], da.scores);
    for (let index = da.statistic.length - 1; index > -1; index--) {
      const xVal = da.statistic[index];
      const yVal = da.scores[index];
      data.push([xVal, yVal]);
      sAndO.push([subject[index], object[index]]);
    }
    // 确保最大值最小值范围
    (min -= 0.05), (max += 0.05);
    let limit = +Object.values(d.extendConfig.mark).toString();
    result = [
      { v: 0.55, n: "高" },
      { v: 0.6, n: "中" },
      { v: 0.65, n: "低" },
    ].map((e) => ({
      type: "line",
      markLine: {
        label: {
          show: true,
          formatter: (params) => {
            return e.n;
          },
        },
        tooltip: {
          show: false,
        },
        symbol: 'none',
        lineStyle: {
          type: limit == e.v ? "solid" : "dashed",
          width: limit == e.v ? 3 : 2,
          opacity: limit == e.v ? 1 : 0.8,
        },
        data: [{ yAxis: e.v }],
      },
    }));

    result.push({
      type: "scatter",
      symbolSize: 10,
      tooltip: {
        formatter: (params, i) => {
          return (
            "度量：" +
            params.value[0] +
            "<br/>离群程度：" +
            params.value[1] +
            "<br/>主体值：" +
            JSON.stringify(sAndO[params.dataIndex][0]) +
            "<br/>客体值" +
            JSON.stringify(sAndO[params.dataIndex][1])
          );
        },
      },
      itemStyle: {
        color: (params) => {
          return limit && params.value[1] >= limit ? "#D06D6A" : "#009F53";
        },
      },
      data,
    });

    return { series: result, min, max };
  }

  getOption(data) {
    return {
      backgroundColor: "#ffffff",
      color: ["#30B7F5"],
      tooltip: {
        show: true,
        trigger: "item",
        axisPointer: {
          type: "cross",
        },
      },
      grid: {
        left: 30,
        right: 60,
        top: 40,
        bottom: 10,
        containLabel: true,
      },
      xAxis: {
        nameLocation: "end",
        name: "度量",
        boundaryGap: false,
        axisLine: {
          show: true,
          symbol: ["none", "arrow"],
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        nameLocation: "end",
        name: "离群程度",
        max: data.max,
        min: data.min,
        axisTick: {
          show: false,
        },
        minInterval: 0.1,
        maxInterval: 0.2,
        axisLine: {
          show: true,
          symbol: ["none", "arrow"],
        },
        axisLabel: {
          show: false,
        },
        splitLine: {
          show: false,
        },
      },
      series: data.series,
    };
  }

  set charOption(options) {
    if (!this.chartObj) {
      this.chartObj = echarts.init(this.chartBox.element.nativeElement);
    }
    this.chartObj.setOption(options);
  }
}
