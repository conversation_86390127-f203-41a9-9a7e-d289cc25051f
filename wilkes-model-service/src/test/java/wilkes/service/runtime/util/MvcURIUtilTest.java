package wilkes.service.runtime.util;


import org.junit.Test;

import java.net.MalformedURLException;

/**
 * <AUTHOR>
 */
public class MvcURIUtilTest {

  @Test
  public void fromControllerMethod() {
//    URI uri = MvcURIUtil.fromControllerMethod(AIAlgorithmBaseController.class, "downloadAIAlgorithm", "abcd", null);
//    System.out.println(uri);
  }

  @Test
  public void getURLPath() throws MalformedURLException {

  }

  @Test
  public void test() {
    String s = "/{a}/abcdefg/{b}";
    String s1 = s.replaceAll("\\{[^}]*\\}", "%s");

    System.out.println(String.format(s1, "xxx", null, "yyy"));
  }
}