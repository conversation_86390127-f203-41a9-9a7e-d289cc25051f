package com.topsec.wilkes.service;

import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.dto.DetectionResult;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Component
public class ModelImportCacheService {
  private Future<DetectionResult> future = new CompletableFuture<>();
  ExecutorService executorService = Executors.newSingleThreadExecutor();

  List<Model> modelCache = Lists.newArrayList();
  AtomicBoolean isLocked = new AtomicBoolean(false);
  public static ThreadLocal<Boolean> isImport = new ThreadLocal<>();

  public void addCache(Model model) {
    modelCache.add(model);
  }

  public void updateCache(List<Model> models) {
    if (models.isEmpty()) {
      return;
    }
    modelCache.addAll(models);
  }

  public List<Model> getCache() {
    return modelCache;
  }

  public void addTask(Callable<DetectionResult> callable) {
    future = executorService.submit(callable);
  }

  public boolean getTaskRunStatus() {
    return future.isDone();
  }

  public DetectionResult getTaskResult() {
//    if (future.isCompletedExceptionally()) {
//      future.exceptionally(throwable -> {
//        throw new RuntimeException(throwable.getCause().getMessage());
//      });
//    }
    try {
      return future.get();
    } catch (Exception e) {
      throw new RuntimeException(e.getCause().getMessage());
    }
  }

  public void checkImportStatus() {
    if ((isImport.get() == null || !isImport.get()) && isLocked.get()) {
      throw new RuntimeException("模型正在导入，请稍后再试！");
    }
  }

  public boolean tryLock() {
    return isLocked.compareAndSet(false, true);
  }

  public void clearCache() {
    modelCache.clear();
    isLocked.set(false);
  }
}
