import { Component, OnInit, Input, AfterViewInit, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TuiNavService, TuiAppService } from '@tui/frame';

import { RuleService } from '../../ofmodules/rules/services/rule.service';
import { ApiHttpService } from '../../services/apiHttp.service';

@Component({
  selector: 'base-event',
  templateUrl: './base-event.component.html',
  styleUrls: ['./base-event.component.less'],
})
export class BaseEventComponent implements OnInit, AfterViewInit {
  @Input() baseInfoData!: FormGroup;
  @Input() isOpen = false;
  @Input() isDebug = false;
  @Input() disabled = false;
  @ViewChild('attck') attck: any;
  // 是否启用历史关联分析启用其他不启用
  isHistory = false;

  /**
   * 是否是联邦分析，用来判断是否展示节点信息
   * 默认 为false
   */
  isUnion = false;

  // 是否启用下拉数据
  public isOpenList = [
    { label: '是', value: 'enabled' },
    { label: '否', value: 'disabled' },
  ];

  // 下拉样式
  public dropDownStyle = {
    width: '100%',
    labelStyle: { height: '28px', lineHeight: '28px' },
  };

  // 配置模型专题、模型分类、attck是否显示
  isConfig = {
    subject: true,
    modelType: true,
    att: true
  }

  // 模型分类
  public modelTypes = [];

  // 专题
  public subjectLists = [];

  // ATT&CK
  public attLists = [];

  // 数据节点
  platLists = [];

  // 权限配置
  aasUriObj = {
    'MODEL-CONFIG': '',
    other: '',
  };

  public minDate: Date;
  public maxDate: Date;

  shortcutKeys = [{ label: '过去7天', value: '-7day', columnAs: 1, styleAs: 'link' }];

  constructor(
    private apiH: ApiHttpService,
    private ruleService: RuleService,
    private tuiAppService: TuiAppService,
    private tuiNavService: TuiNavService) { }

  ngAfterViewInit(): void {
    if (this.isDebug) {
      this.baseInfoData.controls['time'].markAsDirty();
      this.baseInfoData.controls['time'].updateValueAndValidity();
      //@ts-ignore
      this.baseInfoData.get('isOpen').setValue('disabled');
    }
    this.tuiAppService.clickTabEmitter.on('attckTag', (data) => {
      this.baseInfoData.get('attckTag').setValue(data.replace(new RegExp('，', 'g'), ',').split(',').filter(e => e));
    })
  }

  ngOnInit(): void {
    this.isHistory = window.location.hash.indexOf('correlation') != -1;
    this.isUnion = window.location.hash.indexOf('union') != -1;
    this.maxDate = new Date();
    this.minDate = new Date(this.maxDate.getFullYear(), this.maxDate.getMonth(), this.maxDate.getDate() - 6, this.maxDate.getHours(), this.maxDate.getMinutes(), this.maxDate.getSeconds());
    this.getSAndC();
    this.aasUriObj['MODEL-CONFIG'] = this.tuiNavService.getTuiNavByIdentity('subject').uri;
    this.aasUriObj.other = this.tuiNavService.getTuiNavByIdentity('models/correlation').uri;
    this.isUnion && this.getPlat();
  }

  // 获取是否展示模型专题以及模型分类
  async getSAndC(): Promise<void> {
    const that = this;
    const map = {
      CATEGORY: 'modelTypes',
      SUBJECT: 'subjectLists',
      ATTCK_TAG: 'attLists'
    };
    let arr: any = [];
    //@ts-ignore
    this.modelTypes = null;
    //@ts-ignore
    this.subjectLists = null;
    if (this.apiH.config) arr = this.apiH.config;
    else {
      try {
        arr = await this.apiH.getSAndC();
      } catch (e) {
        console.log('参数接口报错');
      }
    }
    this.isConfig.att = arr.includes('ATTCK_TAG');
    this.isConfig.modelType = arr.includes('CATEGORY');
    this.isConfig.subject = arr.includes('SUBJECT');
    arr = Object.keys(map).filter(e => arr.includes(e));
    arr.forEach((e: string | number) => {
      //@ts-ignore
      that[map[e]] = [];
      // 获取模型分类下拉框
      that.ruleService.getTagesTreeForTMT(e).subscribe((res) => {
        //@ts-ignore
        if (map[e] === 'modelTypes') {
          //@ts-ignore
          that[map[e]] = that.dealDataForLevelTree(res);
        } else {
          //@ts-ignore
          that[map[e]] = that.componentDataprocessing(that.subjectdealDataForTree(that.dealDataForTree(res)));
          e == 'ATTCK_TAG' &&
            !(that.attck.dataSer.dSourceCfg || { 'attck-selector': { active: false } })['attck-selector'].active &&
            (that.attck.viewAttckData = that[map[e]]);
        }
      });
    });
  }

  // 获取节点数据
  getPlat() {
    this.ruleService.getPlat().subscribe(e => {
      this.platLists = e.map(p => ({ label: `${p.orgName}/${p.name}`, value: p.id }))
    })
  }

  // 树结构处理  数据源
  subjectdealDataForTree(dataArr: any): any[] {
    const itemsFun = (label: any, data: any, key: any, selectable = true, expandedIcon = '', collapsedIcon = '') => ({
      label,
      data,
      key,
      selectable,
      expandedIcon,
      collapsedIcon,
      expanded: true,
    });
    const r: any[] = [];
    dataArr.forEach((e: any) => {
      const o = itemsFun(e.title, e.value, e.key || e.label || '', e.value, e.key || e.label || '');
      if (e.children && e.children.length > 0) {
        //@ts-ignore
        o['children'] = this.subjectdealDataForTree(e.children);
        o.selectable = false;
      }
      r.push(o);
    });
    return r;
  }

  onHandleChange(e) {
  }

  // 组件库 数据格式化做处理
  componentDataprocessing(dataArr: any) {
    dataArr.forEach((item: any) => {
      item['value'] = item.data;
      item['items'] = item['children'] ? item['children'] : [];
      if (item['items'].length == 0) {
        delete item['items'];
      }
      delete item['children'];
      if (item['items'] && item['items'].length !== 0) {
        this.componentDataprocessing(item.items);
      }
    });
    return dataArr;
  }

  getRangeTime($event: any) {
    const o =
      $event.dateRange.startDateStr && $event.dateRange.endDateStr
        ? {
          endTime: $event.dateRange.endDateStr,
          startTime: $event.dateRange.startDateStr,
        }
        : null;

    //@ts-ignore
    this.baseInfoData.get('time').setValue(o);
  }

  // 树结构处理
  dealDataForTree(dataArr: any[]) {
    const r: any[] = [];
    dataArr.forEach((resToany) => {
      r.push({
        title: resToany.name,
        key: resToany.id,
        value: resToany.id,
        children: resToany.children.length > 0 ? this.dealDataForTree(resToany.children) : [],
        isLeaf: resToany.children.length > 0 ? false : true,
        selectable: resToany.children.length > 0 ? false : resToany.label !== true,
        disabled: resToany.children.length > 0 ? false : resToany.label === true,
      });
    });
    return r;
  }

  dealDataForLevelTree(dataArr: any[]) {
    if (!dataArr || !dataArr.length) {
      return [];
    }
    let result: any[] = [];
    result = dataArr.map((el: any) => {
      return {
        id: el.id,
        no: el.id,
        name: el.name,
        children: this.dealDataForLevelTree(el.children),
      };
    });
    return result;
  }
}
