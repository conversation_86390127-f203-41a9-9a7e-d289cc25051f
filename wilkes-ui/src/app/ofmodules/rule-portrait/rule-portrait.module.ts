import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RulePortraitComponent } from './rule-portrait.component';
import { RouterModule } from '@angular/router';
import { RulePortrait } from './rule-portrait.routing';
import { ComModule } from 'src/app/components/com-module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MessageService } from '@tui/component-library';
import {
  LineChartModule,
  TChartsDirectiveModule,
  PieChartModule,
  BarChartModule,
  GeneralNameValueModule,
  TopnChartModule
} from "@tui/charts-library";
import { WChartModule, AnalyzeModule, AnalyzeWhiteModule } from '@firmament/chart-library';
import { EventTypeComponent } from './components/event-type/event-type.component';
import { AppKillChainComponent } from './components/kill-chain/kill-chain.component';
@NgModule({
  declarations: [
    RulePortraitComponent,
    EventTypeComponent,
    AppKillChainComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ComModule,
    BarChartModule,
    PieChartModule,
    LineChartModule,
    GeneralNameValueModule,
    TChartsDirectiveModule,
    WChartModule,
    AnalyzeModule,
    AnalyzeWhiteModule,
    TopnChartModule,
    RouterModule.forChild(RulePortrait)
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [MessageService],
})
export class RulePortraitModule { }
