@import "~@tui/component-library/src/style/mixin/mixin"; //  引入统一方法与变量
@import "./exp.less";

.app-rules-con .err-con .in-error-con .box-top .ui-dropdown,
.app-rules-con .err-con .in-error-con .box-top .ui-multiselect textarea,
.app-rules-con .err-con .in-error-con .no-err-con,
.app-rules-con .security-con-err .in-error-con .box-top .ui-dropdown,
.app-rules-con .security-con-err .in-error-con .box-top .ui-multiselect textarea,
.app-rules-con .security-con-err .in-error-con .no-err-con {
  border-color: #d9d9d9 !important;
}

.app-rules-con .err-con .in-error-con,
.app-rules-con .err-con .in-error-con .ui-dropdown,
.app-rules-con .err-con .in-error-con .ui-dropdown label.ui-placeholder,
.app-rules-con .err-con .in-error-con .ui-inputtext:hover,
.app-rules-con .err-con .in-error-con .ui-multiselect,
.app-rules-con .err-con .in-error-con input,
.app-rules-con .err-con .in-error-con textarea,
.app-rules-con .security-con-err .in-error-con,
.app-rules-con .security-con-err .in-error-con .ui-dropdown,
.app-rules-con .security-con-err .in-error-con .ui-dropdown label.ui-placeholder,
.app-rules-con .security-con-err .in-error-con .ui-inputtext:hover,
.app-rules-con .security-con-err .in-error-con .ui-multiselect,
.app-rules-con .security-con-err .in-error-con input,
.app-rules-con .security-con-err .in-error-con textarea,
.app-rules-con .err-con .in-error-con .ui-cascaded-select {
  border-color: #f5222d !important;
}

.deleteBox_line {
  width: 175px;
  height: 75px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 10px 15px;
  position: absolute;
  left: 235px;
  top: 0;
  display: none;
  z-index: 999;

  i.svgIcondom {
    color: #faad14;
    display: inline-block;
    margin-right: 5px;
    position: relative;
    top: 1px;
  }

  .sureBtnBox {
    margin-top: 10px;
    text-align: right;
  }

  .sureBtnBox span {
    height: 24px;
    line-height: 24px;
    padding: 0 7px;
    font-size: 12px;
    border-radius: 4px;
    margin-left: 8px;
    display: inline-block;
    border: 1px solid #d9d9d9;
    cursor: pointer;
    cursor: hand;
  }

  .sureBtnBox span.delSureBtn {
    color: #fff;
    background-color: #1890ff;
    border-color: #1890ff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
  }

  .sureBtnBox span.delCancelBtn:hover {
    color: #40a9ff;
    background-color: #fff;
    border-color: #40a9ff;
  }

  .sureBtnBox span.delSureBtn:hover {
    color: #fff;
    background-color: #40a9ff;
    border-color: #40a9ff;
  }

  .clearfix:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }

  i.triangleIcon {
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-right: 7px solid white;
    border-bottom: 5px solid transparent;
    position: absolute;
    left: -7px;
    top: 12px;
  }

  #shadowmask {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
  }
}

.wilkes {
  .file-name,
  .icon-rule {
    color: @primary-color;
  }

  .rule-portrait-con{
    .ui-tabview-panels{
      padding: 0 0 0 6px;
    }
  
    .ui-scrollpanel-content{
      height: calc(100% - 10px);
      padding: 0 12px 18px 0;
    }
  }
}

.fullfill.ui-dialog-maximized {
  height: 100% !important;
}
.ui-dialog.fullfill .ui-dialog-content {
  flex: 1 !important;
  padding: 0 !important;
}

// 拖拽相关样式
.wilke-drag-over {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 999;
  cursor: not-allowed;
  -webkit-user-select: none; /*webkit浏览器*/
  -ms-user-select: none; /*IE10*/
  -khtml-user-select: none; /*早期浏览器*/
  user-select: none;

  .wilke-drag-scope {
    cursor: copy;
    position: absolute;
    z-index: 1;
  }

  .wilke-drag {
    position: absolute;
    z-index: 0;
    background-color: #f3f5f7;
    border-left: 3px solid #0d9be3;
    line-height: 29px;
    padding-left: 8px;
    opacity: 0.7;

    &::after {
      content: "";
      display: block;
      background: url(assets/rules/image/icon3-9.png) 0 0 no-repeat;
      position: absolute;
      right: 6px;
      width: 20px;
      top: 6px;
      height: 30px;
      z-index: 3;
    }
  }
}

.sureBtnBox span.delSureBtn {
  color: #fff;
  background-color: @primary-color;
  border-color: @primary-color;
}

.sureBtnBox span.delCancelBtn:hover {
  color: @primary-color;
  background-color: #fff;
  border-color: @primary-color;
}

.sureBtnBox span.delSureBtn:hover {
  color: #fff;
  background-color: @primary-color;
  border-color: @primary-color;
}
