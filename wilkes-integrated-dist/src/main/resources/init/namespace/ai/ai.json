{"name": "ai", "desc": "深度分析", "options": {"transformations": ["ParallelismOptimize"], "sessionOptions": {"taskManagerMemory": "2g", "taskManagerCpu": 10, "name": "<PERSON>", "taskManagerNum": 1, "taskManagerOpts": "-XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -XX:ErrorFile=${TOP_BASE_PATH}/serviceLogs/wilkes/jvm/ai_hs_err_pid%p.log -XX:HeapDumpPath=${TOP_BASE_PATH}/serviceLogs/wilkes/jvm/ai_oom_$RANDOM.hprof", "autoRestart": true, "params": null}, "jobOptions": {"mainClass": "com.topsec.wilkes.operator.WilkesProcessRunner", "additionalArgs": null, "distributeCache": null, "boundedStream": false, "defaultParallelism": 1, "useKafkaParallelism": false, "parallelism": {}}}, "jars": [{"namespace": "", "name": "", "hashCode": ""}]}