package com.topsec.wilkes.repository;

import com.topsec.wilkes.common.dto.GroupId;
import com.topsec.wilkes.repository.entity.TagEntity;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface TagsRepository extends JpaRepository<TagEntity, String> {

  List<TagEntity> findByGroupIdAndLevelNumOrderByOrder(GroupId groupId, String levelNum);

  List<TagEntity> findByGroupIdAndLabelOrderByOrder(GroupId groupId,String lable);

  List<TagEntity> findByGroupIdAndParentIdAndLevelNumOrderByOrder(GroupId groupId, String parentId, String levelNum);

  List<TagEntity> findAllByIdLikeAndGroupId(String pattern, GroupId groupId);

  List<TagEntity> findAllByIdLike(String pattern);

  List<TagEntity> findAllByGroupIdOrderByOrder(GroupId groupId);

  List<TagEntity> findTagEntityByParentId(String parentId);

  List<TagEntity> findAllByGroupId(GroupId groupId);

  @Transactional
  @Modifying
  @Query(value = "delete from model_tags where tag_id=?1", nativeQuery = true)
  void deleteModelTagsByTagId(String tagId);
}