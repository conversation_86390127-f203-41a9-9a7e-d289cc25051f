package com.topsec.wilkes.behaviour.service.impl.portrait;

import com.topsec.wilkes.behaviour.config.PortraitConfig;
import com.topsec.wilkes.behaviour.dto.definition.AlgorithmType;
import com.topsec.wilkes.behaviour.dto.portrait.PortraitData;
import com.topsec.wilkes.dto.task.StageType;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SetDevPortraitServiceImpl extends BehaviourPortraitServiceImpl {

  @Override
  public List<Object> getList(String no, String queryField, Map<String, Object> conditionMap) {
    conditionMap.put(PortraitConfig.STAGE_TYPE, StageType.LEARN.name().toLowerCase());
    return super.getList(no, queryField, conditionMap);
  }

  @Override
  public PortraitData getPortraitData(String no, Map<String, Object> conditionMap, String from, String to, int pageNo, int pageSize) {
    // 查询字段,使用窗口开始时间
    List<String> queryFields = Lists.newArrayList("b_t", "l_v", "r_v", "s_t");
    List<Map<String, Object>> mapList = super.getResult(no, Lists.newArrayList(), queryFields, conditionMap);
    if (mapList.size() > 0) {
      // 构造返回结果
      PortraitData portraitData = new PortraitData();
      List portraitList = Lists.newArrayList();
      List timeList = Lists.newArrayList();
      for (int i = mapList.size() - 1; i > 0; i--) {
        if ("learn".equals(mapList.get(i).get("s_t").toString())) {
          portraitList = (List) mapList.get(i).get("l_v");
          timeList.add(mapList.get(i).get("b_t"));
          break;
        }
      }
      List subList = Collections.EMPTY_LIST;
      int listFrom = (pageNo - 1) * pageSize;
      int listTo = listFrom + pageSize;
      int total = 0;
      if (portraitList.size() > 0) {
        total = portraitList.size();
        if (listFrom <= total) {
          subList = portraitList.subList(Math.max(listFrom, 0), Math.min(listTo, total));
        }
      }
      List<PortraitData.DataInfo> dataList = Lists.newArrayList();
      dataList.add(new PortraitData.DataInfo("画像集合", subList));

      portraitData.setTimeList(timeList);
      portraitData.setDataList(dataList);
      Map config = new HashMap();
      config.put("total", total);
      portraitData.setExtendConfig(config);
      return portraitData;
    } else {
      return null;
    }
  }

  @Override
  public String accept() {
    return AlgorithmType.SET_DEVIATION.getName().replace("_", "-");
  }
}
