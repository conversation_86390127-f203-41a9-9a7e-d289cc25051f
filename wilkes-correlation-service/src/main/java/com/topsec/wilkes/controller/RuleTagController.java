package com.topsec.wilkes.controller;

import com.topsec.wilkes.common.dto.GroupId;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.common.dto.Tag;
import com.topsec.wilkes.service.TagsService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> (<EMAIL>)
 */
@RestController
@RequestMapping("/ruleTags")
@Api("模型标签管理")
@Slf4j
public class RuleTagController extends AbstractGroupableController<Tag> {

  @Autowired
  private TagsService tagsService;

  @Override
  public List<Tag> listItems(GroupId groupId) {
    return tagsService.findTagsByGroupIds(new GroupId[]{GroupId.DATA_TYPE, GroupId.EVENT_TYPE, GroupId.EVENT_TAG},ModelType.CORRELATION);
  }
}
