package com.topsec.wilkes.controller;

import com.topsec.wilkes.task.impl.HeartBeatTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description:
 * @author: dy
 * @time: 2023/4/26 1:07 下午
 */
@RestController
@RequestMapping("/heart")
public class HeartController {

  @Autowired
  private HeartBeatTask heartBeatTask;

  @GetMapping(value = "/{modelNo}")
  public void getModelReason(@PathVariable("modelNo") String modelNo) {
    heartBeatTask.startHeartBeatModel(modelNo);
  }
}
