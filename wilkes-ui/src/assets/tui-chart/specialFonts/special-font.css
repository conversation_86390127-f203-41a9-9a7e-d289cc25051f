@font-face {
  font-family: 'fzcuyuan-m03regular';
  src: url('fonts/fzcy_gbk-webfont.woff2') format('woff2'),
  url('fonts/fzcy_gbk-webfont.woff') format('woff');
}
@font-face {
  font-family: "DIGITAL-Dream";
  src: url("fonts/DIGITAL-Dream.woff2") format("woff2"),
  url("fonts/DIGITAL-Dream.woff") format("woff"),
  url("fonts/DIGITAL-Dream.TTF") format("truetype"),
  url("fonts/DIGITAL-Dream.eot") format("embedded-opentype"),
  url("fonts/DIGITAL-Dream.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}
/*-- 添加LED字体 --*/
@font-face {
  font-family: 'UnidreamLED';
  src: local('fontName Regular'),
  local('fontName'),
  url('fonts/UnidreamLED.ttf') format('truetype')
}
/*-- Times New Roman --*/
@font-face {
  font-family: 'TimesNewRoman';
  src:url('fonts/TimesNewRoman.fon') format('fon')
}
@font-face {
  font-family: "Agencyb";
  src: url("fonts/AGENCYB.ttf") format('truetype');
}
@font-face {
  font-family: "DIN";
  src: url("fonts/DIN-Bold.otf") format('opentype');
}
@font-face {
  font-family: "DIN-medium";
  src: url("fonts/DIN-Medium.otf") format('opentype');
}
@font-face {
  font-family: "led16sgmnt2-Italic";
  src: url("fonts/led16sgmnt2-Italic-1.ttf") format('truetype');
}
@font-face {
  font-family: "led16sgmnt2-Regular";
  src: url("fonts/led16sgmnt2-Regular-2.ttf") format('truetype');
}
@font-face {
  font-family: "LEDBOARD";
  src: url("fonts/LEDBOARD-1.ttf") format('truetype');
}
/* 优设标题黑 */
@font-face {
  font-family: "youshebiaotihei";
  src: url("fonts/优设标题黑.ttf") format('truetype');
}
