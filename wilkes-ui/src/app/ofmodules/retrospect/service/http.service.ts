import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class HttpRetrospect {
  public serviceUrl;

  constructor(private http: HttpClient) {}

  // 查询模型
  getModels(params: {}): Observable<any> {
    return this.http.get(`${environment.root}/offline`, { params });
  }

  setModel(data: {}): Observable<any> {
    return this.http.post(`${environment.root}/offline`, data);
  }

  delModel(id: any): Observable<any> {
    return this.http.delete(`${environment.root}/offline/${id}`);
  }

  // 离线模型结果查询
  getResult(id: any, p = { page: 200, size: 10 }): Observable<any> {
    return this.http.post(`${environment.root}/offline/${id}/result`, p);
  }

  // 升级没关联分析模型
  upgrade(id: any): Observable<any> {
    return this.http.put(`${environment.root}/offline/${id}/upgrade`, null);
  }
}
