package com.topsec.wilkes.service;

import com.topsec.minsky.dto.Result;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.common.dto.Status;
import com.topsec.wilkes.dto.DetectionResult;
import com.topsec.wilkes.dto.ExecuteInfo;

import com.topsec.wilkes.dto.ModelImportType;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import javax.servlet.ServletOutputStream;

/**
 * <AUTHOR>
 */
public interface ModelInteractiveService {
  Result importCancelModels();

  DetectionResult importExecuteModels(ExecuteInfo executeInfo, String domainName);

  Result importDetectionUpload(MultipartFile multipartFile, ModelImportType modelImportType);

  Boolean importDetectionValidateStatus();

  DetectionResult importDetectionValidateResult();

  void exportModels(ServletOutputStream outputStream, String name, String source, Status status,
                    String[] categoryId, String[] killChainId, String[] subjectId, String[] dataTypeId, String[] attCkId, ModelType modelType, List<String> nos, String creator);

  Model modifyModelSourceByNo(String no);
}
