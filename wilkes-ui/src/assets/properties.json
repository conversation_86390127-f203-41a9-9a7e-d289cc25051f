{"base": {"name": "智能分析", "nametooltip": "wilkes", "vendorLogo": "assets/login/logo.png", "productLogo": "assets/login/logo.png", "productName": "wilkes"}, "main": {"navPanel": {"show": true}, "skinSett": {"disSkin": true, "disCustomTheme": true, "skin": "defaultTheme", "skins": [{"id": "defaultTheme"}, {"id": "blueTheme"}]}, "appInfo": [{"app": "wilkes", "icon": "icon-intelligent-analysis", "label": "智能分析", "uri": "wilkes", "primaryLink": true, "linkChildEnable": false, "viewSett": {"id": "wilkes", "isIntegrated": "false", "toMagicEyeUrl": "/magiceye/magiceye", "toMagicEyeUrlA": "threaten_perception/alarmManagement/", "models-ws": "/wilkes/models/import/lock", "rulesparams": {"eventName": "事件类型", "alarm_rule": true, "securityRate": true}, "isbase": true, "aiComponent": "/wilkes-ai-model", "helpDocument": "/wilkes/docs", "isHenNan": false, "showAttachField": false, "showDirection": false, "subject": "/subject", "showSpecial": {"externalAonnection": false}}, "children": [{"icon": "icon-stackoverflow", "label": "专题管理", "uri": "models/subject", "primaryLink": true, "linkChildEnable": false, "children": [{"icon": "icon-document", "label": "专题详情", "uri": "details", "primaryLink": false, "linkChildEnable": false, "invisible": true, "dynaChildEnable": true}]}, {"icon": "icon-stackoverflow", "label": "模型画像", "uri": "portrait", "primaryLink": true, "linkChildEnable": false}, {"icon": "icon-model-management-one", "label": "模型管理", "uri": "models", "primaryLink": false, "linkChildEnable": false}, {"icon": "icon-relation1", "label": "关联分析", "uri": "models/correlation", "primaryLink": false, "linkChildEnable": false, "children": [{"icon": "icon-document", "label": "关联分析模型", "uri": "edit", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-document", "label": "关联分析模型", "uri": "edit/new", "primaryLink": false, "invisible": true, "linkChildEnable": false}, {"icon": "icon-document", "label": "关联分析模型", "uri": "edit/built-in", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-document", "label": "关联分析模型", "uri": "edit/setting", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-debug", "label": "模型调试", "uri": "offline/debug/edit/new", "primaryLink": false, "invisible": true, "linkChildEnable": false}]}, {"icon": "icon-behavior", "label": "安全异常行为分析", "uri": "models/behaviour", "primaryLink": false, "linkChildEnable": false, "children": [{"icon": "icon-document", "label": "安全异常行为分析模型", "uri": "edit", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-document", "label": "安全异常行为分析模型", "uri": "edit/new", "primaryLink": false, "invisible": true, "linkChildEnable": false}]}, {"icon": "icon-ai", "label": "深度分析", "uri": "models/ai", "primaryLink": false, "linkChildEnable": false, "children": [{"icon": "icon-document", "label": "AI模型", "uri": "edit", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}]}, {"icon": "icon-spinner10", "label": "离线分析", "uri": "models/correlation/offline", "primaryLink": false, "linkChildEnable": false, "invisible": true, "children": [{"icon": "icon-document", "label": "模型调试", "uri": "debug/result", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-document", "label": "模型回溯", "uri": "retrospect", "primaryLink": false, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-debug", "label": "模型调试", "uri": "debug", "primaryLink": false, "linkChildEnable": false, "dynaChildEnable": true}]}, {"icon": "icon-help", "label": "帮助文档", "uri": "help", "primaryLink": false, "linkChildEnable": false, "children": [{"icon": "icon-help", "label": "关联分析", "uri": "correlation", "primaryLink": false, "linkChildEnable": false}, {"icon": "icon-help", "label": "安全异常行为分析", "uri": "behaviour", "primaryLink": false, "linkChildEnable": false}]}]}]}, "serviceUrl": {"rules": "/wilkes"}}