package com.topsec.wilkes.behaviour.service.impl.stream.detection;

import com.topsec.common.utils.ToolUtils;
import com.topsec.minsky.domain.Operator;
import com.topsec.minsky.domain.Process;
import com.topsec.wilkes.behaviour.component.BaseProcessComponent;
import com.topsec.wilkes.behaviour.component.util.ConnectionUtil;
import com.topsec.wilkes.behaviour.component.util.OperatorGenerate;
import com.topsec.wilkes.behaviour.config.BehaviourAlgorithmIDConfig;
import com.topsec.wilkes.behaviour.config.BehaviourModelConfig;
import com.topsec.wilkes.behaviour.config.OperatorIDConfig;
import com.topsec.wilkes.behaviour.dto.definition.BehaviourDefinition;
import com.topsec.wilkes.behaviour.service.DetectionProcessService;
import com.topsec.wilkes.dto.task.info.BaseInfo;
import com.topsec.wilkes.transform.ProcessGenerator;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 基于集合的离群点检测算法检测Process生成实现层
 */
@Slf4j
@Service
public class CollectionOutlierDetectionProcessImpl implements DetectionProcessService {

  @Autowired
  private OperatorGenerate operatorDesGenerate;

  @Autowired
  private BaseProcessComponent baseProcessComponent;

  @Autowired
  private ProcessGenerator processGenerator;

  @Override
  public Process generateProcess(BaseInfo baseInfo) {
    Map<String, Object> commonMap = baseInfo.getCommonMap();
    //获取基础数据过滤模型
    Process baseProcess = baseProcessComponent.getBaseProcess((BehaviourDefinition) commonMap.get(BehaviourModelConfig.BEHAVIOUR_DEFINITION));
    List<Operator> operators = Lists.newArrayList();
    List<Process.Connection> connections = Lists.newArrayList();
    operators.addAll(baseProcess.getOperators());
    connections.addAll(baseProcess.getConnections());
    //连接异常检测算子
    Operator detection = operatorDesGenerate.getOperatorFromBaseInfo(OperatorIDConfig.COLLECTION_OUTLIER_DETECTION, baseInfo);
    connections.add(ConnectionUtil.getConnection(operators.get(operators.size() - 1), detection));
    operators.add(detection);
    Map<String, Boolean> configMap = new HashMap<>();
    configMap.put(OperatorIDConfig.WHITE_LIST_ENABLED, false);
    configMap.put(OperatorIDConfig.SECURITY_EVENT_ENABLED, true);
    try {
      Process process = processGenerator.getCompleteProcess(ToolUtils.OBJECT_MAPPER.writer().writeValueAsString(new Process(operators, connections)), baseInfo.getModel(), configMap);
      //生成es_sink算子
      detection.setInstanceId(detection.getId()+"__"+detection.getInstanceId());
      Operator outlierEsSinkOperator = operatorDesGenerate.getOperatorFromBaseInfo(OperatorIDConfig.OUTLIER_ES_SINK, baseInfo);
      addOutlierEsSinkOperator(process, detection, outlierEsSinkOperator, baseInfo.getModel().getNo());
      return process;
    } catch (Exception e) {
      log.error("", e);
      return new Process();
    }
  }

  @Override
  public String accept() {
    return BehaviourAlgorithmIDConfig.COLLECTION_OUTLIER;
  }

  private void addOutlierEsSinkOperator(Process process, Operator targetOperator, Operator outlierEsSinkOperator, String modelNo) {
    List<Process.Connection> connections = process.getConnections();
    List<Operator> operators = process.getOperators();
    //添加监控信息
    outlierEsSinkOperator.setMergeMappings(Collections.singletonList(new Operator.MergeMapping(modelNo, outlierEsSinkOperator.getInstanceId(), new HashMap<String, Object>())));

    //绑定目标算子的出口连接到es_sink算子
    connections.add(ConnectionUtil.getConnection(targetOperator, 2, outlierEsSinkOperator, 1));
    operators.add(outlierEsSinkOperator);
  }
}
