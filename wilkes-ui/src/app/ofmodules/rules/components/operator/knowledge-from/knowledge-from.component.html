<div class="knowledge-from-con">
  <div class="intelligence-con">
    <app-intelligence #wEdit [toolId]="id" [(ngModel)]="value" [typeNum]="item.templateType.typeNum"
      [disabled]="disabled" [showRadioButton]="false" [fildes]="item.templateType.option"
      (setROptions)="setROptions($event)" (selChange)="selChange($event)" (saveClick)="selChange($event)">
    </app-intelligence>
  </div>
  <div class="files-con" *ngIf="wEdit.aConditionO?.sel?.length && isInclude">
    <label>覆盖日志字段</label>
    <ul>
      <li *ngFor="
          let field of cover_field;
          index as i;
          first as isf;
          last as isl
        ">
        <t-dropdown [options]="field.option" [(ngModel)]="field.value" placeholder="知识库字段"
          (onChange)="removeCover($event, field)" [disabled]="disabled" [showClear]="true" [style]="{ width: '180px' }"
          appendTo="body" panelStyleClass="k-f-field"></t-dropdown>

        <span style="margin: 0 6px;">覆盖</span>
        <t-dropdown [options]="logFields | filterList:'bType':field.dataType:true:'items':true:2"
          [(ngModel)]="field.name" placeholder="日志字段" [disabled]="disabled || !field.value" [showClear]="true"
          [filter]="true" [style]="{ width: '180px' }" appendTo="body" panelStyleClass="k-f-field"
          [layoutModel]="'group-horizontal'">
        </t-dropdown>
        <i class="icon-close" *ngIf="!disabled && !(isl && isf)" (click)="coverFieldOperation(i)"></i>
        <i class="icon-open" *ngIf="!disabled && isl" (click)="coverFieldOperation()"></i>
      </li>
    </ul>
  </div>
  <div class="files-con" *ngIf="attachField && wEdit.aConditionO?.sel?.length && isInclude">
    <label>附加字段</label>
    <ul>
      <li *ngFor="let field of relation_field; index as i;first as isf; last as isl" class="float">
        <t-dropdown [options]="field.option" [(ngModel)]="field.value" placeholder="附加字段" (onChange)="removeIn()"
          [disabled]="disabled" [showClear]="true" [style]="{'width':'130px'}" appendTo="body"
          panelStyleClass="k-f-field"></t-dropdown>
        <i class="icon-close" *ngIf="!disabled && !(isl && isf)" (click)="fieldOperation(i)"></i>
        <i class="icon-open" *ngIf="!disabled && isl" (click)="fieldOperation()"></i>
      </li>
    </ul>
  </div>
</div>