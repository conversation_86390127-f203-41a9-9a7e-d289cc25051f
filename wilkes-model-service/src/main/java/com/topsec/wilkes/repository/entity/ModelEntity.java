package com.topsec.wilkes.repository.entity;

import com.topsec.wilkes.dto.RuleFieldType;
import com.topsec.wilkes.dto.magiceye.ModelBaseInfo;
import com.topsec.wilkes.operator.util.FormatUtil;
import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.common.dto.*;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.annotations.LazyCollection;
import org.hibernate.annotations.LazyCollectionOption;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Entity
@EntityListeners(AuditingEntityListener.class)
@Table(name = "model", uniqueConstraints = @UniqueConstraint(columnNames = {"no", "name"}))
public class ModelEntity implements Serializable {

  @Id
  private String id;

  @Column
  private String no;

  @Column
  private String name;

  @Column(name = "description", length = 4096)
  private String desc;

  @Column
  private String source;

  @Column
  private String creator;

  @Column
  private Date createTime;

  @Column
  private Date lastUpdatedTime;

  @Column
  @Enumerated(EnumType.STRING)
  private ModelType modelType;

  @Column(columnDefinition = "text")
  private String content;

  @Column(columnDefinition = "text")
  private String action;

  @Column
  @Enumerated(EnumType.STRING)
  private Status status;

  @Column(columnDefinition = "text")
  private String abnormal;

  @Column
  private String tenantId;

  //兼容 规则id  和 （深度分析多租户下，no无处存放的问题）。对于旧版，ruleId=编号，对于新版，编号_et_规则id。对于兼容规则问题，此版本尚未正式发布，不影响
  @Column
  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private String ruleId;

  @Column(columnDefinition = "text")
  private String domainDesc;

  @Column(columnDefinition = "text")
  private String reason;

  @Column
  @Enumerated(EnumType.STRING)
  private History history;

  @Column
  private String platforms;

  @ManyToMany
  @LazyCollection(value = LazyCollectionOption.FALSE)
  @JoinTable(joinColumns = @JoinColumn(name = "model_id"), inverseJoinColumns = @JoinColumn(name = "tag_id"))
  private List<TagEntity> tags;

  @OneToMany(mappedBy = "modelEntity", cascade = CascadeType.DETACH, fetch = FetchType.LAZY)
  private List<ModelTaskEntity> taskEntities;

  public ModelEntity() {
  }

  public ModelEntity(String id) {
    this.id = id;
  }

  /**
   * 依赖modelType，读取ruleId中存储的originiNO或者是ruleId
   *
   * @param ruleFieldType
   * @param modelType
   * @return
   */
  public String getParseRuleId(RuleFieldType ruleFieldType, ModelType modelType) {
    if (this.ruleId==null){
      return "";
    }
    if (modelType != ModelType.AI && ruleFieldType == RuleFieldType.ORIGIN_NO) { //对于非AI分析，不需要原始编号
      return null;
    }
    int index = ruleFieldType.getIndex(modelType);
    String[] split = this.ruleId.split(FormatUtil.extendMark, -1);
    if (index==1 && split.length<2){//处理越界行为
      return "";
    }
    return split[index];
  }

  /**
   * 填充规则id，兼容ai，对于关联分析等，no目前无意义
   *
   * @param originNo
   * @param alarmRuleId
   * @param modelType
   */
  public void setBuildRuleId(String originNo,String alarmRuleId, ModelType modelType) {
    if (modelType == ModelType.AI) {
      this.ruleId = String.format("%s%s%s", originNo, FormatUtil.extendMark, Strings.isNullOrEmpty(alarmRuleId) ? "" : alarmRuleId);
    } else {
      this.ruleId = alarmRuleId;
    }
  }

  public Model convertToModel() {
    Model model = convertToBaseModel();
    model.setContent(this.content);
    try {
      model.setActions(
          Arrays.asList(ToolUtils.OBJECT_MAPPER.readValue(this.action, Action[].class)));
    } catch (Exception e) {
      log.error("", e);
    }
    return model;
  }

  public Model convertToBaseModel() {
    Model model = new Model();
    model.setId(this.id);
    model.setNo(this.no);
    model.setName(this.name);
    model.setDesc(this.desc);
    model.setSource(this.source);
    if ("内置".equals(this.source)) {
      model.setCreator("");
    } else {
      model.setCreator(Strings.isNullOrEmpty(this.creator) || "匿名用户".equals(this.creator) ? "Admin"
          : this.creator);
    }
    model.setCreateTime(this.createTime);
    model.setLastUpdatedTime(this.lastUpdatedTime);
    model.setStatus(this.status);
    model.setModelType(this.modelType);
    model.setAbnormal(this.abnormal);
    model.setTenantId(this.tenantId);
    model.setOriginNo(getParseRuleId(RuleFieldType.ORIGIN_NO, modelType));
    model.setRuleId(getParseRuleId(RuleFieldType.RULE_ID, modelType));
    model.setDomainDesc(this.domainDesc);
    model.setReason(this.reason);
    model.setHistory(this.history);
    if (this.platforms != null) {
      model.setPlatforms(Lists.newArrayList(platforms.split(",")));
    }
    model.setLastTriggerTime("未触发");
    if (this.getTags() != null) {
      model.setTags(
          this.getTags().stream().map(tag -> tag.convertToTag()).collect(Collectors.toList()));
    }
    return model;
  }

  public ModelBaseInfo convertToModelBaseInfo() {
    ModelBaseInfo modelBaseInfo = new ModelBaseInfo();
    modelBaseInfo.setLabel(this.name);
    modelBaseInfo.setValue(this.no);
    return modelBaseInfo;
  }


}
