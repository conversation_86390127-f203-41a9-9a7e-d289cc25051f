package com.topsec.wilkes.behaviour.service.impl.portrait;

import com.topsec.wilkes.behaviour.dto.definition.AlgorithmType;
import com.topsec.wilkes.behaviour.dto.portrait.PortraitData;
import com.topsec.wilkes.behaviour.service.impl.util.StatisticTimeUtil;

import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 基于集合的离群点检测算法-画像服务
 *
 * <AUTHOR>
 */
@Service
public class ColOutDevPortraitServiceImpl extends BehaviourPortraitServiceImpl {
  @Override
  public PortraitData getPortraitData(String no, Map<String, Object> conditionMap, String from, String to, int pageNo, int pageSize) {
    if (conditionMap.get("area") != null && !((List) conditionMap.get("area")).isEmpty()) {
      // 查询字段
      List<String> queryFields = Lists.newArrayList("a_v", "p_v");
      List<String> area = (List) conditionMap.get("area");
      conditionMap.remove("area");
      List<Map<String, Object>> mapList = super.getResult(no, Lists.newArrayList(), queryFields, conditionMap);
      if (!mapList.isEmpty()) {
        // 构造返回结果
        PortraitData portraitData = new PortraitData();
        List aggList = Lists.newArrayList();
        List publicList = Lists.newArrayList();
        Object aggValue = mapList.get(0).get("a_v");
        Object publicValue = mapList.get(0).get("p_v");
        if (aggValue instanceof List) {
          aggList = (List) aggValue;
        } else if (aggValue instanceof String) {
          aggList.add(aggValue);
        }
        if (publicValue instanceof List) {
          publicList = (List) publicValue;
        } else if (publicValue instanceof String) {
          publicList.add(publicValue);
        }
        List publicDiffAgg = Lists.newArrayList();
        List intersection = Lists.newArrayList();
        List aggDiffPublic = aggList;
        for (Object element : publicList) {
          if (aggList.contains(element)) {
            intersection.add(element);
          } else {
            publicDiffAgg.add(element);
          }
        }
        aggDiffPublic.removeAll(publicList);
        List<PortraitData.DataInfo> dataList = Lists.newArrayList();
        if (area.contains("A")) {
          dataList.add(new PortraitData.DataInfo("A", publicDiffAgg));
        }
        if (area.contains("B")) {
          dataList.add(new PortraitData.DataInfo("B", intersection));
        }
        if (area.contains("C")) {
          dataList.add(new PortraitData.DataInfo("C", aggDiffPublic));
        }
        portraitData.setDataList(dataList);
        return portraitData;
      }
    } else {
      // 查询字段
      List<String> queryFields = Lists.newArrayList("e_t", "s_v", "o_v", "c_r", "d_f");
      List<Map<String, Object>> mapList = super.getResult(no, Lists.newArrayList(), queryFields, conditionMap);
      if (!mapList.isEmpty()) {
        // 构造返回结果
        PortraitData portraitData = new PortraitData();
        List timeList = Lists.newArrayList();
        List subjectList = Lists.newArrayList();
        List objectList = Lists.newArrayList();
        List contactRatioList = Lists.newArrayList();
        List diversityFactorList = Lists.newArrayList();
        mapList.forEach(map -> {
          timeList.add(map.get("e_t").toString());
          subjectList.add(map.get("s_v"));
          objectList.add(map.get("o_v"));
          contactRatioList.add(map.get("c_r").toString());
          diversityFactorList.add(map.get("d_f").toString());
        });
        List<PortraitData.DataInfo> dataList = Lists.newArrayList();
        dataList.add(new PortraitData.DataInfo("主体值", subjectList));
        dataList.add(new PortraitData.DataInfo("客体值", objectList));
        dataList.add(new PortraitData.DataInfo("重合度", contactRatioList));
        dataList.add(new PortraitData.DataInfo("差异度", diversityFactorList));
        String coincidenceThreshold = behaviourModelService.getBehaviourModel(no).getBehaviourAlgorithm().getParams().get("coincidence_threshold").toString();
        String deviationThreshold = behaviourModelService.getBehaviourModel(no).getBehaviourAlgorithm().getParams().get("deviation_threshold").toString();
        String intervalDesc = behaviourModelService.getBehaviourModel(no).getBehaviourAlgorithm().getParams().get("learn_interval").toString();
        Map<String, Object> markMap = new HashMap<>();
        markMap.put("重合阈值", coincidenceThreshold);
        markMap.put("差异阈值", deviationThreshold);
        Map<String, Object> extendConfigMap = new HashMap<>();
        extendConfigMap.put("nowTime", timeList.size() > 1 ? timeList.get(timeList.size() - 1) : null);
        extendConfigMap.put("nextTime", StatisticTimeUtil.getNextTimeStr(intervalDesc, StatisticTimeUtil.getCurrentTimeStr()));
        extendConfigMap.put("mark", markMap);

        portraitData.setDataList(dataList);
        portraitData.setExtendConfig(extendConfigMap);
        return portraitData;
      }
    }
    return null;
  }

  @Override
  public String accept() {
    return AlgorithmType.COLLECTION_OUTLIER.getName();
  }
}
