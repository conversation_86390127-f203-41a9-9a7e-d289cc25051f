{"base": {"logo": "", "logo_login": "assets/login/logo.png", "name": "æºè½åæ", "version": "3.1.0", "license": "20200415"}, "main": {"i18N": {"lang": "zh_cn", "disChangeable": true, "langs": {"zh_cn": "ä¸­æ", "en": "English"}}, "pageHeader": {"displayed": true, "disChangeable": true, "onNavDisModel": "tab", "copyRightLogo": "assets/rules/image/t-logo.png", "trial": "none"}, "subNavigator": {"displayed": true, "displayedModel": "standard", "subNavCollapsed": false, "shrinkNavDis": true, "shrinkNavOpen": true}, "pageFooter": {"display": true}, "authSett": {"isLogin": false, "loginType": "password", "loginDisCode": true, "isRedirect": false, "storageKey": "bdsm", "aasUsed": false}, "skinSett": {"disSkin": true, "skin": "theme-tui-blue theme-blue", "skins": [{"themeName": "theme-topsec", "themeColor": "theme-tui-topsec"}, {"themeName": "theme-blue", "themeColor": "theme-tui-blue theme-blue"}, {"themeName": "theme-sz", "themeColor": "theme-tui-lightBlue theme-blue"}]}, "rule": {"allowFreeTime": "30m"}}, "navs": [{"icon": "icon-intelligent-analysis", "label": "æºè½åæ", "uri": "wilkes", "primaryLink": true, "linkChildEnable": false, "viewSett": {"id": "wilkes", "isIntegrated": "false", "toMagicEyeUrl": "http://10.7.211.29:22050/magiceye/#/magiceye", "toMagicEyeUrlA": "http://172.21.2.101:27096/MagicEye/#/alarmManagement", "models-ws": "models/import/lock", "rulesparams": {"eventName": "äºä»¶ç±»å"}, "aiComponent": "/wilkes/wilkes-ai-model", "helpDocument": "/wilkes/docs", "isHenNan": false, "subject": "/subject", "showSpecial": {"externalAonnection": false}}, "children": [{"icon": "icon-stackoverflow", "label": "ä¸é¢ç®¡ç", "uri": "models/subject", "primaryLink": true, "linkChildEnable": false, "children": [{"icon": "icon-document", "label": "ä¸é¢è¯¦æ", "uri": "details", "primaryLink": false, "linkChildEnable": false, "invisible": true, "dynaChildEnable": true}]}, {"icon": "icon-model-management-one", "label": "æ¨¡åç®¡ç", "uri": "models", "primaryLink": false, "linkChildEnable": false}, {"icon": "icon-relation1", "label": "å³èåæ", "uri": "models/correlation", "primaryLink": false, "linkChildEnable": false, "children": [{"icon": "icon-document", "label": "å³èåææ¨¡å", "uri": "edit", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-document", "label": "å³èåææ¨¡å", "uri": "edit/new", "primaryLink": false, "invisible": true, "linkChildEnable": false}, {"icon": "icon-document", "label": "å³èåææ¨¡å", "uri": "edit/built-in", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-document", "label": "å³èåææ¨¡å", "uri": "edit/setting", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-debug", "label": "æ¨¡åè°è¯", "uri": "offline/debug/edit/new", "primaryLink": false, "invisible": true, "linkChildEnable": false}]}, {"icon": "icon-behavior", "label": "å®å¨å¼å¸¸è¡ä¸ºåæ", "uri": "models/behaviour", "primaryLink": false, "linkChildEnable": false, "children": [{"icon": "icon-document", "label": "å®å¨å¼å¸¸è¡ä¸ºåææ¨¡å", "uri": "edit", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}]}, {"icon": "icon-ai", "label": "æ·±åº¦åæ", "uri": "models/ai", "primaryLink": false, "linkChildEnable": false, "children": [{"icon": "icon-document", "label": "AIæ¨¡å", "uri": "edit", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}]}, {"icon": "icon-spinner10", "label": "ç¦»çº¿åæ", "uri": "models/correlation/offline", "primaryLink": false, "linkChildEnable": false, "invisible": true, "children": [{"icon": "icon-document", "label": "æ¨¡åè°è¯", "uri": "debug/result", "primaryLink": false, "invisible": true, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-document", "label": "æ¨¡ååæº¯", "uri": "retrospect", "primaryLink": false, "linkChildEnable": false, "dynaChildEnable": true}, {"icon": "icon-debug", "label": "æ¨¡åè°è¯", "uri": "debug", "primaryLink": false, "linkChildEnable": false, "dynaChildEnable": true}]}, {"icon": "icon-help", "label": "å¸®å©ææ¡£", "uri": "help", "primaryLink": false, "linkChildEnable": false, "children": [{"icon": "icon-help", "label": "å³èåæ", "uri": "correlation", "primaryLink": false, "linkChildEnable": false}, {"icon": "icon-help", "label": "å®å¨å¼å¸¸è¡ä¸ºåæ", "uri": "behaviour", "primaryLink": false, "linkChildEnable": false}]}]}], "serviceUrl": {"rules": "/wilkes"}}