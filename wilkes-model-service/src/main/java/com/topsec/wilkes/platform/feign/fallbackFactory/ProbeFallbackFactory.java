package com.topsec.wilkes.platform.feign.fallbackFactory;

import com.topsec.minsky.domain.Process;
import com.topsec.minsky.dto.ProcessMetadata;
import com.topsec.wilkes.common.dto.*;
import com.topsec.wilkes.dto.RestPage;
import com.topsec.wilkes.platform.feign.ProbeService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ProbeFallbackFactory implements FallbackFactory<ProbeService> {

  @Override
  public ProbeService create(Throwable throwable) {
    log.error("fallback; reason was: " + throwable);
    return new ProbeService() {
      @Override
      public Platforms getPlatforms(String isUp, String pageIndex, String pageSize, String status) {
        return null;
      }

      @Override
      public int deleteModels(String platformIdUrl, List<String> nos, String platformId) {
        return -1;
      }

      @Override
      public RestPage getModelsByCondition(String platformIdUrl, String source, String name,
          Status status,
                                           String[] categoryId, String[] killChainId, String[] subjectId,
                                           String[] dataTypeId, String[] modelNos, int pageNo, int pageSize,
          String sort, Sort.Direction sortType, ModelType modelType, String platformId,
          String creator) {
        return new RestPage();
      }

      @Override
      public Model editModel(String platformIdUrl, String no, Model model) {
        return new Model();
      }

      @Override
      public Map<String, Object> editModelStatus(String platformIdUrl, List<String> nos,
          Status status) {
        return null;
      }

      @Override
      public Model createModel(String platformIdUrl, Model model, String platformId) {
        return new Model();
      }

      @Override
      public Object getModelWithRules(String platformIdUrl, String modelNo) {
        return null;
      }

      @Override
      public Object getModelWithBehaviours(String platformIdUrl, String modelNo) {
        return null;
      }

      @Override
      public List<Tag> getTags(String platformIdUrl, String parentId, GroupId groupId, ModelType modelType, List<String> modelNos, String platformIdHeader) {
        return Collections.emptyList();  //todo 不合适
      }

      @Override
      public List<Tag> getTagsWithParentId(String platformIdUrl, String parentId, GroupId groupId, ModelType modelType, List<String> modelNos, String platformIdHeader) {
        return Collections.emptyList();
      }
      @Override
      public ProcessMetadata addProcess(String platformIdUrl, String namespace, Process process) {
        return null;
      }

      @Override
      public List<Map<String, Object>> getFactTableAddress(String platformIdUrl, String var1,
          String var2) {
        return null;
      }

      @Override
      public List<ProcessMetadata> deleteProcess(String platformIdUrl, List<String> ids) {
        return null;
      }

      @Override
      public String getKafkaURI(String platformIdUrl, String topic) {
        return null;
      }

      @Override
      public List<Map<String, Object>> getKafkaInfoFromPensieve(String platformIdUrl, String var1) {
        return null;
      }
    };
  }
}
