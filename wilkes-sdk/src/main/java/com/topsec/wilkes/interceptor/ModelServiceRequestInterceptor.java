package com.topsec.wilkes.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class ModelServiceRequestInterceptor implements RequestInterceptor {
  private static final String CODE = "code";
  private static final String PASSWORD = "fdQrdbOGv0zhOWbW8kWcpA==";

  @Override
  public void apply(RequestTemplate template) {
    template.header(CODE, PASSWORD);
  }
}
