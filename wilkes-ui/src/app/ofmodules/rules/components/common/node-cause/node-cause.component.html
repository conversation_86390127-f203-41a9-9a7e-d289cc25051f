<div class="cause-con" *ngIf="data.length">
    <div class="t-con file-name">
        <span>识别原因</span>
    </div>
    <div class="cause-node-item" *ngFor="let item of data">
        <t-title [text]="item.name" [icon]="'default'"></t-title>
        <div class="table-item-con">
            <p class="table-item" *ngFor="let l of item.list">
                <span class="table-name" [tTooltip]="l.name" tooltipPosition="bottom">{{l.name}}</span>
                <span class="table-value" [tTooltip]="l.value" tooltipPosition="bottom">{{l.value}}</span>
            </p>
        </div>
    </div>
</div>

<div class="cause-con" *ngIf="logData.length">
    <div class="t-con file-name">
        <span>日志信息</span>
    </div>
    <div class="cause-node-item">
        <t-title text="日志列表" [icon]="'default'"></t-title>
        <div class="table-item-con table-cause" [ngStyle]="{'padding-bottom': logData?.length < 11 ? '0' : '10px'}">
            <t-table [columns]="tableHeader" [data]="logData" [scrollable]="true" [scrollHeight]="tableH" [paginator]="logData?.length > 10" [rows]="10" [pageIndex]="0"
                [totalRecords]="logData?.length" [noData]="!logData?.length">
                <ng-template tTemplate="colgroup" let-columns>
                    <colgroup>
                        <col *ngFor="let col of columns" [width]="col.width" />
                    </colgroup>
                </ng-template>
                <ng-template tTemplate="header" let-columns>
                    <tr>
                        <th *ngFor="let col of columns">
                            {{col.header}}
                        </th>
                    </tr>
                </ng-template>
                <ng-template tTemplate="body" let-rowData let-columns="columns" let-rowIndex="rowIndex">
                    <tr>
                        <td *ngFor="let col of columns" [tTooltip]="rowData[col.field]" tooltipPosition="bottom" (click)="toMagiceye(rowData)">
                            <ng-container [ngSwitch]="col.ip">
                                <ng-container *ngSwitchCase="true">
                                    <t-tags [tagsArry]="rowData[col.field]"></t-tags>
                                </ng-container>
                                <ng-container *ngSwitchDefault>
                                    {{rowData[col.field]}}
                                </ng-container>
                            </ng-container>
                        </td>
                    </tr>
                </ng-template>
            </t-table>
            <!-- <p class="table-item" *ngFor="let item of logData; index as i">
                <span class="table-value" style="width: 100%;" [ngClass]="{'table-name': i%2 == 0}" [tTooltip]="item" tooltipPosition="bottom">{{item}}</span>
            </p> -->
        </div>
    </div>
</div>


<!-- <div class="cause-con">
    <div class="t-con file-name">
        <span>检索字段信息</span>
    </div>

</div> -->