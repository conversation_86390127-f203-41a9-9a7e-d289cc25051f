package com.topsec.wilkes.controller;

import com.topsec.minsky.dto.Result;
import com.topsec.pangu.portal.audit.annotation.AuditOperation;
import com.topsec.pangu.portal.common.enums.OperationType;
import com.topsec.wilkes.annotation.SecurityParameter;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.dto.Offline;
import com.topsec.wilkes.dto.OfflineStatus;
import com.topsec.wilkes.dto.OfflineType;
import com.topsec.wilkes.service.OfflineService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/offline")
public class OfflineController {

  @Autowired
  private OfflineService offlineService;

  @RequestMapping(method = RequestMethod.GET)
  public Page<Offline> getOffline(@RequestParam(required = false) String id,
                                  @RequestParam(required = false) String modelName,
                                  @RequestParam(required = false) ModelType modelType,
                                  @RequestParam(required = false) OfflineStatus offlineStatus,
                                  @RequestParam(required = false) OfflineType offlineType,
                                  @RequestParam(required = false, defaultValue = "1") int pageNo,
                                  @RequestParam(required = false, defaultValue = "20") int pageSize,
                                  @RequestParam(required = false, defaultValue = "status") String sort,
                                  @RequestParam(required = false, defaultValue = "DESC") Sort.Direction sortType) {
    return offlineService.getOfflineByCondition(id, modelName, modelType, offlineStatus, offlineType, pageNo, pageSize, sort, sortType);
  }

  @RequestMapping(method = RequestMethod.POST)
  @SecurityParameter(outEncode = false)
  @AuditOperation(description = "新增离线分析模型", operationType = OperationType.CREATE)
  public Offline createOffline(@RequestBody Offline offline) {
    return offlineService.createOffline(offline);
  }

  @RequestMapping(value = "/test", method = RequestMethod.POST)
  @SecurityParameter(outEncode = false)
  public Result testOffline(@RequestBody Offline offline) {
    return offlineService.testOffline(offline);
  }

  @RequestMapping(value = "/{ids}", method = RequestMethod.DELETE)
  @AuditOperation(description = "删除离线分析模型", operationType = OperationType.CREATE)
  public int deleteOffline(@PathVariable List<String> ids) {
    return offlineService.deleteOffline(ids);
  }

  @RequestMapping(value = "/{id}/result", method = RequestMethod.POST)
  public Map<String, Object> getOfflineResult(@PathVariable String id ,@RequestBody Map<String,Object> param) {
      return offlineService.getOfflineResult(id, param);
  }

  @RequestMapping(value = "/{ids}/upgrade", method = RequestMethod.PUT)
  public Map<String, Integer> upgrade(@PathVariable List<String> ids) {
    return offlineService.upgrade(ids);
  }

  @RequestMapping(value = "/{id}", method = RequestMethod.GET)
  @SecurityParameter(inDecode = false)
  public Offline getOffline(@PathVariable(value = "id") String id) {
    return offlineService.getOffline(id);
  }
}
