package com.topsec.wilkes.ai.util;

import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.ai.dto.AIAlgorithm;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.net.URLConnection;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * <AUTHOR>
 */
public class AIAlgorithmBuilder {
  private static final Logger LOGGER = LoggerFactory.getLogger(AIAlgorithmBuilder.class);
  private static ObjectMapper OBJECT_MAPPER = ToolUtils.OBJECT_MAPPER;

  public AIAlgorithm build(URI uri) {
    try {
      byte[] modelPayload = getModelPayLoad(uri);
      String modelDesc = getModelDesc(modelPayload);
      if (modelDesc == null) {
        throw new RuntimeException("AI模型解析失败");
      }
      AIAlgorithm AIAlgorithm = OBJECT_MAPPER.readValue(modelDesc, AIAlgorithm.class);
      AIAlgorithm.setPayload(modelPayload);
      return AIAlgorithm;
    } catch (IOException e) {
      LOGGER.error("can't find ai model from " + uri, e);
      throw new RuntimeException("AI模型解析失败");
    }
  }

  public AIAlgorithm buildFromModelDesc(File modelDesc) {
    try {
      return OBJECT_MAPPER.readValue(new FileInputStream(modelDesc), AIAlgorithm.class);
    } catch (IOException e) {
      LOGGER.error("can't find ai model desc from " + modelDesc.getAbsolutePath(), e);
      throw new RuntimeException("AI模型解析失败");
    }
  }

  private byte[] getModelPayLoad(URI uri) throws IOException {
    switch (uri.getScheme().toLowerCase()) {
      case "file":
        return IOUtils.toByteArray(new FileInputStream(uri.getPath()));
      case "http":
        URL url = uri.toURL();
        URLConnection conn = url.openConnection();
        conn.setReadTimeout(5000);
        return IOUtils.toByteArray(conn);
      default:
        return null;
    }
  }

  private String getModelDesc(byte[] modelPayload) {
    ZipInputStream zin = new ZipInputStream(new ByteArrayInputStream(modelPayload));
    String modelDesc = null;
    try {
      ZipEntry ze = null;
      while ((ze = zin.getNextEntry()) != null) {
        if (ze.getName().equals("model.json")) {
          ByteArrayOutputStream out = new ByteArrayOutputStream();
          try {
            for (int c = zin.read(); c != -1; c = zin.read()) {
              out.write(c);
            }
            zin.closeEntry();
            out.flush();
            modelDesc = out.toString();
          } catch (IOException e) {
            e.printStackTrace();
          } finally {
            out.close();
          }
        }
      }
    } catch (IOException e) {
      throw new RuntimeException("unzip modelPayload failed ! ");
    }
    return modelDesc;
  }
}