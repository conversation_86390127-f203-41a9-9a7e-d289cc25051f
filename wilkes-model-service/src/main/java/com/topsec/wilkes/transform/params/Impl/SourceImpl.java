package com.topsec.wilkes.transform.params.Impl;

import static com.topsec.common.utils.ToolUtils.OBJECT_MAPPER;
import static com.topsec.wilkes.util.MvcURIUtil.getDataModelSchemaURI;

import com.topsec.dm.domain.DataModel;
import com.topsec.dm.domain.EventTime;
import com.topsec.dm.domain.StorageType;
import com.topsec.dm.service.FactTableService;
import com.topsec.minsky.domain.Operator;
import com.topsec.wilkes.transform.params.ParamTransform;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.InvalidParameterException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class SourceImpl implements ParamTransform {

  @Autowired
  private FactTableService factTableService;

  @Value("${operator.source.time_field:time}")
  private String eventTimeField;

  @Value("${operator.source.use_raw:false}")
  private boolean useRaw;

  @Override
  public void apply(Operator operator) throws Exception {
    Map<String, Object> paramsMap = operator.getParams();
    if (paramsMap.containsKey("url")) {
      Object url = paramsMap.get("url");
      if (!(url instanceof List)) {
        return;
      }
      List<Object> urlList = (List<Object>) url;
      if (urlList == null || urlList.size() == 0) {
        throw new InvalidParameterException("数据源不能为空！");
      }
      DataModel dataModel = OBJECT_MAPPER.convertValue(urlList.get(0), DataModel.class);
      String id = dataModel.getId();
      if (id.indexOf('/') > -1) {
        paramsMap.replace("url", "file://wilkes/sample-data/file/" + id);
        id = id.substring(0, id.indexOf('/'));
      } else {
        paramsMap.replace("url", factTableService
            .getFactTableStorageInfo(id, StorageType.DATABASE_KAFKA)
            .toURL(true));
      }
      paramsMap.put("source_name", id);
      paramsMap.put("schemaGetURL", getDataModelSchemaURI(id));
      paramsMap.put("datetimeFields",  Lists.newArrayList("time"));
      EventTime eventTime = factTableService.getEventTime(id);
      paramsMap.put("eventTimeField", eventTimeField);
      paramsMap.put("eventTimeFormat", eventTime.getTimeFieldFormat());
      paramsMap.put("use_raw", useRaw);
    }
  }

  @Override
  public List<String> getOperatorIds() {
    return Collections.singletonList("source");
  }

}
