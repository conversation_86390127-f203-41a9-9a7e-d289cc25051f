import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable()
export class TopHttpService {
  modelId = '';
  constructor(private http: HttpClient) {}

  // 获取专题管理页面获取模型数量接口
  getModelNumber() {
    return this.http.get(`${environment.root}/models/counts`);
  }
  // 获取主模型分类
  getMasterModel(pathdata: any, parentId: string): Observable<any> {
    return this.http.get(`${environment.root}/tags/list${parentId}`, {
      params: pathdata,
    });
  }
  //  根据一级专题获取二级专题
  getAbnormal(parentId: string, Id: string) {
    return this.http.get(`${environment.root}/tags/list/${parentId}?groupId=${Id}`);
  }

  // 根据标签id获取标签信息：
  getIdinformation(id: string) {
    return this.http.get(`${environment.root}/models/byTags?tagId=${id}`);
  }

  // 根据根据标签id查询模型列表
  getModelList(id: string) {
    return this.http.get(`${environment.root}/tags/${id}`);
  }

  // 根据父标签或者子标签id启停模型
  getStartStopModel(parentId: string, id: string) {
    return this.http.put(`${environment.root}/models/${parentId}/${id}`, null);
  }

  // 根据模型父标签或子标签id查询启用及停用模型数量：
  getstopModelNumber(id: string) {
    return this.http.get(`${environment.root}/models/byTags/count?tagId=${id}`);
  }
}
