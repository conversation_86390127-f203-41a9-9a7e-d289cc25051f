<div class="app-rules-con behavior-details ruleManage1">

  <t-loading [zindex]="1200" [position]="'absolute'" [visible]="isLoading"
    [ngStyle]="{ zIndex: isLoading ? '2' : '-2' }">
  </t-loading>

  <div class="ai-d-body ai-d-content" [ngStyle]="{
      zIndex: rightHideO.display || rightHideO.isPlaybook ? '99' : '0'
    }">
    <div class="over-con" [hidden]="!rightHideO.display" (click)="clearPrediction()"></div>
    <div class="ai-d-b-content ai-d-b-left" [ngStyle]="{
        width: !rightHideO.display ? 'calc(100% - 360px)' : 'calc(100% - 579px)'
      }">
      <div class="mTRight-top">
        <ul>
          <li class="aas-con flex-con">
            <form nz-form [formGroup]="baseInfo">
              <div class="form-item" style="margin-bottom: 0">
                <div class="form-control">
                  <input class="top-input" type="text" #inputTop formControlName="name" [ngStyle]="{
                      marginLeft: '20px',
                      fontSize: '18px',
                      color: '#333333',
                      padding: '0px'
                    }" (focus)="validatorsName(inputTop, topSpan, 'in')"
                    (blur)="validatorsName(inputTop, topSpan, 'out')" />
                </div>
              </div>
            </form>
          </li>
          <li>
            <span class="c-icon" (click)="inputTop.focus()" style="
                background: url(/wilkes/assets/rules/image/focus.png) 1px 7px
                  no-repeat;
              "></span>
            <span #topSpan class="top-span-w"></span>
          </li>
          <li class="li-r" style="margin-right: 10px">
            <button tButton type="button" icon="icon-return-left" label="返回" class="ui-button-secondary" [ngStyle]="btn"
              (click)="toAnalysis()"></button>
          </li>
          <ng-container *ngIf="canEdit">
            <!-- pageDisabled -->
            <li class="li-r">
              <button tButton type="button" icon="icon-floppy-disk" (click)="saveClick()" label="保存"
                class="ui-button-secondary aas-con" [ngStyle]="btn" [disabled]="isSave || isPlatform || isSecurity"
                [ngClass]="{ disabled_c: isSave }"></button>
            </li>
            <li class="li-r">
              <button tButton type="button" icon="icon-program" label="测试" class="ui-button-secondary aas-con"
                [ngStyle]="btn" (click)="testClick()" [disabled]="isPlatform"></button>
            </li>
          </ng-container>

          <ng-container *ngIf="!canEdit">
            <li class="li-r">
              <button tButton type="button" icon="icon-edit" label="编辑" [btloading]="editing"
                class="ui-button-secondary aas-con" [ngStyle]="btn" (click)="changeEdit()"></button>
            </li>
          </ng-container>

          <li class="li-r" *ngIf="comMessage?.type == 'edit'">
            <t-splitButton label="查看事件" icon="pi pi-check" styleClass="rule-splitButton" [model]="eventsT">
            </t-splitButton>
          </li>
        </ul>
      </div>
      <div class="ai-l-top">
        <!-- <div echarts [options]="options" class="echarts-box" #echarts style="height: 100%"></div> -->
      </div>
      <div class="ai-l-bottom">
        <div class="ai-l-b-body">
          <div class="middle-con-help-document" tTooltip="帮助文档" tooltipPosition="bottom" (click)="toHelp()">
            <i class="icon-help"></i>
          </div>
          <t-title [text]="'行为定义'" [icon]="'default'"></t-title>
          <t-tabView class="ui-tabviewline" styleClass="ai-details-tabview">
            <t-tabPanel header="事件">
              <!-- <app-node-event></app-node-event> -->
              <ul class="data-model aas-con">
                <!-- <li>
                  <span class="d-m-header d-m-c wd100">数据来源<i></i></span>
                  <div class="d-m-c">
                    <t-choosableButton [optional]="radiosOpt"   [fullPosition]="true"  (gettingData)="radiosEvent($event)" ></t-choosableButton>
                  </div>
                </li> -->
                <li [ngClass]="{'err-con': istest && (!dataModel.ids || dataModel.ids.length == 0)}">
                  <div class="d-m-c even-detial-con in-error-con" [ngStyle]="{ width: '410px' }">
                    <!-- <app-groud-select [placeholder]="'请选择事件数据'" [(ngModel)]="dataModel.ids" (radiosEvent)="radiosEvent($event)"
                       [options]="dataModel.selectedList" [key]="'key'" (ngModelChange)="onChange($event)">
                    </app-groud-select> -->
                    <t-multiSelect [options]="dataModel.selectedList" [layoutModel]="'group-horizontal'"
                      [panelStyle]="{ minWidth: '240px' }" [(ngModel)]="dataModel.ids" placeholder="请选择事件数据"
                      scrollHeight="250px" valueDisplay="chip" [disabled]="!canEdit" (onChange)="onChange($event)">
                      <ng-template let-group pTemplate="group">
                        <div class="p-d-flex p-ai-center">
                          <span>{{ group.label }}</span>
                        </div>
                      </ng-template>
                    </t-multiSelect>
                  </div>
                  <div class="d-m-c height-con soures-con">
                    <button tButton type="button" (click)="filterBtnClick()" label="筛选条件设置" class="ui-button-secondary"
                      [ngClass]="{ 'btn-er': filterConditions.isErrorRule }"></button>
                  </div>
                </li>
                <!-- <li>
                  <span class="d-m-header d-m-c">知识情报关联<i></i></span>
                  <div class="d-m-c line-c">
                    <input type="text" class="w-balse" placeholder="请配置知识库筛选条件" tInputText [(ngModel)]="property"
                      (focus)="rightHideO.display=true;" />
                  </div>
                </li> -->
              </ul>
              <form [formGroup]="trainParameters?.trainO.from">
                <ul class="data-model train-con aas-con">
                  <li>
                    <span class="d-m-header d-m-c">行为主体<i></i></span>
                    <div class="d-m-c">
                      <!-- <app-groud-select [placeholder]="'请选择行为主体'" #subjectGroud [disabled]="(dataModel.ids.length == 0 ?true :false)"
                        formControlName="behaviourSubject" style="display: inline-block;
                      width: 300px;" [options]="trainParameters.trainO.behaviourObjectItem" [key]="'key'"
                        (ngModelChange)="setBehaviourSubject($event, subjectGroud.selection)">
                      </app-groud-select> -->
                      <t-multiSelect class="select-box" [options]="trainParameters.trainO.behaviourObjectItem"
                        [layoutModel]="'group-horizontal'" appendTo="body" [panelStyle]="{ minWidth: '240px' }"
                        scrollHeight="250px" formControlName="behaviourSubject" placeholder="请选择行为主体"
                        (onChange)="setBehaviourSubject($event)" [disabled]="!canEdit || dataModel.ids.length == 0"
                        #behaviorSubject>
                        <ng-template let-group pTemplate="group">
                          <div class="p-d-flex p-ai-center">
                            <span>{{ group.label }}</span>
                          </div>
                        </ng-template>
                      </t-multiSelect>

                      <!-- <nz-tree-select [nzDropdownStyle]="{ 'height': '240px'}" style="width: 300px;"
                        formControlName="behaviourSubject" nzPlaceHolder="请选择行为主体"
                        [nzNodes]="trainParameters.trainO.behaviourObjectItem" nzShowSearch [nzDefaultExpandAll]="true"
                        [nzAllowClear]="true" [nzMultiple]="true" (ngModelChange)="setBehaviourSubject($event);">
                      </nz-tree-select> -->
                      <i class="icon-tps icon-con" tTooltip="定位主体所需要的特征信息" tooltipPosition="bottom"></i>
                    </div>
                  </li>
                  <li>
                    <span class="d-m-header d-m-c">行为客体<i></i></span>
                    <div class="d-m-c even-detial-con">
                      <t-multiSelect class="select-box" [options]="trainParameters.trainO.behaviourSubjectItem"
                        [layoutModel]="'group-horizontal'" appendTo="body" [panelStyle]="{ minWidth: '240px' }"
                        formControlName="behaviourObject" placeholder="请选择行为客体" scrollHeight="250px"
                        (onChange)="setBehaviourObject($event)" [disabled]="
                          !canEdit ||
                          trainParameters.trainO.from.get('behaviourSubject')
                            .value.length == 0
                        " #behaviorObject>
                        <ng-template let-group pTemplate="group">
                          <div class="p-d-flex p-ai-center">
                            <span>{{ group.label }}</span>
                          </div>
                        </ng-template>
                      </t-multiSelect>
                      <!-- <app-groud-select #objectGroud [placeholder]="'请选择行为客体'" style="display: inline-block;
                      width: 300px;" formControlName="behaviourObject"
                        [disabled]="trainParameters.trainO.from.get('behaviourSubject').value[0] === ''"
                        [options]="trainParameters.trainO.behaviourSubjectItem" [key]="'key'"
                        (ngModelChange)="setBehaviourObject($event, objectGroud.selection)">
                      </app-groud-select> -->
                      <!-- <nz-tree-select  [nzDropdownStyle]="{ 'height': '240px'}" style="width: 300px;"
                        formControlName="behaviourObject" nzPlaceHolder="请选择行为客体"
                        [nzNodes]="trainParameters.trainO.behaviourSubjectItem" nzShowSearch [nzDefaultExpandAll]="true"
                        [nzAllowClear]="true" [nzMultiple]="true"
                        [nzDisabled]="this.trainParameters.trainO.from.get('behaviourSubject').value[0] === ''"
                        (ngModelChange)="setBehaviourObject($event);">
                      </nz-tree-select> -->
                      <i class="icon-tps icon-con" tTooltip="定位主体的某种行为的特征信息" tooltipPosition="bottom"></i>
                    </div>
                  </li>
                  <li class="err-con">
                    <span class="d-m-header d-m-c">度量<i></i></span>
                    <div class="d-m-c">
                      <form formGroupName="measure">
                        <div style="float: left" [ngClass]="{
                          'in-error-con':
                            trainParameters.trainO.from
                              .get('measure')
                              .get('function').dirty &&
                            trainParameters.trainO.from
                              .get('measure')
                              .get('function').errors
                        }">
                          <t-dropdown [options]="attributes" formControlName="function" appendTo="body"
                            [layoutModel]="'group-horizontal'" [disabled]="!canEdit" (onChange)="filterFilde($event)"
                            [style]="{ width: '145px', marginRight: '10px' }" placeholder="请选择计算公式方式"></t-dropdown>
                        </div>
                        <div style="float: left" *ngIf="
                            trainParameters.trainO.from
                              .get('measure')
                              .get('function').value !== 'CUSTOM';
                            else funText
                          " [ngClass]="{
                            'in-error-con':
                              trainParameters.trainO.from
                                .get('measure')
                                .get('filed').dirty &&
                              trainParameters.trainO.from
                                .get('measure')
                                .get('filed').errors
                          }">
                          <t-dropdown *ngIf="trainParameters.filedItem?.length > 0"
                            [options]="trainParameters.filedItem" formControlName="filed" appendTo="body"
                            [disabled]="!canEdit" [layoutModel]="'group-horizontal'" [filter]="true"
                            filterBy="label,value,type" [style]="{ width: '145px' }" placeholder="请选择字段" (onChange)="
                              alarmFieldsMap.measureFiled = [$event.option]
                            ">
                          </t-dropdown>
                          <i class="icon-tps icon-con" tTooltip="根据某种统计方法来作为异常行为的判断依据" tooltipPosition="bottom"></i>
                        </div>
                        <ng-template #funText>
                          <div style="float: left">
                            <input type="text" style="width: 145px" placeholder="输入自定义内容" tInputText
                              [disabled]="!canEdit" formControlName="filed" />
                            <i class="icon-tps icon-con" tTooltip="根据某种统计方法来作为异常行为的判断依据" tooltipPosition="bottom"></i>
                          </div>
                        </ng-template>
                      </form>
                    </div>
                  </li>
                </ul>
              </form>
            </t-tabPanel>
            <!-- *ngIf="tabViewIndex > 1" -->
          </t-tabView>
          <t-title [text]="'检测算法'" [icon]="'default'"></t-title>
          <t-tabView class="ui-tabviewline" styleClass="ai-details-tabview" [activeIndex]="trainParameters.tabViewNum"
            (onChange)="handleChange($event)">
            <t-tabPanel header="个体比较" *ngIf="
                trainParameters.tabViewNum == 0 ||
                (canEdit && comMessage.status !== 'enabled')
              ">
              <ng-container *ngTemplateOutlet="tP"></ng-container>
            </t-tabPanel>
            <!-- 个群比较  -->
            <t-tabPanel header="个群比较" *ngIf="
              isbase && trainParameters?.trainO.groupItems.group.length > 0 &&
                (trainParameters.tabViewNum == 1 ||
                  (canEdit && comMessage.status !== 'enabled'))
              ">
              <ng-container *ngTemplateOutlet="tP"></ng-container>
            </t-tabPanel>
          </t-tabView>
        </div>
      </div>
    </div>
    <div class="ai-d-b-content ai-d-b-right edit-con" style="padding: 0" [ngStyle]="{
        width: (rightHideO.display ? 579 : 360) + 'px',
        'z-index': rightHideO.display ? '3' : '1'
      }">
      <t-scrollPanel #scrollPanel [style]="{ width: '100%', height: '100%' }">
        <div class="rigth-detail-con" style="background-color: #fff" [hidden]="!rightHideO.display">
          <div class="rigth-node-top">
            <div class="t-con file-name">
              <i class="icon-operator1"></i>
              <span>筛选条件设置</span>
            </div>
          </div>
          <div class="fliter-con aas-con">
            <div class="title-con file-name">
              <span>过滤条件筛选</span>
            </div>
            <ul class="data-model bottom-b">
              <li>
                <span class="d-m-header d-m-c">过滤类型<i></i></span>
                <div class="d-m-c line-c" style="padding-left: 20px">
                  <t-radioButton name="type1" label="保留" value="include" [(ngModel)]="filterConditions.type"
                    [disabled]="!canEdit">
                  </t-radioButton>
                  <t-radioButton name="type1" label="排除" value="exclude" [(ngModel)]="filterConditions.type"
                    [disabled]="!canEdit">
                  </t-radioButton>
                </div>
              </li>
              <li [ngClass]="{ 'err-con': filterConditions.isErrorRule }">
                <span class="d-m-header d-m-c">过滤条件<i></i></span>
                <div class="d-m-c in-error-con" style="display: block">
                  <t-edit-contion #t_editContion [disabled]="!canEdit" [operators]="filterConditions.seTypes"
                    [attr]="false" [allStyle]="filterConditions.allStyle" [ruleSet]="filterConditions._ruleSet"
                    [fields]="filterConditions.details" [hiddenArr]="hiddenArr" [highValue]="filterConditions.highValue"
                    [sels]="filterConditions.sels">
                  </t-edit-contion>
                </div>
              </li>
            </ul>
            <div class="title-con file-name">
              <span>知识情报筛选</span>
            </div>
            <app-intelligence #intelligence [disabled]="!canEdit" [(ngModel)]="knowLedgeFilter.value"
              [fildes]="filterConditions.details">
            </app-intelligence>
          </div>
        </div>
        <div class="rigth-detail-con" style="background-color: #fff" [hidden]="rightHideO.display">
          <t-accordion styleClass="modelsys-accordion" [multiple]="true">
            <t-accordionTab header="基础设置" [selected]="true" [disabled]="true" [accordionIcon]="true"
              headerIcon="icon-resourceSystem" *ngIf="bType == 'event-set'">
              <base-event [baseInfoData]="baseInfo" [isOpen]="!canEdit"></base-event>
            </t-accordionTab>

            <t-accordionTab [header]="bType != 'event-set' ? '知识输出' : '模型输出'" [selected]="true" [disabled]="true"
              [accordionIcon]="true" headerIcon="icon-enter-omitted">
              <app-security-event-set [type]="bType" [outputDis]="outputDis" #eventSet
                [(ngModel)]="securityModule.actions" [process]="''" [_isValidators]="istest" [pType]="'behaviour'"
                [disabled]="!canEdit" [tipFields]="tipFields" [pScrollPanel]="scrollPanel">
              </app-security-event-set>
            </t-accordionTab>
            <t-accordionTab header="模型响应" [selected]="true" [disabled]="true" [accordionIcon]="true"
              headerIcon="icon-uishow" *ngIf="bType == 'event-set'">
              <form class="even-detial-con" style="width: 100%" nz-form [formGroup]="responseInfo">
                <div class="form-item" style="margin: 10px 0 14px 0">
                  <div class="form-label" style="width: 80px; text-align: left">
                    生成情报
                  </div>
                  <div class="form-control" style="width: calc(100% - 80px); float: right">
                    <t-dropdown [style]="{ width: '100%' }" [options]="intelligenceList" [disabled]="!canEdit"
                      formControlName="ADD_INTELLIGENCE" placeholder="请选择生成情报" appendTo="body">
                    </t-dropdown>
                  </div>
                </div>
                <div class="form-item">
                  <div class="form-label" style="width: 80px; text-align: left">
                    生成知识
                  </div>
                  <div class="form-control" style="
                      width: calc(100% - 80px);
                      float: right;
                      position: relative;
                    ">
                    <div style="width: 100%">
                      <t-dropdown [style]="{
                          width: 'calc( 100% - 60px )',
                          'vertical-align': 'top'
                        }" [disabled]="!canEdit" [options]="intelligenceO.sel" formControlName="ADD_KNOWLEDGE"
                        placeholder="请选择" appendTo="body" [layoutModel]="'group-horizontal'" [filter]="true"
                        filterBy="label,value" [showClear]="true" (onChange)="valueChange($event, 0)">
                      </t-dropdown>
                      <button class="ui-button-primary" tButton type="button" label="配置" [style]="{
                          width: '50px',
                          'margin-left': '10px',
                          'vertical-align': 'top'
                        }" [disabled]="!canEdit" (click)="config(0)"></button>
                    </div>
                  </div>
                </div>
                <div class="form-item">
                  <div class="form-label" style="width: 80px; text-align: left">
                    删除知识
                  </div>
                  <div class="form-control" style="
                      width: calc(100% - 80px);
                      float: right;
                      position: relative;
                    ">
                    <div style="width: 100%">
                      <t-dropdown [style]="{
                          width: 'calc( 100% - 60px )',
                          'vertical-align': 'top'
                        }" [disabled]="!canEdit" [options]="intelligenceO.sel" formControlName="DELETE_KNOWLEDGE"
                        placeholder="请选择" appendTo="body" [layoutModel]="'group-horizontal'" [filter]="true"
                        filterBy="label,value" [showClear]="true" (onChange)="valueChange($event, 1)">
                      </t-dropdown>
                      <button class="ui-button-primary" tButton type="button" label="配置" [disabled]="!canEdit" [style]="{
                          width: '50px',
                          'margin-left': '10px',
                          'vertical-align': 'top'
                        }" (click)="config(1)"></button>
                    </div>
                  </div>
                </div>
                <div class="form-item" *ngIf="eventSet?.alarmObj.isAlarm">
                  <div class="form-label" style="width: 80px; text-align: left">
                    选择剧本
                  </div>
                  <div class="form-control" style="
                      width: calc(100% - 80px);
                      float: right;
                      position: relative;
                    ">
                    <div style="width: 100%">
                      <choose-playbook [isShow]="eventSet?.alarmObj.isAlarm" [showNum]="false"
                        [playbookDtos]="responseInfo.get('PLAYBOOKID').value" [disabled]="!canEdit"
                        (playbookDtosChange)="playbookDtosChange($event)"></choose-playbook>
                    </div>
                  </div>
                </div>
              </form>
            </t-accordionTab>
          </t-accordion>
        </div>
      </t-scrollPanel>
    </div>
  </div>
  <div class="tui-dialog-con">
    <t-dialog header="提示" [(visible)]="isDialog || isReset" [baseZIndex]="98" [style]="{ top: '1px', zIndex: '98' }">
      <i class="icon-exclamation-mark" style="
          color: #fff;
          background: #faad14;
          border-radius: 50%;
          margin-right: 5px;
        "></i>
      {{
      isReset
      ? "修改参数算法将重置"
      : baseInfo.get("isOpen").value === "enabled"
      ? "模型运行中,修改配置将重启!"
      : "模型运行中,修改配置将停用!"
      }}
      <t-footer>
        <button type="button" tButton (click)="saveClick()" label="确定"></button>
        <button type="button" tButton (click)="clear()" label="取消" class="ui-button-secondary"></button>
      </t-footer>
    </t-dialog>
  </div>
  <!-- <div class="echarts-con">
    <div echarts [options]="chartOption" (chartInit)="chartInit($event)" style="height: 80%;"></div>
  </div> -->
</div>
<!-- <div *ngIf="isChart"
  style="position: fixed;width: 800px;z-index: 99;height: 300px;top: 50%;margin-top: -100px;left: 50%;margin-left: -400px;">
  <b-chart [portraitType]="'threshold-deviation'"></b-chart>
</div>
<div class="w-chart-over" *ngIf="isChart" style="z-index: 88;" (click)="isChart=false"></div> -->
<!-- <b-chart></b-chart>
<b-chart [portraitType]="'threshold-deviation'"></b-chart> -->
<t-dialog header="知识情报配置" [(visible)]="knowledgeisDialog" [baseZIndex]="9" [closable]="false">
  <div class="g-k-dialog">
    <ul>
      <li *ngFor="let item of aConditionOfields">
        <div class="g-k-dialog-list g-k-dialog-c">
          <div *ngIf="!item.isAdd; else inputD" class="add-con">
            <span class="icon-editorial-team file-name" title="自定义" (click)="item.isAdd=true;item.assignment=''"></span>
            <t-dropdown [style]="{
                width: '100%',
                'border-color':
                  item.isNull == false &&
                  checkknowledgeisDialog &&
                  (item.assignment == '' || item.assignment == null)
                    ? '#f00'
                    : '#d9d9d9'
              }" [options]="alarmFields" [(ngModel)]="item.assignment" placeholder="请选择" appendTo="body"
              [layoutModel]="'group-horizontal'" [filter]="true" [showClear]="item.assignment !== ''"
              [baseZIndex]="100000000000000" (onChange)="assignmentV($event, item)">
            </t-dropdown>
          </div>
          <ng-template #inputD>
            <div class="add-con">
              <span class="icon-tui-newtabsvg file-name" title="恢复默认"
                (click)="item.isAdd=false;item.assignment=''"></span>
              <input type="text" tInputText [(ngModel)]="item.assignment"
                (ngModelChange)="validatorsInput($event, item)" [ngStyle]="{'border-color':
              item.isNull == false &&
              checkknowledgeisDialog &&
              (item.assignment == '' || item.assignment == null)
                ? '#f00'
                : '#d9d9d9'}" placeholder="自定义内容" style="width: 100%">
            </div>
          </ng-template>
          <!-- <div *ngIf="item.isAdd" class="add-con">
            
          </div> -->
          <span class="r-item" *ngIf="item.isNull == false">*</span>
        </div>
        <p class="icon-arrow-right g-k-dialog-list g-k-dialog-icon"></p>
        <p class="g-k-dialog-list g-k-dialog-tit">{{ item.label }}</p>
      </li>
    </ul>
  </div>
  <t-footer>
    <button class="ui-button-primary" type="button" tButton (click)="checkFun(0)" label="确定"></button>
    <button type="button" tButton (click)="cancelD(0)" label="取消" class="ui-button-secondary"></button>
  </t-footer>
</t-dialog>
<t-dialog header="删除知识情报配置" [(visible)]="knowledgeisDialogDel" [baseZIndex]="9" [closable]="false">
  <div class="g-k-dialog">
    <ul>
      <li *ngFor="let item of aConditionOfieldsDel">
        <div class="g-k-dialog-list g-k-dialog-c">
          <div *ngIf="!item.isAdd; else inputD" class="add-con">
            <span class="icon-editorial-team file-name" title="自定义" (click)="item.isAdd=true;item.assignment=''"></span>
            <t-dropdown [style]="{
                width: '100%',
                'border-color':
                  item.isNull == false &&
                  checkknowledgeisDialogDel &&
                  (item.assignment == '' || item.assignment == null)
                    ? '#f00'
                    : '#d9d9d9'
              }" [options]="alarmFields" [(ngModel)]="item.assignment" placeholder="请选择" appendTo="body"
              [layoutModel]="'group-horizontal'" [filter]="true" [showClear]="item.assignment !== ''"
              [baseZIndex]="100000000000000" (onChange)="assignmentV($event, item)">
            </t-dropdown>
          </div>
          <ng-template #inputD>
            <div class="add-con">
              <span class="icon-tui-newtabsvg file-name" title="恢复默认"
                (click)="item.isAdd=false;item.assignment=''"></span>
              <input type="text" tInputText [(ngModel)]="item.assignment"
                (ngModelChange)="validatorsInput($event, item)" [ngStyle]="{'border-color':
              item.isNull == false &&
              checkknowledgeisDialog &&
              (item.assignment == '' || item.assignment == null)
                ? '#f00'
                : '#d9d9d9'}" placeholder="自定义内容" style="width: 100%">
            </div>
          </ng-template>
          <!-- <div *ngIf="item.isAdd" class="add-con">
            
          </div> -->
          <span class="r-item" *ngIf="item.isNull == false">*</span>
        </div>
        <p class="icon-arrow-right g-k-dialog-list g-k-dialog-icon"></p>
        <p class="g-k-dialog-list g-k-dialog-tit">{{ item.label }}</p>
      </li>
    </ul>
  </div>
  <t-footer>
    <button class="ui-button-primary" type="button" tButton (click)="checkFun(1)" label="确定"></button>
    <button type="button" tButton (click)="cancelD(1)" label="取消" class="ui-button-secondary"></button>
  </t-footer>
</t-dialog>

<t-dialog [header]="portraitTitle" [(visible)]="portraitDialog">
  <t-header> </t-header>
  <div style="width: 1000px; height: 450px; overflow: hidden">
    <b-analysis-chart [visable]="portraitDialog" [modelNo]="comMessage.id" #bChart></b-analysis-chart>
  </div>
  <t-footer>
    <button type="button" tButton (click)="closePortrait()" label="关闭" class="ui-button-secondary"></button>
  </t-footer>
</t-dialog>

<ng-template #tP>
  <form [formGroup]="trainParameters?.trainO.from" class="from-con">
    <ul class="data-model train-con aas-con">
      <li [ngClass]="{'err-con': istest && !trainParameters.trainO.from.get('id').value }">
        <div class="d-m-c in-error-con">
          <t-dropdown [options]="trainParameters.trainO.item" formControlName="id" appendTo="body" [disabled]="!canEdit"
            [style]="{ width: '411px' }" placeholder="请选择检测算法" (onChange)="forecasCh($event)">
          </t-dropdown>
          <button *ngIf="!canEdit && comMessage.type == 'edit'" tButton type="button" (click)="isChartCreate()"
            label="查看画像" class="ui-button-secondary view-btn"></button>
          <!--(click)="isChartCreate()"-->
        </div>
        <div class="text-tips" *ngIf="
            trainParameters.trainO.title && trainParameters.trainO.title != '无'
          ">
          <label>
            <div class="icon"><i class="icon-tps icon-con"></i></div>
            <div>{{ trainParameters.trainO.title }}</div>
          </label>
        </div>
      </li>
      <li [ngClass]="{ disabled: !trainParameters.trainO.from.get('id').value }">
        <div (click)="isHeightCon = !isHeightCon" class="d-m-c height-con" [ngClass]="{
            'height-dis': !isHeightClass && comMessage.type !== 'edit'
          }">
          <span>高级选项</span>
          <i class="icon-down" style="margin-left: 10px"></i>
        </div>
      </li>
      <ng-container *ngFor="let item of trainParameters.forecas.item; index as i">
        <li *ngIf="!item.hidden" [hidden]="isHeightCon" style="padding-left: 10px; border-left: 1px solid #c8c8c8">
          <span class="d-m-header d-m-c">{{ item.alias }}<i></i></span>
          <div class="d-m-c" [ngSwitch]="item.type">
            <ng-container *ngSwitchCase="'interval_time'">
              <app-interval-time [(ngModel)]="item.value" [options]="item.option"
                [ngModelOptions]="{ standalone: true }" (intervalChange)="isSave = true" [isOpen]="!canEdit">
              </app-interval-time>
            </ng-container>
            <ng-container *ngSwitchCase="'single_field'">
              <t-dropdown [options]="item.option || []" [(ngModel)]="item.value" appendTo="body" [disabled]="!canEdit"
                [style]="{ width: '300px' }" [group]="item.group" [placeholder]="'请选择' + item.alias" [filter]="true"
                [ngModelOptions]="{ standalone: true }" [morelayoutNumber]="0" [numberMoreLayout]="true">
              </t-dropdown>
            </ng-container>
            <ng-container *ngSwitchCase="'enum'">
              <t-dropdown [options]="item.option || []" [(ngModel)]="item.value" appendTo="body" [filter]="true"
                [style]="{ width: '300px' }" [placeholder]="'请选择' + item.alias" [disabled]="checkConfidenceLevel(item)"
                [ngModelOptions]="{ standalone: true }">
              </t-dropdown>
            </ng-container>
            <ng-container *ngSwitchCase="'int'">
              <t-spinner class="spinner-fix" [size]="30" [(ngModel)]="item.value"
                [ngModelOptions]="{ standalone: true }" inputId="b-num-int" [inputStyle]="{ width: '269px' }"
                [min]="1"></t-spinner>
              <span style="margin-left: 10px">(%)</span>
            </ng-container>
            <ng-container *ngSwitchCase="'double'">
              <input type="text" style="width: 300px" size="30" tInputText [(ngModel)]="item.value"
                [placeholder]="'请输入' + item.alias" (ngModelChange)="inputModelChange($event, item)"
                [ngModelOptions]="{ standalone: true }" [disabled]="!canEdit" />
            </ng-container>
            <i class="icon-tps icon-con" tTooltip="{{ item.desc }}" tooltipPosition="bottom"></i>
          </div>
        </li>
      </ng-container>

      <ng-container *ngIf="
          !isHeightCon && trainParameters?.trainO.from.get('id').value == 'threshold-deviation'"
        [formGroup]="trainParameters?.trainO.from">
        <!-- 同比周期 -->
        <li style="padding-left: 10px; border-left: 1px solid #c8c8c8">
          <span class="d-m-header d-m-c">同比周期<i></i></span>
          <div class="d-m-c">
            <t-dropdown [options]="trainParameters.trainO.isItems" (onChange)="isShowPeriodic($event)"
              [disabled]="!canEdit" formControlName="isShow" appendTo="body" [style]="{ width: '300px' }"
              placeholder="有无同比周期">
            </t-dropdown>
            <i class="icon-tps icon-con" tTooltip="{{ periodic?.desc || '' }}" tooltipPosition="bottom"></i>
          </div>
        </li>
        <li *ngIf="
          trainParameters.trainO.from.get('isShow').value ===
            'is' && trainParameters.trainO.periodicItem.length > 0
        " style="padding-left: 10px; border-left: 1px solid #c8c8c8" [ngClass]="{
          'err-con':
            trainParameters.trainO.from.get('periodic').dirty &&
            trainParameters.trainO.from.get('periodic').errors
        }">
          <span class="d-m-header d-m-c"></span>
          <div class="d-m-c in-error-con">
            <!-- <t-dropdown [options]="trainParameters.trainO.periodicItem" formControlName="periodic"
            appendTo="body" [style]="{'width':'300px'}" placeholder="请选择同比周期">
          </t-dropdown> -->
            <app-interval-time formControlName="periodic" [isOpen]="!canEdit"
              [options]="trainParameters.trainO.periodicItem" (intervalChange)="timeChange()"></app-interval-time>
          </div>
        </li>
      </ng-container>

      <li *ngFor="let item of trainParameters.cycle; index as i">
        <span class="d-m-header d-m-c">{{ item.alias }}<i></i></span>
        <div class="d-m-c" [ngSwitch]="item.type" [ngClass]="{ 'err-con': istest && !item.value }">
          <ng-container *ngSwitchCase="'interval_time'">
            <app-interval-time [(ngModel)]="item.value" [options]="item.option" [isOpen]="!canEdit"
              [ngModelOptions]="{ standalone: true }" (intervalChange)="isSave = true">
            </app-interval-time>
          </ng-container>
          <ng-container *ngSwitchCase="'single_field'">
            <ng-container *ngIf="item.group">
              <t-dropdown [options]="item.isGFields ? gFields : item.option || []" [(ngModel)]="item.value"
                appendTo="body" styleClass="in-error-con" [disabled]="!canEdit"
                (onChange)="dropDownChange(item.isGFields)" [style]="{ width: '300px' }"
                [layoutModel]="'group-horizontal'" [filter]="true" [placeholder]="'请选择' + item.alias"
                [showClear]="item.group" [ngModelOptions]="{ standalone: true }">
              </t-dropdown>
            </ng-container>
            <ng-container *ngIf="!item.group">
              <t-dropdown [options]="item.isGFields ? gFields : item.option || []" [(ngModel)]="item.value"
                appendTo="body" styleClass="in-error-con" [disabled]="!canEdit"
                (onChange)="dropDownChange(item.isGFields)" [style]="{ width: '300px' }" [filter]="true"
                [placeholder]="'请选择' + item.alias" [showClear]="item.group" [ngModelOptions]="{ standalone: true }">
              </t-dropdown>
            </ng-container>
          </ng-container>
          <ng-container *ngSwitchCase="'group_field'">
            <t-multiSelect [options]="item.option || []" styleClass="no-err-con" [layoutModel]="'group-horizontal'"
              [filter]="true" appendTo="body" [panelStyle]="{ minWidth: '240px' }"
              [ngModelOptions]="{ standalone: true }" [(ngModel)]="item.value" [placeholder]="'请选择' + item.alias"
              scrollHeight="250px" display="chip" [style]="{ width: '300px' }" [disabled]="!canEdit">
              <ng-template let-group pTemplate="group">
                <div class="p-d-flex p-ai-center">
                  <span>{{ group.label }}</span>
                </div>
              </ng-template>
            </t-multiSelect>
          </ng-container>
          <ng-container *ngSwitchCase="'input_threshold_field'">
            <t-spinner class="spinner-fix" [size]="30" [(ngModel)]="item.value" [disabled]="!canEdit"
              [ngModelOptions]="{ standalone: true }" inputId="b-num-interval" [inputStyle]="{ width: '269px' }"
              [min]="item.min" [max]="item.max"></t-spinner>
            <span style="margin-left: 10px">(%)</span>
          </ng-container>
          <i class="icon-tps icon-con" tTooltip="{{ item.desc }}" tooltipPosition="bottom"></i>
        </div>
      </li>
    </ul>
  </form>
</ng-template>
<t-dialog header="控制台" [(visible)]="showConsole" appendTo="body" styleClass="fullfill" [maximizable]="true"
  [style]="{ width: '80%', height: '80%' }" [baseZIndex]="98000000" [closable]="true">
  <iframe *ngIf="showConsole" [src]="debugUrl" frameborder="0" style="width: 100%; height: 100%"></iframe>
</t-dialog>
<t-confirmDialog [baseZIndex]="100000" key="behavior-edit"></t-confirmDialog>
<t-toast key="rules-behavior" position="top-center" [style]="{'marginTop': '80px'}"></t-toast>
<t-toast key="special-fields-search" position="top-center" [style]="{'marginTop': '80px'}"></t-toast>