import { Component, Input, OnInit } from '@angular/core';
import { ComToComService } from '../../../services/com-to-com.service';
import { Util } from 'src/app/utils/util';
import { RuleService } from '../../../services/rule.service';
import { ActivatedRoute } from '@angular/router';
import { ToMagicEyeService } from 'src/app/services/toMagicEye.service';

@Component({
  selector: 'app-node-cause',
  templateUrl: './node-cause.component.html',
  styleUrls: ['./node-cause.component.less']
})
export class NodeCauseComponent implements OnInit {
  @Input() type = ''; // filter 过滤 relation 关联 aggregation 统计
  @Input() instanceId = '';
  data: any = [];
  logData: any = [];
  reason = '';
  public tableHeader: any = [
    {
      header: '时间',
      field: 'time',
      sortType: '',
      width: 170,
      selected: false,
    },
    {
      header: '源IP地址',
      field: 'src_ip',
      // ip: true,
      sortType: '',
      width: 200,
      selected: false,
    },
    {
      header: '目的IP地址',
      field: 'dst_ip',
      // ip: true,
      sortType: '',
      width: 200,
      selected: false,
    },
    {
      header: '目的端口',
      field: 'dst_port',
      sortType: '',
      width: 80,
      selected: false,
    },
    {
      header: '日志分类1',
      field: 'cat1',
      sortType: '',
      width: 90,
      selected: false,
    },
    {
      header: '日志分类2',
      field: 'cat2',
      sortType: '',
      width: 90,
      selected: false,
    }
  ];
  tableH = '400px';
  constructor(
    private comS: ComToComService,
    private api: RuleService,
    private route: ActivatedRoute,
    private toM: ToMagicEyeService) {
    // this.logData = new Array(50).fill(0).map(e => (
    //   { time: '2024-03-09 00:11:22', name: 'jkfjajlfupoafqg', s_ip: '*********,\n*********', dst_ip: '*********,\n*********', dst_port: '10', cat1: '16', cat2: '1' }
    // ));
    // [
    //   {time: '2024-03-09 00:11:22', name: 'jkfjajlfupoafqg', s_ip: [{label: '*********'}, {label: '*********'},{label: '*********'}, {label: '*********'},{label: '*********'}, {label: '*********'}], dst_ip: [{label: '*********'}, {label: '*********'}], dst_port: '10', cat1: '16', cat2: '1'},
    //   {time: '2024-03-09 00:11:22', name: 'jkfjajlfupoafqg', s_ip: [{label: '*********'}, {label: '*********'}], dst_ip: [{label: '*********'}, {label: '*********'}], dst_port: '10', cat1: '16', cat2: '1'},
    // ];
  }

  ngOnInit(): void {
    // Util.asyncM.call(this.comS, 'allFields', () => {
    this.init();
    // });
  }

  init() {
    const orderByArr = [{ n: 'time', w: 170 }, { n: 'src_ip', w: 200 }, { n: 'dst_ip', w: 200 }, { n: 'src_port', w: 80 }, { n: 'dst_port', w: 80 }];
    // 处理识别原因逻辑
    let causeData = JSON.parse(window.localStorage.getItem('wilkes-cause') || 'null');
    const fun = (obj) => Object.keys(obj).map(e => {
      const count = new RegExp(/\(([^\]]*)\)/g).exec(e);
      const z = count ? count[1] : e;
      const name = (this.comS.allFields.find(file => file.value == z) ||
        { label: { number: '次数', start_time: '窗口开始时间(window_start_time)', end_time: '窗口结束时间(window_end)' }[z] || z }).label
      return { name: count ? e.replace(count[1], name) : name, value: obj[e] || '-' }
    });
    const logFun = (d) => {
      Object.keys(log).forEach(e => {
        if (log[e].includes(this.instanceId) && d[e]) {
          this.logData.push({ ...d[e], log_id: e });
          this.tableHeader.length == 0 && (this.tableHeader = Object.keys(d[e]).map(k => {
            const o: any = orderByArr.find(b => b.n == k) || { w: 200 },
              header = (this.comS.allFields.find(file => file.value == k) ||
                { label: { number: '次数', start_time: '窗口开始时间(window_start_time)', end_time: '窗口结束时间(window_end)' }[k] || k }).nameCn,
              sttLen = Math.max(Util.strLength(header), Util.strLength(d[e][k].toString())) + 2;
            return {
              header,
              field: k,
              sortType: '',
              width: Math.max(sttLen * 8.5, 200), //['src_ip', 'dst_ip'].includes(k) ? 'auto' : 
              by: orderByArr.includes(o) ? orderByArr.indexOf(o) : Object.keys(d[e]).length + 1,
              selected: false,
            };
          }));
        }
      });
      this.tableHeader.sort((a, b) => a.by - b.by);
    }
    // 关联算子
    // causeData = {
    //   "data_flow_operators": {
    //     "log_id1454894545678978784894": [
    //       "eeb92835-37e7-455c-8977-66536bdfcc2f",
    //       "153e7963-de85-4f63-8eee-f1caf240faf9"
    //     ],
    //     "log_id1454894545678978784894456487878": [
    //       "eeb92835-37e7-455c-8977-66536bdfcc2f",
    //       "153e7963-de85-4f63-8eee-f1caf240faf9"
    //     ]
    //   },
    //   "operator_identification_reason": {
    //     "eeb92835-37e7-455c-8977-66536bdfcc2f": {
    //       "stream_0": {
    //         "dev_ip": [
    //           "************"
    //         ],
    //         "number": 1,
    //         "time": "2024-03-13 16:39:34",
    //         "dst_ip": [
    //           "**********",
    //           "**********",
    //           "**********",
    //           "**********",
    //           "**********",
    //           "**********",
    //           "**********",
    //           "**********",
    //           "**********",
    //           "**********",
    //           "**********",
    //           "**********",
    //         ]
    //       },
    //       "stream_1": {
    //         "dev_ip": [
    //           "************"
    //         ],
    //         "number": 1,
    //         "time": "2024-03-13 16:39:42"
    //       }
    //     }
    //   }
    // };

    // 统计算子
    //  causeData = {
    //   "data_flow_operators": {
    //     "log_id1": [
    //       "26c93c8a-8cd0-4e21-8418-41834d18d2f49",
    //       "153e7963-de85-4f63-8eee-f1caf240faf9"
    //     ],
    //     "log_id2": [
    //       "26c93c8a-8cd0-4e21-8418-41834d18d2f4",
    //       "153e7963-de85-4f63-8eee-f1caf240faf9"
    //     ]
    //   },
    //   "operator_identification_reason": {
    //     "2a39c8b2-ba34-4465-85df-4c7ea1c6ca9d": {
    //       "http_method": "put"
    //     },
    //     "6337eb2a-0cd5-45a5-8000-10f4fa6659d0":{
    //       "group_field":{"name":"123"},
    //       "agg_value":{"COUNT_DISTINCT(time)":"2"},
    //       "window":{"start_time":"","end_time":""}}
    //   }
    // };
    if (!causeData) return;
    const obj = causeData['operator_identification_reason'][this.instanceId],
      log = causeData['data_flow_operators'];
    if (obj) {
      switch (this.type) {
        case 'filter':
          this.data = [{ name: '过滤信息', list: fun(obj) }];
          break;
        case 'relation':
          this.data = [
            { name: '事件A', list: fun(obj.stream_0) },
            { name: '事件B', list: fun(obj.stream_1) }
          ];
          break;
        case 'aggregation':
          this.data = [
            { name: '分组信息', list: fun(obj.group_value) },
            { name: '统计信息', list: fun(obj.agg_value) },
            { name: '统计窗口', list: fun(obj.window) }
          ];
          break;
        default:
          break;
      }
    }
    this.logData = [];
    this.tableHeader = [];
    this.api.reasonLogs(
        {
          data_flow_operators: log,
          reason: this.route.data['value']['model'].reason
        }
      ).subscribe(res => {
        this.api.logData = res;
        this.api.logData && logFun(this.api.logData);
      });

  }

  toMagiceye(node) {
    this.toM.toMagicEyeUrl({
      index: 'reson',
      value: node.log_id,
      valueName: 'log_id',
    });
  }

}
