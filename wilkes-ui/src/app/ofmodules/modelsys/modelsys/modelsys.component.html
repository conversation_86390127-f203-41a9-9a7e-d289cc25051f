<div class="modelsys" [ngStyle]="style">
  <t-toast key="recall-toast" position="top-center" [style]="{'marginTop': '80px', 'width': '200px'}"></t-toast>
  <t-toast key="models" position="top-center" [zindex]="1000000000" [style]="{'width': '200px'}"></t-toast>
  <t-toast key="behaviour" position="top-center" [zindex]="1000000000" [style]="{'width': '200px'}"></t-toast>
  <t-toast key="correlation" position="top-center" [zindex]="1000000000" [style]="{'width': '200px'}"></t-toast>
  <t-toast key="error-mess" position="top-center" [zindex]="1000000000" [style]="{'width': '200px'}"></t-toast>
  <t-loading [bgColor]="'transparent'" [zindex]="100000000" [position]="'absolute'"
    [visible]="modelsysService.loadingOff"></t-loading>
  <div class="over-con" *ngIf="modelsysService.loadingOff"></div>
  <router-outlet></router-outlet>
</div>