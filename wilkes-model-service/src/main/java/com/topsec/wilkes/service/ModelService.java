package com.topsec.wilkes.service;

import com.topsec.minsky.dto.Result;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.common.dto.Status;
import com.topsec.wilkes.dto.magiceye.ModelAction;
import com.topsec.wilkes.dto.magiceye.ModelBaseInfo;
import com.topsec.wilkes.dto.magiceye.ModelCondition;
import com.topsec.wilkes.dto.reason.ReasonParam;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface ModelService {

  Model getModel(String no);

  List<Model> getModelsByStatus(Status status);

  List<Model> getModels();

  List<Model> getCompleteModel();

  List<Model> getModels(List<String> nos, boolean completeInfo);

  List<Model> getModels(ModelType modelType,boolean details);

  List<Model> getModels(ModelType modelType, Status status,boolean details);

  List<Model> getModels(String tagId);

  Model createModel(Model model);

  Model editModel(String no, Model model);

  // Model editModel(String nos, Status status);

  // todo 处理返回体中的 Object
  Map<String, Object> editModels(List<String> nos, Status status);

  int deleteModel(List<String> nos);

  Result testModel(Model model);

  Model modifyModelSourceByNo(String no);

  Model modelCopyByNo(String no);

  String getJobId(String no);

  List<Model> findModelsDoExport(String source, String name, Status status, String[] categoryId, String[] killChainId, String[] subjectId, String[] dataTypeId, String[] attCkId, int pageNo, int pageSize, String sort, Sort.Direction sortType, ModelType modelType, String creator);

  Page<Model> findModelsByCondition(String source, String name, Status status, String[] categoryId, String[] killChainId, String[] subjectId, String[] dataTypeId, String[] attCkId, String[] modelNos, int pageNo, int pageSize, String sort, Sort.Direction sortType, ModelType modelType, String creator);

  Set<String> getSourceSet(ModelType modelType);

  Map<String, List<Model>> getModelIdentificationInfo(String modelNo);

  Map<Status, Integer> getModelCountByTagId(String tagId);

  Map<ModelType, Integer> getModelCount();

  Page<Model> getConfigPlaybookModels(String source, String name, Status status, int pageNo, int pageSize, String sortField, Sort.Direction sortType);

  Map<String, Object> getModelOriginal(String no);

  Model customizeModel(String no, Model model);

  void deleteOriginalModel(String no);

  Model findModelByName(String name);

  Map<String, String> getModelCreator(ModelType modelType);

  List<String> getRefModels(String modelNo);

  List<ModelBaseInfo> getModelBaseList();

  ModelAction getModelActionByNo(String modelNo);

  List<String> getModelReason(String modelNo);

  List<Map<String, Object>> getModelSourceCountByModelType(ModelType modelType);

  List<Map<String, Object>> getModelStatusCountByModelType(ModelType modelType);

  ModelCondition getModelCondition(String modelNo);

  Map<Object, Object> getModelReasonLogs(ReasonParam reasonParam);

  Map<Object, Object> getModelOutputLogs(String modelNo, String startTime, String endTime, int pageNo, int pageSize);

  Integer getModelNumsByCreateTime(ModelType modelType, String startTime, String endTime);
}
