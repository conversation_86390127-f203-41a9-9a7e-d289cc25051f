import { AfterViewInit, Component, EventEmitter, Input, OnInit, Output, ViewChild, ViewChildren } from '@angular/core';
import { RulePortraitService } from '../../rule-portrait.service';

@Component({
  selector: 'app-event-type',
  templateUrl: './event-type.component.html',
  styleUrls: ['./event-type.component.less'],
})
export class EventTypeComponent implements OnInit, AfterViewInit {
  @Input() set modelType(d) {
    if (d) {
      this._modelType = d;
      this.event();
    }
  };
  get modelType() {
    return this._modelType;
  }
  @ViewChildren('type') types;
  @ViewChild('tool') tool;
  @Input('eventsLoading') eventsLoading = false;
  @Output() changeBtn = new EventEmitter();
  _modelType: any[];
  events = [];
  public currentTheme = '';
  public optional = [
    { icon: 'icon-dobogo-layout', lable: '所有', value: 'all', checked: true },
    { icon: 'icon-Attack1', lable: '命中', value: 'any' },
  ];
  fullcolour = true;
  tooltip: Array<any> = [];

  constructor(
    private ht: RulePortraitService,
  ) { }

  ngOnInit() {
    //this.events = JSON.parse('[{"total":"0","name":"代码注入问题"},{"total":"0","name":"信任管理问题"},{"total":"0","name":"故障注入问题"},{"total":"0","name":"安全特征问题"},{"total":"0","name":"日志信息泄露问题"},{"total":"0","name":"资料不足问题"},{"total":"0","name":"命令注入问题"},{"total":"0","name":"数据转换问题"},{"total":"0","name":"跨站脚本问题"},{"total":"0","name":"数字错误问题"},{"total":"0","name":"SQL注入问题"},{"total":"0","name":"权限许可和访问控制问题"},{"total":"0","name":"竞争条件问题"},{"total":"0","name":"后置链接问题"},{"total":"0","name":"缓冲区错误问题"},{"total":"0","name":"环境问题"},{"total":"0","name":"加密问题"},{"total":"0","name":"资源管理错误"},{"total":"0","name":"未充分验证数据可靠性问题"},{"total":"0","name":"输入验证问题"},{"total":"0","name":"调试信息泄露问题"},{"total":"0","name":"操作系统命令注入问题"},{"total":"0","name":"其他漏洞问题"},{"total":"0","name":"边界条件问题"},{"total":"0","name":"路径遍历问题"},{"total":"0","name":"处理逻辑错误问题"},{"total":"0","name":"访问控制错误问题"},{"total":"0","name":"格式化字符串问题"},{"total":"0","name":"信息泄露问题"},{"total":"0","name":"注入问题"},{"total":"0","name":"跨站请求伪造问题"},{"total":"0","name":"未声明功能问题"},{"total":"0","name":"XML注入问题"},{"total":"0","name":"授权问题"},{"total":"0","name":"代码问题"}]');
    const theme = localStorage.getItem('tui-theme') || '';
    if (theme.includes('blueTheme')) {
      this.currentTheme = 'headerbox-theme-blue';
    } else {
      this.currentTheme = 'headerbox-theme-red';
    }
  }

  ngAfterViewInit(): void {
    // this.event();
  }

  gettingData(item: any) {
    this.changeBtn.emit(item.value);
  }

  getTooltip(e, index, status) {
    if (status) {
      this.tool.nativeElement.style.display = 'block';
      // this.types['_results'][index - 1].nativeElement.appendChild(this.tool.nativeElement);
      this.tooltip = this.events[index - 1].children;
      this.tool.nativeElement.style.left = e.clientX + 20 + 'px';
      this.tool.nativeElement.style.top = e.clientY - 20 + 'px';
    } else {
      this.tool.nativeElement.style.display = 'none';
      // this.types['_results'][index - 1].nativeElement.removeChild(this.tool.nativeElement);
    }
  }

  event() {
    const modelType = this.modelType.find(e => e.isSelect).value;
    this.ht.eventType({ modelType }).subscribe(d => {
      this.events = d;
    })
  }
}
