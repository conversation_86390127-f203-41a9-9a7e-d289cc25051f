<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.topsec.wilkes</groupId>
    <artifactId>wilkes-parent</artifactId>
    <version>${revision}</version>
  </parent>

  <modelVersion>4.0.0</modelVersion>

  <artifactId>wilkes-correlation-service</artifactId>
  <name>Wilkes Correlation Analysis Service</name>

  <properties>
    <junit>4.12</junit>
  </properties>

  <dependencies>
    <!-- jhipster -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-tomcat</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-jpa</artifactId>
    </dependency>
    <!-- test -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <version>${spring-boot.version}</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.topsec.wilkes</groupId>
      <artifactId>wilkes-model-service</artifactId>
      <scope>provided</scope>
    </dependency>
    <!-- common -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>com.topsec.wilkes</groupId>
      <artifactId>wilkes-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.topsec.patronus</groupId>
      <artifactId>data-model-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.topsec.patronus</groupId>
      <artifactId>data-model-service</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mitre.dsmiley.httpproxy</groupId>
      <artifactId>smiley-http-proxy-servlet</artifactId>
      <version>1.7</version>
    </dependency>
    <dependency>
      <groupId>javax.servlet</groupId>
      <artifactId>javax.servlet-api</artifactId>
      <version>4.0.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
    </dependency>
    <!--审计服务-->
    <dependency>
      <groupId>com.topsec.pangu.portal.audit</groupId>
      <artifactId>portal-audit-sdk</artifactId>
    </dependency>
      <dependency>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
          <version>4.13.2</version>
          <scope>test</scope>
      </dependency>
  </dependencies>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
  </build>
</project>