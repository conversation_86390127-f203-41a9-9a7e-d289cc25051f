@import url("~@tui/component-library/src/style/mixin/mixin"); //  引入统一方法与变量

:host {
  ::ng-deep .spinner-fix .ui-spinner {
    width: inherit;
  }

  .behavior-details {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: #f3f4f6;

    .play-line-block {
      display: inline-block;
      vertical-align: top;
    }

    .ai-d-content {
      position: absolute;
      right: 0;
      left: 0;
      padding: 0 16px;
    }

    .ai-d-header {
      top: 0;
      z-index: 1;
      height: 50px;
      box-shadow: 0 0 5px 5px #e0e0e0;

      .ai-d-h-content {
        display: inline-block;
        vertical-align: top;
      }

      .ai-d-h-name {
        width: calc(100% - 300px);
        height: 50px;
        font-size: 20px;
        font-weight: 600;
        line-height: 50px;
        color: #2f2e2e;
      }

      .ai-d-h-operation {
        width: 300px;
        height: 50px;
        line-height: 50px;
        text-align: right;

        button {
          line-height: normal;

          &:nth-child(2) {
            margin: 0 15px;
          }
        }
      }
    }

    .ai-d-body {
      top: 0;
      bottom: 0;
      padding: 8px 8px 0;
      background-color: #f3f4f6;

      .ai-d-b-content {
        position: relative;
        z-index: 0;
        display: inline-block;
        height: 100%;
        padding-right: 1px;
        overflow-y: auto;
        vertical-align: top;
      }

      .ai-d-b-left {
        width: calc(100% - 360px);
        min-width: 400px;

        .ai-l-top {
          height: 60%;

          .ai-l-t-left {
            width: 400px;
            height: 100%;

            li {
              border: 1px solid #dfdfdf;
              border-radius: 2px;

              &:nth-child(1) {
                height: 100%;
              }

              .header1 {
                height: 32px;
                padding: 0 10px;
                line-height: 32px;
                border-bottom: dashed 1px #d1d1d1;
              }

              .content1 {
                padding: 10px;
                text-indent: 24px;
              }
            }
          }

          .ai-l-t-right {
            width: calc(100% - 400px);
            height: 100%;
            border: 1px solid #dfdfdf;
            border-left: none !important;
          }
        }

        .ai-l-bottom {
          position: absolute;
          top: 25px; // 暂时 60%
          bottom: 0;
          width: 100%;

          .ai-l-b-header {
            height: 20px;
            padding-left: 14px;
            margin-bottom: 10px;
            overflow: hidden;
            font-size: 16px;
            font-weight: 600;
            line-height: 20px;
            border-left: solid 3px #36b3f3;
          }

          .ai-l-b-body {
            min-height: 100%;
            padding: 35px 40px 10px;
            background-color: #fff;
            border: 1px solid #dfdfdf;

            .train-con {
              li .d-m-header {
                width: 100px;
              }
            }

            .middle-con-help-document {
              position: absolute;

              /* top: 0; */
              right: 20px;
              bottom: 20px;
              z-index: 1000000;
              width: 20px;
              height: 20px;
              font-size: 20px;
              line-height: 20px;
              color: #959ca9;
              text-align: center;
              cursor: pointer;
            }
          }
        }
      }

      .ai-d-b-right {
        position: absolute;
        top: 8px;
        bottom: 0;
        z-index: 1;
        width: 350px;
        height: auto;
        background-color: #fff;
        // margin-left: 5px;
        border: 1px solid #dfdfdf;
        border-bottom: none;
        border-left: none;
        border-radius: 2px;
      }
    }

    .mTRight-top {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      z-index: 1;
      min-width: 500px;
      height: 50px;
      background-color: #fff;
      // box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.15);
      border: 1px solid #dfdfdf;

      span.c-icon {
        width: 27px;
        height: 0;
        padding-top: 30px;
        text-indent: -9999px;
        cursor: pointer;
        background-size: 17px 17px;
      }

      .top-span-w {
        position: absolute;
        z-index: -99;
        padding-left: 5px;
        font-size: 18px;
        opacity: 0;
      }

      .top-input {
        height: 44px;
        padding: 2px;
        background-color: transparent;
        border-color: transparent;
      }

      .name-con {
        float: left;
        max-width: 358px;
        margin: 0 6px 0 14px;
        overflow: hidden;
        font-size: 21px;
        white-space: nowrap;
      }

      ul {
        width: 100%;

        li {
          display: flex;
          align-items: center;
          float: left;
          height: 50px;
          color: black;
        }

        li.flex-con {
          display: flex !important;
        }

        li.li-r {
          float: right;
          margin-right: 14px;

          span {
            box-sizing: border-box;
            width: 80px;
            height: 34px;
            padding: 7px 10px 7px 28px;
            font-size: 14px;
            line-height: 20px;
            color: #0d9be3;
            text-align: center;
            border: #0e9be3 1px solid;
            border-radius: 1px;
          }
        }
      }
    }

    .echarts-con {
      position: absolute;
      top: 204px;
      right: 413px;
      width: calc(100% - 980px);
      height: 600px;
      user-select: none;
      -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
      // border: 1px solid var(--border-color, #cfcfcf);
      background: #fff;
    }
  }

  .rigth-detail-con {
    position: relative;

    ::ng-deep .ahasHeader {
      width: calc(100% + 18px);
    }

    ::ng-deep .ui-accordion-header-text {
      margin-right: 10px;
    }

    ::ng-deep .ui-accordion-toggle-icon {
      display: none;
    }
  }

  .rule-behavior-con {
    position: relative;
    height: 100%;

    .top-con {
      position: relative;
      height: 50px;
      overflow: hidden;
      line-height: 50px;
      box-shadow: 0 0 10px 0 rgb(0 0 0 / 15%);

      div.left {
        float: left;
        max-width: 600px;
        height: 100%;
        margin-right: 300px;
        background-color: red;
      }

      div.right {
        width: 300px;
        height: 100%;
        background-color: blue;
      }
    }

    .bottom-con {
      position: absolute;
      top: 50px;
      bottom: 0;
      box-sizing: border-box;
      width: 100%;
      padding: 16px;
      background-color: #f3f4f6;
    }
  }

  .over-con {
    position: absolute;
    top: -50px;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
  }

  .rigth-node-top {
    box-sizing: border-box;
    height: 40px;
    padding: 0 0 0 12px;
    font-size: 16px;
    line-height: 40px;
    color: #c62b2f;
    background: #eef1f6;
    border-bottom: 1px solid #dbdbdb;
    border-radius: 0;

    .t-con i {
      margin-right: 5px;
    }
  }

  .fliter-con {
    margin: 0 10px;

    .data-model {
      &>li {
        .d-m-c {
          display: block;
          text-align: left;
        }
      }
    }

    .bottom-b {
      border-bottom: 1px solid #eee;
    }

    .title-con {
      height: 30px;
      line-height: 38px;
      color: #1aa3e9;

      span {
        font-size: 15px;
        font-weight: bold;
      }
    }
  }

  .data-model {
    &>li {
      padding: 10px 0;

      .icon-con {
        margin-left: 10px;
        font-size: 16px;
        color: #83de12;
        vertical-align: middle;
      }

      .view-btn {
        margin-left: 10px;
        vertical-align: middle;
      }

      .text-tips {
        width: 411px;
        padding: 10px 5px;
        margin-top: 8px;
        background-color: rgb(248 249 250);
        border: 1px solid rgb(218 220 224);

        label {
          position: relative;
          display: grid;
          grid-template-columns: 2.5em 1fr;
        }

        .icon {
          vertical-align: middle;

          .icon-con {
            margin-left: 5px;
          }
        }
      }

      .d-m-header {
        min-width: 90px;
        height: 30px;
        margin-right: 10px;
        font-size: 14px;
        line-height: 30px;
        color: #505050;
        text-align: justify;

        i {
          display: inline-block;

          /* padding-left: 100%; */
          width: 100%;
        }
      }

      .d-m-c {
        display: inline-block;
        vertical-align: top;

        .w-balse {
          width: 390px;
        }

        app-interval-time {
          display: inline-block;
        }

        .d-m-c-sHeader {
          padding: 0 10px;
        }
      }

      .height-con {
        font-size: 14px;
        color: #1aa3e9;
        cursor: pointer;
      }

      .soures-con {
        margin-left: 10px;
        vertical-align: text-top;
      }

      .height-dis {
        color: #bfbfbf;
      }

      .line-c {
        height: 27px;
        line-height: 29px;
      }

      .d-m-c-button {
        width: 86px;
        height: 30px;
        margin-bottom: 5px;
        line-height: 30px;
        text-align: center;
        background-size: 100%;
      }
    }
  }

  .hide-chart {
    position: absolute;
    top: 0;
    left: 0;
    z-index: -99;
    width: 100%;
  }

  .chart-b {
    position: relative;
    z-index: 0;
    width: 100%;
  }

  .g-k-dialog ul {
    height: 100%;
    overflow-y: auto;
  }

  .g-k-dialog {
    width: 630px;
    min-height: 200px;
    max-height: 600px;
  }

  .g-k-dialog-list {
    display: inline-block;
    height: 32px;
    line-height: 32px;
  }

  .g-k-dialog ul li {
    height: 32px;
    margin: 10px;
  }

  .g-k-dialog-tit {
    float: left;
    width: 260px;
    text-align: center;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
  }

  .g-k-dialog-icon {
    float: left;
    margin: 0 30px;
  }

  .g-k-dialog-c {
    float: left;
    width: 256px;

    .r-item {
      float: right;
      line-height: 38px;
      color: #f00;
    }

    .add-con {
      position: relative;
      float: left;
      width: calc(100% - 33px);

      span {
        position: absolute;
        top: 10px;
        right: -25px;
        z-index: 7;
        margin-right: 4px;
        cursor: pointer;
        background: #fff;
      }
    }
  }

  .t-con {
    display: flex;
    align-items: center;
    margin-left: 20px;
    cursor: default;
  }

  .wd100 {
    width: 100px;
  }

  .select-box ::ng-deep .ui-multiselect {
    width: 300px;

    &.ui-disabled {
      background-color: #f2f2f2 !important;
      border-color: #dfdfdf !important;
    }
  }

  .form-item {
    margin: 0 0 14px;
  }

  .form-label {
    display: inline-block;
    overflow: hidden;
    font-size: 12px;
    line-height: 29px !important;
    color: rgb(0 0 0 / 65%);
    text-align: right;
    white-space: nowrap;
    vertical-align: middle;
  }

  ::ng-deep .ui-multiselect {
    width: 100%;

    .ui-multiselect-token {
      display: block;
      float: left;
      height: 24px;
      padding: 0 8px;
      margin: 3px;
      line-height: 24px;
      color: #999;
      background: #f0f2f5;
      border-radius: 3px;
    }
  }

  .btn-er {
    color: #f5222d !important;
    background-color: #fff !important;
    border-color: #f5222d !important;
  }

  .from-con {
    ::ng-deep .ui-multiselect.ui-multiselect-chip {
      width: 300px;
    }
  }

  li.disabled {
    pointer-events: none;
  }

  ::ng-deep .ui-accordion {
    border: none !important;

    t-accordiontab {
      display: block;
      // border-bottom: 1px solid #d9d9d9 !important;
    }

    .ui-accordion-header>a {
      font-size: 16px !important;
      border: none !important;
    }

    .ui-accordion-content {
      padding: 12px 0px 12px 12px !important;
      overflow: hidden !important;
      border: none !important;
    }
  }

  ::ng-deep #flow-panel .aaa {
    vertical-align: unset;
    cursor: pointer;
  }

  ::ng-deep .ui-accordion-header>a {
    color: @primary-color !important;
  }

  ::ng-deep .rigth-node-top {
    color: @primary-color !important;
  }

  ::ng-deep t-splitbutton {
    button {
      height: 32px;
      color: @primary-color;
      background-color: #fff;
      border-color: @primary-color !important;
    }

    .ui-splitbutton-menubutton {
      color: #fff;
      background-color: @primary-color;
    }

    .ui-button:enabled:hover {
      color: #fff;
      background-color: @primary-color;
      border-color: @primary-color;
    }
  }

  ::ng-deep .ui-calendar-w-btn {
    width: auto;
  }
}