TopITA 集成智能分析部署文档

# 安装包

- 智能分析服务：wilkes-{version}.tgz
- Python依赖环境：conda-{version}.tgz
- Flink依赖环境：tlink-{version}.zip

# 安装部署

1. 解压wilkes-{version}.tgz 到 /opt/TopITA
2. 解压tlink-{version}.tgz 到 /opt/TopITA/environment/

```shell
tar -zxvf wilkes-*.tgz
cp -r Wilkes/* /opt/TopITA/
tar -zxvf tlink-*.tgz -C /opt/TopITA/environment/
ln -s /opt/TopITA/environment/flink-1* flink
tar -zxvf conda-*.tgz -C /opt/TopITA/environment/
ln -s /opt/TopITA/environment/conda* conda
```

## 数据库初始化
```sql
create database wilkes character set utf8;
create user 'wilkes'@'%' identified by '5yJw5@NrZO';
grant all privileges on wilkes.* to 'wilkes'@'%';
create user 'wilkes'@'localhost' identified by '5yJw5@NrZO';
grant all privileges on wilkes.* to 'wilkes'@'localhost';
create user 'wilkes'@'mysql' identified by '5yJw5@NrZO';
grant all privileges on wilkes.* to 'wilkes'@'mysql';
flush privileges;

create database scheduler character set utf8;
create user 'scheduler'@'%' identified by '3KJxZOQ3&n';
grant all privileges on scheduler.* to 'scheduler'@'%';
create user 'scheduler'@'localhost' identified by '3KJxZOQ3&n';
grant all privileges on scheduler.* to 'scheduler'@'localhost';
create user 'scheduler'@'mysql' identified by '3KJxZOQ3&n';
grant all privileges on scheduler.* to 'scheduler'@'mysql';
flush privileges;

source /opt/TopITA/service/wilkes/init/db/scheduler.sql
``` 

## 环境配置

在/opt/TopITA/customShell/topsec_profile 中正确配置FLINK_HOME,CONDA_HOME,SPARK_HOME,SPARK_OPERATOR_HOME

```shell
export FLINK_HOME=/opt/TopITA/environment/flink
export CONDA_HOME=/opt/TopITA/environment/conda
export SPARK_HOME=/opt/TopITA/environment/spark-2.4.0-bin-hadoop2.6
export SPARK_OPERATOR_HOME=$TOPSEC_SERVICE_BASE_DIR/environment/spark_operator/patronus/operators/SPARK
```
