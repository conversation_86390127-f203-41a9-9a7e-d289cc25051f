package com.topsec.wilkes.controller;

import com.topsec.wilkes.common.dto.GroupId;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.dto.protrait.EventType;
import com.topsec.wilkes.dto.protrait.StatisticsType;
import com.topsec.wilkes.operator.search.bean.EsAggsData;
import com.topsec.wilkes.service.ModelPortraitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/models/portrait")
@Api(tags = "模型画像")
public class ModelPortraitController {

  @Autowired
  ModelPortraitService modelPortraitService;

  @ApiOperation(value = "模型统计")
  @GetMapping(value = "/statistics")
  @ResponseBody
  public List<Map<String, Object>> getModelStatistics(@RequestParam(value = "modelType") ModelType modelType,
                                                      @RequestParam(value = "statisticsType") StatisticsType statisticsType) {
    return modelPortraitService.getModelStatistics(modelType, statisticsType);
  }

  @ApiOperation(value = "事件类型")
  @GetMapping(value = "/eventType")
  @ResponseBody
  public List<EventType> getModelEventType(@RequestParam(value = "modelType") ModelType modelType) {
    return modelPortraitService.getModelEventType(modelType);
  }

  @ApiOperation(value = "安全事件分布")
  @GetMapping(value = "/securityEventDistribution")
  @ResponseBody
  public List<EsAggsData> getSecurityEventDistribution(@RequestParam String startTime,
                                                       @RequestParam String endTime,
                                                       @RequestParam(value = "modelType") ModelType modelType) {
    return modelPortraitService.getSecurityEventDistribution(startTime, endTime, modelType);
  }

  @ApiOperation(value = "活跃数")
  @GetMapping(value = "/activeNum")
  @ResponseBody
  public Map<String, Object> getModelActiveNum(@RequestParam String startTime,
                                               @RequestParam String endTime,
                                               @RequestParam(value = "modelType") ModelType modelType) {
    return modelPortraitService.getModelActiveNum(startTime, endTime, modelType);
  }

  @ApiOperation(value = "标签Top")
  @GetMapping(value = "/tagTop")
  @ResponseBody
  public List<Map<String, Object>> getModelTagTop(@RequestParam(value = "modelType") ModelType modelType,
                                                                  @RequestParam(value = "groupId") GroupId groupId,
                                                                  @RequestParam(value = "size", required = false, defaultValue = "5") Integer size) {
    return modelPortraitService.getModelTagTop(modelType, groupId, size);
  }

  @ApiOperation(value = "安全事件Top")
  @GetMapping(value = "/securityEventTop")
  @ResponseBody
  public List<EsAggsData> getModelSecurityEventTop(@RequestParam String startTime,
                                                   @RequestParam String endTime,
                                                   @RequestParam(value = "modelType") ModelType modelType,
                                                   @RequestParam(value = "aggSize", defaultValue = "5") Integer aggSize) {
    return modelPortraitService.getModelSecurityEventTop(startTime, endTime, modelType, aggSize);
  }


  @GetMapping(value = "/eventTypeActiveNumTop")
  @ResponseBody
  public List<Map<String, Object>> getModelEventTypeActiveNumTop(@RequestParam(value = "modelType", required = false) ModelType modelType,
                                                                 @RequestParam(value = "aggSize", required = false) Integer aggSize) {
    return modelPortraitService.getModelEventTypeActiveNumTop(modelType, aggSize);
  }
}
