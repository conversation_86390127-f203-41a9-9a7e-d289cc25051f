<div class="modelsys-main">
  <div class="mm-left mm-display">
    <t-scrollPanel [style]="{ width: '100%', height: '100%' }">
      <t-accordion styleClass="modelsys-accordion" [multiple]="true">
        <t-accordionTab [accordionIcon]="true" headerIcon="icon-data06" header="&nbsp;事件数据" [selected]="true">
          <div class="searchCriteria" (click)="conditions('selectDatas', 'DATA_TYPE')" tTooltip="清空检索"
            tooltipPosition="bottom" *ngIf="searcConditionsNumber.selectDatas > 0">
            <div class="search-scope-title">清除</div>
            <div class="select-search-thenum">
              {{ searcConditionsNumber.selectDatas }}
            </div>
          </div>
          <t-tree styleClass="modelsys-class-tree" *ngIf="dataLists.length > 0" [options]="dataLists"
            selectionMode="checkbox" [(selection)]="selectDatas" [propagateSelectionDown]="false"
            [propagateSelectionUp]="false" [rightClickHighlight]="false" (onNodeExpand)="getChildrenForTree($event)"
            (onNodeSelect)="getOnNodeSelect($event, 'DATA_TYPE')"
            (onNodeUnselect)="getOnNodeSelect($event, 'DATA_TYPE')" (onNodeContextMenuSelect)="getMenuSelect($event)">
            <ng-template let-node tTemplate="default">
              <div class="modelsys-class-tree-p" [ngClass]="{
                  'add-back-tree': node.leaf,
                  unsetB: !node.leaf,
                  'no-drop': node.total == 0
                }">
                <p style="
                    float: left;
                    vertical-align: middle;
                    width: calc(100% - 10px);
                  " title="{{ node.label }}">
                  <img style="margin-right: 7px" *ngIf="node.expanded === true && node.leaf === false" class="c-dis"
                    src="/wilkes/assets/rules/image/file-open.png" />
                  <img style="margin-right: 7px" *ngIf="node.expanded === false && node.leaf === false" class="c-dis"
                    src="/wilkes/assets/rules/image/file-open.png" />
                  <span class="c-dis" style="
                      width: 100%;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                      word-break: break-all;
                    ">{{ node.label }}</span>
                </p>
                <p class="add-back-tree-t" *ngIf="node.leaf">
                  {{ node.total }}
                </p>
              </div>
            </ng-template>
          </t-tree>
          <t-nodata *ngIf="dataLists.length === 0" [noDataType]="'noDataContent'" [noDatatext]="'无数据'"></t-nodata>
        </t-accordionTab>
        <t-accordionTab [accordionIcon]="true" headerIcon="icon-edit-correlation-model" header="&nbsp;模型分类"
          [selected]="true">
          <div class="searchCriteria" (click)="conditions('selectedLists', 'CATEGORY')" tTooltip="清空检索"
            tooltipPosition="bottom" *ngIf="searcConditionsNumber.selectedLists > 0">
            <div class="search-scope-title">清除</div>
            <div class="select-search-thenum">
              {{ searcConditionsNumber.selectedLists }}
            </div>
          </div>
          <t-tree styleClass="modelsys-class-tree" *ngIf="modelClassLists.length > 0" [options]="modelClassLists"
            selectionMode="checkbox" [(selection)]="selectedLists" [propagateSelectionDown]="false"
            [propagateSelectionUp]="false" [rightClickHighlight]="false" (onNodeExpand)="getChildrenForTree($event)"
            (onNodeSelect)="getOnNodeSelect($event, 'CATEGORY')" (onNodeUnselect)="getOnNodeSelect($event, 'CATEGORY')"
            (onNodeContextMenuSelect)="getMenuSelect($event)">
            <ng-template let-node tTemplate="default">
              <div class="modelsys-class-tree-p" [ngClass]="{
                  'add-back-tree': node.leaf,
                  unsetB: !node.leaf,
                  'no-drop': node.total == 0
                }">
                <p style="
                    float: left;
                    vertical-align: middle;
                    width: calc(100% - 10px);
                  " title="{{ node.label }}">
                  <img style="margin-right: 7px" *ngIf="node.expanded === true && node.leaf === false" class="c-dis"
                    src="/wilkes/assets/rules/image/file-open.png" />
                  <img style="margin-right: 7px" *ngIf="node.expanded === false && node.leaf === false" class="c-dis"
                    src="/wilkes/assets/rules/image/file-open.png" />
                  <span class="c-dis" style="
                      width: 100%;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                      word-break: break-all;
                    ">{{ node.label }}</span>
                </p>
                <p class="add-back-tree-t" *ngIf="node.leaf">
                  {{ node.total }}
                </p>
              </div>
            </ng-template>
          </t-tree>
          <t-nodata *ngIf="modelClassLists.length === 0" [noDataType]="'noDataContent'" [noDatatext]="'无数据'"></t-nodata>
        </t-accordionTab>
        <t-accordionTab [accordionIcon]="true" headerIcon="icon-wantpick" header="&nbsp;模型专题" [selected]="true">
          <div class="searchCriteria" (click)="conditions('selectedTopics', 'SUBJECT')" tTooltip="清空检索"
            tooltipPosition="bottom" *ngIf="searcConditionsNumber.selectedTopics > 0">
            <div class="search-scope-title">清除</div>
            <div class="select-search-thenum">
              {{ searcConditionsNumber.selectedTopics }}
            </div>
          </div>
          <t-tree styleClass="modelsys-class-tree" *ngIf="topicsLists.length > 0" [options]="topicsLists"
            selectionMode="checkbox" [(selection)]="selectedTopics" [propagateSelectionDown]="false"
            [propagateSelectionUp]="false" [rightClickHighlight]="false" (onNodeExpand)="getChildrenForTree($event)"
            (onNodeSelect)="getOnNodeSelect($event, 'SUBJECT')" (onNodeUnselect)="getOnNodeSelect($event, 'SUBJECT')"
            (onNodeContextMenuSelect)="getMenuSelect($event)">
            <ng-template let-node tTemplate="default">
              <div class="modelsys-class-tree-p" [ngClass]="{
                  'add-back-tree': node.leaf,
                  unsetB: !node.leaf,
                  'no-drop': node.total == 0
                }">
                <p style="
                    float: left;
                    vertical-align: middle;
                    width: calc(100% - 10px);
                  " title="{{ node.label }}">
                  <img style="margin-right: 7px" *ngIf="node.expanded === true && node.leaf === false" class="c-dis"
                    src="/wilkes/assets/rules/image/file-open.png" />
                  <img style="margin-right: 7px" *ngIf="node.expanded === false && node.leaf === false" class="c-dis"
                    src="/wilkes/assets/rules/image/file-open.png" />
                  <span class="c-dis" style="
                      width: 100%;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                      overflow: hidden;
                      word-break: break-all;
                    ">{{ node.label }}</span>
                </p>
                <p class="add-back-tree-t" *ngIf="node.leaf">
                  {{ node.total }}
                </p>
              </div>
            </ng-template>
          </t-tree>
          <t-nodata *ngIf="topicsLists.length === 0" [noDataType]="'noDataContent'" [noDatatext]="'无数据'"></t-nodata>
        </t-accordionTab>
        <t-accordionTab [accordionIcon]="true" headerIcon="icon-process-focus" header="&nbsp;杀伤链阶段" [selected]="false">
          <div class="searchCriteria" (click)="conditions('selectedKillValue', 'KILL_CHAIN')" tTooltip="清空检索"
            tooltipPosition="bottom" *ngIf="searcConditionsNumber.selectedKillValue > 0">
            <div class="search-scope-title">清除</div>
            <div class="select-search-thenum">
              {{ searcConditionsNumber.selectedKillValue }}
            </div>
          </div>
          <t-tree styleClass="modelsys-class-tree" *ngIf="killChainList.length > 0" [options]="killChainList"
            selectionMode="checkbox" [(selection)]="selectedKillValue"
            (onNodeSelect)="getOnNodeSelect($event, 'KILL_CHAIN')"
            (onNodeUnselect)="getOnNodeSelect($event, 'KILL_CHAIN')">
            <ng-template let-node tTemplate="default">
              <div class="modelsys-class-tree-p add-back-tree" [ngClass]="{ 'no-drop': node.total == 0 }">
                <p style="float: left; vertical-align: middle">
                  <img style="margin-right: 7px" *ngIf="node.expanded === true && node.leaf === false" class="c-dis"
                    src="/wilkes/assets/rules/image/file-open.png" />
                  <img style="margin-right: 7px" *ngIf="node.expanded === false && node.leaf === false" class="c-dis"
                    src="/wilkes/assets/rules/image/file-open.png" />
                  <span class="c-dis" style="margin-top: 2px">{{
                    node.label
                    }}</span>
                </p>
                <p class="add-back-tree-t" style="float: right">
                  {{ node.total }}
                </p>
              </div>
            </ng-template>
          </t-tree>
          <t-nodata *ngIf="killChainList.length === 0" [noDataType]="'noDataContent'" [noDatatext]="'无数据'"></t-nodata>
        </t-accordionTab>
      </t-accordion>
    </t-scrollPanel>
  </div>
  <div class="mm-right mm-display">
    <div class="mm-r-header" id="header-con">
      <div class="mm-r-h-top">
        <!-- <p class="mm-r-h-top-header">查询条件</p> -->
        <div class="mm-r-h-top-body">
          <div class="r-h-left">
            <label>编号/名称</label>
            <input type="text" [ngStyle]="{
                width: getWindowHeight > 1360 ? '160px' : '120px',
                'font-size': '12px'
              }" tInputText placeholder="请输入编号/名称" [(ngModel)]="searchConditions.parameter.name"
              (ngModelChange)="changeConditions($event, 'name')" />
          </div>
          <div class="r-h-left" style="margin: 0 20px">
            <label>来源</label>
            <t-dropdown [style]="{ width: getWindowHeight > 1360 ? '160px' : '120px' }" [styleClass]="'wilkes-rule-con'"
              [panelStyleClass]="'wilkes-rule-con'" [options]="searchConditions.modelType"
              [(ngModel)]="searchConditions.parameter.source" placeholder="请选择来源"
              [showClear]="searchConditions.parameter.source !== ''" (onChange)="changeConditions($event, 'source')">
            </t-dropdown>
          </div>
          <div class="r-h-left" style="margin: 0" [ngStyle]="{
              marginRight: behaviorDev !== 'modelsys' ? '0' : '20px'
            }">
            <label>状态</label>
            <t-dropdown [style]="{ width: getWindowHeight > 1360 ? '160px' : '120px' }" [styleClass]="'wilkes-rule-con'"
              [panelStyleClass]="'wilkes-rule-con'" [options]="searchConditions.modelStatus"
              [(ngModel)]="searchConditions.parameter.status" placeholder="请选择状态"
              [showClear]="searchConditions.parameter.status !== ''" (onChange)="changeConditions($event, 'status')">
            </t-dropdown>
            <div class="setOperation" *ngIf="behaviorDev !== 'modelsys'">
              <button tButton type="button" icon="icon-search2" label="查询" class="ui-width keep"
                (click)="setOperation(1)"></button>
              <button tButton type="button" icon="icon-loop" label="重置" class="ui-width keep"
                (click)="setOperation(0)"></button>
            </div>
          </div>
          <div class="r-h-left" style="margin: 0px 20px 0px 0px" [ngStyle]="{
              display: behaviorDev == 'modelsys' ? 'inline-block' : 'none'
            }">
            <label>模型类型</label>
            <t-dropdown [style]="{ width: getWindowHeight > 1360 ? '160px' : '120px' }"
              [options]="searchConditions.condition" [(ngModel)]="searchConditions.parameter.modelType"
              placeholder="请选择类型" [showClear]="searchConditions.parameter.modelType !== ''"
              (onChange)="changeConditions($event, 'modelType')"></t-dropdown>
            <div class="setOperation">
              <button tButton type="button" icon="icon-search2" label="查询" class="ui-width keep ui-button-primary"
                (click)="setOperation(1)"></button>
              <button tButton type="button" icon="icon-loop" label="重置" class="ui-width keep ui-button-primary"
                (click)="setOperation(0)"></button>
            </div>
          </div>
        </div>
      </div>
      <ul class="mm-r-h-bottom">
        <li aas [uri]="aasUriObj.other" act="/wilkes/models/behaviour/edit/new || /wilkes/models/correlation/edit/new"
          class="colorModel">
          <t-splitButton icon="iconfont icon-plus" *ngIf="behaviorDev == 'modelsys'"
            [style]="{ 'margin-right': '16px' }" label="添加模型" [model]="addModelsBtn">
          </t-splitButton>
          <button tButton class="ui-button-primary" aas [uri]="aasUriObj.other" [act]="aasUriObj.other + '/edit/new'"
            icon="iconfont icon-plus" *ngIf="behaviorDev !== 'modelsys'" [label]="'添加模型'"
            [style]="{ 'margin-right': '16px', width: 'auto' }" (click)="toEdit(behaviorDev)"></button>
        </li>
        <li *ngIf="selectedListCheck && selectedListCheck.length === 0">
          <button tButton class="ui-button-primary" icon="iconfont icon-play3" [label]="'全部启用'"
            [style]="{ 'margin-right': '16px', width: 'auto' }" (click)="editEDModelBath('open')"
            [disabled]="bathBP.play.isLoading"></button>
        </li>
        <li *ngIf="selectedListCheck && selectedListCheck.length === 0">
          <button tButton class="ui-button-primary" icon="iconfont icon-stop2" [label]="'全部停用'"
            [style]="{ 'margin-right': '16px', width: 'auto' }" (click)="editEDModelBath('close')"
            [disabled]="bathBP.stop.isLoading"></button>
        </li>
        <li aas [uri]="aasUriObj.other" act="/wilkes/built-in/delete"
          *ngIf="selectedListCheck && selectedListCheck.length === 0">
          <button tButton class="ui-button-primary" icon="iconfont icon-delete3" label="全部删除"
            [style]="{ 'margin-right': '16px', width: 'auto' }" (click)="delDModelBath('all')"></button>
        </li>
        <li *ngIf="selectedListCheck && selectedListCheck.length > 0">
          <button tButton class="ui-button-primary" icon="iconfont icon-play3" [label]="'批量启用'"
            [style]="{ 'margin-right': '16px', width: 'auto' }"
            [disabled]="!bathBP.play.status || bathBP.play.isLoading" (click)="editEDModelBath()"></button>
        </li>
        <li *ngIf="selectedListCheck && selectedListCheck.length > 0">
          <button tButton class="ui-button-primary" icon="iconfont icon-stop2" [label]="'批量停用'"
            [style]="{ 'margin-right': '16px', width: 'auto' }"
            [disabled]="!bathBP.stop.status || bathBP.stop.isLoading" (click)="editEDModelBath()"></button>
        </li>
        <li aas [uri]="aasUriObj.other" act="/wilkes/built-in/delete"
          *ngIf="selectedListCheck && selectedListCheck.length > 0">
          <button tButton class="ui-button-primary" icon="iconfont icon-delete3" label="批量删除"
            [style]="{ 'margin-right': '16px', width: 'auto' }"
            [disabled]="selectedListCheck?.length < 1 || bathBP.delLoading" (click)="delDModelBath()"></button>
        </li>
        <li aas [uri]="aasUriObj.other" act="/wilkes/import">
          <button tButton class="ui-button-primary" icon="iconfont icon-import-one" label="批量导入"
            [style]="{ 'margin-right': '16px', width: 'auto' }" [disabled]="modelsImports.isclick"
            (click)="importModels()"></button>
        </li>
        <li aas [uri]="aasUriObj.other" act="/wilkes/export">
          <button tButton class="ui-button-primary" icon="iconfont icon-export-one" label="批量导出"
            [style]="{ 'margin-right': '16px', width: 'auto' }" (click)="exportsModel()"></button>
        </li>
      </ul>
    </div>
    <div class="mm-r-body mid-rb app-rules-con" style="min-width: unset !important">
      <t-table styleClass="modelsys-table" [data]="modelLists" [noData]="true" [noDataType]="'noData'">
        <ng-template tTemplate="header">
          <tr class="m-t-header">
            <th style="width: 30px">
              <t-checkbox [binary]="true" [(ngModel)]="allSelected" (onChange)="setSelectedBoole(allSelected)">
              </t-checkbox>
            </th>
            <th *ngFor="let item of tableHeader" [ngClass]="{ sortHeader: item.sortType !== '' }"
              [ngStyle]="{ width: item.width }" (click)="sortTable(item)">
              <span style="display: inline-block" [ngStyle]="{ marginLeft: item.header == '名称' ? '20px' : 0 }">{{
                item.header }}</span>
              <span style="display: inline-block" *ngIf="item.field != 'lastTriggerTime'">
                <i class="icon-tabicon" *ngIf="item.sortType === ''"></i>
                <i class="icon-tabup" *ngIf="item.sortType === 'ASC'"></i>
                <i class="icon-tabdown" *ngIf="item.sortType === 'DESC'"></i>
              </span>
            </th>
            <th style="width: 130px">操作</th>
          </tr>
        </ng-template>
        <ng-template tTemplate="body" let-model>
          <tr>
            <td style="width: 30px">
              <t-checkbox [binary]="true" [(ngModel)]="model.selected" (onChange)="setSelectedRadio($event, model)">
              </t-checkbox>
            </td>
            <td style="width: 100px">
              <ng-container *ngIf="
                  modelType[model.modelType] === '关联分析' &&
                  model.source === '内置'
                ">
                <a class="a-hover" aas [uri]="aasUriObj.other" [act]="
                    '/wilkes/models/' + model.modelType + '/built-in/edit/*'
                  " dpt="disable" (click)="toBuiltInEdit(model.modelType, model.no)">{{ model.no }}</a>
              </ng-container>
              <ng-container *ngIf="
                  modelType[model.modelType] !== '关联分析' ||
                  model.source !== '内置'
                ">
                <a class="a-hover" aas [uri]="aasUriObj.other" [act]="'/wilkes/models/' + model.modelType + '/edit/*'"
                  dpt="disable" (click)="toEdit(model.modelType, model.no)">{{ model.no }}</a>
              </ng-container>
            </td>
            <td class="td-name-com" style="width: 16%">
              <i class="tage-item icon-star-half file-name" *ngIf="model.isTag" title="已定制"></i>
              <ng-container *ngIf="
                  modelType[model.modelType] === '关联分析' &&
                  model.source === '内置'
                ">
                <a class="a-hover has-tag" aas [uri]="aasUriObj.other" [act]="
                    '/wilkes/models/' + model.modelType + '/built-in/edit/*'
                  " dpt="disable" (click)="toBuiltInEdit(model.modelType, model.no)" tTooltip="{{ model.name }}">
                  {{ model.name }}
                </a>
              </ng-container>
              <ng-container *ngIf="
                  modelType[model.modelType] !== '关联分析' ||
                  model.source !== '内置'
                ">
                <a class="a-hover has-tag" aas [uri]="aasUriObj.other"
                  [act]="'/wilkes/models/' + model.modelType + '/edit/*'" dpt="disable"
                  (click)="toEdit(model.modelType, model.no)" tTooltip="{{ model.name }}">
                  {{ model.name }}
                </a>
              </ng-container>
            </td>
            <td style="width: 7%">{{ model.source }}</td>
            <td style="width: 7%" class="app-rules-con" tTooltip="{{ model.abnormal || '' }}" tooltipPosition="bottom">
              <t-switch [(ngModel)]="model.status" [isLoading]="model.isLoading" [disabled]="!!model.abnormal"
                (change)="editEDModel($event, [model.no], model)"></t-switch>
            </td>
            <td style="width: 8%" *ngIf="behaviorDev == 'modelsys'">
              {{ modelType[model.modelType] }}
            </td>
            <td style="width: 7%">{{ model.creator }}</td>
            <td style="width: 9%; min-width: 108px">
              {{ model.lastTriggerTime }}
            </td>
            <td style="width: 12%">{{ model.createTime }}</td>
            <td style="width: 12%">{{ model.lastUpdatedTime }}</td>
            <td style="width: 130px">
              <a class="operation-img" aas [uri]="aasUriObj.other" act="/wilkes/build-in/copy" *ngIf="
                  modelType[model.modelType] !== '深度分析' &&
                  model.source !== '内置'
                ">
                <i class="icon-copy-one" (click)="creatCopy(model.no)" tTooltip="创建副本"></i>
              </a>

              <!-- 关联分析内置模型的编辑和设置 -->
              <ng-container *ngIf="
                  modelType[model.modelType] === '关联分析' &&
                  model.source === '内置'
                ">
                <a class="operation-img" aas [uri]="aasUriObj.other" [act]="
                    '/wilkes/models/' + model.modelType + '/built-in/edit/*'
                  ">
                  <i class="icon-edit" (click)="toBuiltInEdit(model.modelType, model.no)" tTooltip="编辑"></i>
                </a>
                <a class="operation-img" aas [uri]="aasUriObj.other" [act]="
                    '/wilkes/models/' + model.modelType + '/built-in/setting/*'
                  ">
                  <i class="icon-settings" (click)="toBuiltInEdit(model.modelType, model.no, true)" tTooltip="设置"></i>
                </a>
              </ng-container>

              <!-- 自定义和非关联分析模型编辑 -->
              <ng-container *ngIf="
                  modelType[model.modelType] !== '关联分析' ||
                  model.source !== '内置'
                ">
                <a class="operation-img" aas [uri]="aasUriObj.other"
                  [act]="'/wilkes/models/' + model.modelType + '/edit/*'">
                  <i class="icon-edit" (click)="toBuiltInEdit(model.modelType, model.no)" tTooltip="编辑"></i>
                </a>
              </ng-container>

              <a *ngIf="model.source !== '内置'" tTooltip="转内置" aas [uri]="aasUriObj.other" act="/wilkes/to-built-in"
                class="operation-img" (click)="modelTotoBuilt(model)">
                <i class="icon-change"></i>
              </a>
              <a *ngIf="model.source !== '内置'" class="operation-img" tTooltip="删除" (click)="delModel(model.no)">
                <i class="icon-delete"></i>
              </a>
              <a *ngIf="model.source == '内置'" class="operation-img" tTooltip="删除" aas [uri]="aasUriObj.other"
                act="/wilkes/built-in/delete" (click)="delModel(model.no)">
                <i class="icon-delete"></i>
              </a>
            </td>
          </tr>
        </ng-template>
      </t-table>
    </div>
    <div class="mm-r-page" *ngIf="modelLists.length > 0">
      <t-paginator [rows]="searchConditions.parameter.pageSize" [totalRecords]="searchConditions.total"
        (onPageChange)="onPageChange($event)" [displayPages]="true" [rowsPerPageOptions]="[20, 30, 50]"
        [jumpToPage]="true" #paginator></t-paginator>
    </div>
    <p style="
        position: absolute;
        bottom: 10px;
        left: 20px;
        height: 30px;
        line-height: 30px;
      " *ngIf="modelLists.length > 0">
      共{{ searchConditions.total }}个模型，已选择{{
      selectedListCheck.length
      }}项
    </p>
  </div>
</div>
<!--// 模型导入-->
<t-dialog styleClass="dialog-upload-modelsys" [positionTop]="100" header="导入模型" [(visible)]="modelsImports.show"
  [zindex]="1000000" (onShow)="initDialog()" [style]="{ width: '70%' }" (onHide)="fileUpClose()"
  [closable]="!(uploadData.information && !uploadData.progress)">
  <div class="disk-import" id="disk-import" [ngStyle]="{ 'max-height': uploadData.maxHeight + 'px' }">
    <div class="upload">
      <button [disabled]="uploadData.progress" tButton type="button" label="选择文件"
        class="iconfont icon-plus5 ui-button-primary" style="vertical-align: unset" (click)="uploadFile()"></button>
      <t-fileUpload #uploadDom [style]="{ zIndex: uploadData.progress ? '-20' : '0' }" class="modelsys-upload-file"
        styleClass="modelsys-upload" mode="basic" chooseLabel="选择文件" name="file" method="POST"
        [url]="runHttpService.importFile" [disabled]="uploadData.progress" [maxFileSize]="10485760"
        invalidFileSizeMessageDetail="最大上传大小为10M" (onSelect)="selectedFile($event)" (onUpload)="successFile($event)"
        (onBeforeUpload)="beginUpload()" (onError)="uploadError($event)">
      </t-fileUpload>
      <p class="upload-text" [ngStyle]="{ color: uploadData.disabled ? '#bfbfbf' : '#333' }">
        {{ uploadData.text }}
        <i *ngIf="!uploadData.disabled" class="icon-delete3" (click)="clearFile()"></i>
      </p>
      <button [disabled]="uploadData.progress" tButton type="button" label="上传"
        class="ui-width iconfont icon-upload1 ui-button-primary" style="margin-right: 20px"
        (click)="uploadFile()"></button>
    </div>
    <div style="height: 30px; margin: 10px 0" *ngIf="uploadData.progress">
      <p>正在上传解析中..........</p>
      <t-progressBar mode="indeterminate" [style]="{ height: '6px' }"></t-progressBar>
    </div>
    <div style="display: none" [ngStyle]="{
        display:
          uploadData.information && !uploadData.progress ? 'block' : 'none'
      }">
      <div class="metadata" *ngIf="uploadData.information?.metadata">
        <span *ngIf="uploadData.information?.metadata.creator">模型包创建人：{{ uploadData.information?.metadata.creator }}
          ，&nbsp;
          &nbsp;</span>
        创建时间：{{ uploadData.information?.metadata.createTime }} ，&nbsp;
        &nbsp; 版本号：{{ uploadData.information?.metadata.version }}
      </div>
      <div class="alarm-tip">
        <i class="icon-tps"></i>
        <span>选择覆盖时，定制参数也会被覆盖</span>
      </div>
      <div class="upload-tip">
        共发现模型{{ uploadData.information?.total }}个（内置模型{{
        uploadData.information?.builtIn
        }}个，自定义模型{{ uploadData.information?.unBuiltIn }}个，示例模型{{
        uploadData.information?.sample
        }}个）
        <span *ngIf="uploadData.information?.content.length > 0">，编号/名称冲突{{ uploadData.information?.conflict }}个，损坏{{
          uploadData.information?.damage
          }}个，请选择处理方式：</span>
      </div>
      <div class="upload-table" *ngIf="uploadData.information?.content.length > 0">
        <t-table [data]="uploadData.information?.content" [scrollable]="true" [style]="{ width: '100%' }"
          [scrollHeight]="uploadData.tableHeightToScroll">
          <ng-template tTemplate="header">
            <tr>
              <th style="width: 80px">编号</th>
              <th>模型名称</th>
              <th style="width: 100px">模型类型</th>
              <th style="width: 80px">来源</th>
              <th style="width: 80px">原因</th>
              <th>备注</th>
              <th style="width: 80px">
                <t-checkbox [(ngModel)]="selectAll.cover" [binary]="true" [disabled]="selectAll.electionCover"
                  (onChange)="changeSelectAll($event, 'cover')">
                </t-checkbox>
                覆盖
              </th>
              <th style="width: 80px">
                <t-checkbox [(ngModel)]="selectAll.Rename" [binary]="true" [disabled]="selectAll.electionRename"
                  (onChange)="changeSelectAll($event, 'Rename')">
                </t-checkbox>
                新建
              </th>
              <th style="width: 80px">
                <t-checkbox [(ngModel)]="selectAll.skip" [binary]="true" [disabled]="selectAll.electionSkip"
                  (onChange)="changeSelectAll($event, 'skip')">
                </t-checkbox>
                跳过
              </th>
            </tr>
          </ng-template>
          <ng-template tTemplate="body" let-list>
            <tr>
              <td style="width: 80px" class="text-overflow">{{ list.no }}</td>
              <td class="text-overflow" [tooltipZIndex]="'10000000'" tTooltip="{{ list.name }}"
                tooltipPosition="bottom">
                {{ list.name }}
              </td>
              <td class="text-overflow" [tooltipZIndex]="'10000000'" tTooltip="{{ modelType[list.modelType] }}"
                tooltipPosition="bottom" style="width: 100px">
                {{ modelType[list.modelType] }}
              </td>
              <td class="text-overflow" [tooltipZIndex]="'10000000'" tTooltip="{{ list.source }}"
                tooltipPosition="bottom" style="width: 80px">
                {{ list.source }}
              </td>
              <td class="text-overflow" [tooltipZIndex]="'10000000'" tTooltip="{{ modelUploadType[list.causeType] }}"
                tooltipPosition="bottom" style="width: 80px">
                {{ modelUploadType[list.causeType] }}
              </td>
              <td class="text-overflow" [tooltipZIndex]="'10000000'" tTooltip="{{ list.abnormal }}"
                tooltipPosition="bottom">
                {{ list.abnormal }}
              </td>
              <td style="width: 80px">
                <t-checkbox [(ngModel)]="list.overwrite" [binary]="true" [disabled]="list.causeType === 'defeated'"
                  (onChange)="
                    changeAction(
                      $event,
                      list,
                      'overwrite',
                      uploadData.information?.content
                    )
                  "></t-checkbox>
              </td>
              <td style="width: 80px">
                <t-checkbox [(ngModel)]="list.againno" [binary]="true" (onChange)="
                    changeAction(
                      $event,
                      list,
                      'againno',
                      uploadData.information?.content
                    )
                  "></t-checkbox>
              </td>
              <td style="width: 80px">
                <t-checkbox [(ngModel)]="list.overskip" [binary]="true" (onChange)="
                    changeAction(
                      $event,
                      list,
                      'overskip',
                      uploadData.information?.content
                    )
                  "></t-checkbox>
              </td>
            </tr>
          </ng-template>
        </t-table>
      </div>
      <div class="upload-actions">
        <span class="checkbox">
          <t-checkbox label="是否保留状态" [(ngModel)]="keepstatus" [binary]="true"></t-checkbox>
        </span>
        <button tButton type="button" label="取消导入" class="ui-width" style="margin-right: 20px"
          (click)="cacleUpload()"></button>
        <button tButton type="button" label="确认导入" class="ui-width ui-button-primary"
          (click)="getFileUploadToAction()"></button>
      </div>
    </div>
  </div>
  <t-loading *ngIf="loading" [zindex]="1000000" [position]="'relative'"></t-loading>
</t-dialog>
<t-confirmDialog [zindex]="100000" [key]="modelType.type"></t-confirmDialog>
<!--模型分类新增、编辑-->
<t-dialog header="新增模型分类" [(visible)]="editModelClass.addOff" [zindex]="1000000" (onShow)="
    editModelClass.isEdit
      ? (editModelClass.modelName = editModelClass.toEditModel.name)
      : (editModelClass.modelName = '')
  ">
  <div class="add-model-class">
    <p class="a-m-c-d">模型分类名称：</p>
    <div class="a-m-c-d" style="padding-left: 6px">
      <input type="text" size="30" tInputText [(ngModel)]="editModelClass.modelName" placeholder="请填写模型分类名称" />
    </div>
    <div class="a-m-c-f">
      <button tButton type="button" label="确定" class="ui-width ui-button-primary"
        [disabled]="editModelClass.modelName === ''" style="margin-right: 20px"
        (click)="addToModelClass('go')"></button>
      <button tButton type="button" label="取消" class="ui-width" (click)="addToModelClass('cancel')"></button>
    </div>
  </div>
</t-dialog>