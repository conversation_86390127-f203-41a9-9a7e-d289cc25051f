package com.topsec.wilkes.common.dto;

import lombok.Data;

/**
 * <AUTHOR> (<EMAIL>)
 */
@Data
public class Event {
  private String id;
  private String name;

  public Event() {
  }

  public Event(String id, String name) {
    this.id = id;
    this.name = name;
  }

  @Override
  public int hashCode() {
    return this.name.hashCode();
  }

  @Override
  public boolean equals(Object obj) {
    if (obj == null)
      return false;
    if (this == obj)
      return true;
    if (obj instanceof Event) {
      Event event = (Event) obj;
      return event.id.equals(this.id);
    }
    return false;
  }
}
