

<t-loading  [position]="'absolute'" [visible]="isLoading"
[ngStyle]="{'zIndex': isLoading ? '2' : '-2'}">
</t-loading>

<div class="tui-exp">
  <div class="tui-exp-search" *ngIf="model.subjectField">
    <div class="tui-exp-lione clear clearfix">
      <div class="tui-exp-list clearfix clear">
        <span class="tui-exp-lable">行为主体</span>
        <span class="tui-exp-input">
          <!-- <t-dropdown  [options]="subjectList" [(ngModel)]="subjectValue" [filter]="true" [loading]="loadingSubject" [numberMoreLayout]="false"
            [ischeckbox]="false" placeholder="请选择行为主体" [style]="{'width':'170px'}"
            (onChange)="selectChange($event, 'subjectValue')" [showClear]="true"></t-dropdown> -->
            <app-w-dropdown placeholder="请选择行为主体" [(ngModel)]="subjectValue" [options]="subjectList" (onChange)="selectChange($event, 'subjectValue')"></app-w-dropdown>
        </span>
      </div>
      <div class="tui-exp-list clearfix clear" *ngIf="model.objectField">
        <span class="tui-exp-lable">行为客体</span>
        <span class="tui-exp-input">
          <!-- <t-dropdown [options]="objectList" [(ngModel)]="objectValue" [filter]="true" [loading]="loadingObject" [numberMoreLayout]="false"
            [style]="{'width':'170px'}" placeholder="请选择行为客体" (onChange)="selectChange($event, 'objectValue')"
            [showClear]="true"></t-dropdown> -->
            <app-w-dropdown placeholder="请选择行为客体" [(ngModel)]="objectValue" [options]="objectList" (onChange)="selectChange($event, 'objectValue')"></app-w-dropdown>
        </span>
      </div>

    </div>
  </div>
  <!--表格-->
  <div class="tui-exp-table clear clearfix">
    <div class="body" #chartBox>
    </div>
    <div class="center">
      <span class="now-con"
        >当前画像产生时间：{{ currentTime }}
        <i
          class="icon-tps icon-con"
          tTooltip="以日志源的事件时间为准"
          tooltipPosition="bottom"
        ></i
      ></span>
      <span class="ml-10" *ngIf="forecastTime != ''"
        >预计下次检测时间：{{ forecastTime }}
        <i
          class="icon-tps icon-con"
          tTooltip="以系统时间为准"
          tooltipPosition="bottom"
        ></i
      ></span>
    </div>
    <ng-container *ngIf="!isLoading && isNodata">
      <div class="no-data">
        <div class="ui-data ng-star-inserted">
          <div class="ui-data-contentimg"></div>
          <div class="ui-data-text">暂无数据</div>
          <div class="ui-data-text">预计画像首次生成时间：{{ generateTime }}</div>
        </div>
      </div>
    </ng-container>
  </div>
</div>
