package com.topsec.wilkes.behaviour.controller;

import com.topsec.wilkes.behaviour.dto.definition.BehaviourDefinition;
import com.topsec.wilkes.behaviour.dto.definition.DetectionAlgorithm;
import com.topsec.wilkes.behaviour.service.BehaviourAlgorithmService;

import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/behaviour/algorithm")
public class DetectionAlgorithmController {
  @Autowired
  BehaviourAlgorithmService behaviourAlgorithmService;

  @ApiOperation("获取所有算法")
  @RequestMapping(method = RequestMethod.GET)
  @ResponseBody
  public List<DetectionAlgorithm> getAlgorithms() {
    return behaviourAlgorithmService.getAlgorithms();
  }

  @ApiOperation("根据行为定义获取算法")
  @RequestMapping(method = RequestMethod.POST)
  @ResponseBody
  public List<DetectionAlgorithm> getAlgorithms(@RequestBody BehaviourDefinition behaviourDefinition) {
    return behaviourAlgorithmService.getAlgorithms(behaviourDefinition);
  }

  @ApiOperation("获取算法基础参数")
  @RequestMapping(value = "/parameter", method = RequestMethod.GET)
  @ResponseBody
  public List<Object> getAlgorithmParameter() {
    return behaviourAlgorithmService.getAlgorithmParameter();
  }
}
