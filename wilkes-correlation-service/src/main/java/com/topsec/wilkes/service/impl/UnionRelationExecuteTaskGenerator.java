package com.topsec.wilkes.service.impl;

import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.dto.task.ExecuteTask;
import com.topsec.wilkes.dto.task.ExecuteTaskType;
import com.topsec.wilkes.dto.task.StageType;
import com.topsec.wilkes.dto.task.TaskType;
import com.topsec.wilkes.task.ExecuteTaskGenerator;
import com.topsec.wilkes.transform.ProcessGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class UnionRelationExecuteTaskGenerator implements ExecuteTaskGenerator {

    @Autowired
    private ProcessGenerator processGenerator;

    @Override
    public List<ExecuteTask> getExecuteTask(Model model) throws Exception {
        ExecuteTask executeTask = new ExecuteTask();
        executeTask.setTaskType(TaskType.PROCESS);
        executeTask.setStageType(StageType.UNION);
        executeTask.setExecuteTaskType(ExecuteTaskType.STREAM);
        executeTask.setProcess(processGenerator.getCompleteProcess(model.getContent(), model, new HashMap<>()));
        executeTask.setStatus(model.getStatus());
        executeTask.setModelId(model.getId());
        return Collections.singletonList(executeTask);
    }

    @Override
    public ModelType accept() {
        return ModelType.UNION;
    }

}
