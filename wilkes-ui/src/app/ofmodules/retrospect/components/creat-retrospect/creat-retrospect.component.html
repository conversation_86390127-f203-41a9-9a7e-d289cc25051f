<div class="creat-recall"></div>
<t-dialog [(visible)]="display" [positionTop]="200">
  <t-header> 新建{{ title }}任务 </t-header>
  <ul class="creat-content">
    <li style="display: flex; margin-bottom: 15px; align-items: center" *ngIf="modelInfo.isSearch">
      <label style="width: auto">编号/名称</label>
      <input type="text" tInputText placeholder="请输入编号/名称" style="width: 200px"
        [(ngModel)]="searchConditions.parameter.name" (ngModelChange)="nameChange()" />
    </li>
    <li style="width: 100%">
      <t-table [columns]="tableHeader" [data]="models" [(selection)]="selectedRows" [scrollable]="true"
        scrollHeight="200px" [noData]="true" [noDataType]="'noData'" (onRowSelect)="tableSelect($event)"
        (onRowUnselect)="tableSelect($event)">
        <ng-template tTemplate="header" let-columns>
          <tr>
            <th style="width: 45px" *ngIf="modelInfo.type == 'multiple'"></th>
            <th *ngFor="let col of columns" [ngStyle]="{ width: col.width ? col.width : 'auto' }">
              <p>
                {{ col.header }}
              </p>
            </th>
          </tr>
        </ng-template>
        <ng-template tTemplate="body" let-model let-columns="columns">
          <tr>
            <td style="width: 45px" *ngIf="modelInfo.type == 'multiple'">
              <t-tableRadioButton [value]="model" [disabled]="model.source === '示例' || model.isSaas">
              </t-tableRadioButton>
            </td>
            <td *ngFor="let col of columns; let i = index" [ngStyle]="{ width: col.width ? col.width : 'auto' }">
              <p *ngIf="col.field == 'modelType'">
                {{ modelType1[model[col.field]] }}
              </p>
              <p *ngIf="col.field != 'modelType'">{{ model[col.field] }}</p>
            </td>
          </tr>
        </ng-template>
      </t-table>
      <div class="mm-r-page" *ngIf="
          searchConditions.total > searchConditions.parameter.pageSize &&
          modelInfo.type == 'multiple'
        ">
        <t-paginator [rows]="searchConditions.parameter.pageSize" [totalRecords]="searchConditions.total"
          (onPageChange)="onPageChange($event)" [displayPages]="true" [jumpToPage]="true" #paginator></t-paginator>
      </div>
    </li>
    <li class="parameter">
      <p class="title">{{ title }}时间
        <i class="icon-tps icon-con" tTooltip="仅支持7天内的数据" tooltipPosition="bottom"></i>
      </p>

      <t-double-calendar dateFormat="yyyy-MM-dd HH:mm:ss" [minDate]="minDate" [maxDate]="maxDate" [appendTo]="'body'"
        [shortcutKeys]="shortcutKeys" (onOkClick)="getRangeTime($event)" [initEmpty]="true"></t-double-calendar>
    </li>
    <!-- <li class="parameter">
      <p class="title" style="line-height: 34px;">是否触发响应</p>
      <t-checkbox binary="true" [(ngModel)]="parameter.triggerResponse"></t-checkbox>
    </li> -->
  </ul>
  <t-footer>
    <button class="ui-button-primary" type="button" tButton (click)="save()" label="确定"></button>
    <button type="button" tButton (click)="clear()" label="取消" class="ui-button-secondary"></button>
  </t-footer>
</t-dialog>