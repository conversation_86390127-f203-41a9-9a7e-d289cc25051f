package com.topsec.wilkes.service.impl;

import static com.topsec.common.utils.ToolUtils.OBJECT_MAPPER;

import com.topsec.common.utils.ToolUtils;
import com.topsec.dm.domain.Filter;
import com.topsec.dm.service.AssetService;
import com.topsec.dm.service.FilterService;
import com.topsec.dm.service.IntelligenceService;
import com.topsec.minsky.domain.Expression;
import com.topsec.minsky.domain.Operator;
import com.topsec.minsky.domain.util.ExpressionUtil;
import com.topsec.wilkes.service.FilterOperatorParamService;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.InvalidParameterException;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class FilterOperatorParamServiceImpl implements FilterOperatorParamService {

    @Autowired
    private FilterService filterService;

    @Autowired
    private IntelligenceService intelligenceService;
    @Autowired
    private AssetService AssetService;

    @Override
    public Map<String, List<Filter>> getParams(String modelType, String modelId) {
        Map<String, List<Filter>> filters = filterService.getFilters(modelType, modelId);
        for (List<Filter> value : filters.values()) {
            for (Filter filter : value) {
                filter.setDsl(transform(filter.getDsl()));
            }
        }
        return filters;
    }

    public String transform(String dsl) {
        List<Operator> operatorList;
        String newDsl = "";
        Map<String, String> intelligenceMap = intelligenceService.getIntelligenceDataUrl();
        try {
            operatorList = Arrays.asList(ToolUtils.OBJECT_MAPPER.readValue(dsl, Operator[].class));
            for (Operator operator : operatorList) {
                Map<String, Object> paramsMap = operator.getParams();
                String operatorId = operator.getId();
                switch (operatorId) {
                    case "field-filter":
                        if (paramsMap.get("predication") == null || Strings.isNullOrEmpty(paramsMap.get("predication").toString())) {
                            Expression expression = OBJECT_MAPPER.convertValue(paramsMap.get("expression"), Expression.class);
                            String sql = ExpressionUtil.generateSQL(expression,"field-filter");
                            paramsMap.put("predication", sql);
                            paramsMap.remove("expression");
                        }
                        break;
                    case "knowledge-base-filter":
                        paramsMap.put("predication", paramsMap.get("expression"));
                        paramsMap.remove("expression");
                        String dataUrl = String.format("%s/knowledgeBases/%s/searchDatas", "tib", paramsMap.get("knowledge_base_url"));
                        dataUrl = dataUrl + "," + String.format("%s/knowledgeBasesResource/%s/searchAdvanced", "tib", paramsMap.get("knowledge_base_url"));
                        paramsMap.replace("knowledge_base_url", dataUrl);
                        break;
                    case "threat-base-filter":
                        if (!intelligenceMap.containsKey(paramsMap.get("knowledge_base_url"))) {
                            throw new InvalidParameterException(String.format("%s威胁情报不存在!", paramsMap.get("knowledge_base_url")));
                        }
                        paramsMap.put("predication", paramsMap.get("expression"));
                        paramsMap.remove("expression");
                        paramsMap.replace("knowledge_base_url", intelligenceMap.get(paramsMap.get("knowledge_base_url")));
                        break;
                    case "security-domain-filter":
                        if (paramsMap.containsKey("security_domain_prediction")) {
                            Map<String, List<String>> domain_prediction = OBJECT_MAPPER.convertValue(paramsMap.get("security_domain_prediction"), new TypeReference<Map<String, List<String>>>() {
                            });
                            for (String key : domain_prediction.keySet()) {
                                List<String> newValue = new LinkedList<>();
                                for (String id : domain_prediction.get(key)) {
                                    String completeName = AssetService.getCompleteName(id);
                                    if (!Strings.isNullOrEmpty(completeName)) {
                                        newValue.add(completeName);
                                    }
                                }
                                domain_prediction.put(key, newValue);
                            }
                            paramsMap.put("security_domain_prediction", domain_prediction);
                        }
                        break;
                    default:
                        break;
                }
            }
            newDsl = ToolUtils.OBJECT_MAPPER.writeValueAsString(operatorList);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return newDsl;
    }
}
