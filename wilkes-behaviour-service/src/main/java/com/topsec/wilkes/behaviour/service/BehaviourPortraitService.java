package com.topsec.wilkes.behaviour.service;

import com.topsec.wilkes.behaviour.dto.portrait.PortraitData;
import com.topsec.wilkes.behaviour.dto.portrait.PortraitInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface BehaviourPortraitService {
  PortraitInfo getPortraitInfo(String no);

  List<Object> getList(String no, String queryField, Map<String, Object> conditionMap);

  PortraitData getPortraitData(String no, Map<String, Object> conditionMap, String from, String to, int pageNo, int pageSize);

  String accept();
}
