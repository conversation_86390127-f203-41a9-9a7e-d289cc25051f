package com.topsec.wilkes.repository.entity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "union_process")
@AllArgsConstructor
@NoArgsConstructor
public class UnionProcessEntity {

  @Id
  private String parentProcessId;

  @Column
  private String downPlatformsMap;
}

