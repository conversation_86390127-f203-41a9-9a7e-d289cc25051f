<div class="topic-list">
  <div class="top_topic" [ngClass]="{'blue-topic': getSkin=='blue'}">
    <div class="topic_left">
      <div class="title">
        <span>智能分析</span>
      </div>

      <div class="introduce">
        <p>
          系统搭载了三种适用于安全领域的分析引擎：关联分析引擎、安全异常行为分析引擎、深度分析引擎，形成纵深分析检测网络，通过不同的角度、不同的方法检测、发现组织内部威胁，让入侵者无处遁形。
        </p>
      </div>
      <div class="detail-num-con">
        <div class="analysis relation" style="margin: 0;">
          <span class="relationIcon">
            <span class="svg_dev">
              <svg t="1592190787933" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="13286" xmlns:xlink="http://www.w3.org/1999/xlink" width="25"
                height="25">
                <defs>
                  <style type="text/css"></style>
                </defs>
                <path
                  d="M580.096 284.16H423.936v79.872h156.16c44.032 0 79.872 35.84 79.872 79.872v399.872c0 44.032-35.84 79.872-79.872 79.872H180.224c-44.032 0-79.872-35.84-79.872-79.872V443.904c0-44.032 35.84-79.872 79.872-79.872h103.936V284.16H180.224c-88.576 0-160.256 71.68-160.256 160.256v399.872c0 88.576 71.68 160.256 160.256 160.256h399.872c88.576 0 160.256-71.68 160.256-160.256V443.904c-0.512-88.064-72.192-159.744-160.256-159.744z"
                  p-id="13287" fill="#fff"></path>
                <path
                  d="M843.776 19.968H443.904c-88.576 0-160.256 71.68-160.256 160.256v399.872c0 88.576 71.68 160.256 160.256 160.256h156.16V660.48H443.904c-44.032 0-79.872-35.84-79.872-79.872V180.224c0-44.032 35.84-79.872 79.872-79.872h399.872c44.032 0 79.872 35.84 79.872 79.872v399.872c0 44.032-35.84 79.872-79.872 79.872h-103.936v79.872h103.936c88.576 0 160.256-71.68 160.256-160.256v-399.36c0-88.576-71.68-160.256-160.256-160.256z"
                  p-id="13288" fill="#fff"></path>
              </svg>
            </span>
          </span>
          <span class="name">关联分析</span>
          <span class="number">{{getListData['CORRELATION']}}</span>
        </div>
        <div class="analysis behavior">
          <span class="behaviorIcon">
            <span class="svg_dev">
              <svg t="1592190796840" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="14037" xmlns:xlink="http://www.w3.org/1999/xlink" width="28"
                height="28">
                <defs>
                  <style type="text/css"></style>
                </defs>
                <path
                  d="M510.03 65.245L64.061 405.937l170.345 551.247h551.247L956 405.937 510.03 65.245z m239.072 842.387H306.13l155.703-99.12h214.181l107.332-347.397-272.619-208.278-0.693-0.529-3.402 2.602-269.909 206.205 85.94 278.156-57.199 150.589-143.856-465.532L510.03 127.602l388.42 296.726-149.348 483.304z m-56.367-321.107L569.04 359.745 725.8 479.51l-33.065 107.015zM349.342 808.512l25.681-67.623L516.81 367.527l140.575 257.728-209.992 133.706-77.825 49.552-26.676 16.985 6.451-16.986z m1.918-144.537L294.268 479.51l178.995-136.75L351.26 663.974z m312.631 15.909L639.46 758.96h-99.788l124.22-79.077z"
                  p-id="14038" fill="#fff"></path>
              </svg>
            </span>
          </span>
          <span class="name">安全异常行为分析</span>
          <span class="number">{{getListData['BEHAVIOUR']}}</span>
        </div>
        <div class="analysis AI">
          <span class="AIIcon">
            <span class="svg_dev">
              <svg t="1592191630142" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="26428" xmlns:xlink="http://www.w3.org/1999/xlink" width="25"
                height="25">
                <defs>
                  <style type="text/css"></style>
                </defs>
                <path
                  d="M202.95907555 972.13326222a33.35054222 33.35054222 0 0 1-33.49617777-33.49617778v-233.81788444a3.85934222 3.85934222 0 0 0-3.34961778-3.78652445c-38.73905778-5.09724445-107.33340445-19.51516445-132.45553778-60.14748444-12.23338667-19.87925333-13.32565333-43.76348445-3.05834667-67.06517333 26.94257778-60.65720889 71.87114667-104.42069333 96.11946667-125.02812445a3.78652445 3.78652445 0 0 0 1.31072-2.40298667C136.76771555 375.75566222 198.66282666 23.60888888 552.70286222 23.60888888c192.74865778 0 348.57870222 87.30851555 416.88177778 233.30816 72.67214222 155.83004445 30.00092445 349.08842667-117.23662222 530.40469334a3.49525333 3.49525333 0 0 0-0.72817778 2.33016889v143.15975111a33.64181333 33.64181333 0 0 1-67.21080889 0v-156.70385778c0-7.93713778 2.76707555-15.58300445 7.79150222-21.62688 136.89742222-163.54872889 179.42300445-334.74332445 116.65408-469.31057778-57.01632-121.89696-190.0544-194.56910222-356.07893333-194.56910222-333.06851555 0-358.40910222 361.17617778-359.42855112 376.46791111a34.00590222 34.00590222 0 0 1-13.76256 25.19495111c-6.18951111 4.80597333-60.07466667 46.89464889-87.52696888 108.71694222a3.85934222 3.85934222 0 0 0 0 2.91271112c6.99050667 16.74808889 64.95345778 30.87473778 112.50346666 33.20490666a33.64181333 33.64181333 0 0 1 32.03982222 33.49617778v230.54108444c0 2.11171555 1.67480889 3.78652445 3.71370667 3.78652445h187.57859556a33.64181333 33.64181333 0 0 1 0 67.13799111H202.95907555v0.07281778zM587.21848888 672.56092444a33.56899555 33.56899555 0 0 1-31.60291555-22.13660444l-21.11715555-61.74947556a3.85934222 3.85934222 0 0 0-3.49525334-2.54862222H420.02887111a3.78652445 3.78652445 0 0 0-3.56807111 2.47580444l-22.35505778 62.2592a33.27772445 33.27772445 0 0 1-42.52558222 18.93262222 33.49617778 33.49617778 0 0 1-20.60743112-41.79740444l115.19772445-320.54385778a33.56899555 33.56899555 0 0 1 63.35146667 0.50972445l109.66357333 320.61667555a33.71463111 33.71463111 0 0 1-32.03982222 43.98193778z m-110.31893333-244.66773333a3.78652445 3.78652445 0 0 0-3.64088889 2.47580444l-29.85528888 83.44917333a3.78652445 3.78652445 0 0 0 3.5680711 5.09724445l58.47267556-0.07281778a3.78652445 3.78652445 0 0 0 3.56807111-5.09724444l-28.54456889-83.37635556a3.64088889 3.64088889 0 0 0-3.64088888-2.47580444z m222.8224 242.19192889a33.05927111 33.05927111 0 0 1-33.49617777-32.40391112V317.93834666c0-17.84035555 15.00046222-32.40391111 33.49617777-32.40391111 18.56853333 0 33.64181333 14.56355555 33.64181333 32.40391111v319.88849778a33.13208889 33.13208889 0 0 1-33.56899555 32.25827556z"
                  fill="#666666" p-id="26429" fill="#fff"></path>
              </svg>
            </span>
          </span>
          <span class="name">深度分析</span>
          <span class="number">{{getListData['AI']}}</span>
        </div>
      </div>
    </div>
    <div class="topic_right">
      <topicaAnimation [imgType]="getSkin"></topicaAnimation>
    </div>
  </div>
  <ul *ngIf="mainLIsts.length > 0">
    <li *ngFor="let item of mainLIsts;let index = index;">
      <div class="li-mask">
        <!-- *ngIf="getSkin === 'blue'" -->
        <ng-container>
          <img src="/wilkes/assets/rules/Network/blue.png">
        </ng-container>
        <!-- <ng-container *ngIf="getSkin !==  'blue'">
          <img src="/wilkes/assets/rules/Network/red.png">
        </ng-container> -->
      </div>
      <div class="t-l-left t-display">
        <img style="width: 84px;"
          src="/wilkes/assets/rules/Network/{{getSkin === 'blue' ? item.icon : item.icon + '-1'}}.png">
      </div>
      <div class="t-l-right t-display">

        <p class="t-l-r-name">{{item.name}}&nbsp;&nbsp;<img style="margin-bottom: 2px"
            src="/wilkes/assets/rules/Network/icon-2.png"></p>
        <p *ngIf="!(item.children&&item.children.length>0)" class="no-data">暂无数据...</p>
        <!-- <p class="t-l-t-desc" [title]="item?.description" tooltipPosition="top" [ellipsis]="item?.description">
          {{item?.description}}</p> -->
        <div class="t-butt" *ngFor="let i of item.children;" [attr.title]="i.name">
          <span class="name">{{i.name}}</span>
          <span>{{i.total}}</span>
        </div>
        <div class="operation">
          <span type="button" *ngIf="showSpecial[item.id]"
            [ngClass]="{'left': getSkin === 'blue', 'right': getSkin !== 'blue'}"
            (click)="toGetRouter(1, item)">专题分析</span>
          <span type="button" [ngClass]="{'left': getSkin === 'blue', 'right': getSkin !== 'blue'}"
            (click)="details(item)">模型列表</span>
        </div>
      </div>
    </li>
  </ul>
  <div *ngIf="noData">
    <t-nodata [noDataType]="noDataList.noDataType" [noDatatext]="noDataList.noDatatext"></t-nodata>
  </div>
</div>