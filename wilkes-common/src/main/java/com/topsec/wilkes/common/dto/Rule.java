package com.topsec.wilkes.common.dto;

import com.topsec.common.utils.ToolUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> (<EMAIL>)
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Slf4j
public class Rule extends RuleBaseInfo implements Cloneable {
  /**
   * 模型ID
   * 是否必填： 否
   * 示例： aa5d32f0-a3bb-4c39-8976-e90372bca9a2
   */
  private String id;

  /**
   * 模型编号
   * 是否必填： 否
   * 示例： 00001
   */
  private String no;

  /**
   * 模型标签
   * 是否必填： 否
   * 示例：
   */
  private List<Tag> tags;

  /**
   * 模型事件
   * 是否必填： 否
   * 示例：
   */
  private List<Event> events;

  /**
   * 模型来源
   * 是否必填： 否
   * 示例：
   */
  private String source;

  /**
   * 模型创建时间
   * 是否必填： 否
   * 示例： 2020-08-08 00:00:00
   */
  private Date createTime;

  /**
   * 模型修改时间
   * 是否必填： 否
   * 示例： 2020-08-08 00:00:00
   */
  private Date lastUpdatedTime;

  /**
   * 模型类型
   * 是否必填： 否
   * 示例： CORRELATION
   */
  private ModelType modelType;

  /**
   * 错误信息
   * 是否必填：否
   * 示例： 知识模型 221852c789739c627e52665332e2ab31 不存在！
   */
  private String abnormal;

  /**
   * 模型触发原因
   *
   *
   */
  private String reason;

  /**
   * 历史：启用、停用
   *
   *
   */
  private History history;
  /**
   * 下级节点
   *
   *
   */
  private List<String> platforms;

  /**
   * 规则ID
   */
  private String ruleId;

  public Rule() {
  }

  @Override
  public Object clone() {
    ObjectMapper objectMapper = ToolUtils.OBJECT_MAPPER;
    try {
      return objectMapper.readValue(objectMapper.writeValueAsString(this), Rule.class);
    } catch (Exception e) {
      log.error("", e);
      return new Rule();
    }
  }

}
