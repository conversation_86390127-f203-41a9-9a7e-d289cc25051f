package wilkes;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.common.dto.Action;
import com.topsec.wilkes.util.IpUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.tomcat.util.net.IPv6Utils;
import org.junit.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Class BaseTests
 * @Description 基础测试类
 * @Auther linlin
 * @Date 2019-03-14
 **/

public class BaseTests {

  @Test
  public void contextLoads() {
    Action ruleAction = null;
    try {
      ruleAction = ToolUtils.OBJECT_MAPPER.readValue("{ \"id\": \"generate-alarm-event\", \"params\": { \"mappings\": [ { \"name\": \"GENERATE_ALARM_EVENT\", \"value\": true }, { \"name\": \"INDEXING_FIELD\", \"value\": null } ], \"reason_mappings\": [] } }", Action.class);
    } catch (JsonProcessingException e) {
      e.printStackTrace();
    }

    System.out.println(ruleAction);
  }

  @Test
  public void testToIpv6(){
    String predication ="2408:8409:1880:59b2:f8dc:0000:0000:81fb";
    String shortIPv6 = IpUtil.getShortIPv6(predication);
    System.out.println(shortIPv6);
  }

  @Test
  public void toIpv6(){
    String predication ="src_ip=' 2408:8409:1880:59b2:f8dc:0000:0000:81fb ' and (src_ip6='2408:0000:0000:56ca:a1dc:e390:306:b376' or (dst_ip6='123' and dst_ip='111'  )) or src_ip=''";
    predication=toIpv6(predication);
    System.out.println(predication);
  }

  public String toIpv6(String value) {
    Pattern patternIpv6 = Pattern.compile("(src_ip|src_ip6|dst_ip|dst_ip6)\\s*(=|<>|!=)\\s*\\'(.*?)\\'+");
    Pattern patternValue = Pattern.compile("\\'(.*?)\\'+");
    Matcher matcher = patternIpv6.matcher(value);
    while (matcher.find()) {
      String group = matcher.group();
      if (StringUtils.isNotEmpty(group)) {
        Matcher matcher1 = patternValue.matcher(group);
        if (matcher1.find()) {
          String matched = matcher1.group().substring(1, matcher1.group().length() - 1).trim();
          if (StringUtils.isNotEmpty(matched) && IpUtil.isValidIpv6Addr(matched)) {
            value = value.replace(matched, IpUtil.getShortIPv6(matched));
          }
        }
      }
    }
    return value;
  }
}
