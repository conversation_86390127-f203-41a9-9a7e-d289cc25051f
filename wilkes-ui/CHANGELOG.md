# [3.1.0](https://********/wilkes/wilkes-project/compare/2.7.0-17...3.1.0) (2023-04-04)


### Bug Fixes

*  修改关联分析算子设置页打开报错和加载等待遮罩和模型列表页tooltip位置 ([4276647](https://********/wilkes/wilkes-project/commits/4276647d4eeafd57bb4592ce03b49598d6123b17))
*  修改滚动条重复问题 ([3c93d38](https://********/wilkes/wilkes-project/commits/3c93d38a8a85e1c00a8913a32a9f6ac565840e0a))
*  修改集成环境跳转检索分析页配置错误 ([f05aae7](https://********/wilkes/wilkes-project/commits/f05aae7eedbb184d10a841a0f23a9b28e9599564))
*  删除调试错误代码 ([4b48d7e](https://********/wilkes/wilkes-project/commits/4b48d7e3ae6adcf03a81eb4e6016bbcb7027d6c3))
*  监控功能变更 ([bfa3959](https://********/wilkes/wilkes-project/commits/bfa3959e082eefc9dfd31f20463e3011c6bb6c9e))
*  行为分析变更为安全异常行为分析，行为分析编辑默认选中安全事件 ([3419310](https://********/wilkes/wilkes-project/commits/3419310e3f780b5d76275d8d8f719011e1b9bd8c))
*  行为分析编辑页个群检测下拉分组支持搜索功能 ([7b291a6](https://********/wilkes/wilkes-project/commits/7b291a6f241295a703277b77021f95a4dbebe05c))
* 0726公共组件下拉左右样式变更同步到master分支 ([f7e250a](https://********/wilkes/wilkes-project/commits/f7e250ac05e9ee4372710f80bfae955c9ab74620))
* 2.6算子编辑页控制台弹窗功能变更 ([4215491](https://********/wilkes/wilkes-project/commits/4215491d6c9a829330d63c4c5d5f937347dd95ab))
* 2.7控制台错误信息展示功能变更 ([9e4ebb6](https://********/wilkes/wilkes-project/commits/9e4ebb611f339cc7a77323bc8837c9efa1cb724a))
* 2.7控制台页面添加engine-log/banner页签和job详情接口参数变更 ([5e7c75a](https://********/wilkes/wilkes-project/commits/5e7c75a45af6fb261743087235d73a289ea4b5e2))
* 2.7版本chart包升级到57 ([b6fd9bc](https://********/wilkes/wilkes-project/commits/b6fd9bc85bfe8d7887612bc6d3592f3b68709346))
* 2.7版本公共组件下拉框分组形式替换成左右布局 ([34ef87d](https://********/wilkes/wilkes-project/commits/34ef87d9f54b262042dfd4455d12480dee21162b))
* 3.0分支修改splitbutton组件下拉点击跳转页面不消失bug，临时手动清除div ([2486924](https://********/wilkes/wilkes-project/commits/24869246db5737c03304bc83922c1e38805d7862))
* 3.0分支深度/行为分析页面添加打开控制台功能 ([06a972a](https://********/wilkes/wilkes-project/commits/06a972a90d26d005c4edd1ccccaa69147582f494))
* 3.0分支深度/行为分析页面添加打开控制台功能 ([5b75df5](https://********/wilkes/wilkes-project/commits/5b75df592e843884151851bb80283acd860c39e8))
* 3.0分支算子参数ATTEN、SEVERITY、SEVERITY_ID字段变更 ([ce81e37](https://********/wilkes/wilkes-project/commits/ce81e37d5f61965a26cf1435b8e006d7e418c601))
* 3.0版本兼容模型包3.0,3.1 ([443bfe4](https://********/wilkes/wilkes-project/commits/443bfe44ff5fc259cc42e90cdfc883fa3ba51842))
* 3.0版本模型列表页修改最小宽度为1200像素 ([8a79ea6](https://********/wilkes/wilkes-project/commits/8a79ea6d822e996e1f4a824cb6837176e5812143))
* 3.0版本模型列表页修改最小宽度为1200像素 ([118ae2b](https://********/wilkes/wilkes-project/commits/118ae2ba5ae20d88ef26e67bd019902fcabbb298))
* 3.0算子编辑页打开详情，右侧查看事件下方不回显白名单 ([d94c4c5](https://********/wilkes/wilkes-project/commits/d94c4c56bcd7634812377fc7a0bcb5eff02f9f11))
* AI模型模糊查询大小写敏感调整 ([0e7fc5a](https://********/wilkes/wilkes-project/commits/0e7fc5a1decb9193698aa2d7fa88ba3dffc3bbba))
* desc->event_desc ([f967263](https://********/wilkes/wilkes-project/commits/f9672632339cfade847843321c0351de6709f4d6))
* desc->event_desc ([2fe6666](https://********/wilkes/wilkes-project/commits/2fe66662b12cad624cd024d61d7ddd30050dd089))
* elasticsearch改名openSearch ([0b9d892](https://********/wilkes/wilkes-project/commits/0b9d892db16bcb5ce27d9bfac430431f112a27ed))
* git地址调整 ([e1663f7](https://********/wilkes/wilkes-project/commits/e1663f7f5736d395f8d09c203c715c5c5c6b378f))
* init初始化导入模型默认不测试 ([b41a8e7](https://********/wilkes/wilkes-project/commits/b41a8e7e9943e6e472d9c3fb30813e2cee6da202))
* iPV6代码逻辑错误 ([93e0dbe](https://********/wilkes/wilkes-project/commits/93e0dbe010c8f557834c2360c1f5409e011fb703))
* jenkinsfile配置上传81 ([4334f1d](https://********/wilkes/wilkes-project/commits/4334f1d26d150f9ca666cc4708e0bdb91595621d))
* jenkinsfile配置增加archiveArtifacts ([00989ae](https://********/wilkes/wilkes-project/commits/00989aef250fe7f3cb3440cfdd68da65faf6ecd5))
* jenkinsfile配置增加archiveArtifacts ([26a10c2](https://********/wilkes/wilkes-project/commits/26a10c23a4f95d99777ec5369c73854f6b4cb442))
* jenkinsfile配置增加archiveArtifacts ([c79cfc9](https://********/wilkes/wilkes-project/commits/c79cfc9a55e1b9891dace3cfd1cb02e10a3785fa))
* jenkinsfile配置增加archiveArtifacts ([706767f](https://********/wilkes/wilkes-project/commits/706767f8e796a943ebf6f98f19b0681f07be4ad1))
* jenkinsfile配置增加archiveArtifacts ([5370d6a](https://********/wilkes/wilkes-project/commits/5370d6acdec940f4bd4900e5d87068558785bccd))
* jenkinsfile配置增加整体构建超时时间 ([29df9a2](https://********/wilkes/wilkes-project/commits/29df9a22bb55fc3d9c0330a1e7197ead3a34ea37))
* kc为非杀伤链阶段的编号变更 ([24956df](https://********/wilkes/wilkes-project/commits/24956dfb8a46782e894f73299e6e856fbe2225cf))
* master分支修改样式隔离打包命令 ([7833470](https://********/wilkes/wilkes-project/commits/78334708b90efb7e51d95c60b13601286cef375b))
* master分支修改算子编辑页右侧$符号下拉错位bug ([13cc5b6](https://********/wilkes/wilkes-project/commits/13cc5b6678dc25afb235ded4d7aa791b9fa56652))
* master分支删除自动滚动到页面顶部逻辑 ([c989279](https://********/wilkes/wilkes-project/commits/c9892798663401bd75e108a7221df90f7d13b475))
* master分支升级tui到2.0.2版本 ([96a6c3d](https://********/wilkes/wilkes-project/commits/96a6c3d2f179b8779a1498d06430df9d76521005))
* master分支添加网段匹配操作符 ([03a597b](https://********/wilkes/wilkes-project/commits/03a597b533cc7304ec3f2f874489902e05030bb8))
* master分支联邦分析算子过滤项数据变更 ([7f16c62](https://********/wilkes/wilkes-project/commits/7f16c6216e04849a6b5fc720670f92e450092ece))
* master分支选择剧本弹窗分页显示错乱修改 ([229167d](https://********/wilkes/wilkes-project/commits/229167d0312040fb8557edfb02ecbfe2db8b2ee1))
* master升级到组件库1.1.1-alpha版本 ([1b844cb](https://********/wilkes/wilkes-project/commits/1b844cbc83ad38b60fca84dda8e4c9d3506fc3a1))
* modeltask bug ([85c38bd](https://********/wilkes/wilkes-project/commits/85c38bd6eab58283d04ac48c7b49fb54d1b791b8))
* setenv清理 ([8cb0d70](https://********/wilkes/wilkes-project/commits/8cb0d7018f7381f1dd863beea55986a9d5ab968e))
* taskmanager gc上限配置 ([60ca329](https://********/wilkes/wilkes-project/commits/60ca329fe6de2cfb4823459145d65b67d8c2edc2))
* template.sh脚本调整https ([7fb96bf](https://********/wilkes/wilkes-project/commits/7fb96bfb57551276b5bb4020c0249c71ec9cb96a))
* update ([f97927d](https://********/wilkes/wilkes-project/commits/f97927dfddd3d2bd3a0636ed0916b5c08a9d95b2))
* update ([6451a84](https://********/wilkes/wilkes-project/commits/6451a843dec6d3877db00eba69b28317ed00fd99))
* update ([5988edf](https://********/wilkes/wilkes-project/commits/5988edf7fc39fec98a28b8261cf41f688ca99d84))
* update alarm output field name ([dfc0534](https://********/wilkes/wilkes-project/commits/dfc0534fcc2bbc94ebdbe8b430ebdd7fec61f4ff))
* upgrade hamming version to 3.1.1 ([17900c9](https://********/wilkes/wilkes-project/commits/17900c9304c6ba8584ce7692fc1bcfa8524c7d32))
* wilkes-2.7 知识库附加字段名称与字段规范冲突时，报错 ([43bf539](https://********/wilkes/wilkes-project/commits/43bf53918a1e2b5ddbdbf848eae08cef6c590680))
* wilkes创建规则对接告警引擎 ([daeb13c](https://********/wilkes/wilkes-project/commits/daeb13cef6f0470f3d36749b707d4b394d1a4124))
* 不支持将除“安全日志”外的数据源直接生成告警事件 ([8e06672](https://********/wilkes/wilkes-project/commits/8e06672415ad15d5e1a0c1fc5442b96b841bc1f7))
* 事件主体变更为攻击方向 ([58b3e05](https://********/wilkes/wilkes-project/commits/58b3e0509d1bb89626751056b19c489e825aa25f))
* 事件类型保留编号0开头 ([8f1a9a7](https://********/wilkes/wilkes-project/commits/8f1a9a7f10678dde634f6867b5858a0b37780939))
* 优化删除算子连线逻辑，删除多余日志打印,修复测试参数缺失等 ([c7633ae](https://********/wilkes/wilkes-project/commits/c7633ae49ff53012c3ddd0c8e63809f094134645))
* 优化导入功能websocket无法单体/集成环境同时适配问题 ([7874518](https://********/wilkes/wilkes-project/commits/78745185ab94dec41ad6bee83d461b6495fbbabd))
* 修复2.0全权限不显示问题 ([9edf3f0](https://********/wilkes/wilkes-project/commits/9edf3f0afbec04c7ebb2d81d630ef959616a4b42))
* 修复2.7算子打不开第二次问题 ([96760cf](https://********/wilkes/wilkes-project/commits/96760cf74a0af1f8d00d660d79867fd91a9824d4))
* 修复3.0分支模型描述未禁用和算子编辑配置功能失效bug ([b625854](https://********/wilkes/wilkes-project/commits/b625854b7a5f6a454fe1e06e6edfa9b8834efa3d))
* 修复3.0分支火狐浏览器，算则编辑页连线不显示错误 ([520febb](https://********/wilkes/wilkes-project/commits/520febb86a7ebbc23c6eb0b3a6c9fbd66d9ed946))
* 修复aas在标签上不起作用 ([488d369](https://********/wilkes/wilkes-project/commits/488d369f868f95693edce511e886039eb490c4ce))
* 修复aas在标签上不起作用 ([13ad6f1](https://********/wilkes/wilkes-project/commits/13ad6f1ac6fdadbfa93e3555dd171b6188bd622f))
* 修复ATT&CK列表页不展示问题，修复左侧选择字体颜色不同步主题问题 ([c8b7290](https://********/wilkes/wilkes-project/commits/c8b729027f025cd7fb641eadf49041ea8a91c78f))
* 修复ipv4/ipv6只对过滤条件筛选进行校验 ([96148f0](https://********/wilkes/wilkes-project/commits/96148f0f83a1db24d94a454319b7104169851598))
* 修复stylelint版本依赖导致打包报错问题 ([85dda1a](https://********/wilkes/wilkes-project/commits/85dda1ad4f8a5a348918db36be16a85eae701329))
* 修复下拉框被框架挡住问题，修复下拉时会随外层滚动问题 ([9808684](https://********/wilkes/wilkes-project/commits/98086845980fbba37e8a55cbff72dbe08fc44b0c))
* 修复保存模型丢失关联source ([ea67751](https://********/wilkes/wilkes-project/commits/ea677510f3a612bd95490cc2dc9340eb3ab9ab3a))
* 修复切换路由时下拉框还存在问题 ([f845a85](https://********/wilkes/wilkes-project/commits/f845a85eb8ae4e6541159bfd91c11b502d0533d9))
* 修复列表页面改变表单高度导致的表格区域错位问题 ([9c696c8](https://********/wilkes/wilkes-project/commits/9c696c86b2d148b7b96f7d2c36e0ad68fb099a5d))
* 修复威胁知识情报库算子排除时附加字段能选择问题 ([caf52a8](https://********/wilkes/wilkes-project/commits/caf52a8939203c0f7ec81ee885eafad07212ba92))
* 修复安全域算子打开是报错问题，优化编辑保存流程，修复编辑时还能删除问题 ([deb6bd2](https://********/wilkes/wilkes-project/commits/deb6bd21c06e1b4ff7c0cde6733875ea7a70ef83))
* 修复导入模型1366分辨率下出问题 ([2a944ed](https://********/wilkes/wilkes-project/commits/2a944edf1f8daaddfd60c70f1c63d26530e4a2e4))
* 修复批量操作 ([2c5ad8d](https://********/wilkes/wilkes-project/commits/2c5ad8d5be29f1ea97ebd3b4e84999966c4ba066))
* 修复提测问题 ([1d26296](https://********/wilkes/wilkes-project/commits/1d2629670c58844dbb136926ada8356e75155dd9))
* 修复攻击方向动态配置后，新增和编辑显示不一致错误 ([6333a16](https://********/wilkes/wilkes-project/commits/6333a16bf5f1005433283cf9936c057bfbb98498))
* 修复数据源算子改变时算子内容不重置 ([1549002](https://********/wilkes/wilkes-project/commits/15490022270bc724f5fd77b398afde0d999f7122))
* 修复智能分析打pack包失败,以及切换安全日志是事件类型不验证等问题 ([962df1c](https://********/wilkes/wilkes-project/commits/962df1c6b987826676bee3bb61d218e58bf350ae))
* 修复检索带来的前端页面展示异常，修改知识库接口 ([14ed8f5](https://********/wilkes/wilkes-project/commits/14ed8f51859f85a9a331212dad3ee9129ad05140))
* 修复模型监控有空数据源问题 ([deaa2db](https://********/wilkes/wilkes-project/commits/deaa2db01dbc16354dc870da4ac38d6fc6f08079))
* 修复模型输出中$报错以及滚动问题2.8 ([b691e38](https://********/wilkes/wilkes-project/commits/b691e38799103e6dd4f102f0c177ffd1ec8a4ce6))
* 修复模型输出字体样式问题。同意组建样式等 ([b59985d](https://********/wilkes/wilkes-project/commits/b59985d8229021110e15aecaa0e67fa47f722b5d))
* 修复河南移动项目编辑页面报错问题，同步河南移动列表页面代码 ([b9d6963](https://********/wilkes/wilkes-project/commits/b9d6963967f7f9f344142e87ed263e66d0a34ca7))
* 修复第一次打开算子编辑页监控信息偶发性不显示数据错误 ([e686ac2](https://********/wilkes/wilkes-project/commits/e686ac2579ad9a5c0d38980a7d743a5f18afac09))
* 修复算子拖拽创建由于层级重叠问题导致的新建算子坐标归零 ([34e5c91](https://********/wilkes/wilkes-project/commits/34e5c911937071c82e6f6b9803d825eca8efdaca))
* 修复算子监控中告警事件的触发时间显示问题 ([815c5a3](https://********/wilkes/wilkes-project/commits/815c5a3ee2cb7ced89369f2edd971d4015f7ea65))
* 修复编辑页返回时一定条件不能返回列表页面情况 ([d20ac4b](https://********/wilkes/wilkes-project/commits/d20ac4b89d7281701ad64224d6bc7a2871f1dde6))
* 修复编辑页面每次进去模型输出滚动条滚动下去问题 ([9e020d5](https://********/wilkes/wilkes-project/commits/9e020d53a723cc1f69ccf8cca57ee7f7bcda91b7))
* 修复行为分析度量回去字段类型问题 ([d427283](https://********/wilkes/wilkes-project/commits/d427283f1b803987a7f4bfb4e2e7e16bfe8d0f77))
* 修复设置其它输出信息被隐藏问题 ([88a86e8](https://********/wilkes/wilkes-project/commits/88a86e89ea484d4d78f4b2d77c8a0e0b7ff14764))
* 修复调试时信息多次添加dom问题 ([82fa6df](https://********/wilkes/wilkes-project/commits/82fa6df9e3164bc56898942a2a70cab690bef0dd))
* 修复过滤类型参数不正确不回显问题 ([a7dc2a5](https://********/wilkes/wilkes-project/commits/a7dc2a50574eaab766f09eacd56638094f5ceef3))
* 修复连线三个后不能打开的错误问题 ([e9f96bc](https://********/wilkes/wilkes-project/commits/e9f96bceb89338aa9c815898cd97b67e9fb6a0ec))
* 修复部分浏览器开关启停不起作用，修复基础设置不回显 ([5e93c11](https://********/wilkes/wilkes-project/commits/5e93c11fe373642288e60dc8863981f5a0054c82))
* 修改2.7模型列表页新建回溯任务按钮权限控制 ([f81f2f5](https://********/wilkes/wilkes-project/commits/f81f2f517ceaea8fc43ffb3a0fc7593995f0db8e))
* 修改2.7版本模型类型值变更导致按钮权限控制错误 ([709c2c3](https://********/wilkes/wilkes-project/commits/709c2c3c60d523686492728f067254db93b941bc))
* 修改3.0分支控制台请求接口header携带票据名称变为principal ([875d397](https://********/wilkes/wilkes-project/commits/875d397c1571efba01f1cfdc1e6a2fc67f61cc38))
* 修改3.0分支控制台请求接口header携带票据名称变为principal ([403a968](https://********/wilkes/wilkes-project/commits/403a968d05c59170a6d9d15c36673ee287a82c5e))
* 修改3.0算子编辑页新增时，右侧事件类型选择后下方不自动显示填充数据帮助小手 ([2bdd1ee](https://********/wilkes/wilkes-project/commits/2bdd1eebd73397c91b805aa4dddf269e4bcebdc2))
* 修改angular框架依赖版本升级导致无法正确拉取依赖错误 ([1dd6d0d](https://********/wilkes/wilkes-project/commits/1dd6d0d1e6204d9377429e260c54010986a13926))
* 修改AssetSecurityService类名 ([a5101ac](https://********/wilkes/wilkes-project/commits/a5101ac0e39eab3570c739380d9a08d2fb5b9253))
* 修改AssetSecurityService类名 ([3133746](https://********/wilkes/wilkes-project/commits/31337463ea7a1ae9895a783734b28f6ea4f8e085))
* 修改master分支回溯时间选择添加7天限制并显示提示信息 ([1700d7f](https://********/wilkes/wilkes-project/commits/1700d7f8ab17bc6fd861aefb98f2745af44b349d))
* 修改maven仓库地址，去掉自动发布 ([e1ef197](https://********/wilkes/wilkes-project/commits/e1ef197751cbb04cf28a39dadd7cb6b9e8b66e8a))
* 修改release-2.7列表返回不刷新错误 ([cf847de](https://********/wilkes/wilkes-project/commits/cf847deba26d8fe603b5c0b5c3a9c261e26b1b68))
* 修改socket地址获取错误逻辑 ([f3edfee](https://********/wilkes/wilkes-project/commits/f3edfeef262e549cacd2af7a96f66d54e6dfee57))
* 修改tab页切换导致算子编辑页标题文本隐藏错误 ([916bb81](https://********/wilkes/wilkes-project/commits/916bb81b78ce119461c26432568743ce09c5dd28))
* 修改tab页名称为配置更新 ([15f30a7](https://********/wilkes/wilkes-project/commits/15f30a7997cc9958b7cae21afdb51a57767f17b0))
* 修改topic页面关联分析取值错误 ([e8b7058](https://********/wilkes/wilkes-project/commits/e8b70582cd6be7fd2c6d0cd78cf2716e07b34ba6))
* 修改下拉组件分组属性变更影响bug ([4819f9c](https://********/wilkes/wilkes-project/commits/4819f9ceba533a7d17f2fc71cf09c2a580533b90))
* 修改从url中获取socket地址逻辑 ([dfc2a16](https://********/wilkes/wilkes-project/commits/dfc2a16acfa948ecd34dddcc2f49c2de81da8678))
* 修改代码合并导致依赖未引用错误 ([7c8e2c8](https://********/wilkes/wilkes-project/commits/7c8e2c8f5543a3f13bf0189507593e1eded216a9))
* 修改关联分析列表页下拉组件按钮点击不消失bug ([38abf88](https://********/wilkes/wilkes-project/commits/38abf886fdec33e724e434bfddd2d7937d9722f9))
* 修改关联分析列表页创建人字段太长导致行高跟右侧不一致错误 ([756a107](https://********/wilkes/wilkes-project/commits/756a1071acd62302e161a5b492075605f80c04d7))
* 修改关联分析列表页缓存搜索条件 ([cb02ae4](https://********/wilkes/wilkes-project/commits/cb02ae4582ddb2ff9e1d000c4149ace2038c87de))
* 修改列表页最小宽度错误 ([bfb921e](https://********/wilkes/wilkes-project/commits/bfb921e09a42a7b0ca477e99254934f4b51c78d7))
* 修改升级盘古后分页组件起始页从0开始，跟后台接口不一致错误 ([6615c46](https://********/wilkes/wilkes-project/commits/6615c46c8384f21aaf5ba84056c675dfdb534a41))
* 修改合并文件冲突 ([d16af81](https://********/wilkes/wilkes-project/commits/d16af81732f6d3bf144eb84d724c187a1e7ef2a8))
* 修改弹窗自动关闭功能 ([05678fa](https://********/wilkes/wilkes-project/commits/05678fae082f30cc34c28bfd3f4be37d5db2a739))
* 修改控制台显示逻辑 ([519346e](https://********/wilkes/wilkes-project/commits/519346e27b74d4e8d1a36680d25f4a78dbc08573))
* 修改控制台显示逻辑 ([2b2cd79](https://********/wilkes/wilkes-project/commits/2b2cd7923ab8f8e5f0675a4a39df9a77e4a3dd97))
* 修改控制台调试页签名称修改和样式错乱 ([51127b2](https://********/wilkes/wilkes-project/commits/51127b20ca4e9698b1a523d44843994e9fd92a16))
* 修改控制台错误信息弹窗尺寸太小问题 ([ef55baa](https://********/wilkes/wilkes-project/commits/ef55baa24bf2d23de6a41c8b2e72fec826241701))
* 修改控制台页面提示状态显示错误 ([8acc265](https://********/wilkes/wilkes-project/commits/8acc26555ed7e2908984b885772f8a330b7c30df))
* 修改控制台页面点击位置和功能错误 ([d41f8af](https://********/wilkes/wilkes-project/commits/d41f8af19a81ed3d940aa3d037fcdabe07592d88))
* 修改搜索算子功能失效bug ([1d2d693](https://********/wilkes/wilkes-project/commits/1d2d6935d126f934cbef526ce8ee8a5c530e30dc))
* 修改搜索算子功能失效bug ([a11de2e](https://********/wilkes/wilkes-project/commits/a11de2ed73d678aedd3ca7e9604f7edbdaa021e1))
* 修改权限控制和算子编辑页分离设置和编辑 更新到2.7 ([e3119cf](https://********/wilkes/wilkes-project/commits/e3119cf11daf3cdc562f1a59b8d52359b776b6ae))
* 修改权限获取uri值错误 ([2a9c82f](https://********/wilkes/wilkes-project/commits/2a9c82f08e40eac40fea5738f5241877df7dc6ed))
* 修改权限迁移2.7 错误 ([1a74710](https://********/wilkes/wilkes-project/commits/1a747101b6a65f89c0ec16fcf70d1f5546361a75))
* 修改样式未隔离导致错误和事件点击不生效bug ([774e51f](https://********/wilkes/wilkes-project/commits/774e51f76ccf1cc681ce1c08305555a8e5de9632))
* 修改样式隔离错误 ([1ebd72f](https://********/wilkes/wilkes-project/commits/1ebd72f2076e928dec05034db23b8f32010882cb))
* 修改样式隔离错误 ([9fd47a8](https://********/wilkes/wilkes-project/commits/9fd47a8b5ac41386f673e1ac4413c2bbc3a6e653))
* 修改深度分析列表页标题显示不全-2.7 ([a364720](https://********/wilkes/wilkes-project/commits/a364720952ddb6ab00eab871812124e0e6b39968))
* 修改深度分析页面展示样式 ([4f33637](https://********/wilkes/wilkes-project/commits/4f33637bd3bc29cc82360dcbc450c3d753918702))
* 修改状态样式 ([5bcbf3a](https://********/wilkes/wilkes-project/commits/5bcbf3addde5e7c07706cc650286d80d69798ddb))
* 修改状态样式和flink配置接口变更 ([3adc96e](https://********/wilkes/wilkes-project/commits/3adc96e8bd789f373c03be2b471f041823190e3a))
* 修改算子编辑页$符号下拉位置计算错误和显示问题 ([dc525c8](https://********/wilkes/wilkes-project/commits/dc525c871065d36aea44ca0cbc6acb6b1b42d88d))
* 修改算子编辑页事件关联右侧不同字段下拉展示错乱错误 2.7 ([52fdcc3](https://********/wilkes/wilkes-project/commits/52fdcc3f28a8c54c6ae3ebd463ce4d50202a5478))
* 修改算子编辑页保存后返回列表页刷新接口错误 2.7 ([329abaf](https://********/wilkes/wilkes-project/commits/329abafc844a6fa068248bf3569198180dab32d4))
* 修改算子编辑页右侧多选下拉组件覆盖下方选择项样式错误 ([f66da97](https://********/wilkes/wilkes-project/commits/f66da97d45091f94d914a4e7b996fb5592112f5a))
* 修改算子编辑页归并字段新增传递参数缺失错误 ([2784163](https://********/wilkes/wilkes-project/commits/278416307f2ff85348f597d70f1b258ecf288137))
* 修改算子编辑页归并字段新增传递参数缺失错误 ([6ffd3a3](https://********/wilkes/wilkes-project/commits/6ffd3a337708a138848df443c31ee0df75bf8a30))
* 修改算子编辑页查看事件在点击测试后点击无法触发跳转错误2.7 ([26c4ed5](https://********/wilkes/wilkes-project/commits/26c4ed54cc1a24c0d05385bc84fd3429def09741))
* 修改算子编辑页输入事件名称自动带出事件类型后测试接口报错bug ([3edf255](https://********/wilkes/wilkes-project/commits/3edf255c2f01493b87d78e2b1a6abcd207b66bb0))
* 修改算子设置页左侧不显示错误 2.7 ([8fe48d8](https://********/wilkes/wilkes-project/commits/8fe48d856f43d80fe14d88249f50bb2565a7fa69))
* 修改算子设置页面标题长度超过4个添加省略标记，并且为不可编辑 ([a14862c](https://********/wilkes/wilkes-project/commits/a14862c8dfa3072c2f25e688cb3b9a88a22db848))
* 修改算子设置页面标题长度超过4个添加省略标记，并且为不可编辑 ([94eeab0](https://********/wilkes/wilkes-project/commits/94eeab0c6ab282c304e70f5315dd9bef303c8b06))
* 修改组件属性类型错误导致打包失败 ([45ce8c4](https://********/wilkes/wilkes-project/commits/45ce8c41f8fea2670a3a410beb983a1609437b91))
* 修改菜单名称 ([9951716](https://********/wilkes/wilkes-project/commits/995171677a0e98be54dea605b01260d3310190d9))
* 修改行为分析查看画像弹窗数据提前加载问题 ([0aad0b4](https://********/wilkes/wilkes-project/commits/0aad0b41149e21066014993e62e9ca87554b48d5))
* 修改行为分析编辑页个体/个群下拉选择tips显示错误 ([9d85745](https://********/wilkes/wilkes-project/commits/9d8574557761c7f08cfdf9be166c92b76bf6554e))
* 修改适配盘古t-tree-select组件属性变更和确认弹窗组件取消按钮事件监听 ([cca7549](https://********/wilkes/wilkes-project/commits/cca7549fe5fe9331bb57d7adc38ab89e02e3e626))
* 修改适配盘古title组件标题属性名称变更 ([07bb9bc](https://********/wilkes/wilkes-project/commits/07bb9bcc4354e7b34955fa7e2f7bd8ba78866849))
* 修改适配盘古使用原生get请求参数中文转码错误 ([2896de4](https://********/wilkes/wilkes-project/commits/2896de45cc90a90685d435b28f8bef21ade3898c))
* 修改适配盘古分页组件重置查询时起始页不变为第一页错误 ([e7da15b](https://********/wilkes/wilkes-project/commits/e7da15b9abd47f15b8394bd734d4d1d1920252c4))
* 修改适配盘古分页组件重置查询时起始页不变为第一页错误 ([92cabfd](https://********/wilkes/wilkes-project/commits/92cabfd9c955880ead541cb357c65a2de1417903))
* 修改适配盘古标题组件变更，默认不带前缀竖线错误 ([985f3f1](https://********/wilkes/wilkes-project/commits/985f3f1f470879a4e04b8e72787cff11d5f8f2e3))
* 修改适配盘古组件名变更和样式错乱 ([ca544b6](https://********/wilkes/wilkes-project/commits/ca544b654fa6d37b06202507d5e00947630e0322))
* 修改适配盘古获取不到用户票据错误和url拼接错误 ([fd7f864](https://********/wilkes/wilkes-project/commits/fd7f864c2cc85cb9577a657c36c296a0b2975661))
* 修改集成环境manifest输出文件名 ([9c9c6a6](https://********/wilkes/wilkes-project/commits/9c9c6a608dbb88e12f3d4306356d5f816e5bc907))
* 修改默认告警规则名称为wilkes_CUSTOM_default ([e66b8da](https://********/wilkes/wilkes-project/commits/e66b8dae2440d1dae10debe62f3b5e829cea50c3))
* 关联分析编辑页适配盘古 ([8eff909](https://********/wilkes/wilkes-project/commits/8eff90922ccced31369d728813171d8753db5438))
* 关闭/确认按钮点击报错修改 ([625ea77](https://********/wilkes/wilkes-project/commits/625ea7732cd5036f8caac330f771b0207ce7ff45))
* 内置模型创建人统一改为Sadmin,AI除外 ([36de7b2](https://********/wilkes/wilkes-project/commits/36de7b2c204a4c88d60f9769e40b2d0da3295457))
* 删除rule编辑页面生成知识测试标记 ([59980a8](https://********/wilkes/wilkes-project/commits/59980a869b38dd6d0cd703764f1ad3e18f6cd6b2))
* 删除和编辑模型时，添加引用确认弹窗，修改集成打包错误 ([a8ec435](https://********/wilkes/wilkes-project/commits/a8ec4358efe77a35ca8c46dd223b97bed817e904))
* 删除模型输出dom高度计算逻辑和固定textarea行高 ([fc528f9](https://********/wilkes/wilkes-project/commits/fc528f98330e599b5703596ff031ff264c99a4fa))
* 删除算子中使用的组件滚动避免编辑时，出现留影现象2.7 ([61e7d6e](https://********/wilkes/wilkes-project/commits/61e7d6e61639fd0ded20634aaaaa577763508354))
* 删除页面组件，保留主应用目录 ([6fa1f73](https://********/wilkes/wilkes-project/commits/6fa1f73ea7c5d29fad6ced070ff61f7c6187ce4f))
* 升级@tui/plus版本 ([48365af](https://********/wilkes/wilkes-project/commits/48365afc331de4dc822cb060ee71ffdfaf83d17a))
* 升级angular/cli版本修复编译报错和修改打包脚本 ([85affb2](https://********/wilkes/wilkes-project/commits/85affb2bd4173d8503b60b1020b309282bcbd9a8))
* 升级chart版本到59 ([847a9b3](https://********/wilkes/wilkes-project/commits/847a9b391bde79305554b3ebb2b56e61413b3348))
* 升级chart组件到55 ([0ccfa7f](https://********/wilkes/wilkes-project/commits/0ccfa7f718a57e490a3aae283eb3937607053fc9))
* 升级盘古依赖到1.0.4并且修改适配导致的错误 ([05115af](https://********/wilkes/wilkes-project/commits/05115af896da3bacf1ae9f8f4e83f6ff526a5581))
* 升级盘古版本到1.0.3 ([040d988](https://********/wilkes/wilkes-project/commits/040d988c5daccdaf992804f8ab7d358d98560495))
* 升级组件库版本到2.14.56和添加tui-component-secbizlib依赖 ([301ec00](https://********/wilkes/wilkes-project/commits/301ec00aa20d91b6ec744906ca63ae46150d770a))
* 去掉jenkins触发器构建 ([c080935](https://********/wilkes/wilkes-project/commits/c080935b3f1e690b4760fb71633252a64cb49705))
* 变更接口环境变量前缀取值目录 ([d0c3a32](https://********/wilkes/wilkes-project/commits/d0c3a326fed611621961d057f9dc0affbb1f74a6))
* 处理过滤器为空的情况 ([5326edd](https://********/wilkes/wilkes-project/commits/5326eddb921cfbd7ba004f021c8250227daa5ec6))
* 多级项目迁移至3.1分支 ([8981d30](https://********/wilkes/wilkes-project/commits/8981d306837feb6bb8f567a52117fef9a51e30d8))
* 导入模型增加文件头格式校验 ([52dc1e0](https://********/wilkes/wilkes-project/commits/52dc1e0a04bfbdece3fe9e65aef70c96c5294b5f))
* 导入自定义模型保留状态变为内置 ([31ee71c](https://********/wilkes/wilkes-project/commits/31ee71c2d237279ea15a496e8eb592d582604ba5))
* 导出全部tags丢失 ([dd0b038](https://********/wilkes/wilkes-project/commits/dd0b038ac2e6dba1a05f25084e6672fb4da31686))
* 帮助手册模块适配盘古 ([69813d3](https://********/wilkes/wilkes-project/commits/69813d343b03467c6a3c6dfb775fc7b315f98848))
* 开启定时任务切面开关 ([39c6b5b](https://********/wilkes/wilkes-project/commits/39c6b5ba708fb77997b9162b48941b2ab9eb4484))
* 所有页面跳转添加路由守卫 ([c09eda1](https://********/wilkes/wilkes-project/commits/c09eda16d202ce4aca41c32db62616b63ec37c40))
* 打包移除拷贝minsky ([edc40ee](https://********/wilkes/wilkes-project/commits/edc40eeb819bebc0f7949f9f9f3ef8ef081d161c))
* 持续未发生算子的触发条件添加等待时间 ([19f6929](https://********/wilkes/wilkes-project/commits/19f69295379093f59bf4b3b744ff0072285883dc))
* 指定算子包名称 ([9848955](https://********/wilkes/wilkes-project/commits/9848955770f473380c1c1a4c777ba898ceffdd7f))
* 控制台添加数据刷新功能 ([604a073](https://********/wilkes/wilkes-project/commits/604a073aa2468fc05786b620a2574799a14a3988))
* 控制台错误信息查看改为弹窗形式 ([905b72d](https://********/wilkes/wilkes-project/commits/905b72d32e4be40a2ab207b91eea95a0940e9abe))
* 提取yarnModel公共配置,template.sh脚本调整 ([aac7b75](https://********/wilkes/wilkes-project/commits/aac7b75a1552dd31524353f6a7ef7fa8bedf724e))
* 攻击方向字段变更为动态配置 ([42bebbf](https://********/wilkes/wilkes-project/commits/42bebbf154b916f38b41003facafde9be81fa08c))
* 日志字段添加过滤搜索功能 ([2a6e1cd](https://********/wilkes/wilkes-project/commits/2a6e1cdeb210032e65ebbc98b6b61ce6f9fe576e))
* 更新tui公共组件库版本到56和删除ai模块内研判页面代码 ([e037123](https://********/wilkes/wilkes-project/commits/e03712335d7acc975bea66a3a9e7a0ace7c1e7df))
* 更新内置模型专题，以后专题由wilkes维护 ([d48275e](https://********/wilkes/wilkes-project/commits/d48275e5157e13c25dc4b1fe0ccf3ae0317f7a0d))
* 替换模型编辑页基础设置ATT&CK组件为公共组件,ibrary框架升级到2.14.38 ([7cd2aec](https://********/wilkes/wilkes-project/commits/7cd2aecfa667064ac8724dd5f21516b1d334cdde))
* 权限excel添加告警规则相关接口 ([b1eed69](https://********/wilkes/wilkes-project/commits/b1eed694f729a2dc24e52f8b6b0ce7ddbc935335))
* 样式修改 ([bbba1cb](https://********/wilkes/wilkes-project/commits/bbba1cb5a688c116f75ccdc4e5688c115fbd5398))
* 模型专题详情页switch切换逻辑错误修改 ([f7f22ff](https://********/wilkes/wilkes-project/commits/f7f22ffacad4ecd2de2706f9ac8514417655eb0f))
* 模型列表页面加载时闪动以及页面报错问题 ([bf97271](https://********/wilkes/wilkes-project/commits/bf972715866df03484886efa69790f05d0b452ed))
* 模型监控展示中英文问题 ([ab89c8b](https://********/wilkes/wilkes-project/commits/ab89c8b9f0a312a50035e8de415bd1e629ecdc77))
* 模型管理页面添加按钮splitButton添加权限控制 ([ca84a1d](https://********/wilkes/wilkes-project/commits/ca84a1d3e7fc9ee952e0d14ddfda04211e96c032))
* 模型调试性情页面路由增加label属性，防止页签出现英文问题 ([83324d5](https://********/wilkes/wilkes-project/commits/83324d5b5e8d4d0e82b1a4d7bd2bf1f5ad447aed))
* 沉默资产检测算子添加模型id参数 ([478cfda](https://********/wilkes/wilkes-project/commits/478cfda16850ff19fe41a99aafd9f100d9a895a9))
* 深度分析模块适配修改 ([e9230e1](https://********/wilkes/wilkes-project/commits/e9230e1542b568fe5ddf2078a35e55cfc1cbb5bf))
* 添加模型管理页面 ([0f2698f](https://********/wilkes/wilkes-project/commits/0f2698f3939f6b41184207f430fd29639472f3a6))
* 添加解密脚本 ([1d12fbc](https://********/wilkes/wilkes-project/commits/1d12fbc819f0c63670a739723f629144c50700e8))
* 添加配置更新功能 ([cff521c](https://********/wilkes/wilkes-project/commits/cff521c0b9a8dda753ee50d8a1e44ba866aa17ee))
* 画像课题接口报错 ([790a636](https://********/wilkes/wilkes-project/commits/790a63663dcf2a4703216a1b78359927aa84bd64))
* 相同字段不同字段为空时值为null ([d256f3e](https://********/wilkes/wilkes-project/commits/d256f3ecb7d7c143bbbe94300ffe53bf2d5dd785))
* 移除hamming依赖和接口 ([192e21c](https://********/wilkes/wilkes-project/commits/192e21c97d0457c989424770926181c08bf6e4b7))
* 移除关联告警规则 ([291acf7](https://********/wilkes/wilkes-project/commits/291acf7ae2b30d5f07074a8ecc4de908aca11cc3))
* 移除导入模型专题ID首字母下划线转大写 ([1cf22fe](https://********/wilkes/wilkes-project/commits/1cf22fef0c8234ef192fdba45e242c6f05f7c963))
* 移除导入模型专题ID首字母下划线转大写 ([41921d4](https://********/wilkes/wilkes-project/commits/41921d4060ff1ee2d25c91dc1c2bc98c9ed78ed7))
* 移除明文密码 ([4ce5ab7](https://********/wilkes/wilkes-project/commits/4ce5ab7f7d905b22bcd57c6ae99ebbb0500222d0))
* 移除服务启动注册无效离线任务 ([e5ec6e8](https://********/wilkes/wilkes-project/commits/e5ec6e8698e77f419ed4e9049fc1d114da2f2061))
* 算子Group增加SQL ([73eebb9](https://********/wilkes/wilkes-project/commits/73eebb9fe4387a9dc6c77436ee4e3db31ae5e7c5))
* 算子编辑添加覆盖日志字段功能 ([69987de](https://********/wilkes/wilkes-project/commits/69987de9b6e93a0edada2608a100c3387f58d29f))
* 算子编辑页控制台弹窗iframe携带票据功能修改 ([e5ecb4a](https://********/wilkes/wilkes-project/commits/e5ecb4a2cc1b31b25ff09a7fd5fbe61c5a006710))
* 算子编辑页模型分类三级组件展示样式变更 ([8b9cbfe](https://********/wilkes/wilkes-project/commits/8b9cbfe10b45d9cd4815ab219d025d88b1cf8015))
* 算子编辑页模型分类组件变更为支持3级选择形式 ([edc2000](https://********/wilkes/wilkes-project/commits/edc2000349a311cf84b5d5c99cb1af3b76d38084))
* 算子编辑页编辑区块选中删除和编辑按钮功能展示形式变更 ([2051595](https://********/wilkes/wilkes-project/commits/2051595aa884293073e8ed378cc31ae4d0729493))
* 维护杀伤链阶段tag英文名 ([ae15384](https://********/wilkes/wilkes-project/commits/ae15384fdf4b9eaaed569cc72db43fae8af57f79))
* 获取调试模型结果esurl拼接调整 ([bbf4d28](https://********/wilkes/wilkes-project/commits/bbf4d280cd7d050cd5363dc3371a58443a0d101a))
* 获取调试模型结果esurl拼接调整 ([4b39e4e](https://********/wilkes/wilkes-project/commits/4b39e4ed010cf714544e84ca0958440a9650023b))
* 行为分析模型保存校验检测算法不为空 ([b754bfd](https://********/wilkes/wilkes-project/commits/b754bfd0291b817092f4247a130a66a9f418c6b7))
* 行为分析编辑/查看切换功能添加状态校验和提示 ([1caf577](https://********/wilkes/wilkes-project/commits/1caf5775c18c53527ee6b7a38a4841957908afa5))
* 行为分析编辑切换提示语变更 ([5acae83](https://********/wilkes/wilkes-project/commits/5acae83b396730ab2a4291e3d31c29b6e99f8210))
* 行为分析编辑页变化周期和置信水平功能变更 ([4f1b215](https://********/wilkes/wilkes-project/commits/4f1b2155c19e809a0946feefaae5d102993630ce))
* 行为分析编辑页已启用模型事件、主/客体、度量、周期都不能修改 ([c82d1fb](https://********/wilkes/wilkes-project/commits/c82d1fb0a5ff8b661ea205bfda2827c27988cd63))
* 行为分析编辑页打开根据状态切换查看模式 ([5d57ff8](https://********/wilkes/wilkes-project/commits/5d57ff842fba340687cc687ce7d4bf50263df777))
* 行为分析编辑页查看画像功能修改为弹窗形式 ([656e78d](https://********/wilkes/wilkes-project/commits/656e78d8b4a2541d19f7449ea141f0633a5dea56))
* 行为分析编辑页行为主体/客体/群体划分下拉选项互斥功能修改 ([5b2606c](https://********/wilkes/wilkes-project/commits/5b2606c9e3c6766bc49258657ea136a42c815cc5))
* 行为分析编辑页行为主体/群体划分改为必填项 ([235732e](https://********/wilkes/wilkes-project/commits/235732e200632ee4deeb65f4a30c85e4bd43edae))
* 行为分析编辑页适配盘古 ([ab1b666](https://********/wilkes/wilkes-project/commits/ab1b6661d06c49c3e1b1ad13287a82cfc31a4a43))
* 行为分析详情，启用状态算法限制编辑配置项 ([6cdca8c](https://********/wilkes/wilkes-project/commits/6cdca8c255d553b42a0530554e1a3780a4328277))
* 行为分析详情，启用状态算法限制编辑配置项 ([d24c930](https://********/wilkes/wilkes-project/commits/d24c9304205a0f6447cf128d093d565c3cd0c3e4))
* 解决影响多宝阁样式 ([a19bd24](https://********/wilkes/wilkes-project/commits/a19bd2405d2af76f02032ca9c16795b3902ce19e))
* 调整es证书密码 ([12a98f2](https://********/wilkes/wilkes-project/commits/12a98f2c5b739e8010df52b5e41b3e00615b4704))
* 调整feign超时时间 ([c42af1f](https://********/wilkes/wilkes-project/commits/c42af1fb4914f6480bd51827ac6727067d8163c2))
* 调整init.d脚本改用${TOP_BASE_PATH}变量 ([f6a7d54](https://********/wilkes/wilkes-project/commits/f6a7d5469c8dc78d5143f34c10d83fd61c2c8cd8))
* 调整template.sh脚本根据auth取协议 ([86c1b09](https://********/wilkes/wilkes-project/commits/86c1b0999d9597f688454e5a3cbd81275f963df5))
* 调整为第一次启动初始化namespace拷贝算子包 ([4b975b1](https://********/wilkes/wilkes-project/commits/4b975b1ea0f0bc2adc6cd9f269dfb175abe1e774))
* 调整停用知识情报测试模型提示信息 ([699c52a](https://********/wilkes/wilkes-project/commits/699c52a7a01f65fab060504ddb09234f91587f8b))
* 调整默认开启es认证 ([0bbf7f1](https://********/wilkes/wilkes-project/commits/0bbf7f1c886d75e95d98015993e0ca5b27cfbd5e))
* 跳转检索获取字段值接口参数调整 ([429d2cd](https://********/wilkes/wilkes-project/commits/429d2cdec744fb6d8d8a9652efd39a568278c316))
* 转换模型分类 ([670ad48](https://********/wilkes/wilkes-project/commits/670ad48f4e9e7b51106cd329990613a4d02c5365))
* 适配@tui/plus 和添加攻击方向 ([fbab5d3](https://********/wilkes/wilkes-project/commits/fbab5d302e5fcfacdcbd8b01f1f31a7c091b20df))
* 适配盘古修改 ([4b7645a](https://********/wilkes/wilkes-project/commits/4b7645ac153240683a5bcfcc27ef3758e5aabab6))
* 适配盘古修改bug ([7c6bdc9](https://********/wilkes/wilkes-project/commits/7c6bdc90823d78b85075eb93513b981dcad3854f))
* 适配盘古修改bug和依赖升级 ([9b5dee0](https://********/wilkes/wilkes-project/commits/9b5dee077c8267636e3066ed4d09134ffa9c8b68))
* 适配盘古功能bug变更 ([0d88308](https://********/wilkes/wilkes-project/commits/0d883089218774bb09f3e4783339fdb14b62de69))
* 适配盘古功能修改 ([c6e347e](https://********/wilkes/wilkes-project/commits/c6e347e69ea9cc21bcc99b7d896e59cf36e01472))
* 选择模型产生的事件算子，列表去掉UNION ([0b078fe](https://********/wilkes/wilkes-project/commits/0b078fe1b7f6347efd7b6ab3cf314b90617a0103))
* 配置JPA字段值为NULL时，不参与升序排序 ([3b3682a](https://********/wilkes/wilkes-project/commits/3b3682a9740e2601076418d7b4130227b508aa20))
* 配置单机模式引擎GC数 ([583ab31](https://********/wilkes/wilkes-project/commits/583ab31472ff5e9fddc8747f944b312e68de3dcd))
* 配置知识库过滤算子知识情报不存在 ([a8f62c2](https://********/wilkes/wilkes-project/commits/a8f62c2faffd78990e7d0c077523bd4768733ad7))
* 限制上传文件大小10M（同步2.7） ([1473303](https://********/wilkes/wilkes-project/commits/1473303d087ab941fdde688ea12eabf3eb20ae09))
* 项目内规范字典值变更 ([ea87ffe](https://********/wilkes/wilkes-project/commits/ea87ffe8c268dc147593abbf3ad93ebd35a87c40))
* 验证上传文件大小并提示错误信息 ([ecd962d](https://********/wilkes/wilkes-project/commits/ecd962d78bc9c74c1d67aa47e0d67c01cb404578))
* 默认先禁用配置项可编辑功能 ([67b593d](https://********/wilkes/wilkes-project/commits/67b593d33cd396eddce209b73dd74b7446096e8f))
* 默认隐藏算子编辑页输出类型和列表左侧树显示错乱bug ([c580bf1](https://********/wilkes/wilkes-project/commits/c580bf10aafb1916823fbbf67e81d5f7b32ca3ff))


### Features

* master分支联邦分析编辑左侧条件变更 ([120266d](https://********/wilkes/wilkes-project/commits/120266daab67bdb9aec2119a58fee8196c3f9a0a))
* unset HDFS_PATH ([5b1acf8](https://********/wilkes/wilkes-project/commits/5b1acf8ae3fcd0df9c07403cac902ab3da5eec22))
* update process of SilentAssetsFilterOperator ([ac24b78](https://********/wilkes/wilkes-project/commits/ac24b78c37b962cb49ad0078e1fc55fd555b0858))
* wilkes-2.7 内置spark算 ([d17861b](https://********/wilkes/wilkes-project/commits/d17861b6ae04947dd8428740d5d9de52313796e6))
* 事件持续未发生开发 ([24549c7](https://********/wilkes/wilkes-project/commits/24549c7f1598f9bf80bc543f3fede41a0ef5085f))
* 修改匹配规则 ([d30eec2](https://********/wilkes/wilkes-project/commits/d30eec25c582d2ef329fa45d54b953d7ad1df7f4))
* 修改模型响应中的生成知识及删除知识的下拉内容接口2.7 ([e34fce1](https://********/wilkes/wilkes-project/commits/e34fce1e848b3a12c2fba092a3e13a5c9586d77a))
* 修改模型调试安全日志不能点击且选中的逻辑 ([f189ba4](https://********/wilkes/wilkes-project/commits/f189ba481b8a0f4835fba93e9ae2c1dcfd0e0c9f))
* 修改行为分析检测算法的类型 ([bc81053](https://********/wilkes/wilkes-project/commits/bc8105313312a82128b808c12cb2f0096a690558))
* 修改调试模型测试接口 ([854c27f](https://********/wilkes/wilkes-project/commits/854c27f3fda0d096b028695337385adc277f1909))
* 修改过滤器算子，根据条件获取未匹配中数据 ([35e6a43](https://********/wilkes/wilkes-project/commits/35e6a437a890a2e778aba4f29b5eca4e6f6d5bd7))
* 典型页面表单样式调整 ([9cb8886](https://********/wilkes/wilkes-project/commits/9cb8886f47a2721194096fc723fdefdfba6fcd1a))
* 典型页面调整，修复静态资源引用 ([3965261](https://********/wilkes/wilkes-project/commits/3965261bf7b410d08b376617c25fb59c851e1e34))
* 前端典型页面文件更新新内容适配 ([86c140c](https://********/wilkes/wilkes-project/commits/86c140c11a4ca57060520096552c36e45315a86d))
* 升级业务组件库支持ATTCK接口返回 ([672e5fc](https://********/wilkes/wilkes-project/commits/672e5fce5e4660b5c78364fa2143766e7031025f))
* 升级组件库，去掉示例算子 ([ecd7868](https://********/wilkes/wilkes-project/commits/ecd7868990e9e12d13af5705670800ab7de898ad))
* 合并离线分析模型 ([89f678d](https://********/wilkes/wilkes-project/commits/89f678d034deaf7af8c3ee7038eba4d0380953bb))
* 回溯分析增加名称编号查询 ([3ae0e01](https://********/wilkes/wilkes-project/commits/3ae0e0125991ef0fb173681801a06ba064b969af))
* 回溯及调试按钮添加权限暂管理 ([6cc2f35](https://********/wilkes/wilkes-project/commits/6cc2f350d0a009f20a211fd2348b9b5543589b62))
* 回溯及调试页面典型页面适配 ([81b9ec6](https://********/wilkes/wilkes-project/commits/81b9ec66fbcf032504683efec862c53745b07f65))
* 处理浏览器缓存机制 ([c336f3b](https://********/wilkes/wilkes-project/commits/c336f3b63e15f3b4004ee8a7e91a385a39aee439))
* 平台任务开发 ([793ed18](https://********/wilkes/wilkes-project/commits/793ed18c00a5cd8ebf573d31676ad3652b53a8a5))
* 开发拖拽指令，优化低版本火狐多次拖拽不能拖拽的问题 ([9dbb2ba](https://********/wilkes/wilkes-project/commits/9dbb2ba1f19e0f19e7b1654b8204bbecf7e83442))
* 开发沉默资产检测算子 ([a622b3d](https://********/wilkes/wilkes-project/commits/a622b3dddea313642a2d4c45aba10f1d14f842a9))
* 开发监控功能 ([7949838](https://********/wilkes/wilkes-project/commits/79498382b2473ee03cb0c088840d571ebed9d910))
* 控制台功能添加离线任务tab页签 ([4fccc31](https://********/wilkes/wilkes-project/commits/4fccc31437686491cfdfb1daa1db1f32bece8965))
* 控制台页面功能添加 ([11edca3](https://********/wilkes/wilkes-project/commits/11edca3dd5d7706129656374fed6602acebed6eb))
* 控制台页面输出文件添加 ([9efe378](https://********/wilkes/wilkes-project/commits/9efe378f4fd792a0f06e80fcea78d13fe7357d55))
* 整理html文件格式 ([2fe2ac3](https://********/wilkes/wilkes-project/commits/2fe2ac3e3518053165f27797d3638f0753b1c607))
* 新增ATT&CK筛选项 ([e317afc](https://********/wilkes/wilkes-project/commits/e317afc04b1ee62b7ba2c40b2b6855282a1ed829))
* 新增ATT&CK筛选项 ([f89075f](https://********/wilkes/wilkes-project/commits/f89075ff9afb3d9580940c87af9b7d586488c780))
* 新增沉默资产算子 ([b2d8fd9](https://********/wilkes/wilkes-project/commits/b2d8fd91ebd6807896f7681897a614dce1e05578))
* 新增维度统计筛选过滤字段 ([4655d91](https://********/wilkes/wilkes-project/commits/4655d91e16a4554e6851aaa26bc50037652108db))
* 新增行为分析算法类型 ([244a891](https://********/wilkes/wilkes-project/commits/244a891b2a36e7a1df6c388abef1ef718b59bbe2))
* 新增过滤清除按钮，修改批量操作逻辑 ([d62a99d](https://********/wilkes/wilkes-project/commits/d62a99d5512c228a41626868c8839c944362b7b6))
* 更新业务组件库 ([ddb15e3](https://********/wilkes/wilkes-project/commits/ddb15e3596feb664d347e44782ad77a8d014615e))
* 服务处理沉默资产检测算子参数 ([1ee6db3](https://********/wilkes/wilkes-project/commits/1ee6db36cc0b23daa1027f7059789ea34d005a7d))
* 模型停用时判断添加是否引用确认框 ([faac22c](https://********/wilkes/wilkes-project/commits/faac22cf399aafc77daebc3708e7227ccffda968))
* 模型启用停用接口请求时按钮禁用状态，避免多次操作 ([9334e30](https://********/wilkes/wilkes-project/commits/9334e30c6b89e64639746e1078e54b82fc5bf70a))
* 模型回溯以及模型调试提示信息 ([4994834](https://********/wilkes/wilkes-project/commits/4994834782360d5a437096b4983b0e7553c7714c))
* 模型编辑页面测试保存逻辑修改 ([dffc9e0](https://********/wilkes/wilkes-project/commits/dffc9e0c97566b62b4f816f7ad50989cd5a079d9))
* 沉默资产对接资产管理；资产字段支持多个 ([ec88fd1](https://********/wilkes/wilkes-project/commits/ec88fd18bcc657b01fd0cc7108a4c96a31246a52))
* 沉默资产识别功能，在攻击识别原因中，加上沉默资产信息 ([a77bfa0](https://********/wilkes/wilkes-project/commits/a77bfa0a3bf71112b0a6ecaffa5b5889e23a2410))
* 深度分析模型支持YARN模式 ([8a4a282](https://********/wilkes/wilkes-project/commits/8a4a2824594e8bf48bc931ab948849764cdc100b))
* 添加grouped-process页签和查看失败任务日志功能、bug修改 ([231c03f](https://********/wilkes/wilkes-project/commits/231c03f9d820b87707dfa9d9524a0a08611e9973))
* 生成知识删除知识业务逻辑修改 ([47feae3](https://********/wilkes/wilkes-project/commits/47feae3764ce3c40afdce1c42226efb8ced9f2be))
* 知识库字段新增knowledgeFieldTypes类型验证字段 ([be64364](https://********/wilkes/wilkes-project/commits/be64364036d6292b0ce00fb108503451dd500787))
* 知识情报库算子重构更新 ([32a2031](https://********/wilkes/wilkes-project/commits/32a2031f0fc47fa928b1aa477337f7cff8c2f589))
* 示例模型不能转内置，搜索中添加示例模型 ([f461d87](https://********/wilkes/wilkes-project/commits/f461d8777e8c9b53c630f79d04256f640bc501cd))
* 算子列表排序 ([05d9cc2](https://********/wilkes/wilkes-project/commits/05d9cc262938b9d29a2971d656c56322ecfe4bdb))
* 触发时间排序 ([5215204](https://********/wilkes/wilkes-project/commits/5215204b460f6f3db6dd02277445f277ca10b1ec))
* 调整事件统计算子顺序 ([16e0ebf](https://********/wilkes/wilkes-project/commits/16e0ebf361cbe6c23cf86d8276d396d8032d987a))
* 调整沉默资产检测算子描述及结构 ([06ff1fc](https://********/wilkes/wilkes-project/commits/06ff1fc4370298a2b3f3685615f4fd4e278e01ba))
* 调试模式下安全日志不能点击且选中 ([90db860](https://********/wilkes/wilkes-project/commits/90db860d478b46a7686e1df25b57a0be88bc5e09))
* 适配 前端开规范 ([8878ebd](https://********/wilkes/wilkes-project/commits/8878ebd19c267072a478b083cc8dd7f6b976df50))
* 适配资产管理 ([b98c791](https://********/wilkes/wilkes-project/commits/b98c791f5c0516b1dc3757ae2adcb664b74286b3))
* 适配资产管理，调整界面 ([dffb233](https://********/wilkes/wilkes-project/commits/dffb233e1d9d799652bef539d2bf94436e7880ef))
* 适配过滤条件筛选支持ipv6 ([d72f6b6](https://********/wilkes/wilkes-project/commits/d72f6b65d1b813269d9e84d183440469cdba62bd))
* 重构内置模型设置 ([1770eb4](https://********/wilkes/wilkes-project/commits/1770eb4079d711fcf415be3f3b51b8d421f83265))
* 重构算子编辑开发事件关联算子 ([583b5a9](https://********/wilkes/wilkes-project/commits/583b5a9ac01c00665b2b50817357473e6c64802e))


### Reverts

* Revert "智能分析引擎算子迁移" ([85b3dd8](https://********/wilkes/wilkes-project/commits/85b3dd8ee494200c83624fd626b699bdf684a906))



