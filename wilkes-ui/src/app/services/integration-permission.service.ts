import { Injectable } from '@angular/core';
import { TuiUserService, TuiAppService } from '@tui/frame';

@Injectable({
  providedIn: 'root',
})
// 权限服务
export class IntegrationPermissionService {
  roleId: any;
  get viewSett() {
    return this.tuiAppService.getAppViewSettById('wilkes', 'wilkes');
  }
  constructor(public userService: TuiUserService, public tuiAppService: TuiAppService) {
    // 公司建模员_内网威胁    PF7eAISvTH---Ok95ovqLA
    // 现场普通用户          Ze1jgkaFRyWy6Pjp200HAQ
    // 现场管理员            uD1685g4SV2eYYZl2Fb9Yw
    this.jurisdiction();
  }

  jurisdiction() {
    if (this.viewSett?.isIntegrated === 'true') {
      try {
        const testStr = this.userService.curUser['roles'].map((e: any) => e.roleCode).toString();
        if (testStr.indexOf('wilkes_role_3') !== -1) {
          this.roleId = '建模员';
        } else if (testStr.indexOf('wilkes_role_1') !== -1) {
          this.roleId = '普通用户';
        } else if (testStr.indexOf('wilkes_role_2') !== -1) {
          this.roleId = '管理员';
        } else {
          this.roleId = '普通用户';
        }
      } catch (error) {
        this.roleId = '建模员';
      }
    } else {
      // 如果id无法判断默认是建模员保证可以正常使用
      this.roleId = '建模员';
    }
  }
}
