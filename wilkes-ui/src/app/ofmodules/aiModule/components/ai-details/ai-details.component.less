@import url("~@tui/component-library/src/style/mixin/mixin"); //  引入统一方法与变量

:host {
  .ai-details {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 99;

    button[disabled] {
      color: #d9d9d9 !important;
      background: #fff !important;
    }

    .play-line-block {
      display: inline-block;
      vertical-align: top;
    }

    .ai-d-content {
      position: absolute;
      right: 0;
      left: 0;
      padding: 0 16px;
    }

    .ai-d-header {
      top: 1px;
      height: 50px;
      box-shadow: 0 0 5px 5px #e0e0e0;

      .ai-d-h-content {
        display: inline-block;
        vertical-align: top;
      }

      .ai-d-h-name {
        height: 50px;
        font-size: 18px;
        line-height: 50px;
        color: #2f2e2e;
      }

      .ai-d-h-operation {
        display: flex;
        align-items: center;
        float: right;
        height: 50px;
        text-align: center;

        button {
          margin-right: 15px;
          line-height: normal;

          &:nth-child(2) {
            margin: 0 15px;
          }

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .ai-d-body {
      top: 50px;
      bottom: 0;
      padding: 8px 8px 0;
      background-color: #f3f4f6;

      .ai-d-b-content {
        height: 100%;
        overflow-y: auto;
        vertical-align: top;
      }

      .ai-l-top {
        height: 60%;
        min-height: 420px;
        background-color: #f7f7f7;
      }

      .ai-l-bottom {
        height: calc(40% - 10px);
        padding-top: 10px;

        .ai-l-b-header {
          height: 20px;
          padding-left: 14px;
          margin-bottom: 10px;
          overflow: hidden;
          font-size: 16px;
          font-weight: 600;
          line-height: 20px;
          border-left: solid 3px #36b3f3;
        }

        .ai-l-b-body {
          display: flex;
          min-height: calc(100% - 20px);
          padding: 15px 20px;
          margin-top: 13px;
          background-color: #fff;
          border: 1px solid #dfdfdf;

          .divider-vertical {
            position: relative;
            top: -0.06em;
            display: inline-block;
            margin: 0 16px;
            vertical-align: middle;
            border-top: 0;
            border-left: 1px solid rgb(0 0 0 / 6%);
          }

          .data-model {
            & > li {
              padding: 10px 0;

              .d-m-header {
                width: 85px;
                height: 30px;
                font-size: 14px;
                line-height: 30px;
                color: #505050;
                text-align: right;

                i {
                  display: inline-block; /* padding-left: 100%; */
                  width: 100%;
                }
              }

              .d-m-c {
                display: inline-block;
                vertical-align: top;

                & > li {
                  margin-bottom: 10px;
                }
              }

              .d-m-c-button {
                width: 86px;
                height: 30px;
                margin-bottom: 5px;
                line-height: 30px;
                color: #fff;
                text-align: center;
                background-size: 100%;
              }
            }
          }
        }
      }

      .ai-d-b-right {
        width: 360px;
        // margin-left: 5px;
        border-radius: 2px;
      }
    }
  }

  .d-m-c-sHeader {
    padding: 0 10px;
  }

  .ai-conditions {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 1001;
    width: 560px;
    background-color: #fff;
    border: 1px solid #dfdfdf;
  }

  .fliter-con {
    margin: 0 10px;

    .data-model {
      & > li {
        .d-m-c {
          display: block;
          text-align: left;
        }
      }
    }

    .bottom-b {
      border-bottom: 1px solid #eee;
    }

    .title-con {
      height: 30px;
      line-height: 38px;

      span {
        font-size: 15px;
        font-weight: bold;
        color: #1aa3e9;
      }
    }
  }

  .data-model {
    & > li {
      padding: 7px 0;

      .icon-con {
        margin-left: 10px;
        font-size: 16px;
        color: #83de12;
        vertical-align: middle;
      }

      .d-m-header {
        min-width: 100px;
        height: 30px;
        font-size: 14px;
        line-height: 30px;
        color: #505050;
        text-align: justify;

        i {
          display: inline-block;

          /* padding-left: 100%; */
          width: 100%;
        }
      }

      .d-m-c {
        display: inline-block;
        vertical-align: top;

        .w-balse {
          width: 390px;
        }

        app-interval-time {
          display: inline-block;
        }

        .d-m-c-sHeader {
          padding: 0 10px;
        }
      }

      .height-con {
        font-size: 14px;
        color: #1aa3e9;
        cursor: pointer;
      }

      .soures-con {
        margin-left: 10px;
        vertical-align: text-top;
      }

      .height-dis {
        color: #bfbfbf;
      }

      .line-c {
        height: 27px;
        line-height: 29px;
      }

      .d-m-c-button {
        width: 86px;
        height: 30px;
        margin-bottom: 5px;
        line-height: 30px;
        text-align: center;
        background-size: 100%;
      }
    }
  }

  .g-k-dialog ul {
    height: 100%;
    overflow-y: auto;
  }

  .g-k-dialog {
    width: 630px;
    min-height: 200px;
    max-height: 600px;
  }

  .g-k-dialog-list {
    display: inline-block;
    height: 32px;
    line-height: 32px;
  }

  .g-k-dialog ul li {
    height: 32px;
    margin: 10px;
  }

  .g-k-dialog-tit {
    width: 260px;
    text-align: center;
    border: 1px solid #d9d9d9;
    border-radius: 3px;
  }

  .g-k-dialog-icon {
    margin: 0 30px;
  }

  .g-k-dialog-c {
    width: 260px;
  }

  .t-con {
    display: flex;
    align-items: center;
    margin-left: 20px;
    cursor: default;
  }

  t-multiselect ::ng-deep .ui-multiselect {
    width: 670px !important;
  }

  ::ng-deep .ui-accordion {
    border: none !important;

    t-accordiontab {
      display: block;
      border-bottom: 1px solid #d9d9d9 !important;
    }

    .ui-accordion-header > a {
      font-size: 16px !important;
      border: none !important;
    }

    .ui-accordion-content {
      border: none !important;
    }
  }

  .info-title {
    flex: auto;
    overflow: hidden;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.5715;
    color: #000000d9;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .info-view {
    width: 100%;
    margin-bottom: 20px;
    border: 1px solid #f0f0f0;
    border-radius: 2px;

    > table {
      width: 100%;
      table-layout: auto;
      border-collapse: collapse;
    }
  }

  .info-label,
  .info-content {
    padding: 16px 24px;
    border-right: 1px solid #f0f0f0;
  }

  .info-label {
    width: 150px;
    background-color: #fafafa;
  }

  .info-row {
    border-bottom: 1px solid #f0f0f0;
  }

  ::ng-deep .ui-accordion-header > a {
    color: @primary-color !important;
  }

  ::ng-deep .rule-splitButton {
    button {
      height: 32px;
      color: @primary-color;
      background-color: #fff;
      border-color: @primary-color !important;
    }

    ::ng-deep .ui-optionsbutton-menubutton {
      color: #fff !important;
      background-color: @primary-color !important;
    }

    .ui-button:enabled:hover {
      color: #fff;
      background-color: @primary-color;
      border-color: @primary-color;
    }
  }
}
