:host {
  .recall-result {
    height: calc(100% - 60px);
    padding: 10px;
    background-color: #fff;

    .json-view {
      .json-include-box {
        margin-left: 20px;

        .item-json-key {
          margin-right: 6px;
          color: #98a4af;
        }

        .item-json-value {
          word-break: break-all;
        }

        .fold {
          display: inline-block;
          width: 13px;
          height: 13px;
          margin-right: 4px;
          line-height: 9px;
          text-align: center;
          vertical-align: text-bottom;
          cursor: pointer;
          border: 1px solid #98a4af;
        }

        .arr-value {
          margin-top: 4px;
          margin-left: 20px;
        }
      }
    }

    .table-view {
      .table-view-item {
        display: flex;
        flex-flow: row wrap;
        justify-content: flex-start;

        & > li {
          display: flex;
          flex-flow: row nowrap;
          justify-content: flex-start;
          width: 25%;
          border: 1px solid #e3e6e8;

          &:nth-child(n + 5) {
            border-top: none;
          }

          &:not(:nth-child(4n)) {
            border-right: none;
          }

          &:last-child {
            border-right: 1px solid #e3e6e8;
          }

          .tableItem-name {
            width: 154px;
            height: 44px;
            padding: 10px;
            overflow: hidden;
            line-height: 24px;
            text-align: right;
            text-overflow: ellipsis;
            white-space: nowrap;
            background: #f2f4f5;
            border-right: 1px solid #e3e6e8;
          }

          .tableItem-value {
            max-width: calc(100% - 154px);
            padding: 0 10px;
            overflow: hidden;
            line-height: 44px;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .retrospect-result-con {
    position: relative;
    height: 100%;

    .retrospect-detail {
      position: relative;
      display: flex;
      align-items: center;
      height: 50px;
      margin-bottom: 10px;
      background: #fff;

      span {
        padding: 0 14px;
        font-size: 18px;
        color: rgb(51 51 51);
      }

      button {
        position: absolute;
        right: 20px;
      }
    }
  }
}
