package com.topsec.wilkes;


import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.File;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> (<EMAIL>)
 */
@Slf4j
@ServletComponentScan
@SpringBootApplication(exclude = GsonAutoConfiguration.class)
@EnableDiscoveryClient
@EnableScheduling
@EnableJpaRepositories(basePackages = {"com.topsec"})
@EnableJpaAuditing
@EnableFeignClients(basePackages = {"com.topsec.wilkes", "com.topsec.dm", "com.topsec.minsky", "com.topsec.scheduler", "com.topsec.pangu.portal.audit.feign", "com.topsec.hamming"})
@ComponentScan(value = {"com.topsec.scheduler", "com.topsec.dm", "com.topsec.blum", "com.topsec.minsky", "com.topsec.wilkes",
    "com.topsec.pangu.portal.audit", "com.topsec.ti", "com.topsec.flyway"})
@EntityScan(basePackages = {"com.topsec"})
@EnableAspectJAutoProxy(exposeProxy = true)
public class WilkesApplication extends SpringBootServletInitializer {
  static {
    try {
      System.getProperties().load(WilkesApplication.class.getResourceAsStream("/git.properties"));
    } catch (Exception e) {
      log.info("The file git.properties currently not exist !");
    }
  }

  public static void main(String[] args) {
    SpringApplication.run(WilkesApplication.class);
  }

  @Override
  protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
    return application.sources(WilkesApplication.class).profiles("default", getDatasourceName(), getProductName(), "update");
  }

  public static String getDatasourceName() {
    String datasourceName = "default";
    if (!Strings.isNullOrEmpty(System.getenv("DB_TYPE"))) {
      String dbType = System.getenv("DB_TYPE");
      if ("postgreSQL".equalsIgnoreCase(dbType) || "openGauss".equalsIgnoreCase(dbType)) {
        datasourceName = "PostgreSQL";
      } else if ("dm8".equalsIgnoreCase(dbType) || "dm".equalsIgnoreCase(dbType)) {
        datasourceName = "DmSQL";
      } else {
        datasourceName = "MySQL";
      }
    } else {
      datasourceName = "MySQL";
    }
    return datasourceName;
  }

  public static String getProductName() {
    String productName = "Wilkes";
    if (!Strings.isNullOrEmpty(System.getenv("TOP_PRODUCT"))) {
      productName = System.getenv("TOP_PRODUCT");
    } else {
      String catalinaBase = System.getProperty("catalina.base");
      if (!Strings.isNullOrEmpty(catalinaBase)) {
        String name = new File(catalinaBase).getParentFile().getParentFile().getName();
        Matcher matcher = Pattern.compile("([a-zA-Z]+)").matcher(name);
        if (matcher.find()) {
          productName = matcher.group(1);
        }
      }
    }
    return productName;
  }
}
