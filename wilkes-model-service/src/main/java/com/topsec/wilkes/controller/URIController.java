package com.topsec.wilkes.controller;

import com.topsec.dm.domain.StorageType;
import com.topsec.dm.service.FactTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: dy
 * @time: 2022/12/15 4:30 下午
 */
@Slf4j
@RestController
public class URIController {

  @Autowired
  private FactTableService factTableService;

  @ResponseBody
  @RequestMapping(value = "/getKafkaURI/{topic}", method = RequestMethod.POST)
  public String getKafkaURI(@PathVariable(value = "topic")String topic) {
    return factTableService.getFactTableStorageInfo(topic, StorageType.DATABASE_KAFKA).toURL(true);
  }

}
