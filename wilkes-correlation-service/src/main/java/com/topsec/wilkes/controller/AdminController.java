package com.topsec.wilkes.controller;

import com.topsec.pangu.portal.audit.annotation.AuditOperation;
import com.topsec.pangu.portal.common.enums.OperationType;
import com.topsec.wilkes.common.dto.Rule;
import com.topsec.wilkes.service.RuleService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> (<EMAIL>)
 */
@RestController
@RequestMapping("/admin")
public class AdminController {

  @Autowired
  private RuleService ruleService;

  @RequestMapping(value = "/rules", method = RequestMethod.GET)
  @ResponseBody
  @AuditOperation(description = "查看模型后台管理页面", operationType = OperationType.QUERY)
  public List<Rule> getRules() {
    return ruleService.getRules();
  }
}
