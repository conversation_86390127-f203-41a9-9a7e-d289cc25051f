const webpack = require('@tui/frame/webpack.config.js');
const fs = require('fs');

webpack.plugins.push(new FilePlugin());
module.exports = webpack;

// 解决打包后哈希未变造成的浏览器缓存问题
// eslint-disable-next-line prettier/prettier
function FilePlugin() { }
FilePlugin.prototype.apply = function (compiler) {
  compiler.hooks.emit.tap('FilePlugin', (e) => {
    const output = compiler.path || e.options.output.path;
    fs.readdir(output, (err, files) => {
      fileFun(
        files.filter((fileName) => ['html', 'json'].find((name) => new RegExp(name).test(fileName))),
        output,
      );
    });
  });
};

function fileFun(fileNames, url) {
  fileNames.forEach((name) => {
    try {
      fs.readFile(`${url}/${name}`, 'utf-8', (err, data) => {
        if (err) throw err;
        if (new RegExp('html').test(name)) {
          data = data
            .replace(/\?d=[0-9]*/g, '')
            .replace(/\.css/g, `.css?d=${new Date().getTime()}`)
            .replace(/\.js/g, `.js?d=${new Date().getTime()}`);
        } else {
          data = data.replace(/\.js",/g, `.js?d=${new Date().getTime()}",`);
        }
        fs.writeFile(url + '/' + name, new Uint8Array(Buffer.from(data)), (wErr) => {
          if (wErr) throw wErr;
        });
      });
    } catch (error) {
      console.log(error);
    }
  });
}
