package com.topsec.wilkes.behaviour.service.impl;

import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.behaviour.dto.portrait.PortraitInfo;
import com.topsec.wilkes.behaviour.service.impl.util.ResourcesUtil;

import org.junit.Test;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class BehaviourPortraitServiceImplTest {
  private static final String PORTRAIT_BASE_INFO = "/com/topsec/wilkes/behaviour/parameter/portrait-base-info.json";

  @Test
  public void getPortraitInfo() {
    Map<String, PortraitInfo> portraitInfoMap = ResourcesUtil.getPortraitBaseInfo(PORTRAIT_BASE_INFO).stream()
        .map(object -> ToolUtils.OBJECT_MAPPER.convertValue(object, PortraitInfo.class))
        .collect(Collectors.toMap(PortraitInfo::getType, portraitInfo -> portraitInfo));
    System.out.println(portraitInfoMap.keySet());
  }

  @Test
  public void xxx() {
    System.out.println(String.format("com/topsec/wilkes/operator/elasticsearch/mapping/%s.json", "aaa"));
  }
}