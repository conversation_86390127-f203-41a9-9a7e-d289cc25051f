package com.topsec.wilkes.behaviour.config;

/**
 * <AUTHOR> (<EMAIL>)
 * @Description 行为分析度量类型常量类
 */
public class BehaviourMeasureTypeConfig {
  //求和
  public static final String SUM = "SUM";
  //计数
  public static final String COUNT = "COUNT";
  //去重计数
  public static final String COUNT_DISTINCT = "COUNT_DISTINCT";
  //最大值
  public static final String MAX = "MAX";
  //最小值
  public static final String MIN = "MIN";
  //平均值
  public static final String AVG = "AVG";
  //频繁项
  public static final String COLLECT_SET = "COLLECT_SET";
  //分组统计
  public static final String GROUP_STATISTIC = "group_items";
  //自定义
  public static final String CUSTOM = "CUSTOM";
}
