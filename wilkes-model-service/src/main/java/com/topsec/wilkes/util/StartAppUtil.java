package com.topsec.wilkes.util;

/**
 * <AUTHOR>
 */
public class StartAppUtil {
  private static boolean checked = true;
  private static boolean started = false;

  public static boolean getChecked() {
    return checked;
  }

  public static void setChecked(boolean isPass) {
    checked = isPass;
  }

  public static boolean getStarted() {
    return started;
  }

  public static void setStarted(boolean isOver) {
    started = isOver;
  }
}
