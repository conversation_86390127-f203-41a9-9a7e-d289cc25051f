package com.topsec.wilkes.behaviour.service;

import com.topsec.wilkes.behaviour.dto.definition.BehaviourDefinition;
import com.topsec.wilkes.behaviour.dto.definition.DetectionAlgorithm;
import com.topsec.wilkes.behaviour.dto.task.StorageInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface BehaviourAlgorithmService {

  List<DetectionAlgorithm> getAlgorithms();

  List<DetectionAlgorithm> getAlgorithms(BehaviourDefinition behaviourDefinition);

  List<Object> getAlgorithmParameter();

  StorageInfo getStorageInfo(String id);
}
