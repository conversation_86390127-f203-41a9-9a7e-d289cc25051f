package wilkes;


import com.topsec.wilkes.util.OperatorDefinition;
import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR>
 */
public class GetOperatorDefinitionTest {

  @Test
  public void testGetOperatorDefinition() {
    List<OperatorDefinition> operatorDefinitionList = OperatorDefinition.getAllOperatorDefinition();
    operatorDefinitionList.forEach(operatorDefinition ->
        System.out.println(operatorDefinition.getGroup() + " : " + operatorDefinition.getName() + " : " + operatorDefinition.getClassName())
    );


    System.out.println("-------------------");

    List<OperatorDefinition> publicOperatorDefinition = OperatorDefinition.getVisibleOperatorDefinition();
    publicOperatorDefinition.forEach(operatorDefinition ->
        System.out.println(operatorDefinition.getGroup() + " : " + operatorDefinition.getName() + " : " + operatorDefinition.getClassName())
    );
  }

  @Test
  public void testGetOperatorDefinition1() {
    OperatorDefinition operatorDefinition = OperatorDefinition.getAllOperatorDefinition().get(0);
//    Assert.assertEquals("在《一段时间》内，对于相同《字段》，符合《条件》的事件", operatorDefinition.getName());
//    Assert.assertEquals("field-aggregation", operatorDefinition.getId());
//    Assert.assertEquals("com.topsec.wilkes.operator.AggregationOperator", operatorDefinition.getClassName());
  }
}
