import { Component, OnInit, ViewChild, AfterViewInit, ElementRef } from '@angular/core';
import { RuleService } from '../../../rules/services/rule.service';
import { Routingname } from '../../../../routing/routingname.enum';
import { MessageService } from '@tui/component-library';
import { TuiAppService, TuiNavService, TuiUserService } from '@tui/frame';
import { Router } from '@angular/router';

@Component({
  selector: 'app-rules-manage',
  templateUrl: './manage-admin.component.html',
  styleUrls: ['./manage-admin.component.less'],
})
export class RulesManageComponent implements OnInit, AfterViewInit {
  @ViewChild('manageCon') manageCon: ElementRef;
  @ViewChild('table') table: any;
  searchObj: any; // 筛选对象
  statusOptions: any[]; // 状态数组
  sourceOptions: any[]; // 来源组
  cols: any[]; // 表格头
  listData: any[]; // 表格数据
  listDataCopy: any[]; //  表格数据副本
  selecteds: any[]; //  暂时没用
  scrollHeight: any; // 表格滚动高度
  resize: any; //  监听table滚动区域
  serviceUrl: any; //  导入url
  jumpFilds: any[]; // 能跳转的字段
  delDisabled = true; // 批量删除禁用
  first = 0;

  constructor(private router: Router, private ruleService: RuleService, private tuiAppService: TuiAppService, private messageService: MessageService, private tuiNavService: TuiNavService, private tuiUserService: TuiUserService) {}

  ngOnInit() {
    this.variateInit();
  }

  ngAfterViewInit() {
    // this.scrollHeight = dom.clientHeight - 232 + 'px';
    // this.resize = fromEvent(window, 'resize')
    //   .subscribe(() => {
    //     this.scrollHeight = (this.manageCon.nativeElement.clientHeight - 180) + 'px';
    //   });
  }

  variateInit() {
    this.searchObj = {
      name: '',
      status: '',
      source: '',
    };
    this.statusOptions = [
      { label: '启用', value: 'enabled' },
      { label: '启用中', value: 'enabling' },
      { label: '启用失败', value: 'unenabled' },
      { label: '停用', value: 'disabled' },
      { label: '停用中', value: 'disabling' },
      { label: '停用失败', value: 'undisabled' },
    ];
    this.cols = [
      { field: 'no', header: '编号' },
      { field: 'name', header: '规则名称' },
      { field: 'source', header: '来源' },
      { field: 'showStatus', header: '状态' },
      { field: 'operation', header: '操作' },
    ];
    this.selecteds = [];
    this.jumpFilds = ['no', 'name'];
    this.processListData();
    this.processDropData();
    this.serviceUrl = this.tuiAppService.tuiApp.serviceUrl.rules + '/rules/import';
  }

  processListData() {
    // 列表数据获取后处理
    this.ruleService.getAdminList().subscribe((res) => {
      this.listData = res;
      this.listDataCopy = res;
      this.listData.forEach((item) => this.getShowStatus(item));
    });
  }

  getShowStatus(e) {
    switch (e.status) {
      case 'enabled':
        e['showStatus'] = '启用';
        break;
      case 'enabling':
        e['showStatus'] = '启用中';
        break;
      case 'unenabled':
        e['showStatus'] = '启用失败';
        break;
      case 'disabled':
        e['showStatus'] = '停用';
        break;
      case 'disabling':
        e['showStatus'] = '停用中';
        break;
      case 'undisabled':
        e['showStatus'] = '停用失败';
        break;
      default:
        break;
    }
  }

  processDropData() {
    // 来源下拉数据获取后处理成组件库需要的格式(可沟通后端修改返回格式)
    this.ruleService.getSourceTag().subscribe((res) => {
      this.sourceOptions = res.map((item) => ({ label: item, value: item }));
    });
  }

  // 跳转到manageyemian
  goManage1(id: string) {
    this.router.navigateByUrl(Routingname.R + '/' + Routingname.R_N_L + '/edit/' + id);
  }

  searchRules(pType?: any) {
    if (!this.searchObj.name && !this.searchObj.status && !this.searchObj.source) {
      this.listData = this.listDataCopy.slice();
      this.searchObj.name = '';
      this.searchObj.status = '';
      this.searchObj.source = '';
      return;
    }
    if (!this.searchObj[pType]) {
      this.searchObj[pType] = '';
    }
    let data = [];
    this.searchObj.name = this.searchObj.name.trim();
    const filterFunc = (item) => {
      const hasStatus = item.status === this.searchObj.status;
      const hasSource = item.source === this.searchObj.source;
      const hasNameOrId = item.name.indexOf(this.searchObj.name) > -1 || item.no.indexOf(this.searchObj.name) > -1;
      return (this.searchObj.name !== '' ? hasNameOrId : true) && (this.searchObj.status !== '' ? hasStatus : true) && (this.searchObj.source !== '' ? hasSource : true);
    };
    data = this.listDataCopy.filter((item) => filterFunc(item));
    this.listData = [...data];
  }

  uploadSuccess(e) {
    this.messageService.add({
      key: 'rules-rule',
      severity: 'success',
      summary: '提示',
      detail: '导入成功!',
    });
    this.processListData();
  }

  uploadError(e) {
    this.messageService.add({
      key: 'rules-rule',
      severity: 'error',
      summary: '提示',
      detail: e.error.error.message,
    });
  }

  // 批量删除
  deleteRules() {
    const p = this.selecteds.map((e) => e['id']).toString();
    this.ruleService.deleteRule(p).subscribe(
      (res) => {
        this.messageService.add({
          key: 'rules-rule',
          severity: 'success',
          summary: '提示',
          detail: '批量删除成功!',
        });
        this.processListData();
      },
      () => {
        this.messageService.add({
          key: 'rules-rule',
          severity: 'error',
          summary: '提示',
          detail: '批量删失败,请重试!',
        });
      },
    );
  }

  // 选择
  select() {
    this.delDisabled = this.selecteds.length < 1 || this.selecteds.some((e) => e['source'] === '内置' || new RegExp('en', 'i').test(e['status']));
  }

  checkAll(e) {
    const end = this.table.totalRecords - this.table.first > this.table.rows ? this.table.first + 20 : this.table.totalRecords;
    if (e.checked) {
      this.selecteds = this.listData.slice(this.table.first, end);
    }
    this.select();
  }

  ruleExport() {
    const p = this.selecteds.map((e) => e['id']).toString() || 'all';
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = `${this.tuiAppService.tuiApp.serviceUrl.rules}/rules/export/${p}?principal=${this.tuiUserService.curUser.principal}`;
    link.setAttribute('download', '');
    document.body.appendChild(link);
    link.click();
  }

  retry() {
    const o = {
      enabled: [],
      disabled: [],
    };
    const fun = (d, s) => {
      // this.ruleService.enableRule(d, s).subscribe((res) => {
      //   this.listData.forEach((e) => {
      //     e.status = (res.find((re) => re.id === e.id) || { status: e.status }).status;
      //     this.getShowStatus(e);
      //   });
      //   this.listData = [...this.listData];
      // });
    };
    this.listData.forEach((e) => {
      let sta = e.status;
      const isNo = new RegExp('un', 'i').test(sta);
      if (isNo) {
        sta = sta.replace('un', '').toLocaleLowerCase();
        o[sta].push(e.id);
      }
    });
    for (const key in o) {
      if (o.hasOwnProperty(key)) {
        const e = o[key];
        e.length > 0 && fun(e.toString(), key.toLocaleUpperCase());
      }
    }
  }

  changePage() {
    this.selecteds = [];
  }

  setFirst() {
    this.first = -1;
    setTimeout(() => {
      this.first = 0;
    }, 0);
  }

  lookJob(id) {
    window.open(`${this.ruleService.serviceUrl}/rules/jobInfo/${id}`);
    // this.ruleService.lookJob(id).subscribe(d => {
    //   console.log('查看');
    // });
  }
}
