package com.topsec.wilkes.advice;

import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.common.dto.Rule;
import com.topsec.wilkes.dto.platformlevel.UpPlatformCondition;
import com.topsec.wilkes.platform.service.PlatformService;
import com.topsec.wilkes.util.AESUtils;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
@Aspect
@Conditional(UpPlatformCondition.class)
public class RuleUpPlatformAdvice {
  private static final String PLATFORMID = "platformId";

  @Autowired
  PlatformService platformService;

  @Pointcut("execution(* com.topsec.wilkes.controller.RuleController.getRule(..))")
  public void getModel() {
  }

  @Around(value = "getModel()")
  public Object getModelEnhanced(ProceedingJoinPoint point) throws Throwable {
    HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
    String platformId = request.getHeader(PLATFORMID);
    Object result = null;
    if (!Strings.isNullOrEmpty(platformId)) {  //取消分发的情况
      String modelNo = point.getArgs()[0].toString();
      result = platformService.getModelWithRule(platformId, modelNo);
      return ToolUtils.OBJECT_MAPPER.readValue(AESUtils.decrypt(result.toString()), Rule.class);
    } else {
      return point.proceed();
    }
  }
}
