package com.topsec.wilkes.repository;

import com.topsec.wilkes.repository.entity.ModelHistoryEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface ModelHistoryRepository extends JpaRepository<ModelHistoryEntity, String>,
    JpaSpecificationExecutor<ModelHistoryEntity> {

  List<ModelHistoryEntity> findAllByNo(String no);

  @Query(value = "SELECT * from model_history mt where mt.no=?1 and mt.last_updated_time >=?2 order by mt.last_updated_time",nativeQuery = true)
  List<ModelHistoryEntity> findByNoAndLastUpdatedTimeBefore(String no, Date time);
}
