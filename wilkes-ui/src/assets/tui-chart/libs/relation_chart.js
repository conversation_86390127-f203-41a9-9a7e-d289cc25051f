!function(t){var n={};function e(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,e),i.l=!0,i.exports}e.m=t,e.c=n,e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{enumerable:!0,get:r})},e.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},e.t=function(t,n){if(1&n&&(t=e(t)),8&n)return t;if(4&n&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(e.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&n&&"string"!=typeof t)for(var i in t)e.d(r,i,function(n){return t[n]}.bind(null,i));return r},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,n){return Object.prototype.hasOwnProperty.call(t,n)},e.p="",e(e.s=7)}([function(t,n,e){"use strict";e.r(n);var r=function(t,n){return t<n?-1:t>n?1:t>=n?0:NaN},i=function(t){var n;return 1===t.length&&(n=t,t=function(t,e){return r(n(t),e)}),{left:function(n,e,r,i){for(null==r&&(r=0),null==i&&(i=n.length);r<i;){var o=r+i>>>1;t(n[o],e)<0?r=o+1:i=o}return r},right:function(n,e,r,i){for(null==r&&(r=0),null==i&&(i=n.length);r<i;){var o=r+i>>>1;t(n[o],e)>0?i=o:r=o+1}return r}}};var o=i(r),u=o.right,a=o.left,c=u,f=function(t,n){null==n&&(n=s);for(var e=0,r=t.length-1,i=t[0],o=new Array(r<0?0:r);e<r;)o[e]=n(i,i=t[++e]);return o};function s(t,n){return[t,n]}var l=function(t,n,e){var r,i,o,u,a=t.length,c=n.length,f=new Array(a*c);for(null==e&&(e=s),r=o=0;r<a;++r)for(u=t[r],i=0;i<c;++i,++o)f[o]=e(u,n[i]);return f},h=function(t,n){return n<t?-1:n>t?1:n>=t?0:NaN},d=function(t){return null===t?NaN:+t},p=function(t,n){var e,r,i=t.length,o=0,u=-1,a=0,c=0;if(null==n)for(;++u<i;)isNaN(e=d(t[u]))||(c+=(r=e-a)*(e-(a+=r/++o)));else for(;++u<i;)isNaN(e=d(n(t[u],u,t)))||(c+=(r=e-a)*(e-(a+=r/++o)));if(o>1)return c/(o-1)},g=function(t,n){var e=p(t,n);return e?Math.sqrt(e):e},v=function(t,n){var e,r,i,o=t.length,u=-1;if(null==n){for(;++u<o;)if(null!=(e=t[u])&&e>=e)for(r=i=e;++u<o;)null!=(e=t[u])&&(r>e&&(r=e),i<e&&(i=e))}else for(;++u<o;)if(null!=(e=n(t[u],u,t))&&e>=e)for(r=i=e;++u<o;)null!=(e=n(t[u],u,t))&&(r>e&&(r=e),i<e&&(i=e));return[r,i]},y=Array.prototype,b=y.slice,m=y.map,_=function(t){return function(){return t}},x=function(t){return t},w=function(t,n,e){t=+t,n=+n,e=(i=arguments.length)<2?(n=t,t=0,1):i<3?1:+e;for(var r=-1,i=0|Math.max(0,Math.ceil((n-t)/e)),o=new Array(i);++r<i;)o[r]=t+r*e;return o},M=Math.sqrt(50),k=Math.sqrt(10),N=Math.sqrt(2),S=function(t,n,e){var r,i,o,u,a=-1;if(e=+e,(t=+t)===(n=+n)&&e>0)return[t];if((r=n<t)&&(i=t,t=n,n=i),0===(u=T(t,n,e))||!isFinite(u))return[];if(u>0)for(t=Math.ceil(t/u),n=Math.floor(n/u),o=new Array(i=Math.ceil(n-t+1));++a<i;)o[a]=(t+a)*u;else for(t=Math.floor(t*u),n=Math.ceil(n*u),o=new Array(i=Math.ceil(t-n+1));++a<i;)o[a]=(t-a)/u;return r&&o.reverse(),o};function T(t,n,e){var r=(n-t)/Math.max(0,e),i=Math.floor(Math.log(r)/Math.LN10),o=r/Math.pow(10,i);return i>=0?(o>=M?10:o>=k?5:o>=N?2:1)*Math.pow(10,i):-Math.pow(10,-i)/(o>=M?10:o>=k?5:o>=N?2:1)}function A(t,n,e){var r=Math.abs(n-t)/Math.max(0,e),i=Math.pow(10,Math.floor(Math.log(r)/Math.LN10)),o=r/i;return o>=M?i*=10:o>=k?i*=5:o>=N&&(i*=2),n<t?-i:i}var C=function(t){return Math.ceil(Math.log(t.length)/Math.LN2)+1},E=function(){var t=x,n=v,e=C;function r(r){var i,o,u=r.length,a=new Array(u);for(i=0;i<u;++i)a[i]=t(r[i],i,r);var f=n(a),s=f[0],l=f[1],h=e(a,s,l);Array.isArray(h)||(h=A(s,l,h),h=w(Math.ceil(s/h)*h,l,h));for(var d=h.length;h[0]<=s;)h.shift(),--d;for(;h[d-1]>l;)h.pop(),--d;var p,g=new Array(d+1);for(i=0;i<=d;++i)(p=g[i]=[]).x0=i>0?h[i-1]:s,p.x1=i<d?h[i]:l;for(i=0;i<u;++i)s<=(o=a[i])&&o<=l&&g[c(h,o,0,d)].push(r[i]);return g}return r.value=function(n){return arguments.length?(t="function"==typeof n?n:_(n),r):t},r.domain=function(t){return arguments.length?(n="function"==typeof t?t:_([t[0],t[1]]),r):n},r.thresholds=function(t){return arguments.length?(e="function"==typeof t?t:Array.isArray(t)?_(b.call(t)):_(t),r):e},r},L=function(t,n,e){if(null==e&&(e=d),r=t.length){if((n=+n)<=0||r<2)return+e(t[0],0,t);if(n>=1)return+e(t[r-1],r-1,t);var r,i=(r-1)*n,o=Math.floor(i),u=+e(t[o],o,t);return u+(+e(t[o+1],o+1,t)-u)*(i-o)}},R=function(t,n,e){return t=m.call(t,d).sort(r),Math.ceil((e-n)/(2*(L(t,.75)-L(t,.25))*Math.pow(t.length,-1/3)))},D=function(t,n,e){return Math.ceil((e-n)/(3.5*g(t)*Math.pow(t.length,-1/3)))},P=function(t,n){var e,r,i=t.length,o=-1;if(null==n){for(;++o<i;)if(null!=(e=t[o])&&e>=e)for(r=e;++o<i;)null!=(e=t[o])&&e>r&&(r=e)}else for(;++o<i;)if(null!=(e=n(t[o],o,t))&&e>=e)for(r=e;++o<i;)null!=(e=n(t[o],o,t))&&e>r&&(r=e);return r},z=function(t,n){var e,r=t.length,i=r,o=-1,u=0;if(null==n)for(;++o<r;)isNaN(e=d(t[o]))?--i:u+=e;else for(;++o<r;)isNaN(e=d(n(t[o],o,t)))?--i:u+=e;if(i)return u/i},O=function(t,n){var e,i=t.length,o=-1,u=[];if(null==n)for(;++o<i;)isNaN(e=d(t[o]))||u.push(e);else for(;++o<i;)isNaN(e=d(n(t[o],o,t)))||u.push(e);return L(u.sort(r),.5)},U=function(t){for(var n,e,r,i=t.length,o=-1,u=0;++o<i;)u+=t[o].length;for(e=new Array(u);--i>=0;)for(n=(r=t[i]).length;--n>=0;)e[--u]=r[n];return e},q=function(t,n){var e,r,i=t.length,o=-1;if(null==n){for(;++o<i;)if(null!=(e=t[o])&&e>=e)for(r=e;++o<i;)null!=(e=t[o])&&r>e&&(r=e)}else for(;++o<i;)if(null!=(e=n(t[o],o,t))&&e>=e)for(r=e;++o<i;)null!=(e=n(t[o],o,t))&&r>e&&(r=e);return r},I=function(t,n){for(var e=n.length,r=new Array(e);e--;)r[e]=t[n[e]];return r},B=function(t,n){if(e=t.length){var e,i,o=0,u=0,a=t[u];for(null==n&&(n=r);++o<e;)(n(i=t[o],a)<0||0!==n(a,a))&&(a=i,u=o);return 0===n(a,a)?u:void 0}},F=function(t,n,e){for(var r,i,o=(null==e?t.length:e)-(n=null==n?0:+n);o;)i=Math.random()*o--|0,r=t[o+n],t[o+n]=t[i+n],t[i+n]=r;return t},j=function(t,n){var e,r=t.length,i=-1,o=0;if(null==n)for(;++i<r;)(e=+t[i])&&(o+=e);else for(;++i<r;)(e=+n(t[i],i,t))&&(o+=e);return o},Y=function(t){if(!(i=t.length))return[];for(var n=-1,e=q(t,H),r=new Array(e);++n<e;)for(var i,o=-1,u=r[n]=new Array(i);++o<i;)u[o]=t[o][n];return r};function H(t){return t.length}var V=function(){return Y(arguments)},X=Array.prototype.slice,G=function(t){return t},W=1,$=2,Z=3,Q=4,J=1e-6;function K(t){return"translate("+(t+.5)+",0)"}function tt(t){return"translate(0,"+(t+.5)+")"}function nt(){return!this.__axis}function et(t,n){var e=[],r=null,i=null,o=6,u=6,a=3,c=t===W||t===Q?-1:1,f=t===Q||t===$?"x":"y",s=t===W||t===Z?K:tt;function l(l){var h=null==r?n.ticks?n.ticks.apply(n,e):n.domain():r,d=null==i?n.tickFormat?n.tickFormat.apply(n,e):G:i,p=Math.max(o,0)+a,g=n.range(),v=+g[0]+.5,y=+g[g.length-1]+.5,b=(n.bandwidth?function(t){var n=Math.max(0,t.bandwidth()-1)/2;return t.round()&&(n=Math.round(n)),function(e){return+t(e)+n}}:function(t){return function(n){return+t(n)}})(n.copy()),m=l.selection?l.selection():l,_=m.selectAll(".domain").data([null]),x=m.selectAll(".tick").data(h,n).order(),w=x.exit(),M=x.enter().append("g").attr("class","tick"),k=x.select("line"),N=x.select("text");_=_.merge(_.enter().insert("path",".tick").attr("class","domain").attr("stroke","currentColor")),x=x.merge(M),k=k.merge(M.append("line").attr("stroke","currentColor").attr(f+"2",c*o)),N=N.merge(M.append("text").attr("fill","currentColor").attr(f,c*p).attr("dy",t===W?"0em":t===Z?"0.71em":"0.32em")),l!==m&&(_=_.transition(l),x=x.transition(l),k=k.transition(l),N=N.transition(l),w=w.transition(l).attr("opacity",J).attr("transform",function(t){return isFinite(t=b(t))?s(t):this.getAttribute("transform")}),M.attr("opacity",J).attr("transform",function(t){var n=this.parentNode.__axis;return s(n&&isFinite(n=n(t))?n:b(t))})),w.remove(),_.attr("d",t===Q||t==$?u?"M"+c*u+","+v+"H0.5V"+y+"H"+c*u:"M0.5,"+v+"V"+y:u?"M"+v+","+c*u+"V0.5H"+y+"V"+c*u:"M"+v+",0.5H"+y),x.attr("opacity",1).attr("transform",function(t){return s(b(t))}),k.attr(f+"2",c*o),N.attr(f,c*p).text(d),m.filter(nt).attr("fill","none").attr("font-size",10).attr("font-family","sans-serif").attr("text-anchor",t===$?"start":t===Q?"end":"middle"),m.each(function(){this.__axis=b})}return l.scale=function(t){return arguments.length?(n=t,l):n},l.ticks=function(){return e=X.call(arguments),l},l.tickArguments=function(t){return arguments.length?(e=null==t?[]:X.call(t),l):e.slice()},l.tickValues=function(t){return arguments.length?(r=null==t?null:X.call(t),l):r&&r.slice()},l.tickFormat=function(t){return arguments.length?(i=t,l):i},l.tickSize=function(t){return arguments.length?(o=u=+t,l):o},l.tickSizeInner=function(t){return arguments.length?(o=+t,l):o},l.tickSizeOuter=function(t){return arguments.length?(u=+t,l):u},l.tickPadding=function(t){return arguments.length?(a=+t,l):a},l}function rt(t){return et(W,t)}function it(t){return et($,t)}function ot(t){return et(Z,t)}function ut(t){return et(Q,t)}var at={value:function(){}};function ct(){for(var t,n=0,e=arguments.length,r={};n<e;++n){if(!(t=arguments[n]+"")||t in r)throw new Error("illegal type: "+t);r[t]=[]}return new ft(r)}function ft(t){this._=t}function st(t,n){for(var e,r=0,i=t.length;r<i;++r)if((e=t[r]).name===n)return e.value}function lt(t,n,e){for(var r=0,i=t.length;r<i;++r)if(t[r].name===n){t[r]=at,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=e&&t.push({name:n,value:e}),t}ft.prototype=ct.prototype={constructor:ft,on:function(t,n){var e,r,i=this._,o=(r=i,(t+"").trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");if(e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),t&&!r.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:n}})),u=-1,a=o.length;if(!(arguments.length<2)){if(null!=n&&"function"!=typeof n)throw new Error("invalid callback: "+n);for(;++u<a;)if(e=(t=o[u]).type)i[e]=lt(i[e],t.name,n);else if(null==n)for(e in i)i[e]=lt(i[e],t.name,null);return this}for(;++u<a;)if((e=(t=o[u]).type)&&(e=st(i[e],t.name)))return e},copy:function(){var t={},n=this._;for(var e in n)t[e]=n[e].slice();return new ft(t)},call:function(t,n){if((e=arguments.length-2)>0)for(var e,r,i=new Array(e),o=0;o<e;++o)i[o]=arguments[o+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(o=0,e=(r=this._[t]).length;o<e;++o)r[o].value.apply(n,i)},apply:function(t,n,e){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var r=this._[t],i=0,o=r.length;i<o;++i)r[i].value.apply(n,e)}};var ht=ct,dt="http://www.w3.org/1999/xhtml",pt={svg:"http://www.w3.org/2000/svg",xhtml:dt,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},gt=function(t){var n=t+="",e=n.indexOf(":");return e>=0&&"xmlns"!==(n=t.slice(0,e))&&(t=t.slice(e+1)),pt.hasOwnProperty(n)?{space:pt[n],local:t}:t};var vt=function(t){var n=gt(t);return(n.local?function(t){return function(){return this.ownerDocument.createElementNS(t.space,t.local)}}:function(t){return function(){var n=this.ownerDocument,e=this.namespaceURI;return e===dt&&n.documentElement.namespaceURI===dt?n.createElement(t):n.createElementNS(e,t)}})(n)};function yt(){}var bt=function(t){return null==t?yt:function(){return this.querySelector(t)}};function mt(){return[]}var _t=function(t){return null==t?mt:function(){return this.querySelectorAll(t)}},xt=function(t){return function(){return this.matches(t)}},wt=function(t){return new Array(t.length)};function Mt(t,n){this.ownerDocument=t.ownerDocument,this.namespaceURI=t.namespaceURI,this._next=null,this._parent=t,this.__data__=n}Mt.prototype={constructor:Mt,appendChild:function(t){return this._parent.insertBefore(t,this._next)},insertBefore:function(t,n){return this._parent.insertBefore(t,n)},querySelector:function(t){return this._parent.querySelector(t)},querySelectorAll:function(t){return this._parent.querySelectorAll(t)}};var kt="$";function Nt(t,n,e,r,i,o){for(var u,a=0,c=n.length,f=o.length;a<f;++a)(u=n[a])?(u.__data__=o[a],r[a]=u):e[a]=new Mt(t,o[a]);for(;a<c;++a)(u=n[a])&&(i[a]=u)}function St(t,n,e,r,i,o,u){var a,c,f,s={},l=n.length,h=o.length,d=new Array(l);for(a=0;a<l;++a)(c=n[a])&&(d[a]=f=kt+u.call(c,c.__data__,a,n),f in s?i[a]=c:s[f]=c);for(a=0;a<h;++a)(c=s[f=kt+u.call(t,o[a],a,o)])?(r[a]=c,c.__data__=o[a],s[f]=null):e[a]=new Mt(t,o[a]);for(a=0;a<l;++a)(c=n[a])&&s[d[a]]===c&&(i[a]=c)}function Tt(t,n){return t<n?-1:t>n?1:t>=n?0:NaN}var At=function(t){return t.ownerDocument&&t.ownerDocument.defaultView||t.document&&t||t.defaultView};function Ct(t,n){return t.style.getPropertyValue(n)||At(t).getComputedStyle(t,null).getPropertyValue(n)}function Et(t){return t.trim().split(/^|\s+/)}function Lt(t){return t.classList||new Rt(t)}function Rt(t){this._node=t,this._names=Et(t.getAttribute("class")||"")}function Dt(t,n){for(var e=Lt(t),r=-1,i=n.length;++r<i;)e.add(n[r])}function Pt(t,n){for(var e=Lt(t),r=-1,i=n.length;++r<i;)e.remove(n[r])}Rt.prototype={add:function(t){this._names.indexOf(t)<0&&(this._names.push(t),this._node.setAttribute("class",this._names.join(" ")))},remove:function(t){var n=this._names.indexOf(t);n>=0&&(this._names.splice(n,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(t){return this._names.indexOf(t)>=0}};function zt(){this.textContent=""}function Ot(){this.innerHTML=""}function Ut(){this.nextSibling&&this.parentNode.appendChild(this)}function qt(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function It(){return null}function Bt(){var t=this.parentNode;t&&t.removeChild(this)}function Ft(){return this.parentNode.insertBefore(this.cloneNode(!1),this.nextSibling)}function jt(){return this.parentNode.insertBefore(this.cloneNode(!0),this.nextSibling)}var Yt={},Ht=null;"undefined"!=typeof document&&("onmouseenter"in document.documentElement||(Yt={mouseenter:"mouseover",mouseleave:"mouseout"}));function Vt(t,n,e){return t=Xt(t,n,e),function(n){var e=n.relatedTarget;e&&(e===this||8&e.compareDocumentPosition(this))||t.call(this,n)}}function Xt(t,n,e){return function(r){var i=Ht;Ht=r;try{t.call(this,this.__data__,n,e)}finally{Ht=i}}}function Gt(t){return function(){var n=this.__on;if(n){for(var e,r=0,i=-1,o=n.length;r<o;++r)e=n[r],t.type&&e.type!==t.type||e.name!==t.name?n[++i]=e:this.removeEventListener(e.type,e.listener,e.capture);++i?n.length=i:delete this.__on}}}function Wt(t,n,e){var r=Yt.hasOwnProperty(t.type)?Vt:Xt;return function(i,o,u){var a,c=this.__on,f=r(n,o,u);if(c)for(var s=0,l=c.length;s<l;++s)if((a=c[s]).type===t.type&&a.name===t.name)return this.removeEventListener(a.type,a.listener,a.capture),this.addEventListener(a.type,a.listener=f,a.capture=e),void(a.value=n);this.addEventListener(t.type,f,e),a={type:t.type,name:t.name,value:n,listener:f,capture:e},c?c.push(a):this.__on=[a]}}function $t(t,n,e,r){var i=Ht;t.sourceEvent=Ht,Ht=t;try{return n.apply(e,r)}finally{Ht=i}}function Zt(t,n,e){var r=At(t),i=r.CustomEvent;"function"==typeof i?i=new i(n,e):(i=r.document.createEvent("Event"),e?(i.initEvent(n,e.bubbles,e.cancelable),i.detail=e.detail):i.initEvent(n,!1,!1)),t.dispatchEvent(i)}var Qt=[null];function Jt(t,n){this._groups=t,this._parents=n}function Kt(){return new Jt([[document.documentElement]],Qt)}Jt.prototype=Kt.prototype={constructor:Jt,select:function(t){"function"!=typeof t&&(t=bt(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,u,a=n[i],c=a.length,f=r[i]=new Array(c),s=0;s<c;++s)(o=a[s])&&(u=t.call(o,o.__data__,s,a))&&("__data__"in o&&(u.__data__=o.__data__),f[s]=u);return new Jt(r,this._parents)},selectAll:function(t){"function"!=typeof t&&(t=_t(t));for(var n=this._groups,e=n.length,r=[],i=[],o=0;o<e;++o)for(var u,a=n[o],c=a.length,f=0;f<c;++f)(u=a[f])&&(r.push(t.call(u,u.__data__,f,a)),i.push(u));return new Jt(r,i)},filter:function(t){"function"!=typeof t&&(t=xt(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,c=r[i]=[],f=0;f<a;++f)(o=u[f])&&t.call(o,o.__data__,f,u)&&c.push(o);return new Jt(r,this._parents)},data:function(t,n){if(!t)return p=new Array(this.size()),s=-1,this.each(function(t){p[++s]=t}),p;var e,r=n?St:Nt,i=this._parents,o=this._groups;"function"!=typeof t&&(e=t,t=function(){return e});for(var u=o.length,a=new Array(u),c=new Array(u),f=new Array(u),s=0;s<u;++s){var l=i[s],h=o[s],d=h.length,p=t.call(l,l&&l.__data__,s,i),g=p.length,v=c[s]=new Array(g),y=a[s]=new Array(g);r(l,h,v,y,f[s]=new Array(d),p,n);for(var b,m,_=0,x=0;_<g;++_)if(b=v[_]){for(_>=x&&(x=_+1);!(m=y[x])&&++x<g;);b._next=m||null}}return(a=new Jt(a,i))._enter=c,a._exit=f,a},enter:function(){return new Jt(this._enter||this._groups.map(wt),this._parents)},exit:function(){return new Jt(this._exit||this._groups.map(wt),this._parents)},join:function(t,n,e){var r=this.enter(),i=this,o=this.exit();return r="function"==typeof t?t(r):r.append(t+""),null!=n&&(i=n(i)),null==e?o.remove():e(o),r&&i?r.merge(i).order():i},merge:function(t){for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),a=0;a<o;++a)for(var c,f=n[a],s=e[a],l=f.length,h=u[a]=new Array(l),d=0;d<l;++d)(c=f[d]||s[d])&&(h[d]=c);for(;a<r;++a)u[a]=n[a];return new Jt(u,this._parents)},order:function(){for(var t=this._groups,n=-1,e=t.length;++n<e;)for(var r,i=t[n],o=i.length-1,u=i[o];--o>=0;)(r=i[o])&&(u&&4^r.compareDocumentPosition(u)&&u.parentNode.insertBefore(r,u),u=r);return this},sort:function(t){function n(n,e){return n&&e?t(n.__data__,e.__data__):!n-!e}t||(t=Tt);for(var e=this._groups,r=e.length,i=new Array(r),o=0;o<r;++o){for(var u,a=e[o],c=a.length,f=i[o]=new Array(c),s=0;s<c;++s)(u=a[s])&&(f[s]=u);f.sort(n)}return new Jt(i,this._parents).order()},call:function(){var t=arguments[0];return arguments[0]=this,t.apply(null,arguments),this},nodes:function(){var t=new Array(this.size()),n=-1;return this.each(function(){t[++n]=this}),t},node:function(){for(var t=this._groups,n=0,e=t.length;n<e;++n)for(var r=t[n],i=0,o=r.length;i<o;++i){var u=r[i];if(u)return u}return null},size:function(){var t=0;return this.each(function(){++t}),t},empty:function(){return!this.node()},each:function(t){for(var n=this._groups,e=0,r=n.length;e<r;++e)for(var i,o=n[e],u=0,a=o.length;u<a;++u)(i=o[u])&&t.call(i,i.__data__,u,o);return this},attr:function(t,n){var e=gt(t);if(arguments.length<2){var r=this.node();return e.local?r.getAttributeNS(e.space,e.local):r.getAttribute(e)}return this.each((null==n?e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}}:"function"==typeof n?e.local?function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttributeNS(t.space,t.local):this.setAttributeNS(t.space,t.local,e)}}:function(t,n){return function(){var e=n.apply(this,arguments);null==e?this.removeAttribute(t):this.setAttribute(t,e)}}:e.local?function(t,n){return function(){this.setAttributeNS(t.space,t.local,n)}}:function(t,n){return function(){this.setAttribute(t,n)}})(e,n))},style:function(t,n,e){return arguments.length>1?this.each((null==n?function(t){return function(){this.style.removeProperty(t)}}:"function"==typeof n?function(t,n,e){return function(){var r=n.apply(this,arguments);null==r?this.style.removeProperty(t):this.style.setProperty(t,r,e)}}:function(t,n,e){return function(){this.style.setProperty(t,n,e)}})(t,n,null==e?"":e)):Ct(this.node(),t)},property:function(t,n){return arguments.length>1?this.each((null==n?function(t){return function(){delete this[t]}}:"function"==typeof n?function(t,n){return function(){var e=n.apply(this,arguments);null==e?delete this[t]:this[t]=e}}:function(t,n){return function(){this[t]=n}})(t,n)):this.node()[t]},classed:function(t,n){var e=Et(t+"");if(arguments.length<2){for(var r=Lt(this.node()),i=-1,o=e.length;++i<o;)if(!r.contains(e[i]))return!1;return!0}return this.each(("function"==typeof n?function(t,n){return function(){(n.apply(this,arguments)?Dt:Pt)(this,t)}}:n?function(t){return function(){Dt(this,t)}}:function(t){return function(){Pt(this,t)}})(e,n))},text:function(t){return arguments.length?this.each(null==t?zt:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.textContent=null==n?"":n}}:function(t){return function(){this.textContent=t}})(t)):this.node().textContent},html:function(t){return arguments.length?this.each(null==t?Ot:("function"==typeof t?function(t){return function(){var n=t.apply(this,arguments);this.innerHTML=null==n?"":n}}:function(t){return function(){this.innerHTML=t}})(t)):this.node().innerHTML},raise:function(){return this.each(Ut)},lower:function(){return this.each(qt)},append:function(t){var n="function"==typeof t?t:vt(t);return this.select(function(){return this.appendChild(n.apply(this,arguments))})},insert:function(t,n){var e="function"==typeof t?t:vt(t),r=null==n?It:"function"==typeof n?n:bt(n);return this.select(function(){return this.insertBefore(e.apply(this,arguments),r.apply(this,arguments)||null)})},remove:function(){return this.each(Bt)},clone:function(t){return this.select(t?jt:Ft)},datum:function(t){return arguments.length?this.property("__data__",t):this.node().__data__},on:function(t,n,e){var r,i,o=function(t){return t.trim().split(/^|\s+/).map(function(t){var n="",e=t.indexOf(".");return e>=0&&(n=t.slice(e+1),t=t.slice(0,e)),{type:t,name:n}})}(t+""),u=o.length;if(!(arguments.length<2)){for(a=n?Wt:Gt,null==e&&(e=!1),r=0;r<u;++r)this.each(a(o[r],n,e));return this}var a=this.node().__on;if(a)for(var c,f=0,s=a.length;f<s;++f)for(r=0,c=a[f];r<u;++r)if((i=o[r]).type===c.type&&i.name===c.name)return c.value},dispatch:function(t,n){return this.each(("function"==typeof n?function(t,n){return function(){return Zt(this,t,n.apply(this,arguments))}}:function(t,n){return function(){return Zt(this,t,n)}})(t,n))}};var tn=Kt,nn=function(t){return"string"==typeof t?new Jt([[document.querySelector(t)]],[document.documentElement]):new Jt([[t]],Qt)},en=function(t){return nn(vt(t).call(document.documentElement))},rn=0;function on(){return new un}function un(){this._="@"+(++rn).toString(36)}un.prototype=on.prototype={constructor:un,get:function(t){for(var n=this._;!(n in t);)if(!(t=t.parentNode))return;return t[n]},set:function(t,n){return t[this._]=n},remove:function(t){return this._ in t&&delete t[this._]},toString:function(){return this._}};var an=function(){for(var t,n=Ht;t=n.sourceEvent;)n=t;return n},cn=function(t,n){var e=t.ownerSVGElement||t;if(e.createSVGPoint){var r=e.createSVGPoint();return r.x=n.clientX,r.y=n.clientY,[(r=r.matrixTransform(t.getScreenCTM().inverse())).x,r.y]}var i=t.getBoundingClientRect();return[n.clientX-i.left-t.clientLeft,n.clientY-i.top-t.clientTop]},fn=function(t){var n=an();return n.changedTouches&&(n=n.changedTouches[0]),cn(t,n)},sn=function(t){return"string"==typeof t?new Jt([document.querySelectorAll(t)],[document.documentElement]):new Jt([null==t?[]:t],Qt)},ln=function(t,n,e){arguments.length<3&&(e=n,n=an().changedTouches);for(var r,i=0,o=n?n.length:0;i<o;++i)if((r=n[i]).identifier===e)return cn(t,r);return null},hn=function(t,n){null==n&&(n=an().touches);for(var e=0,r=n?n.length:0,i=new Array(r);e<r;++e)i[e]=cn(t,n[e]);return i};function dn(){Ht.stopImmediatePropagation()}var pn=function(){Ht.preventDefault(),Ht.stopImmediatePropagation()},gn=function(t){var n=t.document.documentElement,e=nn(t).on("dragstart.drag",pn,!0);"onselectstart"in n?e.on("selectstart.drag",pn,!0):(n.__noselect=n.style.MozUserSelect,n.style.MozUserSelect="none")};function vn(t,n){var e=t.document.documentElement,r=nn(t).on("dragstart.drag",null);n&&(r.on("click.drag",pn,!0),setTimeout(function(){r.on("click.drag",null)},0)),"onselectstart"in e?r.on("selectstart.drag",null):(e.style.MozUserSelect=e.__noselect,delete e.__noselect)}var yn=function(t){return function(){return t}};function bn(t,n,e,r,i,o,u,a,c,f){this.target=t,this.type=n,this.subject=e,this.identifier=r,this.active=i,this.x=o,this.y=u,this.dx=a,this.dy=c,this._=f}function mn(){return!Ht.button}function _n(){return this.parentNode}function xn(t){return null==t?{x:Ht.x,y:Ht.y}:t}function wn(){return"ontouchstart"in this}bn.prototype.on=function(){var t=this._.on.apply(this._,arguments);return t===this._?this:t};var Mn=function(){var t,n,e,r,i=mn,o=_n,u=xn,a=wn,c={},f=ht("start","drag","end"),s=0,l=0;function h(t){t.on("mousedown.drag",d).filter(a).on("touchstart.drag",v).on("touchmove.drag",y).on("touchend.drag touchcancel.drag",b).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function d(){if(!r&&i.apply(this,arguments)){var u=m("mouse",o.apply(this,arguments),fn,this,arguments);u&&(nn(Ht.view).on("mousemove.drag",p,!0).on("mouseup.drag",g,!0),gn(Ht.view),dn(),e=!1,t=Ht.clientX,n=Ht.clientY,u("start"))}}function p(){if(pn(),!e){var r=Ht.clientX-t,i=Ht.clientY-n;e=r*r+i*i>l}c.mouse("drag")}function g(){nn(Ht.view).on("mousemove.drag mouseup.drag",null),vn(Ht.view,e),pn(),c.mouse("end")}function v(){if(i.apply(this,arguments)){var t,n,e=Ht.changedTouches,r=o.apply(this,arguments),u=e.length;for(t=0;t<u;++t)(n=m(e[t].identifier,r,ln,this,arguments))&&(dn(),n("start"))}}function y(){var t,n,e=Ht.changedTouches,r=e.length;for(t=0;t<r;++t)(n=c[e[t].identifier])&&(pn(),n("drag"))}function b(){var t,n,e=Ht.changedTouches,i=e.length;for(r&&clearTimeout(r),r=setTimeout(function(){r=null},500),t=0;t<i;++t)(n=c[e[t].identifier])&&(dn(),n("end"))}function m(t,n,e,r,i){var o,a,l,d=e(n,t),p=f.copy();if($t(new bn(h,"beforestart",o,t,s,d[0],d[1],0,0,p),function(){return null!=(Ht.subject=o=u.apply(r,i))&&(a=o.x-d[0]||0,l=o.y-d[1]||0,!0)}))return function u(f){var g,v=d;switch(f){case"start":c[t]=u,g=s++;break;case"end":delete c[t],--s;case"drag":d=e(n,t),g=s}$t(new bn(h,f,o,t,g,d[0]+a,d[1]+l,d[0]-v[0],d[1]-v[1],p),p.apply,p,[f,r,i])}}return h.filter=function(t){return arguments.length?(i="function"==typeof t?t:yn(!!t),h):i},h.container=function(t){return arguments.length?(o="function"==typeof t?t:yn(t),h):o},h.subject=function(t){return arguments.length?(u="function"==typeof t?t:yn(t),h):u},h.touchable=function(t){return arguments.length?(a="function"==typeof t?t:yn(!!t),h):a},h.on=function(){var t=f.on.apply(f,arguments);return t===f?h:t},h.clickDistance=function(t){return arguments.length?(l=(t=+t)*t,h):Math.sqrt(l)},h},kn=function(t,n,e){t.prototype=n.prototype=e,e.constructor=t};function Nn(t,n){var e=Object.create(t.prototype);for(var r in n)e[r]=n[r];return e}function Sn(){}var Tn="\\s*([+-]?\\d+)\\s*",An="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)\\s*",Cn="\\s*([+-]?\\d*\\.?\\d+(?:[eE][+-]?\\d+)?)%\\s*",En=/^#([0-9a-f]{3})$/,Ln=/^#([0-9a-f]{6})$/,Rn=new RegExp("^rgb\\("+[Tn,Tn,Tn]+"\\)$"),Dn=new RegExp("^rgb\\("+[Cn,Cn,Cn]+"\\)$"),Pn=new RegExp("^rgba\\("+[Tn,Tn,Tn,An]+"\\)$"),zn=new RegExp("^rgba\\("+[Cn,Cn,Cn,An]+"\\)$"),On=new RegExp("^hsl\\("+[An,Cn,Cn]+"\\)$"),Un=new RegExp("^hsla\\("+[An,Cn,Cn,An]+"\\)$"),qn={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function In(t){var n;return t=(t+"").trim().toLowerCase(),(n=En.exec(t))?new Hn((n=parseInt(n[1],16))>>8&15|n>>4&240,n>>4&15|240&n,(15&n)<<4|15&n,1):(n=Ln.exec(t))?Bn(parseInt(n[1],16)):(n=Rn.exec(t))?new Hn(n[1],n[2],n[3],1):(n=Dn.exec(t))?new Hn(255*n[1]/100,255*n[2]/100,255*n[3]/100,1):(n=Pn.exec(t))?Fn(n[1],n[2],n[3],n[4]):(n=zn.exec(t))?Fn(255*n[1]/100,255*n[2]/100,255*n[3]/100,n[4]):(n=On.exec(t))?Xn(n[1],n[2]/100,n[3]/100,1):(n=Un.exec(t))?Xn(n[1],n[2]/100,n[3]/100,n[4]):qn.hasOwnProperty(t)?Bn(qn[t]):"transparent"===t?new Hn(NaN,NaN,NaN,0):null}function Bn(t){return new Hn(t>>16&255,t>>8&255,255&t,1)}function Fn(t,n,e,r){return r<=0&&(t=n=e=NaN),new Hn(t,n,e,r)}function jn(t){return t instanceof Sn||(t=In(t)),t?new Hn((t=t.rgb()).r,t.g,t.b,t.opacity):new Hn}function Yn(t,n,e,r){return 1===arguments.length?jn(t):new Hn(t,n,e,null==r?1:r)}function Hn(t,n,e,r){this.r=+t,this.g=+n,this.b=+e,this.opacity=+r}function Vn(t){return((t=Math.max(0,Math.min(255,Math.round(t)||0)))<16?"0":"")+t.toString(16)}function Xn(t,n,e,r){return r<=0?t=n=e=NaN:e<=0||e>=1?t=n=NaN:n<=0&&(t=NaN),new Wn(t,n,e,r)}function Gn(t,n,e,r){return 1===arguments.length?function(t){if(t instanceof Wn)return new Wn(t.h,t.s,t.l,t.opacity);if(t instanceof Sn||(t=In(t)),!t)return new Wn;if(t instanceof Wn)return t;var n=(t=t.rgb()).r/255,e=t.g/255,r=t.b/255,i=Math.min(n,e,r),o=Math.max(n,e,r),u=NaN,a=o-i,c=(o+i)/2;return a?(u=n===o?(e-r)/a+6*(e<r):e===o?(r-n)/a+2:(n-e)/a+4,a/=c<.5?o+i:2-o-i,u*=60):a=c>0&&c<1?0:u,new Wn(u,a,c,t.opacity)}(t):new Wn(t,n,e,null==r?1:r)}function Wn(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function $n(t,n,e){return 255*(t<60?n+(e-n)*t/60:t<180?e:t<240?n+(e-n)*(240-t)/60:n)}kn(Sn,In,{displayable:function(){return this.rgb().displayable()},hex:function(){return this.rgb().hex()},toString:function(){return this.rgb()+""}}),kn(Hn,Yn,Nn(Sn,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new Hn(this.r*t,this.g*t,this.b*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new Hn(this.r*t,this.g*t,this.b*t,this.opacity)},rgb:function(){return this},displayable:function(){return 0<=this.r&&this.r<=255&&0<=this.g&&this.g<=255&&0<=this.b&&this.b<=255&&0<=this.opacity&&this.opacity<=1},hex:function(){return"#"+Vn(this.r)+Vn(this.g)+Vn(this.b)},toString:function(){var t=this.opacity;return(1===(t=isNaN(t)?1:Math.max(0,Math.min(1,t)))?"rgb(":"rgba(")+Math.max(0,Math.min(255,Math.round(this.r)||0))+", "+Math.max(0,Math.min(255,Math.round(this.g)||0))+", "+Math.max(0,Math.min(255,Math.round(this.b)||0))+(1===t?")":", "+t+")")}})),kn(Wn,Gn,Nn(Sn,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new Wn(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new Wn(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=this.h%360+360*(this.h<0),n=isNaN(t)||isNaN(this.s)?0:this.s,e=this.l,r=e+(e<.5?e:1-e)*n,i=2*e-r;return new Hn($n(t>=240?t-240:t+120,i,r),$n(t,i,r),$n(t<120?t+240:t-120,i,r),this.opacity)},displayable:function(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1}}));var Zn=Math.PI/180,Qn=180/Math.PI,Jn=.96422,Kn=1,te=.82521,ne=4/29,ee=6/29,re=3*ee*ee,ie=ee*ee*ee;function oe(t){if(t instanceof ce)return new ce(t.l,t.a,t.b,t.opacity);if(t instanceof ve){if(isNaN(t.h))return new ce(t.l,0,0,t.opacity);var n=t.h*Zn;return new ce(t.l,Math.cos(n)*t.c,Math.sin(n)*t.c,t.opacity)}t instanceof Hn||(t=jn(t));var e,r,i=he(t.r),o=he(t.g),u=he(t.b),a=fe((.2225045*i+.7168786*o+.0606169*u)/Kn);return i===o&&o===u?e=r=a:(e=fe((.4360747*i+.3850649*o+.1430804*u)/Jn),r=fe((.0139322*i+.0971045*o+.7141733*u)/te)),new ce(116*a-16,500*(e-a),200*(a-r),t.opacity)}function ue(t,n){return new ce(t,0,0,null==n?1:n)}function ae(t,n,e,r){return 1===arguments.length?oe(t):new ce(t,n,e,null==r?1:r)}function ce(t,n,e,r){this.l=+t,this.a=+n,this.b=+e,this.opacity=+r}function fe(t){return t>ie?Math.pow(t,1/3):t/re+ne}function se(t){return t>ee?t*t*t:re*(t-ne)}function le(t){return 255*(t<=.0031308?12.92*t:1.055*Math.pow(t,1/2.4)-.055)}function he(t){return(t/=255)<=.04045?t/12.92:Math.pow((t+.055)/1.055,2.4)}function de(t){if(t instanceof ve)return new ve(t.h,t.c,t.l,t.opacity);if(t instanceof ce||(t=oe(t)),0===t.a&&0===t.b)return new ve(NaN,0,t.l,t.opacity);var n=Math.atan2(t.b,t.a)*Qn;return new ve(n<0?n+360:n,Math.sqrt(t.a*t.a+t.b*t.b),t.l,t.opacity)}function pe(t,n,e,r){return 1===arguments.length?de(t):new ve(e,n,t,null==r?1:r)}function ge(t,n,e,r){return 1===arguments.length?de(t):new ve(t,n,e,null==r?1:r)}function ve(t,n,e,r){this.h=+t,this.c=+n,this.l=+e,this.opacity=+r}kn(ce,ae,Nn(Sn,{brighter:function(t){return new ce(this.l+18*(null==t?1:t),this.a,this.b,this.opacity)},darker:function(t){return new ce(this.l-18*(null==t?1:t),this.a,this.b,this.opacity)},rgb:function(){var t=(this.l+16)/116,n=isNaN(this.a)?t:t+this.a/500,e=isNaN(this.b)?t:t-this.b/200;return new Hn(le(3.1338561*(n=Jn*se(n))-1.6168667*(t=Kn*se(t))-.4906146*(e=te*se(e))),le(-.9787684*n+1.9161415*t+.033454*e),le(.0719453*n-.2289914*t+1.4052427*e),this.opacity)}})),kn(ve,ge,Nn(Sn,{brighter:function(t){return new ve(this.h,this.c,this.l+18*(null==t?1:t),this.opacity)},darker:function(t){return new ve(this.h,this.c,this.l-18*(null==t?1:t),this.opacity)},rgb:function(){return oe(this).rgb()}}));var ye=-.29227,be=-.90649,me=1.97294,_e=me*be,xe=1.78277*me,we=1.78277*ye- -.14861*be;function Me(t,n,e,r){return 1===arguments.length?function(t){if(t instanceof ke)return new ke(t.h,t.s,t.l,t.opacity);t instanceof Hn||(t=jn(t));var n=t.r/255,e=t.g/255,r=t.b/255,i=(we*r+_e*n-xe*e)/(we+_e-xe),o=r-i,u=(me*(e-i)-ye*o)/be,a=Math.sqrt(u*u+o*o)/(me*i*(1-i)),c=a?Math.atan2(u,o)*Qn-120:NaN;return new ke(c<0?c+360:c,a,i,t.opacity)}(t):new ke(t,n,e,null==r?1:r)}function ke(t,n,e,r){this.h=+t,this.s=+n,this.l=+e,this.opacity=+r}function Ne(t,n,e,r,i){var o=t*t,u=o*t;return((1-3*t+3*o-u)*n+(4-6*o+3*u)*e+(1+3*t+3*o-3*u)*r+u*i)/6}kn(ke,Me,Nn(Sn,{brighter:function(t){return t=null==t?1/.7:Math.pow(1/.7,t),new ke(this.h,this.s,this.l*t,this.opacity)},darker:function(t){return t=null==t?.7:Math.pow(.7,t),new ke(this.h,this.s,this.l*t,this.opacity)},rgb:function(){var t=isNaN(this.h)?0:(this.h+120)*Zn,n=+this.l,e=isNaN(this.s)?0:this.s*n*(1-n),r=Math.cos(t),i=Math.sin(t);return new Hn(255*(n+e*(-.14861*r+1.78277*i)),255*(n+e*(ye*r+be*i)),255*(n+e*(me*r)),this.opacity)}}));var Se=function(t){var n=t.length-1;return function(e){var r=e<=0?e=0:e>=1?(e=1,n-1):Math.floor(e*n),i=t[r],o=t[r+1],u=r>0?t[r-1]:2*i-o,a=r<n-1?t[r+2]:2*o-i;return Ne((e-r/n)*n,u,i,o,a)}},Te=function(t){var n=t.length;return function(e){var r=Math.floor(((e%=1)<0?++e:e)*n),i=t[(r+n-1)%n],o=t[r%n],u=t[(r+1)%n],a=t[(r+2)%n];return Ne((e-r/n)*n,i,o,u,a)}},Ae=function(t){return function(){return t}};function Ce(t,n){return function(e){return t+e*n}}function Ee(t,n){var e=n-t;return e?Ce(t,e>180||e<-180?e-360*Math.round(e/360):e):Ae(isNaN(t)?n:t)}function Le(t){return 1==(t=+t)?Re:function(n,e){return e-n?function(t,n,e){return t=Math.pow(t,e),n=Math.pow(n,e)-t,e=1/e,function(r){return Math.pow(t+r*n,e)}}(n,e,t):Ae(isNaN(n)?e:n)}}function Re(t,n){var e=n-t;return e?Ce(t,e):Ae(isNaN(t)?n:t)}var De=function t(n){var e=Le(n);function r(t,n){var r=e((t=Yn(t)).r,(n=Yn(n)).r),i=e(t.g,n.g),o=e(t.b,n.b),u=Re(t.opacity,n.opacity);return function(n){return t.r=r(n),t.g=i(n),t.b=o(n),t.opacity=u(n),t+""}}return r.gamma=t,r}(1);function Pe(t){return function(n){var e,r,i=n.length,o=new Array(i),u=new Array(i),a=new Array(i);for(e=0;e<i;++e)r=Yn(n[e]),o[e]=r.r||0,u[e]=r.g||0,a[e]=r.b||0;return o=t(o),u=t(u),a=t(a),r.opacity=1,function(t){return r.r=o(t),r.g=u(t),r.b=a(t),r+""}}}var ze=Pe(Se),Oe=Pe(Te),Ue=function(t,n){var e,r=n?n.length:0,i=t?Math.min(r,t.length):0,o=new Array(i),u=new Array(r);for(e=0;e<i;++e)o[e]=We(t[e],n[e]);for(;e<r;++e)u[e]=n[e];return function(t){for(e=0;e<i;++e)u[e]=o[e](t);return u}},qe=function(t,n){var e=new Date;return n-=t=+t,function(r){return e.setTime(t+n*r),e}},Ie=function(t,n){return n-=t=+t,function(e){return t+n*e}},Be=function(t,n){var e,r={},i={};for(e in null!==t&&"object"==typeof t||(t={}),null!==n&&"object"==typeof n||(n={}),n)e in t?r[e]=We(t[e],n[e]):i[e]=n[e];return function(t){for(e in r)i[e]=r[e](t);return i}},Fe=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,je=new RegExp(Fe.source,"g");var Ye,He,Ve,Xe,Ge=function(t,n){var e,r,i,o=Fe.lastIndex=je.lastIndex=0,u=-1,a=[],c=[];for(t+="",n+="";(e=Fe.exec(t))&&(r=je.exec(n));)(i=r.index)>o&&(i=n.slice(o,i),a[u]?a[u]+=i:a[++u]=i),(e=e[0])===(r=r[0])?a[u]?a[u]+=r:a[++u]=r:(a[++u]=null,c.push({i:u,x:Ie(e,r)})),o=je.lastIndex;return o<n.length&&(i=n.slice(o),a[u]?a[u]+=i:a[++u]=i),a.length<2?c[0]?function(t){return function(n){return t(n)+""}}(c[0].x):function(t){return function(){return t}}(n):(n=c.length,function(t){for(var e,r=0;r<n;++r)a[(e=c[r]).i]=e.x(t);return a.join("")})},We=function(t,n){var e,r=typeof n;return null==n||"boolean"===r?Ae(n):("number"===r?Ie:"string"===r?(e=In(n))?(n=e,De):Ge:n instanceof In?De:n instanceof Date?qe:Array.isArray(n)?Ue:"function"!=typeof n.valueOf&&"function"!=typeof n.toString||isNaN(n)?Be:Ie)(t,n)},$e=function(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}},Ze=function(t,n){var e=Ee(+t,+n);return function(t){var n=e(t);return n-360*Math.floor(n/360)}},Qe=function(t,n){return n-=t=+t,function(e){return Math.round(t+n*e)}},Je=180/Math.PI,Ke={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1},tr=function(t,n,e,r,i,o){var u,a,c;return(u=Math.sqrt(t*t+n*n))&&(t/=u,n/=u),(c=t*e+n*r)&&(e-=t*c,r-=n*c),(a=Math.sqrt(e*e+r*r))&&(e/=a,r/=a,c/=a),t*r<n*e&&(t=-t,n=-n,c=-c,u=-u),{translateX:i,translateY:o,rotate:Math.atan2(n,t)*Je,skewX:Math.atan(c)*Je,scaleX:u,scaleY:a}};function nr(t,n,e,r){function i(t){return t.length?t.pop()+" ":""}return function(o,u){var a=[],c=[];return o=t(o),u=t(u),function(t,r,i,o,u,a){if(t!==i||r!==o){var c=u.push("translate(",null,n,null,e);a.push({i:c-4,x:Ie(t,i)},{i:c-2,x:Ie(r,o)})}else(i||o)&&u.push("translate("+i+n+o+e)}(o.translateX,o.translateY,u.translateX,u.translateY,a,c),function(t,n,e,o){t!==n?(t-n>180?n+=360:n-t>180&&(t+=360),o.push({i:e.push(i(e)+"rotate(",null,r)-2,x:Ie(t,n)})):n&&e.push(i(e)+"rotate("+n+r)}(o.rotate,u.rotate,a,c),function(t,n,e,o){t!==n?o.push({i:e.push(i(e)+"skewX(",null,r)-2,x:Ie(t,n)}):n&&e.push(i(e)+"skewX("+n+r)}(o.skewX,u.skewX,a,c),function(t,n,e,r,o,u){if(t!==e||n!==r){var a=o.push(i(o)+"scale(",null,",",null,")");u.push({i:a-4,x:Ie(t,e)},{i:a-2,x:Ie(n,r)})}else 1===e&&1===r||o.push(i(o)+"scale("+e+","+r+")")}(o.scaleX,o.scaleY,u.scaleX,u.scaleY,a,c),o=u=null,function(t){for(var n,e=-1,r=c.length;++e<r;)a[(n=c[e]).i]=n.x(t);return a.join("")}}}var er=nr(function(t){return"none"===t?Ke:(Ye||(Ye=document.createElement("DIV"),He=document.documentElement,Ve=document.defaultView),Ye.style.transform=t,t=Ve.getComputedStyle(He.appendChild(Ye),null).getPropertyValue("transform"),He.removeChild(Ye),t=t.slice(7,-1).split(","),tr(+t[0],+t[1],+t[2],+t[3],+t[4],+t[5]))},"px, ","px)","deg)"),rr=nr(function(t){return null==t?Ke:(Xe||(Xe=document.createElementNS("http://www.w3.org/2000/svg","g")),Xe.setAttribute("transform",t),(t=Xe.transform.baseVal.consolidate())?(t=t.matrix,tr(t.a,t.b,t.c,t.d,t.e,t.f)):Ke)},", ",")",")"),ir=Math.SQRT2;function or(t){return((t=Math.exp(t))+1/t)/2}var ur=function(t,n){var e,r,i=t[0],o=t[1],u=t[2],a=n[0],c=n[1],f=n[2],s=a-i,l=c-o,h=s*s+l*l;if(h<1e-12)r=Math.log(f/u)/ir,e=function(t){return[i+t*s,o+t*l,u*Math.exp(ir*t*r)]};else{var d=Math.sqrt(h),p=(f*f-u*u+4*h)/(2*u*2*d),g=(f*f-u*u-4*h)/(2*f*2*d),v=Math.log(Math.sqrt(p*p+1)-p),y=Math.log(Math.sqrt(g*g+1)-g);r=(y-v)/ir,e=function(t){var n,e=t*r,a=or(v),c=u/(2*d)*(a*(n=ir*e+v,((n=Math.exp(2*n))-1)/(n+1))-function(t){return((t=Math.exp(t))-1/t)/2}(v));return[i+c*s,o+c*l,u*a/or(ir*e+v)]}}return e.duration=1e3*r,e};function ar(t){return function(n,e){var r=t((n=Gn(n)).h,(e=Gn(e)).h),i=Re(n.s,e.s),o=Re(n.l,e.l),u=Re(n.opacity,e.opacity);return function(t){return n.h=r(t),n.s=i(t),n.l=o(t),n.opacity=u(t),n+""}}}var cr=ar(Ee),fr=ar(Re);function sr(t,n){var e=Re((t=ae(t)).l,(n=ae(n)).l),r=Re(t.a,n.a),i=Re(t.b,n.b),o=Re(t.opacity,n.opacity);return function(n){return t.l=e(n),t.a=r(n),t.b=i(n),t.opacity=o(n),t+""}}function lr(t){return function(n,e){var r=t((n=ge(n)).h,(e=ge(e)).h),i=Re(n.c,e.c),o=Re(n.l,e.l),u=Re(n.opacity,e.opacity);return function(t){return n.h=r(t),n.c=i(t),n.l=o(t),n.opacity=u(t),n+""}}}var hr=lr(Ee),dr=lr(Re);function pr(t){return function n(e){function r(n,r){var i=t((n=Me(n)).h,(r=Me(r)).h),o=Re(n.s,r.s),u=Re(n.l,r.l),a=Re(n.opacity,r.opacity);return function(t){return n.h=i(t),n.s=o(t),n.l=u(Math.pow(t,e)),n.opacity=a(t),n+""}}return e=+e,r.gamma=n,r}(1)}var gr=pr(Ee),vr=pr(Re);function yr(t,n){for(var e=0,r=n.length-1,i=n[0],o=new Array(r<0?0:r);e<r;)o[e]=t(i,i=n[++e]);return function(t){var n=Math.max(0,Math.min(r-1,Math.floor(t*=r)));return o[n](t-n)}}var br,mr,_r=function(t,n){for(var e=new Array(n),r=0;r<n;++r)e[r]=t(r/(n-1));return e},xr=0,wr=0,Mr=0,kr=1e3,Nr=0,Sr=0,Tr=0,Ar="object"==typeof performance&&performance.now?performance:Date,Cr="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function Er(){return Sr||(Cr(Lr),Sr=Ar.now()+Tr)}function Lr(){Sr=0}function Rr(){this._call=this._time=this._next=null}function Dr(t,n,e){var r=new Rr;return r.restart(t,n,e),r}function Pr(){Er(),++xr;for(var t,n=br;n;)(t=Sr-n._time)>=0&&n._call.call(null,t),n=n._next;--xr}function zr(){Sr=(Nr=Ar.now())+Tr,xr=wr=0;try{Pr()}finally{xr=0,function(){var t,n,e=br,r=1/0;for(;e;)e._call?(r>e._time&&(r=e._time),t=e,e=e._next):(n=e._next,e._next=null,e=t?t._next=n:br=n);mr=t,Ur(r)}(),Sr=0}}function Or(){var t=Ar.now(),n=t-Nr;n>kr&&(Tr-=n,Nr=t)}function Ur(t){xr||(wr&&(wr=clearTimeout(wr)),t-Sr>24?(t<1/0&&(wr=setTimeout(zr,t-Ar.now()-Tr)),Mr&&(Mr=clearInterval(Mr))):(Mr||(Nr=Ar.now(),Mr=setInterval(Or,kr)),xr=1,Cr(zr)))}Rr.prototype=Dr.prototype={constructor:Rr,restart:function(t,n,e){if("function"!=typeof t)throw new TypeError("callback is not a function");e=(null==e?Er():+e)+(null==n?0:+n),this._next||mr===this||(mr?mr._next=this:br=this,mr=this),this._call=t,this._time=e,Ur()},stop:function(){this._call&&(this._call=null,this._time=1/0,Ur())}};var qr=function(t,n,e){var r=new Rr;return n=null==n?0:+n,r.restart(function(e){r.stop(),t(e+n)},n,e),r},Ir=function(t,n,e){var r=new Rr,i=n;return null==n?(r.restart(t,n,e),r):(n=+n,e=null==e?Er():+e,r.restart(function o(u){u+=i,r.restart(o,i+=n,e),t(u)},n,e),r)},Br=ht("start","end","cancel","interrupt"),Fr=[],jr=0,Yr=1,Hr=2,Vr=3,Xr=4,Gr=5,Wr=6,$r=function(t,n,e,r,i,o){var u=t.__transition;if(u){if(e in u)return}else t.__transition={};!function(t,n,e){var r,i=t.__transition;function o(c){var f,s,l,h;if(e.state!==Yr)return a();for(f in i)if((h=i[f]).name===e.name){if(h.state===Vr)return qr(o);h.state===Xr?(h.state=Wr,h.timer.stop(),h.on.call("interrupt",t,t.__data__,h.index,h.group),delete i[f]):+f<n&&(h.state=Wr,h.timer.stop(),h.on.call("cancel",t,t.__data__,h.index,h.group),delete i[f])}if(qr(function(){e.state===Vr&&(e.state=Xr,e.timer.restart(u,e.delay,e.time),u(c))}),e.state=Hr,e.on.call("start",t,t.__data__,e.index,e.group),e.state===Hr){for(e.state=Vr,r=new Array(l=e.tween.length),f=0,s=-1;f<l;++f)(h=e.tween[f].value.call(t,t.__data__,e.index,e.group))&&(r[++s]=h);r.length=s+1}}function u(n){for(var i=n<e.duration?e.ease.call(null,n/e.duration):(e.timer.restart(a),e.state=Gr,1),o=-1,u=r.length;++o<u;)r[o].call(t,i);e.state===Gr&&(e.on.call("end",t,t.__data__,e.index,e.group),a())}function a(){for(var r in e.state=Wr,e.timer.stop(),delete i[n],i)return;delete t.__transition}i[n]=e,e.timer=Dr(function(t){e.state=Yr,e.timer.restart(o,e.delay,e.time),e.delay<=t&&o(t-e.delay)},0,e.time)}(t,e,{name:n,index:r,group:i,on:Br,tween:Fr,time:o.time,delay:o.delay,duration:o.duration,ease:o.ease,timer:null,state:jr})};function Zr(t,n){var e=Jr(t,n);if(e.state>jr)throw new Error("too late; already scheduled");return e}function Qr(t,n){var e=Jr(t,n);if(e.state>Vr)throw new Error("too late; already running");return e}function Jr(t,n){var e=t.__transition;if(!e||!(e=e[n]))throw new Error("transition not found");return e}var Kr=function(t,n){var e,r,i,o=t.__transition,u=!0;if(o){for(i in n=null==n?null:n+"",o)(e=o[i]).name===n?(r=e.state>Hr&&e.state<Gr,e.state=Wr,e.timer.stop(),e.on.call(r?"interrupt":"cancel",t,t.__data__,e.index,e.group),delete o[i]):u=!1;u&&delete t.__transition}};function ti(t,n,e){var r=t._id;return t.each(function(){var t=Qr(this,r);(t.value||(t.value={}))[n]=e.apply(this,arguments)}),function(t){return Jr(t,r).value[n]}}var ni=function(t,n){var e;return("number"==typeof n?Ie:n instanceof In?De:(e=In(n))?(n=e,De):Ge)(t,n)};var ei=tn.prototype.constructor;function ri(t){return function(){this.style.removeProperty(t)}}var ii=0;function oi(t,n,e,r){this._groups=t,this._parents=n,this._name=e,this._id=r}function ui(t){return tn().transition(t)}function ai(){return++ii}var ci=tn.prototype;function fi(t){return+t}function si(t){return t*t}function li(t){return t*(2-t)}function hi(t){return((t*=2)<=1?t*t:--t*(2-t)+1)/2}function di(t){return t*t*t}function pi(t){return--t*t*t+1}function gi(t){return((t*=2)<=1?t*t*t:(t-=2)*t*t+2)/2}oi.prototype=ui.prototype={constructor:oi,select:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=bt(t));for(var r=this._groups,i=r.length,o=new Array(i),u=0;u<i;++u)for(var a,c,f=r[u],s=f.length,l=o[u]=new Array(s),h=0;h<s;++h)(a=f[h])&&(c=t.call(a,a.__data__,h,f))&&("__data__"in a&&(c.__data__=a.__data__),l[h]=c,$r(l[h],n,e,h,l,Jr(a,e)));return new oi(o,this._parents,n,e)},selectAll:function(t){var n=this._name,e=this._id;"function"!=typeof t&&(t=_t(t));for(var r=this._groups,i=r.length,o=[],u=[],a=0;a<i;++a)for(var c,f=r[a],s=f.length,l=0;l<s;++l)if(c=f[l]){for(var h,d=t.call(c,c.__data__,l,f),p=Jr(c,e),g=0,v=d.length;g<v;++g)(h=d[g])&&$r(h,n,e,g,d,p);o.push(d),u.push(c)}return new oi(o,u,n,e)},filter:function(t){"function"!=typeof t&&(t=xt(t));for(var n=this._groups,e=n.length,r=new Array(e),i=0;i<e;++i)for(var o,u=n[i],a=u.length,c=r[i]=[],f=0;f<a;++f)(o=u[f])&&t.call(o,o.__data__,f,u)&&c.push(o);return new oi(r,this._parents,this._name,this._id)},merge:function(t){if(t._id!==this._id)throw new Error;for(var n=this._groups,e=t._groups,r=n.length,i=e.length,o=Math.min(r,i),u=new Array(r),a=0;a<o;++a)for(var c,f=n[a],s=e[a],l=f.length,h=u[a]=new Array(l),d=0;d<l;++d)(c=f[d]||s[d])&&(h[d]=c);for(;a<r;++a)u[a]=n[a];return new oi(u,this._parents,this._name,this._id)},selection:function(){return new ei(this._groups,this._parents)},transition:function(){for(var t=this._name,n=this._id,e=ai(),r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],c=a.length,f=0;f<c;++f)if(u=a[f]){var s=Jr(u,n);$r(u,t,e,f,a,{time:s.time+s.delay+s.duration,delay:0,duration:s.duration,ease:s.ease})}return new oi(r,this._parents,t,e)},call:ci.call,nodes:ci.nodes,node:ci.node,size:ci.size,empty:ci.empty,each:ci.each,on:function(t,n){var e=this._id;return arguments.length<2?Jr(this.node(),e).on.on(t):this.each(function(t,n,e){var r,i,o=function(t){return(t+"").trim().split(/^|\s+/).every(function(t){var n=t.indexOf(".");return n>=0&&(t=t.slice(0,n)),!t||"start"===t})}(n)?Zr:Qr;return function(){var u=o(this,t),a=u.on;a!==r&&(i=(r=a).copy()).on(n,e),u.on=i}}(e,t,n))},attr:function(t,n){var e=gt(t),r="transform"===e?rr:ni;return this.attrTween(t,"function"==typeof n?(e.local?function(t,n,e){var r,i,o;return function(){var u,a,c=e(this);if(null!=c)return(u=this.getAttributeNS(t.space,t.local))===(a=c+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,c));this.removeAttributeNS(t.space,t.local)}}:function(t,n,e){var r,i,o;return function(){var u,a,c=e(this);if(null!=c)return(u=this.getAttribute(t))===(a=c+"")?null:u===r&&a===i?o:(i=a,o=n(r=u,c));this.removeAttribute(t)}})(e,r,ti(this,"attr."+t,n)):null==n?(e.local?function(t){return function(){this.removeAttributeNS(t.space,t.local)}}:function(t){return function(){this.removeAttribute(t)}})(e):(e.local?function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttributeNS(t.space,t.local);return u===o?null:u===r?i:i=n(r=u,e)}}:function(t,n,e){var r,i,o=e+"";return function(){var u=this.getAttribute(t);return u===o?null:u===r?i:i=n(r=u,e)}})(e,r,n))},attrTween:function(t,n){var e="attr."+t;if(arguments.length<2)return(e=this.tween(e))&&e._value;if(null==n)return this.tween(e,null);if("function"!=typeof n)throw new Error;var r=gt(t);return this.tween(e,(r.local?function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttributeNS(t.space,t.local,n(e))}}(t,i)),e}return i._value=n,i}:function(t,n){var e,r;function i(){var i=n.apply(this,arguments);return i!==r&&(e=(r=i)&&function(t,n){return function(e){this.setAttribute(t,n(e))}}(t,i)),e}return i._value=n,i})(r,n))},style:function(t,n,e){var r="transform"==(t+="")?er:ni;return null==n?this.styleTween(t,function(t,n){var e,r,i;return function(){var o=Ct(this,t),u=(this.style.removeProperty(t),Ct(this,t));return o===u?null:o===e&&u===r?i:i=n(e=o,r=u)}}(t,r)).on("end.style."+t,ri(t)):"function"==typeof n?this.styleTween(t,function(t,n,e){var r,i,o;return function(){var u=Ct(this,t),a=e(this),c=a+"";return null==a&&(this.style.removeProperty(t),c=a=Ct(this,t)),u===c?null:u===r&&c===i?o:(i=c,o=n(r=u,a))}}(t,r,ti(this,"style."+t,n))).each(function(t,n){var e,r,i,o,u="style."+n,a="end."+u;return function(){var c=Qr(this,t),f=c.on,s=null==c.value[u]?o||(o=ri(n)):void 0;f===e&&i===s||(r=(e=f).copy()).on(a,i=s),c.on=r}}(this._id,t)):this.styleTween(t,function(t,n,e){var r,i,o=e+"";return function(){var u=Ct(this,t);return u===o?null:u===r?i:i=n(r=u,e)}}(t,r,n),e).on("end.style."+t,null)},styleTween:function(t,n,e){var r="style."+(t+="");if(arguments.length<2)return(r=this.tween(r))&&r._value;if(null==n)return this.tween(r,null);if("function"!=typeof n)throw new Error;return this.tween(r,function(t,n,e){var r,i;function o(){var o=n.apply(this,arguments);return o!==i&&(r=(i=o)&&function(t,n,e){return function(r){this.style.setProperty(t,n(r),e)}}(t,o,e)),r}return o._value=n,o}(t,n,null==e?"":e))},text:function(t){return this.tween("text","function"==typeof t?function(t){return function(){var n=t(this);this.textContent=null==n?"":n}}(ti(this,"text",t)):function(t){return function(){this.textContent=t}}(null==t?"":t+""))},remove:function(){return this.on("end.remove",(t=this._id,function(){var n=this.parentNode;for(var e in this.__transition)if(+e!==t)return;n&&n.removeChild(this)}));var t},tween:function(t,n){var e=this._id;if(t+="",arguments.length<2){for(var r,i=Jr(this.node(),e).tween,o=0,u=i.length;o<u;++o)if((r=i[o]).name===t)return r.value;return null}return this.each((null==n?function(t,n){var e,r;return function(){var i=Qr(this,t),o=i.tween;if(o!==e)for(var u=0,a=(r=e=o).length;u<a;++u)if(r[u].name===n){(r=r.slice()).splice(u,1);break}i.tween=r}}:function(t,n,e){var r,i;if("function"!=typeof e)throw new Error;return function(){var o=Qr(this,t),u=o.tween;if(u!==r){i=(r=u).slice();for(var a={name:n,value:e},c=0,f=i.length;c<f;++c)if(i[c].name===n){i[c]=a;break}c===f&&i.push(a)}o.tween=i}})(e,t,n))},delay:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){Zr(this,t).delay=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){Zr(this,t).delay=n}})(n,t)):Jr(this.node(),n).delay},duration:function(t){var n=this._id;return arguments.length?this.each(("function"==typeof t?function(t,n){return function(){Qr(this,t).duration=+n.apply(this,arguments)}}:function(t,n){return n=+n,function(){Qr(this,t).duration=n}})(n,t)):Jr(this.node(),n).duration},ease:function(t){var n=this._id;return arguments.length?this.each(function(t,n){if("function"!=typeof n)throw new Error;return function(){Qr(this,t).ease=n}}(n,t)):Jr(this.node(),n).ease},end:function(){var t,n,e=this,r=e._id,i=e.size();return new Promise(function(o,u){var a={value:u},c={value:function(){0==--i&&o()}};e.each(function(){var e=Qr(this,r),i=e.on;i!==t&&((n=(t=i).copy())._.cancel.push(a),n._.interrupt.push(a),n._.end.push(c)),e.on=n})})}};var vi=function t(n){function e(t){return Math.pow(t,n)}return n=+n,e.exponent=t,e}(3),yi=function t(n){function e(t){return 1-Math.pow(1-t,n)}return n=+n,e.exponent=t,e}(3),bi=function t(n){function e(t){return((t*=2)<=1?Math.pow(t,n):2-Math.pow(2-t,n))/2}return n=+n,e.exponent=t,e}(3),mi=Math.PI,_i=mi/2;function xi(t){return 1-Math.cos(t*_i)}function wi(t){return Math.sin(t*_i)}function Mi(t){return(1-Math.cos(mi*t))/2}function ki(t){return Math.pow(2,10*t-10)}function Ni(t){return 1-Math.pow(2,-10*t)}function Si(t){return((t*=2)<=1?Math.pow(2,10*t-10):2-Math.pow(2,10-10*t))/2}function Ti(t){return 1-Math.sqrt(1-t*t)}function Ai(t){return Math.sqrt(1- --t*t)}function Ci(t){return((t*=2)<=1?1-Math.sqrt(1-t*t):Math.sqrt(1-(t-=2)*t)+1)/2}var Ei=4/11,Li=6/11,Ri=8/11,Di=.75,Pi=9/11,zi=10/11,Oi=.9375,Ui=21/22,qi=63/64,Ii=1/Ei/Ei;function Bi(t){return 1-Fi(1-t)}function Fi(t){return(t=+t)<Ei?Ii*t*t:t<Ri?Ii*(t-=Li)*t+Di:t<zi?Ii*(t-=Pi)*t+Oi:Ii*(t-=Ui)*t+qi}function ji(t){return((t*=2)<=1?1-Fi(1-t):Fi(t-1)+1)/2}var Yi=function t(n){function e(t){return t*t*((n+1)*t-n)}return n=+n,e.overshoot=t,e}(1.70158),Hi=function t(n){function e(t){return--t*t*((n+1)*t+n)+1}return n=+n,e.overshoot=t,e}(1.70158),Vi=function t(n){function e(t){return((t*=2)<1?t*t*((n+1)*t-n):(t-=2)*t*((n+1)*t+n)+2)/2}return n=+n,e.overshoot=t,e}(1.70158),Xi=2*Math.PI,Gi=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=Xi);function i(t){return n*Math.pow(2,10*--t)*Math.sin((r-t)/e)}return i.amplitude=function(n){return t(n,e*Xi)},i.period=function(e){return t(n,e)},i}(1,.3),Wi=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=Xi);function i(t){return 1-n*Math.pow(2,-10*(t=+t))*Math.sin((t+r)/e)}return i.amplitude=function(n){return t(n,e*Xi)},i.period=function(e){return t(n,e)},i}(1,.3),$i=function t(n,e){var r=Math.asin(1/(n=Math.max(1,n)))*(e/=Xi);function i(t){return((t=2*t-1)<0?n*Math.pow(2,10*t)*Math.sin((r-t)/e):2-n*Math.pow(2,-10*t)*Math.sin((r+t)/e))/2}return i.amplitude=function(n){return t(n,e*Xi)},i.period=function(e){return t(n,e)},i}(1,.3),Zi={time:null,delay:0,duration:250,ease:gi};function Qi(t,n){for(var e;!(e=t.__transition)||!(e=e[n]);)if(!(t=t.parentNode))return Zi.time=Er(),Zi;return e}tn.prototype.interrupt=function(t){return this.each(function(){Kr(this,t)})},tn.prototype.transition=function(t){var n,e;t instanceof oi?(n=t._id,t=t._name):(n=ai(),(e=Zi).time=Er(),t=null==t?null:t+"");for(var r=this._groups,i=r.length,o=0;o<i;++o)for(var u,a=r[o],c=a.length,f=0;f<c;++f)(u=a[f])&&$r(u,t,n,f,a,e||Qi(u,n));return new oi(r,this._parents,t,n)};var Ji=[null],Ki=function(t,n){var e,r,i=t.__transition;if(i)for(r in n=null==n?null:n+"",i)if((e=i[r]).state>Yr&&e.name===n)return new oi([[t]],Ji,n,+r);return null},to=function(t){return function(){return t}},no=function(t,n,e){this.target=t,this.type=n,this.selection=e};function eo(){Ht.stopImmediatePropagation()}var ro=function(){Ht.preventDefault(),Ht.stopImmediatePropagation()},io={name:"drag"},oo={name:"space"},uo={name:"handle"},ao={name:"center"},co={name:"x",handles:["e","w"].map(yo),input:function(t,n){return t&&[[t[0],n[0][1]],[t[1],n[1][1]]]},output:function(t){return t&&[t[0][0],t[1][0]]}},fo={name:"y",handles:["n","s"].map(yo),input:function(t,n){return t&&[[n[0][0],t[0]],[n[1][0],t[1]]]},output:function(t){return t&&[t[0][1],t[1][1]]}},so={name:"xy",handles:["n","e","s","w","nw","ne","se","sw"].map(yo),input:function(t){return t},output:function(t){return t}},lo={overlay:"crosshair",selection:"move",n:"ns-resize",e:"ew-resize",s:"ns-resize",w:"ew-resize",nw:"nwse-resize",ne:"nesw-resize",se:"nwse-resize",sw:"nesw-resize"},ho={e:"w",w:"e",nw:"ne",ne:"nw",se:"sw",sw:"se"},po={n:"s",s:"n",nw:"sw",ne:"se",se:"ne",sw:"nw"},go={overlay:1,selection:1,n:null,e:1,s:null,w:-1,nw:-1,ne:1,se:1,sw:-1},vo={overlay:1,selection:1,n:-1,e:null,s:1,w:null,nw:-1,ne:-1,se:1,sw:1};function yo(t){return{type:t}}function bo(){return!Ht.button}function mo(){var t=this.ownerSVGElement||this;return[[0,0],[t.width.baseVal.value,t.height.baseVal.value]]}function _o(t){for(;!t.__brush;)if(!(t=t.parentNode))return;return t.__brush}function xo(t){return t[0][0]===t[1][0]||t[0][1]===t[1][1]}function wo(t){var n=t.__brush;return n?n.dim.output(n.selection):null}function Mo(){return So(co)}function ko(){return So(fo)}var No=function(){return So(so)};function So(t){var n,e=mo,r=bo,i=ht(u,"start","brush","end"),o=6;function u(n){var e=n.property("__brush",l).selectAll(".overlay").data([yo("overlay")]);e.enter().append("rect").attr("class","overlay").attr("pointer-events","all").attr("cursor",lo.overlay).merge(e).each(function(){var t=_o(this).extent;nn(this).attr("x",t[0][0]).attr("y",t[0][1]).attr("width",t[1][0]-t[0][0]).attr("height",t[1][1]-t[0][1])}),n.selectAll(".selection").data([yo("selection")]).enter().append("rect").attr("class","selection").attr("cursor",lo.selection).attr("fill","#777").attr("fill-opacity",.3).attr("stroke","#fff").attr("shape-rendering","crispEdges");var r=n.selectAll(".handle").data(t.handles,function(t){return t.type});r.exit().remove(),r.enter().append("rect").attr("class",function(t){return"handle handle--"+t.type}).attr("cursor",function(t){return lo[t.type]}),n.each(a).attr("fill","none").attr("pointer-events","all").style("-webkit-tap-highlight-color","rgba(0,0,0,0)").on("mousedown.brush touchstart.brush",s)}function a(){var t=nn(this),n=_o(this).selection;n?(t.selectAll(".selection").style("display",null).attr("x",n[0][0]).attr("y",n[0][1]).attr("width",n[1][0]-n[0][0]).attr("height",n[1][1]-n[0][1]),t.selectAll(".handle").style("display",null).attr("x",function(t){return"e"===t.type[t.type.length-1]?n[1][0]-o/2:n[0][0]-o/2}).attr("y",function(t){return"s"===t.type[0]?n[1][1]-o/2:n[0][1]-o/2}).attr("width",function(t){return"n"===t.type||"s"===t.type?n[1][0]-n[0][0]+o:o}).attr("height",function(t){return"e"===t.type||"w"===t.type?n[1][1]-n[0][1]+o:o})):t.selectAll(".selection,.handle").style("display","none").attr("x",null).attr("y",null).attr("width",null).attr("height",null)}function c(t,n){return t.__brush.emitter||new f(t,n)}function f(t,n){this.that=t,this.args=n,this.state=t.__brush,this.active=0}function s(){if(Ht.touches){if(Ht.changedTouches.length<Ht.touches.length)return ro()}else if(n)return;if(r.apply(this,arguments)){var e,i,o,u,f,s,l,h,d,p,g,v,y,b=this,m=Ht.target.__data__.type,_="selection"===(Ht.metaKey?m="overlay":m)?io:Ht.altKey?ao:uo,x=t===fo?null:go[m],w=t===co?null:vo[m],M=_o(b),k=M.extent,N=M.selection,S=k[0][0],T=k[0][1],A=k[1][0],C=k[1][1],E=x&&w&&Ht.shiftKey,L=fn(b),R=L,D=c(b,arguments).beforestart();"overlay"===m?M.selection=N=[[e=t===fo?S:L[0],o=t===co?T:L[1]],[f=t===fo?A:e,l=t===co?C:o]]:(e=N[0][0],o=N[0][1],f=N[1][0],l=N[1][1]),i=e,u=o,s=f,h=l;var P=nn(b).attr("pointer-events","none"),z=P.selectAll(".overlay").attr("cursor",lo[m]);if(Ht.touches)P.on("touchmove.brush",U,!0).on("touchend.brush touchcancel.brush",I,!0);else{var O=nn(Ht.view).on("keydown.brush",function(){switch(Ht.keyCode){case 16:E=x&&w;break;case 18:_===uo&&(x&&(f=s-d*x,e=i+d*x),w&&(l=h-p*w,o=u+p*w),_=ao,q());break;case 32:_!==uo&&_!==ao||(x<0?f=s-d:x>0&&(e=i-d),w<0?l=h-p:w>0&&(o=u-p),_=oo,z.attr("cursor",lo.selection),q());break;default:return}ro()},!0).on("keyup.brush",function(){switch(Ht.keyCode){case 16:E&&(v=y=E=!1,q());break;case 18:_===ao&&(x<0?f=s:x>0&&(e=i),w<0?l=h:w>0&&(o=u),_=uo,q());break;case 32:_===oo&&(Ht.altKey?(x&&(f=s-d*x,e=i+d*x),w&&(l=h-p*w,o=u+p*w),_=ao):(x<0?f=s:x>0&&(e=i),w<0?l=h:w>0&&(o=u),_=uo),z.attr("cursor",lo[m]),q());break;default:return}ro()},!0).on("mousemove.brush",U,!0).on("mouseup.brush",I,!0);gn(Ht.view)}eo(),Kr(b),a.call(b),D.start()}function U(){var t=fn(b);!E||v||y||(Math.abs(t[0]-R[0])>Math.abs(t[1]-R[1])?y=!0:v=!0),R=t,g=!0,ro(),q()}function q(){var t;switch(d=R[0]-L[0],p=R[1]-L[1],_){case oo:case io:x&&(d=Math.max(S-e,Math.min(A-f,d)),i=e+d,s=f+d),w&&(p=Math.max(T-o,Math.min(C-l,p)),u=o+p,h=l+p);break;case uo:x<0?(d=Math.max(S-e,Math.min(A-e,d)),i=e+d,s=f):x>0&&(d=Math.max(S-f,Math.min(A-f,d)),i=e,s=f+d),w<0?(p=Math.max(T-o,Math.min(C-o,p)),u=o+p,h=l):w>0&&(p=Math.max(T-l,Math.min(C-l,p)),u=o,h=l+p);break;case ao:x&&(i=Math.max(S,Math.min(A,e-d*x)),s=Math.max(S,Math.min(A,f+d*x))),w&&(u=Math.max(T,Math.min(C,o-p*w)),h=Math.max(T,Math.min(C,l+p*w)))}s<i&&(x*=-1,t=e,e=f,f=t,t=i,i=s,s=t,m in ho&&z.attr("cursor",lo[m=ho[m]])),h<u&&(w*=-1,t=o,o=l,l=t,t=u,u=h,h=t,m in po&&z.attr("cursor",lo[m=po[m]])),M.selection&&(N=M.selection),v&&(i=N[0][0],s=N[1][0]),y&&(u=N[0][1],h=N[1][1]),N[0][0]===i&&N[0][1]===u&&N[1][0]===s&&N[1][1]===h||(M.selection=[[i,u],[s,h]],a.call(b),D.brush())}function I(){if(eo(),Ht.touches){if(Ht.touches.length)return;n&&clearTimeout(n),n=setTimeout(function(){n=null},500),P.on("touchmove.brush touchend.brush touchcancel.brush",null)}else vn(Ht.view,g),O.on("keydown.brush keyup.brush mousemove.brush mouseup.brush",null);P.attr("pointer-events","all"),z.attr("cursor",lo.overlay),M.selection&&(N=M.selection),xo(N)&&(M.selection=null,a.call(b)),D.end()}}function l(){var n=this.__brush||{selection:null};return n.extent=e.apply(this,arguments),n.dim=t,n}return u.move=function(n,e){n.selection?n.on("start.brush",function(){c(this,arguments).beforestart().start()}).on("interrupt.brush end.brush",function(){c(this,arguments).end()}).tween("brush",function(){var n=this,r=n.__brush,i=c(n,arguments),o=r.selection,u=t.input("function"==typeof e?e.apply(this,arguments):e,r.extent),f=We(o,u);function s(t){r.selection=1===t&&xo(u)?null:f(t),a.call(n),i.brush()}return o&&u?s:s(1)}):n.each(function(){var n=arguments,r=this.__brush,i=t.input("function"==typeof e?e.apply(this,n):e,r.extent),o=c(this,n).beforestart();Kr(this),r.selection=null==i||xo(i)?null:i,a.call(this),o.start().brush().end()})},f.prototype={beforestart:function(){return 1==++this.active&&(this.state.emitter=this,this.starting=!0),this},start:function(){return this.starting&&(this.starting=!1,this.emit("start")),this},brush:function(){return this.emit("brush"),this},end:function(){return 0==--this.active&&(delete this.state.emitter,this.emit("end")),this},emit:function(n){$t(new no(u,n,t.output(this.state.selection)),i.apply,i,[n,this.that,this.args])}},u.extent=function(t){return arguments.length?(e="function"==typeof t?t:to([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),u):e},u.filter=function(t){return arguments.length?(r="function"==typeof t?t:to(!!t),u):r},u.handleSize=function(t){return arguments.length?(o=+t,u):o},u.on=function(){var t=i.on.apply(i,arguments);return t===i?u:t},u}var To=Math.cos,Ao=Math.sin,Co=Math.PI,Eo=Co/2,Lo=2*Co,Ro=Math.max;var Do=function(){var t=0,n=null,e=null,r=null;function i(i){var o,u,a,c,f,s,l=i.length,h=[],d=w(l),p=[],g=[],v=g.groups=new Array(l),y=new Array(l*l);for(o=0,f=-1;++f<l;){for(u=0,s=-1;++s<l;)u+=i[f][s];h.push(u),p.push(w(l)),o+=u}for(n&&d.sort(function(t,e){return n(h[t],h[e])}),e&&p.forEach(function(t,n){t.sort(function(t,r){return e(i[n][t],i[n][r])})}),c=(o=Ro(0,Lo-t*l)/o)?t:Lo/l,u=0,f=-1;++f<l;){for(a=u,s=-1;++s<l;){var b=d[f],m=p[b][s],_=i[b][m],x=u,M=u+=_*o;y[m*l+b]={index:b,subindex:m,startAngle:x,endAngle:M,value:_}}v[b]={index:b,startAngle:a,endAngle:u,value:h[b]},u+=c}for(f=-1;++f<l;)for(s=f-1;++s<l;){var k=y[s*l+f],N=y[f*l+s];(k.value||N.value)&&g.push(k.value<N.value?{source:N,target:k}:{source:k,target:N})}return r?g.sort(r):g}return i.padAngle=function(n){return arguments.length?(t=Ro(0,n),i):t},i.sortGroups=function(t){return arguments.length?(n=t,i):n},i.sortSubgroups=function(t){return arguments.length?(e=t,i):e},i.sortChords=function(t){return arguments.length?(null==t?r=null:(n=t,r=function(t,e){return n(t.source.value+t.target.value,e.source.value+e.target.value)})._=t,i):r&&r._;var n},i},Po=Array.prototype.slice,zo=function(t){return function(){return t}},Oo=Math.PI,Uo=2*Oo,qo=Uo-1e-6;function Io(){this._x0=this._y0=this._x1=this._y1=null,this._=""}function Bo(){return new Io}Io.prototype=Bo.prototype={constructor:Io,moveTo:function(t,n){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)},closePath:function(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._+="Z")},lineTo:function(t,n){this._+="L"+(this._x1=+t)+","+(this._y1=+n)},quadraticCurveTo:function(t,n,e,r){this._+="Q"+ +t+","+ +n+","+(this._x1=+e)+","+(this._y1=+r)},bezierCurveTo:function(t,n,e,r,i,o){this._+="C"+ +t+","+ +n+","+ +e+","+ +r+","+(this._x1=+i)+","+(this._y1=+o)},arcTo:function(t,n,e,r,i){t=+t,n=+n,e=+e,r=+r,i=+i;var o=this._x1,u=this._y1,a=e-t,c=r-n,f=o-t,s=u-n,l=f*f+s*s;if(i<0)throw new Error("negative radius: "+i);if(null===this._x1)this._+="M"+(this._x1=t)+","+(this._y1=n);else if(l>1e-6)if(Math.abs(s*a-c*f)>1e-6&&i){var h=e-o,d=r-u,p=a*a+c*c,g=h*h+d*d,v=Math.sqrt(p),y=Math.sqrt(l),b=i*Math.tan((Oo-Math.acos((p+l-g)/(2*v*y)))/2),m=b/y,_=b/v;Math.abs(m-1)>1e-6&&(this._+="L"+(t+m*f)+","+(n+m*s)),this._+="A"+i+","+i+",0,0,"+ +(s*h>f*d)+","+(this._x1=t+_*a)+","+(this._y1=n+_*c)}else this._+="L"+(this._x1=t)+","+(this._y1=n);else;},arc:function(t,n,e,r,i,o){t=+t,n=+n;var u=(e=+e)*Math.cos(r),a=e*Math.sin(r),c=t+u,f=n+a,s=1^o,l=o?r-i:i-r;if(e<0)throw new Error("negative radius: "+e);null===this._x1?this._+="M"+c+","+f:(Math.abs(this._x1-c)>1e-6||Math.abs(this._y1-f)>1e-6)&&(this._+="L"+c+","+f),e&&(l<0&&(l=l%Uo+Uo),l>qo?this._+="A"+e+","+e+",0,1,"+s+","+(t-u)+","+(n-a)+"A"+e+","+e+",0,1,"+s+","+(this._x1=c)+","+(this._y1=f):l>1e-6&&(this._+="A"+e+","+e+",0,"+ +(l>=Oo)+","+s+","+(this._x1=t+e*Math.cos(i))+","+(this._y1=n+e*Math.sin(i))))},rect:function(t,n,e,r){this._+="M"+(this._x0=this._x1=+t)+","+(this._y0=this._y1=+n)+"h"+ +e+"v"+ +r+"h"+-e+"Z"},toString:function(){return this._}};var Fo=Bo;function jo(t){return t.source}function Yo(t){return t.target}function Ho(t){return t.radius}function Vo(t){return t.startAngle}function Xo(t){return t.endAngle}var Go=function(){var t=jo,n=Yo,e=Ho,r=Vo,i=Xo,o=null;function u(){var u,a=Po.call(arguments),c=t.apply(this,a),f=n.apply(this,a),s=+e.apply(this,(a[0]=c,a)),l=r.apply(this,a)-Eo,h=i.apply(this,a)-Eo,d=s*To(l),p=s*Ao(l),g=+e.apply(this,(a[0]=f,a)),v=r.apply(this,a)-Eo,y=i.apply(this,a)-Eo;if(o||(o=u=Fo()),o.moveTo(d,p),o.arc(0,0,s,l,h),l===v&&h===y||(o.quadraticCurveTo(0,0,g*To(v),g*Ao(v)),o.arc(0,0,g,v,y)),o.quadraticCurveTo(0,0,d,p),o.closePath(),u)return o=null,u+""||null}return u.radius=function(t){return arguments.length?(e="function"==typeof t?t:zo(+t),u):e},u.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:zo(+t),u):r},u.endAngle=function(t){return arguments.length?(i="function"==typeof t?t:zo(+t),u):i},u.source=function(n){return arguments.length?(t=n,u):t},u.target=function(t){return arguments.length?(n=t,u):n},u.context=function(t){return arguments.length?(o=null==t?null:t,u):o},u};function Wo(){}function $o(t,n){var e=new Wo;if(t instanceof Wo)t.each(function(t,n){e.set(n,t)});else if(Array.isArray(t)){var r,i=-1,o=t.length;if(null==n)for(;++i<o;)e.set(i,t[i]);else for(;++i<o;)e.set(n(r=t[i],i,t),r)}else if(t)for(var u in t)e.set(u,t[u]);return e}Wo.prototype=$o.prototype={constructor:Wo,has:function(t){return"$"+t in this},get:function(t){return this["$"+t]},set:function(t,n){return this["$"+t]=n,this},remove:function(t){var n="$"+t;return n in this&&delete this[n]},clear:function(){for(var t in this)"$"===t[0]&&delete this[t]},keys:function(){var t=[];for(var n in this)"$"===n[0]&&t.push(n.slice(1));return t},values:function(){var t=[];for(var n in this)"$"===n[0]&&t.push(this[n]);return t},entries:function(){var t=[];for(var n in this)"$"===n[0]&&t.push({key:n.slice(1),value:this[n]});return t},size:function(){var t=0;for(var n in this)"$"===n[0]&&++t;return t},empty:function(){for(var t in this)if("$"===t[0])return!1;return!0},each:function(t){for(var n in this)"$"===n[0]&&t(this[n],n.slice(1),this)}};var Zo=$o,Qo=function(){var t,n,e,r=[],i=[];function o(e,i,u,a){if(i>=r.length)return null!=t&&e.sort(t),null!=n?n(e):e;for(var c,f,s,l=-1,h=e.length,d=r[i++],p=Zo(),g=u();++l<h;)(s=p.get(c=d(f=e[l])+""))?s.push(f):p.set(c,[f]);return p.each(function(t,n){a(g,n,o(t,i,u,a))}),g}return e={object:function(t){return o(t,0,Jo,Ko)},map:function(t){return o(t,0,tu,nu)},entries:function(t){return function t(e,o){if(++o>r.length)return e;var u,a=i[o-1];return null!=n&&o>=r.length?u=e.entries():(u=[],e.each(function(n,e){u.push({key:e,values:t(n,o)})})),null!=a?u.sort(function(t,n){return a(t.key,n.key)}):u}(o(t,0,tu,nu),0)},key:function(t){return r.push(t),e},sortKeys:function(t){return i[r.length-1]=t,e},sortValues:function(n){return t=n,e},rollup:function(t){return n=t,e}}};function Jo(){return{}}function Ko(t,n,e){t[n]=e}function tu(){return Zo()}function nu(t,n,e){t.set(n,e)}function eu(){}var ru=Zo.prototype;function iu(t,n){var e=new eu;if(t instanceof eu)t.each(function(t){e.add(t)});else if(t){var r=-1,i=t.length;if(null==n)for(;++r<i;)e.add(t[r]);else for(;++r<i;)e.add(n(t[r],r,t))}return e}eu.prototype=iu.prototype={constructor:eu,has:ru.has,add:function(t){return this["$"+(t+="")]=t,this},remove:ru.remove,clear:ru.clear,values:ru.keys,size:ru.size,empty:ru.empty,each:ru.each};var ou=iu,uu=function(t){var n=[];for(var e in t)n.push(e);return n},au=function(t){var n=[];for(var e in t)n.push(t[e]);return n},cu=function(t){var n=[];for(var e in t)n.push({key:e,value:t[e]});return n},fu=Array.prototype.slice,su=function(t,n){return t-n},lu=function(t){for(var n=0,e=t.length,r=t[e-1][1]*t[0][0]-t[e-1][0]*t[0][1];++n<e;)r+=t[n-1][1]*t[n][0]-t[n-1][0]*t[n][1];return r},hu=function(t){return function(){return t}},du=function(t,n){for(var e,r=-1,i=n.length;++r<i;)if(e=pu(t,n[r]))return e;return 0};function pu(t,n){for(var e=n[0],r=n[1],i=-1,o=0,u=t.length,a=u-1;o<u;a=o++){var c=t[o],f=c[0],s=c[1],l=t[a],h=l[0],d=l[1];if(gu(c,l,n))return 0;s>r!=d>r&&e<(h-f)*(r-s)/(d-s)+f&&(i=-i)}return i}function gu(t,n,e){var r,i,o,u;return function(t,n,e){return(n[0]-t[0])*(e[1]-t[1])==(e[0]-t[0])*(n[1]-t[1])}(t,n,e)&&(i=t[r=+(t[0]===n[0])],o=e[r],u=n[r],i<=o&&o<=u||u<=o&&o<=i)}var vu=function(){},yu=[[],[[[1,1.5],[.5,1]]],[[[1.5,1],[1,1.5]]],[[[1.5,1],[.5,1]]],[[[1,.5],[1.5,1]]],[[[1,1.5],[.5,1]],[[1,.5],[1.5,1]]],[[[1,.5],[1,1.5]]],[[[1,.5],[.5,1]]],[[[.5,1],[1,.5]]],[[[1,1.5],[1,.5]]],[[[.5,1],[1,.5]],[[1.5,1],[1,1.5]]],[[[1.5,1],[1,.5]]],[[[.5,1],[1.5,1]]],[[[1,1.5],[1.5,1]]],[[[.5,1],[1,1.5]]],[]],bu=function(){var t=1,n=1,e=C,r=a;function i(t){var n=e(t);if(Array.isArray(n))n=n.slice().sort(su);else{var r=v(t),i=r[0],u=r[1];n=A(i,u,n),n=w(Math.floor(i/n)*n,Math.floor(u/n)*n,n)}return n.map(function(n){return o(t,n)})}function o(e,i){var o=[],a=[];return function(e,r,i){var o,a,c,f,s,l,h=new Array,d=new Array;o=a=-1,f=e[0]>=r,yu[f<<1].forEach(p);for(;++o<t-1;)c=f,f=e[o+1]>=r,yu[c|f<<1].forEach(p);yu[f<<0].forEach(p);for(;++a<n-1;){for(o=-1,f=e[a*t+t]>=r,s=e[a*t]>=r,yu[f<<1|s<<2].forEach(p);++o<t-1;)c=f,f=e[a*t+t+o+1]>=r,l=s,s=e[a*t+o+1]>=r,yu[c|f<<1|s<<2|l<<3].forEach(p);yu[f|s<<3].forEach(p)}o=-1,s=e[a*t]>=r,yu[s<<2].forEach(p);for(;++o<t-1;)l=s,s=e[a*t+o+1]>=r,yu[s<<2|l<<3].forEach(p);function p(t){var n,e,r=[t[0][0]+o,t[0][1]+a],c=[t[1][0]+o,t[1][1]+a],f=u(r),s=u(c);(n=d[f])?(e=h[s])?(delete d[n.end],delete h[e.start],n===e?(n.ring.push(c),i(n.ring)):h[n.start]=d[e.end]={start:n.start,end:e.end,ring:n.ring.concat(e.ring)}):(delete d[n.end],n.ring.push(c),d[n.end=s]=n):(n=h[s])?(e=d[f])?(delete h[n.start],delete d[e.end],n===e?(n.ring.push(c),i(n.ring)):h[e.start]=d[n.end]={start:e.start,end:n.end,ring:e.ring.concat(n.ring)}):(delete h[n.start],n.ring.unshift(r),h[n.start=f]=n):h[f]=d[s]={start:f,end:s,ring:[r,c]}}yu[s<<3].forEach(p)}(e,i,function(t){r(t,e,i),lu(t)>0?o.push([t]):a.push(t)}),a.forEach(function(t){for(var n,e=0,r=o.length;e<r;++e)if(-1!==du((n=o[e])[0],t))return void n.push(t)}),{type:"MultiPolygon",value:i,coordinates:o}}function u(n){return 2*n[0]+n[1]*(t+1)*4}function a(e,r,i){e.forEach(function(e){var o,u=e[0],a=e[1],c=0|u,f=0|a,s=r[f*t+c];u>0&&u<t&&c===u&&(o=r[f*t+c-1],e[0]=u+(i-o)/(s-o)-.5),a>0&&a<n&&f===a&&(o=r[(f-1)*t+c],e[1]=a+(i-o)/(s-o)-.5)})}return i.contour=o,i.size=function(e){if(!arguments.length)return[t,n];var r=Math.ceil(e[0]),o=Math.ceil(e[1]);if(!(r>0&&o>0))throw new Error("invalid size");return t=r,n=o,i},i.thresholds=function(t){return arguments.length?(e="function"==typeof t?t:Array.isArray(t)?hu(fu.call(t)):hu(t),i):e},i.smooth=function(t){return arguments.length?(r=t?a:vu,i):r===a},i};function mu(t,n,e){for(var r=t.width,i=t.height,o=1+(e<<1),u=0;u<i;++u)for(var a=0,c=0;a<r+e;++a)a<r&&(c+=t.data[a+u*r]),a>=e&&(a>=o&&(c-=t.data[a-o+u*r]),n.data[a-e+u*r]=c/Math.min(a+1,r-1+o-a,o))}function _u(t,n,e){for(var r=t.width,i=t.height,o=1+(e<<1),u=0;u<r;++u)for(var a=0,c=0;a<i+e;++a)a<i&&(c+=t.data[u+a*r]),a>=e&&(a>=o&&(c-=t.data[u+(a-o)*r]),n.data[u+(a-e)*r]=c/Math.min(a+1,i-1+o-a,o))}function xu(t){return t[0]}function wu(t){return t[1]}function Mu(){return 1}var ku=function(){var t=xu,n=wu,e=Mu,r=960,i=500,o=20,u=2,a=3*o,c=r+2*a>>u,f=i+2*a>>u,s=hu(20);function l(r){var i=new Float32Array(c*f),l=new Float32Array(c*f);r.forEach(function(r,o,s){var l=+t(r,o,s)+a>>u,h=+n(r,o,s)+a>>u,d=+e(r,o,s);l>=0&&l<c&&h>=0&&h<f&&(i[l+h*c]+=d)}),mu({width:c,height:f,data:i},{width:c,height:f,data:l},o>>u),_u({width:c,height:f,data:l},{width:c,height:f,data:i},o>>u),mu({width:c,height:f,data:i},{width:c,height:f,data:l},o>>u),_u({width:c,height:f,data:l},{width:c,height:f,data:i},o>>u),mu({width:c,height:f,data:i},{width:c,height:f,data:l},o>>u),_u({width:c,height:f,data:l},{width:c,height:f,data:i},o>>u);var d=s(i);if(!Array.isArray(d)){var p=P(i);d=A(0,p,d),(d=w(0,Math.floor(p/d)*d,d)).shift()}return bu().thresholds(d).size([c,f])(i).map(h)}function h(t){return t.value*=Math.pow(2,-2*u),t.coordinates.forEach(d),t}function d(t){t.forEach(p)}function p(t){t.forEach(g)}function g(t){t[0]=t[0]*Math.pow(2,u)-a,t[1]=t[1]*Math.pow(2,u)-a}function v(){return c=r+2*(a=3*o)>>u,f=i+2*a>>u,l}return l.x=function(n){return arguments.length?(t="function"==typeof n?n:hu(+n),l):t},l.y=function(t){return arguments.length?(n="function"==typeof t?t:hu(+t),l):n},l.weight=function(t){return arguments.length?(e="function"==typeof t?t:hu(+t),l):e},l.size=function(t){if(!arguments.length)return[r,i];var n=Math.ceil(t[0]),e=Math.ceil(t[1]);if(!(n>=0||n>=0))throw new Error("invalid size");return r=n,i=e,v()},l.cellSize=function(t){if(!arguments.length)return 1<<u;if(!((t=+t)>=1))throw new Error("invalid cell size");return u=Math.floor(Math.log(t)/Math.LN2),v()},l.thresholds=function(t){return arguments.length?(s="function"==typeof t?t:Array.isArray(t)?hu(fu.call(t)):hu(t),l):s},l.bandwidth=function(t){if(!arguments.length)return Math.sqrt(o*(o+1));if(!((t=+t)>=0))throw new Error("invalid bandwidth");return o=Math.round((Math.sqrt(4*t*t+1)-1)/2),v()},l},Nu={},Su={},Tu=34,Au=10,Cu=13;function Eu(t){return new Function("d","return {"+t.map(function(t,n){return JSON.stringify(t)+": d["+n+"]"}).join(",")+"}")}function Lu(t){var n=Object.create(null),e=[];return t.forEach(function(t){for(var r in t)r in n||e.push(n[r]=r)}),e}function Ru(t,n){var e=t+"",r=e.length;return r<n?new Array(n-r+1).join(0)+e:e}function Du(t){var n,e=t.getUTCHours(),r=t.getUTCMinutes(),i=t.getUTCSeconds(),o=t.getUTCMilliseconds();return isNaN(t)?"Invalid Date":((n=t.getUTCFullYear())<0?"-"+Ru(-n,6):n>9999?"+"+Ru(n,6):Ru(n,4))+"-"+Ru(t.getUTCMonth()+1,2)+"-"+Ru(t.getUTCDate(),2)+(o?"T"+Ru(e,2)+":"+Ru(r,2)+":"+Ru(i,2)+"."+Ru(o,3)+"Z":i?"T"+Ru(e,2)+":"+Ru(r,2)+":"+Ru(i,2)+"Z":r||e?"T"+Ru(e,2)+":"+Ru(r,2)+"Z":"")}var Pu=function(t){var n=new RegExp('["'+t+"\n\r]"),e=t.charCodeAt(0);function r(t,n){var r,i=[],o=t.length,u=0,a=0,c=o<=0,f=!1;function s(){if(c)return Su;if(f)return f=!1,Nu;var n,r,i=u;if(t.charCodeAt(i)===Tu){for(;u++<o&&t.charCodeAt(u)!==Tu||t.charCodeAt(++u)===Tu;);return(n=u)>=o?c=!0:(r=t.charCodeAt(u++))===Au?f=!0:r===Cu&&(f=!0,t.charCodeAt(u)===Au&&++u),t.slice(i+1,n-1).replace(/""/g,'"')}for(;u<o;){if((r=t.charCodeAt(n=u++))===Au)f=!0;else if(r===Cu)f=!0,t.charCodeAt(u)===Au&&++u;else if(r!==e)continue;return t.slice(i,n)}return c=!0,t.slice(i,o)}for(t.charCodeAt(o-1)===Au&&--o,t.charCodeAt(o-1)===Cu&&--o;(r=s())!==Su;){for(var l=[];r!==Nu&&r!==Su;)l.push(r),r=s();n&&null==(l=n(l,a++))||i.push(l)}return i}function i(n,e){return n.map(function(n){return e.map(function(t){return u(n[t])}).join(t)})}function o(n){return n.map(u).join(t)}function u(t){return null==t?"":t instanceof Date?Du(t):n.test(t+="")?'"'+t.replace(/"/g,'""')+'"':t}return{parse:function(t,n){var e,i,o=r(t,function(t,r){if(e)return e(t,r-1);i=t,e=n?function(t,n){var e=Eu(t);return function(r,i){return n(e(r),i,t)}}(t,n):Eu(t)});return o.columns=i||[],o},parseRows:r,format:function(n,e){return null==e&&(e=Lu(n)),[e.map(u).join(t)].concat(i(n,e)).join("\n")},formatBody:function(t,n){return null==n&&(n=Lu(t)),i(t,n).join("\n")},formatRows:function(t){return t.map(o).join("\n")}}},zu=Pu(","),Ou=zu.parse,Uu=zu.parseRows,qu=zu.format,Iu=zu.formatBody,Bu=zu.formatRows,Fu=Pu("\t"),ju=Fu.parse,Yu=Fu.parseRows,Hu=Fu.format,Vu=Fu.formatBody,Xu=Fu.formatRows;function Gu(t){for(var n in t){var e,r=t[n].trim();if(r)if("true"===r)r=!0;else if("false"===r)r=!1;else if("NaN"===r)r=NaN;else if(isNaN(e=+r)){if(!/^([-+]\d{2})?\d{4}(-\d{2}(-\d{2})?)?(T\d{2}:\d{2}(:\d{2}(\.\d{3})?)?(Z|[-+]\d{2}:\d{2})?)?$/.test(r))continue;r=new Date(r)}else r=e;else r=null;t[n]=r}return t}function Wu(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.blob()}var $u=function(t,n){return fetch(t,n).then(Wu)};function Zu(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.arrayBuffer()}var Qu=function(t,n){return fetch(t,n).then(Zu)};function Ju(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.text()}var Ku=function(t,n){return fetch(t,n).then(Ju)};function ta(t){return function(n,e,r){return 2===arguments.length&&"function"==typeof e&&(r=e,e=void 0),Ku(n,e).then(function(n){return t(n,r)})}}function na(t,n,e,r){3===arguments.length&&"function"==typeof e&&(r=e,e=void 0);var i=Pu(t);return Ku(n,e).then(function(t){return i.parse(t,r)})}var ea=ta(Ou),ra=ta(ju),ia=function(t,n){return new Promise(function(e,r){var i=new Image;for(var o in n)i[o]=n[o];i.onerror=r,i.onload=function(){e(i)},i.src=t})};function oa(t){if(!t.ok)throw new Error(t.status+" "+t.statusText);return t.json()}var ua=function(t,n){return fetch(t,n).then(oa)};function aa(t){return function(n,e){return Ku(n,e).then(function(n){return(new DOMParser).parseFromString(n,t)})}}var ca=aa("application/xml"),fa=aa("text/html"),sa=aa("image/svg+xml"),la=function(t,n){var e;function r(){var r,i,o=e.length,u=0,a=0;for(r=0;r<o;++r)u+=(i=e[r]).x,a+=i.y;for(u=u/o-t,a=a/o-n,r=0;r<o;++r)(i=e[r]).x-=u,i.y-=a}return null==t&&(t=0),null==n&&(n=0),r.initialize=function(t){e=t},r.x=function(n){return arguments.length?(t=+n,r):t},r.y=function(t){return arguments.length?(n=+t,r):n},r},ha=function(t){return function(){return t}},da=function(){return 1e-6*(Math.random()-.5)};function pa(t,n,e,r){if(isNaN(n)||isNaN(e))return t;var i,o,u,a,c,f,s,l,h,d=t._root,p={data:r},g=t._x0,v=t._y0,y=t._x1,b=t._y1;if(!d)return t._root=p,t;for(;d.length;)if((f=n>=(o=(g+y)/2))?g=o:y=o,(s=e>=(u=(v+b)/2))?v=u:b=u,i=d,!(d=d[l=s<<1|f]))return i[l]=p,t;if(a=+t._x.call(null,d.data),c=+t._y.call(null,d.data),n===a&&e===c)return p.next=d,i?i[l]=p:t._root=p,t;do{i=i?i[l]=new Array(4):t._root=new Array(4),(f=n>=(o=(g+y)/2))?g=o:y=o,(s=e>=(u=(v+b)/2))?v=u:b=u}while((l=s<<1|f)==(h=(c>=u)<<1|a>=o));return i[h]=d,i[l]=p,t}var ga=function(t,n,e,r,i){this.node=t,this.x0=n,this.y0=e,this.x1=r,this.y1=i};function va(t){return t[0]}function ya(t){return t[1]}function ba(t,n,e){var r=new ma(null==n?va:n,null==e?ya:e,NaN,NaN,NaN,NaN);return null==t?r:r.addAll(t)}function ma(t,n,e,r,i,o){this._x=t,this._y=n,this._x0=e,this._y0=r,this._x1=i,this._y1=o,this._root=void 0}function _a(t){for(var n={data:t.data},e=n;t=t.next;)e=e.next={data:t.data};return n}var xa=ba.prototype=ma.prototype;function wa(t){return t.x+t.vx}function Ma(t){return t.y+t.vy}xa.copy=function(){var t,n,e=new ma(this._x,this._y,this._x0,this._y0,this._x1,this._y1),r=this._root;if(!r)return e;if(!r.length)return e._root=_a(r),e;for(t=[{source:r,target:e._root=new Array(4)}];r=t.pop();)for(var i=0;i<4;++i)(n=r.source[i])&&(n.length?t.push({source:n,target:r.target[i]=new Array(4)}):r.target[i]=_a(n));return e},xa.add=function(t){var n=+this._x.call(null,t),e=+this._y.call(null,t);return pa(this.cover(n,e),n,e,t)},xa.addAll=function(t){var n,e,r,i,o=t.length,u=new Array(o),a=new Array(o),c=1/0,f=1/0,s=-1/0,l=-1/0;for(e=0;e<o;++e)isNaN(r=+this._x.call(null,n=t[e]))||isNaN(i=+this._y.call(null,n))||(u[e]=r,a[e]=i,r<c&&(c=r),r>s&&(s=r),i<f&&(f=i),i>l&&(l=i));if(c>s||f>l)return this;for(this.cover(c,f).cover(s,l),e=0;e<o;++e)pa(this,u[e],a[e],t[e]);return this},xa.cover=function(t,n){if(isNaN(t=+t)||isNaN(n=+n))return this;var e=this._x0,r=this._y0,i=this._x1,o=this._y1;if(isNaN(e))i=(e=Math.floor(t))+1,o=(r=Math.floor(n))+1;else{for(var u,a,c=i-e,f=this._root;e>t||t>=i||r>n||n>=o;)switch(a=(n<r)<<1|t<e,(u=new Array(4))[a]=f,f=u,c*=2,a){case 0:i=e+c,o=r+c;break;case 1:e=i-c,o=r+c;break;case 2:i=e+c,r=o-c;break;case 3:e=i-c,r=o-c}this._root&&this._root.length&&(this._root=f)}return this._x0=e,this._y0=r,this._x1=i,this._y1=o,this},xa.data=function(){var t=[];return this.visit(function(n){if(!n.length)do{t.push(n.data)}while(n=n.next)}),t},xa.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},xa.find=function(t,n,e){var r,i,o,u,a,c,f,s=this._x0,l=this._y0,h=this._x1,d=this._y1,p=[],g=this._root;for(g&&p.push(new ga(g,s,l,h,d)),null==e?e=1/0:(s=t-e,l=n-e,h=t+e,d=n+e,e*=e);c=p.pop();)if(!(!(g=c.node)||(i=c.x0)>h||(o=c.y0)>d||(u=c.x1)<s||(a=c.y1)<l))if(g.length){var v=(i+u)/2,y=(o+a)/2;p.push(new ga(g[3],v,y,u,a),new ga(g[2],i,y,v,a),new ga(g[1],v,o,u,y),new ga(g[0],i,o,v,y)),(f=(n>=y)<<1|t>=v)&&(c=p[p.length-1],p[p.length-1]=p[p.length-1-f],p[p.length-1-f]=c)}else{var b=t-+this._x.call(null,g.data),m=n-+this._y.call(null,g.data),_=b*b+m*m;if(_<e){var x=Math.sqrt(e=_);s=t-x,l=n-x,h=t+x,d=n+x,r=g.data}}return r},xa.remove=function(t){if(isNaN(o=+this._x.call(null,t))||isNaN(u=+this._y.call(null,t)))return this;var n,e,r,i,o,u,a,c,f,s,l,h,d=this._root,p=this._x0,g=this._y0,v=this._x1,y=this._y1;if(!d)return this;if(d.length)for(;;){if((f=o>=(a=(p+v)/2))?p=a:v=a,(s=u>=(c=(g+y)/2))?g=c:y=c,n=d,!(d=d[l=s<<1|f]))return this;if(!d.length)break;(n[l+1&3]||n[l+2&3]||n[l+3&3])&&(e=n,h=l)}for(;d.data!==t;)if(r=d,!(d=d.next))return this;return(i=d.next)&&delete d.next,r?(i?r.next=i:delete r.next,this):n?(i?n[l]=i:delete n[l],(d=n[0]||n[1]||n[2]||n[3])&&d===(n[3]||n[2]||n[1]||n[0])&&!d.length&&(e?e[h]=d:this._root=d),this):(this._root=i,this)},xa.removeAll=function(t){for(var n=0,e=t.length;n<e;++n)this.remove(t[n]);return this},xa.root=function(){return this._root},xa.size=function(){var t=0;return this.visit(function(n){if(!n.length)do{++t}while(n=n.next)}),t},xa.visit=function(t){var n,e,r,i,o,u,a=[],c=this._root;for(c&&a.push(new ga(c,this._x0,this._y0,this._x1,this._y1));n=a.pop();)if(!t(c=n.node,r=n.x0,i=n.y0,o=n.x1,u=n.y1)&&c.length){var f=(r+o)/2,s=(i+u)/2;(e=c[3])&&a.push(new ga(e,f,s,o,u)),(e=c[2])&&a.push(new ga(e,r,s,f,u)),(e=c[1])&&a.push(new ga(e,f,i,o,s)),(e=c[0])&&a.push(new ga(e,r,i,f,s))}return this},xa.visitAfter=function(t){var n,e=[],r=[];for(this._root&&e.push(new ga(this._root,this._x0,this._y0,this._x1,this._y1));n=e.pop();){var i=n.node;if(i.length){var o,u=n.x0,a=n.y0,c=n.x1,f=n.y1,s=(u+c)/2,l=(a+f)/2;(o=i[0])&&e.push(new ga(o,u,a,s,l)),(o=i[1])&&e.push(new ga(o,s,a,c,l)),(o=i[2])&&e.push(new ga(o,u,l,s,f)),(o=i[3])&&e.push(new ga(o,s,l,c,f))}r.push(n)}for(;n=r.pop();)t(n.node,n.x0,n.y0,n.x1,n.y1);return this},xa.x=function(t){return arguments.length?(this._x=t,this):this._x},xa.y=function(t){return arguments.length?(this._y=t,this):this._y};var ka=function(t){var n,e,r=1,i=1;function o(){for(var t,o,a,c,f,s,l,h=n.length,d=0;d<i;++d)for(o=ba(n,wa,Ma).visitAfter(u),t=0;t<h;++t)a=n[t],s=e[a.index],l=s*s,c=a.x+a.vx,f=a.y+a.vy,o.visit(p);function p(t,n,e,i,o){var u=t.data,h=t.r,d=s+h;if(!u)return n>c+d||i<c-d||e>f+d||o<f-d;if(u.index>a.index){var p=c-u.x-u.vx,g=f-u.y-u.vy,v=p*p+g*g;v<d*d&&(0===p&&(v+=(p=da())*p),0===g&&(v+=(g=da())*g),v=(d-(v=Math.sqrt(v)))/v*r,a.vx+=(p*=v)*(d=(h*=h)/(l+h)),a.vy+=(g*=v)*d,u.vx-=p*(d=1-d),u.vy-=g*d)}}}function u(t){if(t.data)return t.r=e[t.data.index];for(var n=t.r=0;n<4;++n)t[n]&&t[n].r>t.r&&(t.r=t[n].r)}function a(){if(n){var r,i,o=n.length;for(e=new Array(o),r=0;r<o;++r)i=n[r],e[i.index]=+t(i,r,n)}}return"function"!=typeof t&&(t=ha(null==t?1:+t)),o.initialize=function(t){n=t,a()},o.iterations=function(t){return arguments.length?(i=+t,o):i},o.strength=function(t){return arguments.length?(r=+t,o):r},o.radius=function(n){return arguments.length?(t="function"==typeof n?n:ha(+n),a(),o):t},o};function Na(t){return t.index}function Sa(t,n){var e=t.get(n);if(!e)throw new Error("missing: "+n);return e}var Ta=function(t){var n,e,r,i,o,u=Na,a=function(t){return 1/Math.min(i[t.source.index],i[t.target.index])},c=ha(30),f=1;function s(r){for(var i=0,u=t.length;i<f;++i)for(var a,c,s,l,h,d,p,g=0;g<u;++g)c=(a=t[g]).source,l=(s=a.target).x+s.vx-c.x-c.vx||da(),h=s.y+s.vy-c.y-c.vy||da(),l*=d=((d=Math.sqrt(l*l+h*h))-e[g])/d*r*n[g],h*=d,s.vx-=l*(p=o[g]),s.vy-=h*p,c.vx+=l*(p=1-p),c.vy+=h*p}function l(){if(r){var a,c,f=r.length,s=t.length,l=Zo(r,u);for(a=0,i=new Array(f);a<s;++a)(c=t[a]).index=a,"object"!=typeof c.source&&(c.source=Sa(l,c.source)),"object"!=typeof c.target&&(c.target=Sa(l,c.target)),i[c.source.index]=(i[c.source.index]||0)+1,i[c.target.index]=(i[c.target.index]||0)+1;for(a=0,o=new Array(s);a<s;++a)c=t[a],o[a]=i[c.source.index]/(i[c.source.index]+i[c.target.index]);n=new Array(s),h(),e=new Array(s),d()}}function h(){if(r)for(var e=0,i=t.length;e<i;++e)n[e]=+a(t[e],e,t)}function d(){if(r)for(var n=0,i=t.length;n<i;++n)e[n]=+c(t[n],n,t)}return null==t&&(t=[]),s.initialize=function(t){r=t,l()},s.links=function(n){return arguments.length?(t=n,l(),s):t},s.id=function(t){return arguments.length?(u=t,s):u},s.iterations=function(t){return arguments.length?(f=+t,s):f},s.strength=function(t){return arguments.length?(a="function"==typeof t?t:ha(+t),h(),s):a},s.distance=function(t){return arguments.length?(c="function"==typeof t?t:ha(+t),d(),s):c},s};function Aa(t){return t.x}function Ca(t){return t.y}var Ea=10,La=Math.PI*(3-Math.sqrt(5)),Ra=function(t){var n,e=1,r=.001,i=1-Math.pow(r,1/300),o=0,u=.6,a=Zo(),c=Dr(s),f=ht("tick","end");function s(){l(),f.call("tick",n),e<r&&(c.stop(),f.call("end",n))}function l(r){var c,f,s=t.length;void 0===r&&(r=1);for(var l=0;l<r;++l)for(e+=(o-e)*i,a.each(function(t){t(e)}),c=0;c<s;++c)null==(f=t[c]).fx?f.x+=f.vx*=u:(f.x=f.fx,f.vx=0),null==f.fy?f.y+=f.vy*=u:(f.y=f.fy,f.vy=0);return n}function h(){for(var n,e=0,r=t.length;e<r;++e){if((n=t[e]).index=e,null!=n.fx&&(n.x=n.fx),null!=n.fy&&(n.y=n.fy),isNaN(n.x)||isNaN(n.y)){var i=Ea*Math.sqrt(e),o=e*La;n.x=i*Math.cos(o),n.y=i*Math.sin(o)}(isNaN(n.vx)||isNaN(n.vy))&&(n.vx=n.vy=0)}}function d(n){return n.initialize&&n.initialize(t),n}return null==t&&(t=[]),h(),n={tick:l,restart:function(){return c.restart(s),n},stop:function(){return c.stop(),n},nodes:function(e){return arguments.length?(t=e,h(),a.each(d),n):t},alpha:function(t){return arguments.length?(e=+t,n):e},alphaMin:function(t){return arguments.length?(r=+t,n):r},alphaDecay:function(t){return arguments.length?(i=+t,n):+i},alphaTarget:function(t){return arguments.length?(o=+t,n):o},velocityDecay:function(t){return arguments.length?(u=1-t,n):1-u},force:function(t,e){return arguments.length>1?(null==e?a.remove(t):a.set(t,d(e)),n):a.get(t)},find:function(n,e,r){var i,o,u,a,c,f=0,s=t.length;for(null==r?r=1/0:r*=r,f=0;f<s;++f)(u=(i=n-(a=t[f]).x)*i+(o=e-a.y)*o)<r&&(c=a,r=u);return c},on:function(t,e){return arguments.length>1?(f.on(t,e),n):f.on(t)}}},Da=function(){var t,n,e,r,i=ha(-30),o=1,u=1/0,a=.81;function c(r){var i,o=t.length,u=ba(t,Aa,Ca).visitAfter(s);for(e=r,i=0;i<o;++i)n=t[i],u.visit(l)}function f(){if(t){var n,e,o=t.length;for(r=new Array(o),n=0;n<o;++n)e=t[n],r[e.index]=+i(e,n,t)}}function s(t){var n,e,i,o,u,a=0,c=0;if(t.length){for(i=o=u=0;u<4;++u)(n=t[u])&&(e=Math.abs(n.value))&&(a+=n.value,c+=e,i+=e*n.x,o+=e*n.y);t.x=i/c,t.y=o/c}else{(n=t).x=n.data.x,n.y=n.data.y;do{a+=r[n.data.index]}while(n=n.next)}t.value=a}function l(t,i,c,f){if(!t.value)return!0;var s=t.x-n.x,l=t.y-n.y,h=f-i,d=s*s+l*l;if(h*h/a<d)return d<u&&(0===s&&(d+=(s=da())*s),0===l&&(d+=(l=da())*l),d<o&&(d=Math.sqrt(o*d)),n.vx+=s*t.value*e/d,n.vy+=l*t.value*e/d),!0;if(!(t.length||d>=u)){(t.data!==n||t.next)&&(0===s&&(d+=(s=da())*s),0===l&&(d+=(l=da())*l),d<o&&(d=Math.sqrt(o*d)));do{t.data!==n&&(h=r[t.data.index]*e/d,n.vx+=s*h,n.vy+=l*h)}while(t=t.next)}}return c.initialize=function(n){t=n,f()},c.strength=function(t){return arguments.length?(i="function"==typeof t?t:ha(+t),f(),c):i},c.distanceMin=function(t){return arguments.length?(o=t*t,c):Math.sqrt(o)},c.distanceMax=function(t){return arguments.length?(u=t*t,c):Math.sqrt(u)},c.theta=function(t){return arguments.length?(a=t*t,c):Math.sqrt(a)},c},Pa=function(t,n,e){var r,i,o,u=ha(.1);function a(t){for(var u=0,a=r.length;u<a;++u){var c=r[u],f=c.x-n||1e-6,s=c.y-e||1e-6,l=Math.sqrt(f*f+s*s),h=(o[u]-l)*i[u]*t/l;c.vx+=f*h,c.vy+=s*h}}function c(){if(r){var n,e=r.length;for(i=new Array(e),o=new Array(e),n=0;n<e;++n)o[n]=+t(r[n],n,r),i[n]=isNaN(o[n])?0:+u(r[n],n,r)}}return"function"!=typeof t&&(t=ha(+t)),null==n&&(n=0),null==e&&(e=0),a.initialize=function(t){r=t,c()},a.strength=function(t){return arguments.length?(u="function"==typeof t?t:ha(+t),c(),a):u},a.radius=function(n){return arguments.length?(t="function"==typeof n?n:ha(+n),c(),a):t},a.x=function(t){return arguments.length?(n=+t,a):n},a.y=function(t){return arguments.length?(e=+t,a):e},a},za=function(t){var n,e,r,i=ha(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)(i=n[o]).vx+=(r[o]-i.x)*e[o]*t}function u(){if(n){var o,u=n.length;for(e=new Array(u),r=new Array(u),o=0;o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=ha(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:ha(+t),u(),o):i},o.x=function(n){return arguments.length?(t="function"==typeof n?n:ha(+n),u(),o):t},o},Oa=function(t){var n,e,r,i=ha(.1);function o(t){for(var i,o=0,u=n.length;o<u;++o)(i=n[o]).vy+=(r[o]-i.y)*e[o]*t}function u(){if(n){var o,u=n.length;for(e=new Array(u),r=new Array(u),o=0;o<u;++o)e[o]=isNaN(r[o]=+t(n[o],o,n))?0:+i(n[o],o,n)}}return"function"!=typeof t&&(t=ha(null==t?0:+t)),o.initialize=function(t){n=t,u()},o.strength=function(t){return arguments.length?(i="function"==typeof t?t:ha(+t),u(),o):i},o.y=function(n){return arguments.length?(t="function"==typeof n?n:ha(+n),u(),o):t},o},Ua=function(t,n){if((e=(t=n?t.toExponential(n-1):t.toExponential()).indexOf("e"))<0)return null;var e,r=t.slice(0,e);return[r.length>1?r[0]+r.slice(2):r,+t.slice(e+1)]},qa=function(t){return(t=Ua(Math.abs(t)))?t[1]:NaN},Ia=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function Ba(t){return new Fa(t)}function Fa(t){if(!(n=Ia.exec(t)))throw new Error("invalid format: "+t);var n;this.fill=n[1]||" ",this.align=n[2]||">",this.sign=n[3]||"-",this.symbol=n[4]||"",this.zero=!!n[5],this.width=n[6]&&+n[6],this.comma=!!n[7],this.precision=n[8]&&+n[8].slice(1),this.trim=!!n[9],this.type=n[10]||""}Ba.prototype=Fa.prototype,Fa.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(null==this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(null==this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var ja,Ya,Ha,Va,Xa=function(t){t:for(var n,e=t.length,r=1,i=-1;r<e;++r)switch(t[r]){case".":i=n=r;break;case"0":0===i&&(i=r),n=r;break;default:if(i>0){if(!+t[r])break t;i=0}}return i>0?t.slice(0,i)+t.slice(n+1):t},Ga=function(t,n){var e=Ua(t,n);if(!e)return t+"";var r=e[0],i=e[1];return i<0?"0."+new Array(-i).join("0")+r:r.length>i+1?r.slice(0,i+1)+"."+r.slice(i+1):r+new Array(i-r.length+2).join("0")},Wa={"%":function(t,n){return(100*t).toFixed(n)},b:function(t){return Math.round(t).toString(2)},c:function(t){return t+""},d:function(t){return Math.round(t).toString(10)},e:function(t,n){return t.toExponential(n)},f:function(t,n){return t.toFixed(n)},g:function(t,n){return t.toPrecision(n)},o:function(t){return Math.round(t).toString(8)},p:function(t,n){return Ga(100*t,n)},r:Ga,s:function(t,n){var e=Ua(t,n);if(!e)return t+"";var r=e[0],i=e[1],o=i-(ja=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,u=r.length;return o===u?r:o>u?r+new Array(o-u+1).join("0"):o>0?r.slice(0,o)+"."+r.slice(o):"0."+new Array(1-o).join("0")+Ua(t,Math.max(0,n+o-1))[0]},X:function(t){return Math.round(t).toString(16).toUpperCase()},x:function(t){return Math.round(t).toString(16)}},$a=function(t){return t},Za=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"],Qa=function(t){var n,e,r=t.grouping&&t.thousands?(n=t.grouping,e=t.thousands,function(t,r){for(var i=t.length,o=[],u=0,a=n[0],c=0;i>0&&a>0&&(c+a+1>r&&(a=Math.max(1,r-c)),o.push(t.substring(i-=a,i+a)),!((c+=a+1)>r));)a=n[u=(u+1)%n.length];return o.reverse().join(e)}):$a,i=t.currency,o=t.decimal,u=t.numerals?function(t){return function(n){return n.replace(/[0-9]/g,function(n){return t[+n]})}}(t.numerals):$a,a=t.percent||"%";function c(t){var n=(t=Ba(t)).fill,e=t.align,c=t.sign,f=t.symbol,s=t.zero,l=t.width,h=t.comma,d=t.precision,p=t.trim,g=t.type;"n"===g?(h=!0,g="g"):Wa[g]||(null==d&&(d=12),p=!0,g="g"),(s||"0"===n&&"="===e)&&(s=!0,n="0",e="=");var v="$"===f?i[0]:"#"===f&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",y="$"===f?i[1]:/[%p]/.test(g)?a:"",b=Wa[g],m=/[defgprs%]/.test(g);function _(t){var i,a,f,_=v,x=y;if("c"===g)x=b(t)+x,t="";else{var w=(t=+t)<0;if(t=b(Math.abs(t),d),p&&(t=Xa(t)),w&&0==+t&&(w=!1),_=(w?"("===c?c:"-":"-"===c||"("===c?"":c)+_,x=("s"===g?Za[8+ja/3]:"")+x+(w&&"("===c?")":""),m)for(i=-1,a=t.length;++i<a;)if(48>(f=t.charCodeAt(i))||f>57){x=(46===f?o+t.slice(i+1):t.slice(i))+x,t=t.slice(0,i);break}}h&&!s&&(t=r(t,1/0));var M=_.length+t.length+x.length,k=M<l?new Array(l-M+1).join(n):"";switch(h&&s&&(t=r(k+t,k.length?l-x.length:1/0),k=""),e){case"<":t=_+t+x+k;break;case"=":t=_+k+t+x;break;case"^":t=k.slice(0,M=k.length>>1)+_+t+x+k.slice(M);break;default:t=k+_+t+x}return u(t)}return d=null==d?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,d)):Math.max(0,Math.min(20,d)),_.toString=function(){return t+""},_}return{format:c,formatPrefix:function(t,n){var e=c(((t=Ba(t)).type="f",t)),r=3*Math.max(-8,Math.min(8,Math.floor(qa(n)/3))),i=Math.pow(10,-r),o=Za[8+r/3];return function(t){return e(i*t)+o}}}};function Ja(t){return Ya=Qa(t),Ha=Ya.format,Va=Ya.formatPrefix,Ya}Ja({decimal:".",thousands:",",grouping:[3],currency:["$",""]});var Ka=function(t){return Math.max(0,-qa(Math.abs(t)))},tc=function(t,n){return Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(qa(n)/3)))-qa(Math.abs(t)))},nc=function(t,n){return t=Math.abs(t),n=Math.abs(n)-t,Math.max(0,qa(n)-qa(t))+1},ec=function(){return new rc};function rc(){this.reset()}rc.prototype={constructor:rc,reset:function(){this.s=this.t=0},add:function(t){oc(ic,t,this.t),oc(this,ic.s,this.s),this.s?this.t+=ic.t:this.s=ic.t},valueOf:function(){return this.s}};var ic=new rc;function oc(t,n,e){var r=t.s=n+e,i=r-n,o=r-i;t.t=n-o+(e-i)}var uc=1e-6,ac=Math.PI,cc=ac/2,fc=ac/4,sc=2*ac,lc=180/ac,hc=ac/180,dc=Math.abs,pc=Math.atan,gc=Math.atan2,vc=Math.cos,yc=Math.ceil,bc=Math.exp,mc=(Math.floor,Math.log),_c=Math.pow,xc=Math.sin,wc=Math.sign||function(t){return t>0?1:t<0?-1:0},Mc=Math.sqrt,kc=Math.tan;function Nc(t){return t>1?0:t<-1?ac:Math.acos(t)}function Sc(t){return t>1?cc:t<-1?-cc:Math.asin(t)}function Tc(t){return(t=xc(t/2))*t}function Ac(){}function Cc(t,n){t&&Lc.hasOwnProperty(t.type)&&Lc[t.type](t,n)}var Ec={Feature:function(t,n){Cc(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)Cc(e[r].geometry,n)}},Lc={Sphere:function(t,n){n.sphere()},Point:function(t,n){t=t.coordinates,n.point(t[0],t[1],t[2])},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)t=e[r],n.point(t[0],t[1],t[2])},LineString:function(t,n){Rc(t.coordinates,n,0)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)Rc(e[r],n,0)},Polygon:function(t,n){Dc(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)Dc(e[r],n)},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)Cc(e[r],n)}};function Rc(t,n,e){var r,i=-1,o=t.length-e;for(n.lineStart();++i<o;)r=t[i],n.point(r[0],r[1],r[2]);n.lineEnd()}function Dc(t,n){var e=-1,r=t.length;for(n.polygonStart();++e<r;)Rc(t[e],n,1);n.polygonEnd()}var Pc,zc,Oc,Uc,qc,Ic=function(t,n){t&&Ec.hasOwnProperty(t.type)?Ec[t.type](t,n):Cc(t,n)},Bc=ec(),Fc=ec(),jc={point:Ac,lineStart:Ac,lineEnd:Ac,polygonStart:function(){Bc.reset(),jc.lineStart=Yc,jc.lineEnd=Hc},polygonEnd:function(){var t=+Bc;Fc.add(t<0?sc+t:t),this.lineStart=this.lineEnd=this.point=Ac},sphere:function(){Fc.add(sc)}};function Yc(){jc.point=Vc}function Hc(){Xc(Pc,zc)}function Vc(t,n){jc.point=Xc,Pc=t,zc=n,Oc=t*=hc,Uc=vc(n=(n*=hc)/2+fc),qc=xc(n)}function Xc(t,n){var e=(t*=hc)-Oc,r=e>=0?1:-1,i=r*e,o=vc(n=(n*=hc)/2+fc),u=xc(n),a=qc*u,c=Uc*o+a*vc(i),f=a*r*xc(i);Bc.add(gc(f,c)),Oc=t,Uc=o,qc=u}var Gc=function(t){return Fc.reset(),Ic(t,jc),2*Fc};function Wc(t){return[gc(t[1],t[0]),Sc(t[2])]}function $c(t){var n=t[0],e=t[1],r=vc(e);return[r*vc(n),r*xc(n),xc(e)]}function Zc(t,n){return t[0]*n[0]+t[1]*n[1]+t[2]*n[2]}function Qc(t,n){return[t[1]*n[2]-t[2]*n[1],t[2]*n[0]-t[0]*n[2],t[0]*n[1]-t[1]*n[0]]}function Jc(t,n){t[0]+=n[0],t[1]+=n[1],t[2]+=n[2]}function Kc(t,n){return[t[0]*n,t[1]*n,t[2]*n]}function tf(t){var n=Mc(t[0]*t[0]+t[1]*t[1]+t[2]*t[2]);t[0]/=n,t[1]/=n,t[2]/=n}var nf,ef,rf,of,uf,af,cf,ff,sf,lf,hf=ec(),df={point:pf,lineStart:vf,lineEnd:yf,polygonStart:function(){df.point=bf,df.lineStart=mf,df.lineEnd=_f,hf.reset(),jc.polygonStart()},polygonEnd:function(){jc.polygonEnd(),df.point=pf,df.lineStart=vf,df.lineEnd=yf,Bc<0?(nf=-(rf=180),ef=-(of=90)):hf>uc?of=90:hf<-uc&&(ef=-90),lf[0]=nf,lf[1]=rf}};function pf(t,n){sf.push(lf=[nf=t,rf=t]),n<ef&&(ef=n),n>of&&(of=n)}function gf(t,n){var e=$c([t*hc,n*hc]);if(ff){var r=Qc(ff,e),i=Qc([r[1],-r[0],0],r);tf(i),i=Wc(i);var o,u=t-uf,a=u>0?1:-1,c=i[0]*lc*a,f=dc(u)>180;f^(a*uf<c&&c<a*t)?(o=i[1]*lc)>of&&(of=o):f^(a*uf<(c=(c+360)%360-180)&&c<a*t)?(o=-i[1]*lc)<ef&&(ef=o):(n<ef&&(ef=n),n>of&&(of=n)),f?t<uf?xf(nf,t)>xf(nf,rf)&&(rf=t):xf(t,rf)>xf(nf,rf)&&(nf=t):rf>=nf?(t<nf&&(nf=t),t>rf&&(rf=t)):t>uf?xf(nf,t)>xf(nf,rf)&&(rf=t):xf(t,rf)>xf(nf,rf)&&(nf=t)}else sf.push(lf=[nf=t,rf=t]);n<ef&&(ef=n),n>of&&(of=n),ff=e,uf=t}function vf(){df.point=gf}function yf(){lf[0]=nf,lf[1]=rf,df.point=pf,ff=null}function bf(t,n){if(ff){var e=t-uf;hf.add(dc(e)>180?e+(e>0?360:-360):e)}else af=t,cf=n;jc.point(t,n),gf(t,n)}function mf(){jc.lineStart()}function _f(){bf(af,cf),jc.lineEnd(),dc(hf)>uc&&(nf=-(rf=180)),lf[0]=nf,lf[1]=rf,ff=null}function xf(t,n){return(n-=t)<0?n+360:n}function wf(t,n){return t[0]-n[0]}function Mf(t,n){return t[0]<=t[1]?t[0]<=n&&n<=t[1]:n<t[0]||t[1]<n}var kf,Nf,Sf,Tf,Af,Cf,Ef,Lf,Rf,Df,Pf,zf,Of,Uf,qf,If,Bf=function(t){var n,e,r,i,o,u,a;if(of=rf=-(nf=ef=1/0),sf=[],Ic(t,df),e=sf.length){for(sf.sort(wf),n=1,o=[r=sf[0]];n<e;++n)Mf(r,(i=sf[n])[0])||Mf(r,i[1])?(xf(r[0],i[1])>xf(r[0],r[1])&&(r[1]=i[1]),xf(i[0],r[1])>xf(r[0],r[1])&&(r[0]=i[0])):o.push(r=i);for(u=-1/0,n=0,r=o[e=o.length-1];n<=e;r=i,++n)i=o[n],(a=xf(r[1],i[0]))>u&&(u=a,nf=i[0],rf=r[1])}return sf=lf=null,nf===1/0||ef===1/0?[[NaN,NaN],[NaN,NaN]]:[[nf,ef],[rf,of]]},Ff={sphere:Ac,point:jf,lineStart:Hf,lineEnd:Gf,polygonStart:function(){Ff.lineStart=Wf,Ff.lineEnd=$f},polygonEnd:function(){Ff.lineStart=Hf,Ff.lineEnd=Gf}};function jf(t,n){t*=hc;var e=vc(n*=hc);Yf(e*vc(t),e*xc(t),xc(n))}function Yf(t,n,e){Sf+=(t-Sf)/++kf,Tf+=(n-Tf)/kf,Af+=(e-Af)/kf}function Hf(){Ff.point=Vf}function Vf(t,n){t*=hc;var e=vc(n*=hc);Uf=e*vc(t),qf=e*xc(t),If=xc(n),Ff.point=Xf,Yf(Uf,qf,If)}function Xf(t,n){t*=hc;var e=vc(n*=hc),r=e*vc(t),i=e*xc(t),o=xc(n),u=gc(Mc((u=qf*o-If*i)*u+(u=If*r-Uf*o)*u+(u=Uf*i-qf*r)*u),Uf*r+qf*i+If*o);Nf+=u,Cf+=u*(Uf+(Uf=r)),Ef+=u*(qf+(qf=i)),Lf+=u*(If+(If=o)),Yf(Uf,qf,If)}function Gf(){Ff.point=jf}function Wf(){Ff.point=Zf}function $f(){Qf(zf,Of),Ff.point=jf}function Zf(t,n){zf=t,Of=n,t*=hc,n*=hc,Ff.point=Qf;var e=vc(n);Uf=e*vc(t),qf=e*xc(t),If=xc(n),Yf(Uf,qf,If)}function Qf(t,n){t*=hc;var e=vc(n*=hc),r=e*vc(t),i=e*xc(t),o=xc(n),u=qf*o-If*i,a=If*r-Uf*o,c=Uf*i-qf*r,f=Mc(u*u+a*a+c*c),s=Sc(f),l=f&&-s/f;Rf+=l*u,Df+=l*a,Pf+=l*c,Nf+=s,Cf+=s*(Uf+(Uf=r)),Ef+=s*(qf+(qf=i)),Lf+=s*(If+(If=o)),Yf(Uf,qf,If)}var Jf=function(t){kf=Nf=Sf=Tf=Af=Cf=Ef=Lf=Rf=Df=Pf=0,Ic(t,Ff);var n=Rf,e=Df,r=Pf,i=n*n+e*e+r*r;return i<1e-12&&(n=Cf,e=Ef,r=Lf,Nf<uc&&(n=Sf,e=Tf,r=Af),(i=n*n+e*e+r*r)<1e-12)?[NaN,NaN]:[gc(e,n)*lc,Sc(r/Mc(i))*lc]},Kf=function(t){return function(){return t}},ts=function(t,n){function e(e,r){return e=t(e,r),n(e[0],e[1])}return t.invert&&n.invert&&(e.invert=function(e,r){return(e=n.invert(e,r))&&t.invert(e[0],e[1])}),e};function ns(t,n){return[dc(t)>ac?t+Math.round(-t/sc)*sc:t,n]}function es(t,n,e){return(t%=sc)?n||e?ts(is(t),os(n,e)):is(t):n||e?os(n,e):ns}function rs(t){return function(n,e){return[(n+=t)>ac?n-sc:n<-ac?n+sc:n,e]}}function is(t){var n=rs(t);return n.invert=rs(-t),n}function os(t,n){var e=vc(t),r=xc(t),i=vc(n),o=xc(n);function u(t,n){var u=vc(n),a=vc(t)*u,c=xc(t)*u,f=xc(n),s=f*e+a*r;return[gc(c*i-s*o,a*e-f*r),Sc(s*i+c*o)]}return u.invert=function(t,n){var u=vc(n),a=vc(t)*u,c=xc(t)*u,f=xc(n),s=f*i-c*o;return[gc(c*i+f*o,a*e+s*r),Sc(s*e-a*r)]},u}ns.invert=ns;var us=function(t){function n(n){return(n=t(n[0]*hc,n[1]*hc))[0]*=lc,n[1]*=lc,n}return t=es(t[0]*hc,t[1]*hc,t.length>2?t[2]*hc:0),n.invert=function(n){return(n=t.invert(n[0]*hc,n[1]*hc))[0]*=lc,n[1]*=lc,n},n};function as(t,n,e,r,i,o){if(e){var u=vc(n),a=xc(n),c=r*e;null==i?(i=n+r*sc,o=n-c/2):(i=cs(u,i),o=cs(u,o),(r>0?i<o:i>o)&&(i+=r*sc));for(var f,s=i;r>0?s>o:s<o;s-=c)f=Wc([u,-a*vc(s),-a*xc(s)]),t.point(f[0],f[1])}}function cs(t,n){(n=$c(n))[0]-=t,tf(n);var e=Nc(-n[1]);return((-n[2]<0?-e:e)+sc-uc)%sc}var fs=function(){var t,n,e=Kf([0,0]),r=Kf(90),i=Kf(6),o={point:function(e,r){t.push(e=n(e,r)),e[0]*=lc,e[1]*=lc}};function u(){var u=e.apply(this,arguments),a=r.apply(this,arguments)*hc,c=i.apply(this,arguments)*hc;return t=[],n=es(-u[0]*hc,-u[1]*hc,0).invert,as(o,a,c,1),u={type:"Polygon",coordinates:[t]},t=n=null,u}return u.center=function(t){return arguments.length?(e="function"==typeof t?t:Kf([+t[0],+t[1]]),u):e},u.radius=function(t){return arguments.length?(r="function"==typeof t?t:Kf(+t),u):r},u.precision=function(t){return arguments.length?(i="function"==typeof t?t:Kf(+t),u):i},u},ss=function(){var t,n=[];return{point:function(n,e){t.push([n,e])},lineStart:function(){n.push(t=[])},lineEnd:Ac,rejoin:function(){n.length>1&&n.push(n.pop().concat(n.shift()))},result:function(){var e=n;return n=[],t=null,e}}},ls=function(t,n){return dc(t[0]-n[0])<uc&&dc(t[1]-n[1])<uc};function hs(t,n,e,r){this.x=t,this.z=n,this.o=e,this.e=r,this.v=!1,this.n=this.p=null}var ds=function(t,n,e,r,i){var o,u,a=[],c=[];if(t.forEach(function(t){if(!((n=t.length-1)<=0)){var n,e,r=t[0],u=t[n];if(ls(r,u)){for(i.lineStart(),o=0;o<n;++o)i.point((r=t[o])[0],r[1]);i.lineEnd()}else a.push(e=new hs(r,t,null,!0)),c.push(e.o=new hs(r,null,e,!1)),a.push(e=new hs(u,t,null,!1)),c.push(e.o=new hs(u,null,e,!0))}}),a.length){for(c.sort(n),ps(a),ps(c),o=0,u=c.length;o<u;++o)c[o].e=e=!e;for(var f,s,l=a[0];;){for(var h=l,d=!0;h.v;)if((h=h.n)===l)return;f=h.z,i.lineStart();do{if(h.v=h.o.v=!0,h.e){if(d)for(o=0,u=f.length;o<u;++o)i.point((s=f[o])[0],s[1]);else r(h.x,h.n.x,1,i);h=h.n}else{if(d)for(f=h.p.z,o=f.length-1;o>=0;--o)i.point((s=f[o])[0],s[1]);else r(h.x,h.p.x,-1,i);h=h.p}f=(h=h.o).z,d=!d}while(!h.v);i.lineEnd()}}};function ps(t){if(n=t.length){for(var n,e,r=0,i=t[0];++r<n;)i.n=e=t[r],e.p=i,i=e;i.n=e=t[0],e.p=i}}var gs=ec(),vs=function(t,n){var e=n[0],r=n[1],i=xc(r),o=[xc(e),-vc(e),0],u=0,a=0;gs.reset(),1===i?r=cc+uc:-1===i&&(r=-cc-uc);for(var c=0,f=t.length;c<f;++c)if(l=(s=t[c]).length)for(var s,l,h=s[l-1],d=h[0],p=h[1]/2+fc,g=xc(p),v=vc(p),y=0;y<l;++y,d=m,g=x,v=w,h=b){var b=s[y],m=b[0],_=b[1]/2+fc,x=xc(_),w=vc(_),M=m-d,k=M>=0?1:-1,N=k*M,S=N>ac,T=g*x;if(gs.add(gc(T*k*xc(N),v*w+T*vc(N))),u+=S?M+k*sc:M,S^d>=e^m>=e){var A=Qc($c(h),$c(b));tf(A);var C=Qc(o,A);tf(C);var E=(S^M>=0?-1:1)*Sc(C[2]);(r>E||r===E&&(A[0]||A[1]))&&(a+=S^M>=0?1:-1)}}return(u<-uc||u<uc&&gs<-uc)^1&a},ys=function(t,n,e,r){return function(i){var o,u,a,c=n(i),f=ss(),s=n(f),l=!1,h={point:d,lineStart:g,lineEnd:v,polygonStart:function(){h.point=y,h.lineStart=b,h.lineEnd=m,u=[],o=[]},polygonEnd:function(){h.point=d,h.lineStart=g,h.lineEnd=v,u=U(u);var t=vs(o,r);u.length?(l||(i.polygonStart(),l=!0),ds(u,ms,t,e,i)):t&&(l||(i.polygonStart(),l=!0),i.lineStart(),e(null,null,1,i),i.lineEnd()),l&&(i.polygonEnd(),l=!1),u=o=null},sphere:function(){i.polygonStart(),i.lineStart(),e(null,null,1,i),i.lineEnd(),i.polygonEnd()}};function d(n,e){t(n,e)&&i.point(n,e)}function p(t,n){c.point(t,n)}function g(){h.point=p,c.lineStart()}function v(){h.point=d,c.lineEnd()}function y(t,n){a.push([t,n]),s.point(t,n)}function b(){s.lineStart(),a=[]}function m(){y(a[0][0],a[0][1]),s.lineEnd();var t,n,e,r,c=s.clean(),h=f.result(),d=h.length;if(a.pop(),o.push(a),a=null,d)if(1&c){if((n=(e=h[0]).length-1)>0){for(l||(i.polygonStart(),l=!0),i.lineStart(),t=0;t<n;++t)i.point((r=e[t])[0],r[1]);i.lineEnd()}}else d>1&&2&c&&h.push(h.pop().concat(h.shift())),u.push(h.filter(bs))}return h}};function bs(t){return t.length>1}function ms(t,n){return((t=t.x)[0]<0?t[1]-cc-uc:cc-t[1])-((n=n.x)[0]<0?n[1]-cc-uc:cc-n[1])}var _s=ys(function(){return!0},function(t){var n,e=NaN,r=NaN,i=NaN;return{lineStart:function(){t.lineStart(),n=1},point:function(o,u){var a=o>0?ac:-ac,c=dc(o-e);dc(c-ac)<uc?(t.point(e,r=(r+u)/2>0?cc:-cc),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(a,r),t.point(o,r),n=0):i!==a&&c>=ac&&(dc(e-i)<uc&&(e-=i*uc),dc(o-a)<uc&&(o-=a*uc),r=function(t,n,e,r){var i,o,u=xc(t-e);return dc(u)>uc?pc((xc(n)*(o=vc(r))*xc(e)-xc(r)*(i=vc(n))*xc(t))/(i*o*u)):(n+r)/2}(e,r,o,u),t.point(i,r),t.lineEnd(),t.lineStart(),t.point(a,r),n=0),t.point(e=o,r=u),i=a},lineEnd:function(){t.lineEnd(),e=r=NaN},clean:function(){return 2-n}}},function(t,n,e,r){var i;if(null==t)i=e*cc,r.point(-ac,i),r.point(0,i),r.point(ac,i),r.point(ac,0),r.point(ac,-i),r.point(0,-i),r.point(-ac,-i),r.point(-ac,0),r.point(-ac,i);else if(dc(t[0]-n[0])>uc){var o=t[0]<n[0]?ac:-ac;i=e*o/2,r.point(-o,i),r.point(0,i),r.point(o,i)}else r.point(n[0],n[1])},[-ac,-cc]);var xs=function(t){var n=vc(t),e=6*hc,r=n>0,i=dc(n)>uc;function o(t,e){return vc(t)*vc(e)>n}function u(t,e,r){var i=[1,0,0],o=Qc($c(t),$c(e)),u=Zc(o,o),a=o[0],c=u-a*a;if(!c)return!r&&t;var f=n*u/c,s=-n*a/c,l=Qc(i,o),h=Kc(i,f);Jc(h,Kc(o,s));var d=l,p=Zc(h,d),g=Zc(d,d),v=p*p-g*(Zc(h,h)-1);if(!(v<0)){var y=Mc(v),b=Kc(d,(-p-y)/g);if(Jc(b,h),b=Wc(b),!r)return b;var m,_=t[0],x=e[0],w=t[1],M=e[1];x<_&&(m=_,_=x,x=m);var k=x-_,N=dc(k-ac)<uc;if(!N&&M<w&&(m=w,w=M,M=m),N||k<uc?N?w+M>0^b[1]<(dc(b[0]-_)<uc?w:M):w<=b[1]&&b[1]<=M:k>ac^(_<=b[0]&&b[0]<=x)){var S=Kc(d,(-p+y)/g);return Jc(S,h),[b,Wc(S)]}}}function a(n,e){var i=r?t:ac-t,o=0;return n<-i?o|=1:n>i&&(o|=2),e<-i?o|=4:e>i&&(o|=8),o}return ys(o,function(t){var n,e,c,f,s;return{lineStart:function(){f=c=!1,s=1},point:function(l,h){var d,p=[l,h],g=o(l,h),v=r?g?0:a(l,h):g?a(l+(l<0?ac:-ac),h):0;if(!n&&(f=c=g)&&t.lineStart(),g!==c&&(!(d=u(n,p))||ls(n,d)||ls(p,d))&&(p[0]+=uc,p[1]+=uc,g=o(p[0],p[1])),g!==c)s=0,g?(t.lineStart(),d=u(p,n),t.point(d[0],d[1])):(d=u(n,p),t.point(d[0],d[1]),t.lineEnd()),n=d;else if(i&&n&&r^g){var y;v&e||!(y=u(p,n,!0))||(s=0,r?(t.lineStart(),t.point(y[0][0],y[0][1]),t.point(y[1][0],y[1][1]),t.lineEnd()):(t.point(y[1][0],y[1][1]),t.lineEnd(),t.lineStart(),t.point(y[0][0],y[0][1])))}!g||n&&ls(n,p)||t.point(p[0],p[1]),n=p,c=g,e=v},lineEnd:function(){c&&t.lineEnd(),n=null},clean:function(){return s|(f&&c)<<1}}},function(n,r,i,o){as(o,t,e,i,n,r)},r?[0,-t]:[-ac,t-ac])},ws=function(t,n,e,r,i,o){var u,a=t[0],c=t[1],f=0,s=1,l=n[0]-a,h=n[1]-c;if(u=e-a,l||!(u>0)){if(u/=l,l<0){if(u<f)return;u<s&&(s=u)}else if(l>0){if(u>s)return;u>f&&(f=u)}if(u=i-a,l||!(u<0)){if(u/=l,l<0){if(u>s)return;u>f&&(f=u)}else if(l>0){if(u<f)return;u<s&&(s=u)}if(u=r-c,h||!(u>0)){if(u/=h,h<0){if(u<f)return;u<s&&(s=u)}else if(h>0){if(u>s)return;u>f&&(f=u)}if(u=o-c,h||!(u<0)){if(u/=h,h<0){if(u>s)return;u>f&&(f=u)}else if(h>0){if(u<f)return;u<s&&(s=u)}return f>0&&(t[0]=a+f*l,t[1]=c+f*h),s<1&&(n[0]=a+s*l,n[1]=c+s*h),!0}}}}},Ms=1e9,ks=-Ms;function Ns(t,n,e,r){function i(i,o){return t<=i&&i<=e&&n<=o&&o<=r}function o(i,o,a,f){var s=0,l=0;if(null==i||(s=u(i,a))!==(l=u(o,a))||c(i,o)<0^a>0)do{f.point(0===s||3===s?t:e,s>1?r:n)}while((s=(s+a+4)%4)!==l);else f.point(o[0],o[1])}function u(r,i){return dc(r[0]-t)<uc?i>0?0:3:dc(r[0]-e)<uc?i>0?2:1:dc(r[1]-n)<uc?i>0?1:0:i>0?3:2}function a(t,n){return c(t.x,n.x)}function c(t,n){var e=u(t,1),r=u(n,1);return e!==r?e-r:0===e?n[1]-t[1]:1===e?t[0]-n[0]:2===e?t[1]-n[1]:n[0]-t[0]}return function(u){var c,f,s,l,h,d,p,g,v,y,b,m=u,_=ss(),x={point:w,lineStart:function(){x.point=M,f&&f.push(s=[]);y=!0,v=!1,p=g=NaN},lineEnd:function(){c&&(M(l,h),d&&v&&_.rejoin(),c.push(_.result()));x.point=w,v&&m.lineEnd()},polygonStart:function(){m=_,c=[],f=[],b=!0},polygonEnd:function(){var n=function(){for(var n=0,e=0,i=f.length;e<i;++e)for(var o,u,a=f[e],c=1,s=a.length,l=a[0],h=l[0],d=l[1];c<s;++c)o=h,u=d,l=a[c],h=l[0],d=l[1],u<=r?d>r&&(h-o)*(r-u)>(d-u)*(t-o)&&++n:d<=r&&(h-o)*(r-u)<(d-u)*(t-o)&&--n;return n}(),e=b&&n,i=(c=U(c)).length;(e||i)&&(u.polygonStart(),e&&(u.lineStart(),o(null,null,1,u),u.lineEnd()),i&&ds(c,a,n,o,u),u.polygonEnd());m=u,c=f=s=null}};function w(t,n){i(t,n)&&m.point(t,n)}function M(o,u){var a=i(o,u);if(f&&s.push([o,u]),y)l=o,h=u,d=a,y=!1,a&&(m.lineStart(),m.point(o,u));else if(a&&v)m.point(o,u);else{var c=[p=Math.max(ks,Math.min(Ms,p)),g=Math.max(ks,Math.min(Ms,g))],_=[o=Math.max(ks,Math.min(Ms,o)),u=Math.max(ks,Math.min(Ms,u))];ws(c,_,t,n,e,r)?(v||(m.lineStart(),m.point(c[0],c[1])),m.point(_[0],_[1]),a||m.lineEnd(),b=!1):a&&(m.lineStart(),m.point(o,u),b=!1)}p=o,g=u,v=a}return x}}var Ss,Ts,As,Cs=function(){var t,n,e,r=0,i=0,o=960,u=500;return e={stream:function(e){return t&&n===e?t:t=Ns(r,i,o,u)(n=e)},extent:function(a){return arguments.length?(r=+a[0][0],i=+a[0][1],o=+a[1][0],u=+a[1][1],t=n=null,e):[[r,i],[o,u]]}}},Es=ec(),Ls={sphere:Ac,point:Ac,lineStart:function(){Ls.point=Ds,Ls.lineEnd=Rs},lineEnd:Ac,polygonStart:Ac,polygonEnd:Ac};function Rs(){Ls.point=Ls.lineEnd=Ac}function Ds(t,n){Ss=t*=hc,Ts=xc(n*=hc),As=vc(n),Ls.point=Ps}function Ps(t,n){t*=hc;var e=xc(n*=hc),r=vc(n),i=dc(t-Ss),o=vc(i),u=r*xc(i),a=As*e-Ts*r*o,c=Ts*e+As*r*o;Es.add(gc(Mc(u*u+a*a),c)),Ss=t,Ts=e,As=r}var zs=function(t){return Es.reset(),Ic(t,Ls),+Es},Os=[null,null],Us={type:"LineString",coordinates:Os},qs=function(t,n){return Os[0]=t,Os[1]=n,zs(Us)},Is={Feature:function(t,n){return Fs(t.geometry,n)},FeatureCollection:function(t,n){for(var e=t.features,r=-1,i=e.length;++r<i;)if(Fs(e[r].geometry,n))return!0;return!1}},Bs={Sphere:function(){return!0},Point:function(t,n){return js(t.coordinates,n)},MultiPoint:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(js(e[r],n))return!0;return!1},LineString:function(t,n){return Ys(t.coordinates,n)},MultiLineString:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(Ys(e[r],n))return!0;return!1},Polygon:function(t,n){return Hs(t.coordinates,n)},MultiPolygon:function(t,n){for(var e=t.coordinates,r=-1,i=e.length;++r<i;)if(Hs(e[r],n))return!0;return!1},GeometryCollection:function(t,n){for(var e=t.geometries,r=-1,i=e.length;++r<i;)if(Fs(e[r],n))return!0;return!1}};function Fs(t,n){return!(!t||!Bs.hasOwnProperty(t.type))&&Bs[t.type](t,n)}function js(t,n){return 0===qs(t,n)}function Ys(t,n){var e=qs(t[0],t[1]);return qs(t[0],n)+qs(n,t[1])<=e+uc}function Hs(t,n){return!!vs(t.map(Vs),Xs(n))}function Vs(t){return(t=t.map(Xs)).pop(),t}function Xs(t){return[t[0]*hc,t[1]*hc]}var Gs=function(t,n){return(t&&Is.hasOwnProperty(t.type)?Is[t.type]:Fs)(t,n)};function Ws(t,n,e){var r=w(t,n-uc,e).concat(n);return function(t){return r.map(function(n){return[t,n]})}}function $s(t,n,e){var r=w(t,n-uc,e).concat(n);return function(t){return r.map(function(n){return[n,t]})}}function Zs(){var t,n,e,r,i,o,u,a,c,f,s,l,h=10,d=h,p=90,g=360,v=2.5;function y(){return{type:"MultiLineString",coordinates:b()}}function b(){return w(yc(r/p)*p,e,p).map(s).concat(w(yc(a/g)*g,u,g).map(l)).concat(w(yc(n/h)*h,t,h).filter(function(t){return dc(t%p)>uc}).map(c)).concat(w(yc(o/d)*d,i,d).filter(function(t){return dc(t%g)>uc}).map(f))}return y.lines=function(){return b().map(function(t){return{type:"LineString",coordinates:t}})},y.outline=function(){return{type:"Polygon",coordinates:[s(r).concat(l(u).slice(1),s(e).reverse().slice(1),l(a).reverse().slice(1))]}},y.extent=function(t){return arguments.length?y.extentMajor(t).extentMinor(t):y.extentMinor()},y.extentMajor=function(t){return arguments.length?(r=+t[0][0],e=+t[1][0],a=+t[0][1],u=+t[1][1],r>e&&(t=r,r=e,e=t),a>u&&(t=a,a=u,u=t),y.precision(v)):[[r,a],[e,u]]},y.extentMinor=function(e){return arguments.length?(n=+e[0][0],t=+e[1][0],o=+e[0][1],i=+e[1][1],n>t&&(e=n,n=t,t=e),o>i&&(e=o,o=i,i=e),y.precision(v)):[[n,o],[t,i]]},y.step=function(t){return arguments.length?y.stepMajor(t).stepMinor(t):y.stepMinor()},y.stepMajor=function(t){return arguments.length?(p=+t[0],g=+t[1],y):[p,g]},y.stepMinor=function(t){return arguments.length?(h=+t[0],d=+t[1],y):[h,d]},y.precision=function(h){return arguments.length?(v=+h,c=Ws(o,i,90),f=$s(n,t,v),s=Ws(a,u,90),l=$s(r,e,v),y):v},y.extentMajor([[-180,-90+uc],[180,90-uc]]).extentMinor([[-180,-80-uc],[180,80+uc]])}function Qs(){return Zs()()}var Js,Ks,tl,nl,el=function(t,n){var e=t[0]*hc,r=t[1]*hc,i=n[0]*hc,o=n[1]*hc,u=vc(r),a=xc(r),c=vc(o),f=xc(o),s=u*vc(e),l=u*xc(e),h=c*vc(i),d=c*xc(i),p=2*Sc(Mc(Tc(o-r)+u*c*Tc(i-e))),g=xc(p),v=p?function(t){var n=xc(t*=p)/g,e=xc(p-t)/g,r=e*s+n*h,i=e*l+n*d,o=e*a+n*f;return[gc(i,r)*lc,gc(o,Mc(r*r+i*i))*lc]}:function(){return[e*lc,r*lc]};return v.distance=p,v},rl=function(t){return t},il=ec(),ol=ec(),ul={point:Ac,lineStart:Ac,lineEnd:Ac,polygonStart:function(){ul.lineStart=al,ul.lineEnd=sl},polygonEnd:function(){ul.lineStart=ul.lineEnd=ul.point=Ac,il.add(dc(ol)),ol.reset()},result:function(){var t=il/2;return il.reset(),t}};function al(){ul.point=cl}function cl(t,n){ul.point=fl,Js=tl=t,Ks=nl=n}function fl(t,n){ol.add(nl*t-tl*n),tl=t,nl=n}function sl(){fl(Js,Ks)}var ll=ul,hl=1/0,dl=hl,pl=-hl,gl=pl;var vl,yl,bl,ml,_l={point:function(t,n){t<hl&&(hl=t);t>pl&&(pl=t);n<dl&&(dl=n);n>gl&&(gl=n)},lineStart:Ac,lineEnd:Ac,polygonStart:Ac,polygonEnd:Ac,result:function(){var t=[[hl,dl],[pl,gl]];return pl=gl=-(dl=hl=1/0),t}},xl=0,wl=0,Ml=0,kl=0,Nl=0,Sl=0,Tl=0,Al=0,Cl=0,El={point:Ll,lineStart:Rl,lineEnd:zl,polygonStart:function(){El.lineStart=Ol,El.lineEnd=Ul},polygonEnd:function(){El.point=Ll,El.lineStart=Rl,El.lineEnd=zl},result:function(){var t=Cl?[Tl/Cl,Al/Cl]:Sl?[kl/Sl,Nl/Sl]:Ml?[xl/Ml,wl/Ml]:[NaN,NaN];return xl=wl=Ml=kl=Nl=Sl=Tl=Al=Cl=0,t}};function Ll(t,n){xl+=t,wl+=n,++Ml}function Rl(){El.point=Dl}function Dl(t,n){El.point=Pl,Ll(bl=t,ml=n)}function Pl(t,n){var e=t-bl,r=n-ml,i=Mc(e*e+r*r);kl+=i*(bl+t)/2,Nl+=i*(ml+n)/2,Sl+=i,Ll(bl=t,ml=n)}function zl(){El.point=Ll}function Ol(){El.point=ql}function Ul(){Il(vl,yl)}function ql(t,n){El.point=Il,Ll(vl=bl=t,yl=ml=n)}function Il(t,n){var e=t-bl,r=n-ml,i=Mc(e*e+r*r);kl+=i*(bl+t)/2,Nl+=i*(ml+n)/2,Sl+=i,Tl+=(i=ml*t-bl*n)*(bl+t),Al+=i*(ml+n),Cl+=3*i,Ll(bl=t,ml=n)}var Bl=El;function Fl(t){this._context=t}Fl.prototype={_radius:4.5,pointRadius:function(t){return this._radius=t,this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._context.closePath(),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._context.moveTo(t,n),this._point=1;break;case 1:this._context.lineTo(t,n);break;default:this._context.moveTo(t+this._radius,n),this._context.arc(t,n,this._radius,0,sc)}},result:Ac};var jl,Yl,Hl,Vl,Xl,Gl=ec(),Wl={point:Ac,lineStart:function(){Wl.point=$l},lineEnd:function(){jl&&Zl(Yl,Hl),Wl.point=Ac},polygonStart:function(){jl=!0},polygonEnd:function(){jl=null},result:function(){var t=+Gl;return Gl.reset(),t}};function $l(t,n){Wl.point=Zl,Yl=Vl=t,Hl=Xl=n}function Zl(t,n){Vl-=t,Xl-=n,Gl.add(Mc(Vl*Vl+Xl*Xl)),Vl=t,Xl=n}var Ql=Wl;function Jl(){this._string=[]}function Kl(t){return"m0,"+t+"a"+t+","+t+" 0 1,1 0,"+-2*t+"a"+t+","+t+" 0 1,1 0,"+2*t+"z"}Jl.prototype={_radius:4.5,_circle:Kl(4.5),pointRadius:function(t){return(t=+t)!==this._radius&&(this._radius=t,this._circle=null),this},polygonStart:function(){this._line=0},polygonEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){0===this._line&&this._string.push("Z"),this._point=NaN},point:function(t,n){switch(this._point){case 0:this._string.push("M",t,",",n),this._point=1;break;case 1:this._string.push("L",t,",",n);break;default:null==this._circle&&(this._circle=Kl(this._radius)),this._string.push("M",t,",",n,this._circle)}},result:function(){if(this._string.length){var t=this._string.join("");return this._string=[],t}return null}};var th=function(t,n){var e,r,i=4.5;function o(t){return t&&("function"==typeof i&&r.pointRadius(+i.apply(this,arguments)),Ic(t,e(r))),r.result()}return o.area=function(t){return Ic(t,e(ll)),ll.result()},o.measure=function(t){return Ic(t,e(Ql)),Ql.result()},o.bounds=function(t){return Ic(t,e(_l)),_l.result()},o.centroid=function(t){return Ic(t,e(Bl)),Bl.result()},o.projection=function(n){return arguments.length?(e=null==n?(t=null,rl):(t=n).stream,o):t},o.context=function(t){return arguments.length?(r=null==t?(n=null,new Jl):new Fl(n=t),"function"!=typeof i&&r.pointRadius(i),o):n},o.pointRadius=function(t){return arguments.length?(i="function"==typeof t?t:(r.pointRadius(+t),+t),o):i},o.projection(t).context(n)},nh=function(t){return{stream:eh(t)}};function eh(t){return function(n){var e=new rh;for(var r in t)e[r]=t[r];return e.stream=n,e}}function rh(){}function ih(t,n,e){var r=t.clipExtent&&t.clipExtent();return t.scale(150).translate([0,0]),null!=r&&t.clipExtent(null),Ic(e,t.stream(_l)),n(_l.result()),null!=r&&t.clipExtent(r),t}function oh(t,n,e){return ih(t,function(e){var r=n[1][0]-n[0][0],i=n[1][1]-n[0][1],o=Math.min(r/(e[1][0]-e[0][0]),i/(e[1][1]-e[0][1])),u=+n[0][0]+(r-o*(e[1][0]+e[0][0]))/2,a=+n[0][1]+(i-o*(e[1][1]+e[0][1]))/2;t.scale(150*o).translate([u,a])},e)}function uh(t,n,e){return oh(t,[[0,0],n],e)}function ah(t,n,e){return ih(t,function(e){var r=+n,i=r/(e[1][0]-e[0][0]),o=(r-i*(e[1][0]+e[0][0]))/2,u=-i*e[0][1];t.scale(150*i).translate([o,u])},e)}function ch(t,n,e){return ih(t,function(e){var r=+n,i=r/(e[1][1]-e[0][1]),o=-i*e[0][0],u=(r-i*(e[1][1]+e[0][1]))/2;t.scale(150*i).translate([o,u])},e)}rh.prototype={constructor:rh,point:function(t,n){this.stream.point(t,n)},sphere:function(){this.stream.sphere()},lineStart:function(){this.stream.lineStart()},lineEnd:function(){this.stream.lineEnd()},polygonStart:function(){this.stream.polygonStart()},polygonEnd:function(){this.stream.polygonEnd()}};var fh=16,sh=vc(30*hc),lh=function(t,n){return+n?function(t,n){function e(r,i,o,u,a,c,f,s,l,h,d,p,g,v){var y=f-r,b=s-i,m=y*y+b*b;if(m>4*n&&g--){var _=u+h,x=a+d,w=c+p,M=Mc(_*_+x*x+w*w),k=Sc(w/=M),N=dc(dc(w)-1)<uc||dc(o-l)<uc?(o+l)/2:gc(x,_),S=t(N,k),T=S[0],A=S[1],C=T-r,E=A-i,L=b*C-y*E;(L*L/m>n||dc((y*C+b*E)/m-.5)>.3||u*h+a*d+c*p<sh)&&(e(r,i,o,u,a,c,T,A,N,_/=M,x/=M,w,g,v),v.point(T,A),e(T,A,N,_,x,w,f,s,l,h,d,p,g,v))}}return function(n){var r,i,o,u,a,c,f,s,l,h,d,p,g={point:v,lineStart:y,lineEnd:m,polygonStart:function(){n.polygonStart(),g.lineStart=_},polygonEnd:function(){n.polygonEnd(),g.lineStart=y}};function v(e,r){e=t(e,r),n.point(e[0],e[1])}function y(){s=NaN,g.point=b,n.lineStart()}function b(r,i){var o=$c([r,i]),u=t(r,i);e(s,l,f,h,d,p,s=u[0],l=u[1],f=r,h=o[0],d=o[1],p=o[2],fh,n),n.point(s,l)}function m(){g.point=v,n.lineEnd()}function _(){y(),g.point=x,g.lineEnd=w}function x(t,n){b(r=t,n),i=s,o=l,u=h,a=d,c=p,g.point=b}function w(){e(s,l,f,h,d,p,i,o,r,u,a,c,fh,n),g.lineEnd=m,m()}return g}}(t,n):function(t){return eh({point:function(n,e){n=t(n,e),this.stream.point(n[0],n[1])}})}(t)};var hh=eh({point:function(t,n){this.stream.point(t*hc,n*hc)}});function dh(t,n,e,r){var i=vc(r),o=xc(r),u=i*t,a=o*t,c=i/t,f=o/t,s=(o*e-i*n)/t,l=(o*n+i*e)/t;function h(t,r){return[u*t-a*r+n,e-a*t-u*r]}return h.invert=function(t,n){return[c*t-f*n+s,l-f*t-c*n]},h}function ph(t){return gh(function(){return t})()}function gh(t){var n,e,r,i,o,u,a,c,f,s,l=150,h=480,d=250,p=0,g=0,v=0,y=0,b=0,m=0,_=null,x=_s,w=null,M=rl,k=.5;function N(t){return c(t[0]*hc,t[1]*hc)}function S(t){return(t=c.invert(t[0],t[1]))&&[t[0]*lc,t[1]*lc]}function T(){var t=dh(l,0,0,m).apply(null,n(p,g)),r=(m?dh:function(t,n,e){function r(r,i){return[n+t*r,e-t*i]}return r.invert=function(r,i){return[(r-n)/t,(e-i)/t]},r})(l,h-t[0],d-t[1],m);return e=es(v,y,b),a=ts(n,r),c=ts(e,a),u=lh(a,k),A()}function A(){return f=s=null,N}return N.stream=function(t){return f&&s===t?f:f=hh(function(t){return eh({point:function(n,e){var r=t(n,e);return this.stream.point(r[0],r[1])}})}(e)(x(u(M(s=t)))))},N.preclip=function(t){return arguments.length?(x=t,_=void 0,A()):x},N.postclip=function(t){return arguments.length?(M=t,w=r=i=o=null,A()):M},N.clipAngle=function(t){return arguments.length?(x=+t?xs(_=t*hc):(_=null,_s),A()):_*lc},N.clipExtent=function(t){return arguments.length?(M=null==t?(w=r=i=o=null,rl):Ns(w=+t[0][0],r=+t[0][1],i=+t[1][0],o=+t[1][1]),A()):null==w?null:[[w,r],[i,o]]},N.scale=function(t){return arguments.length?(l=+t,T()):l},N.translate=function(t){return arguments.length?(h=+t[0],d=+t[1],T()):[h,d]},N.center=function(t){return arguments.length?(p=t[0]%360*hc,g=t[1]%360*hc,T()):[p*lc,g*lc]},N.rotate=function(t){return arguments.length?(v=t[0]%360*hc,y=t[1]%360*hc,b=t.length>2?t[2]%360*hc:0,T()):[v*lc,y*lc,b*lc]},N.angle=function(t){return arguments.length?(m=t%360*hc,T()):m*lc},N.precision=function(t){return arguments.length?(u=lh(a,k=t*t),A()):Mc(k)},N.fitExtent=function(t,n){return oh(N,t,n)},N.fitSize=function(t,n){return uh(N,t,n)},N.fitWidth=function(t,n){return ah(N,t,n)},N.fitHeight=function(t,n){return ch(N,t,n)},function(){return n=t.apply(this,arguments),N.invert=n.invert&&S,T()}}function vh(t){var n=0,e=ac/3,r=gh(t),i=r(n,e);return i.parallels=function(t){return arguments.length?r(n=t[0]*hc,e=t[1]*hc):[n*lc,e*lc]},i}function yh(t,n){var e=xc(t),r=(e+xc(n))/2;if(dc(r)<uc)return function(t){var n=vc(t);function e(t,e){return[t*n,xc(e)/n]}return e.invert=function(t,e){return[t/n,Sc(e*n)]},e}(t);var i=1+e*(2*r-e),o=Mc(i)/r;function u(t,n){var e=Mc(i-2*r*xc(n))/r;return[e*xc(t*=r),o-e*vc(t)]}return u.invert=function(t,n){var e=o-n;return[gc(t,dc(e))/r*wc(e),Sc((i-(t*t+e*e)*r*r)/(2*r))]},u}var bh=function(){return vh(yh).scale(155.424).center([0,33.6442])},mh=function(){return bh().parallels([29.5,45.5]).scale(1070).translate([480,250]).rotate([96,0]).center([-.6,38.7])};var _h=function(){var t,n,e,r,i,o,u=mh(),a=bh().rotate([154,0]).center([-2,58.5]).parallels([55,65]),c=bh().rotate([157,0]).center([-3,19.9]).parallels([8,18]),f={point:function(t,n){o=[t,n]}};function s(t){var n=t[0],u=t[1];return o=null,e.point(n,u),o||(r.point(n,u),o)||(i.point(n,u),o)}function l(){return t=n=null,s}return s.invert=function(t){var n=u.scale(),e=u.translate(),r=(t[0]-e[0])/n,i=(t[1]-e[1])/n;return(i>=.12&&i<.234&&r>=-.425&&r<-.214?a:i>=.166&&i<.234&&r>=-.214&&r<-.115?c:u).invert(t)},s.stream=function(e){return t&&n===e?t:(r=[u.stream(n=e),a.stream(e),c.stream(e)],i=r.length,t={point:function(t,n){for(var e=-1;++e<i;)r[e].point(t,n)},sphere:function(){for(var t=-1;++t<i;)r[t].sphere()},lineStart:function(){for(var t=-1;++t<i;)r[t].lineStart()},lineEnd:function(){for(var t=-1;++t<i;)r[t].lineEnd()},polygonStart:function(){for(var t=-1;++t<i;)r[t].polygonStart()},polygonEnd:function(){for(var t=-1;++t<i;)r[t].polygonEnd()}});var r,i},s.precision=function(t){return arguments.length?(u.precision(t),a.precision(t),c.precision(t),l()):u.precision()},s.scale=function(t){return arguments.length?(u.scale(t),a.scale(.35*t),c.scale(t),s.translate(u.translate())):u.scale()},s.translate=function(t){if(!arguments.length)return u.translate();var n=u.scale(),o=+t[0],s=+t[1];return e=u.translate(t).clipExtent([[o-.455*n,s-.238*n],[o+.455*n,s+.238*n]]).stream(f),r=a.translate([o-.307*n,s+.201*n]).clipExtent([[o-.425*n+uc,s+.12*n+uc],[o-.214*n-uc,s+.234*n-uc]]).stream(f),i=c.translate([o-.205*n,s+.212*n]).clipExtent([[o-.214*n+uc,s+.166*n+uc],[o-.115*n-uc,s+.234*n-uc]]).stream(f),l()},s.fitExtent=function(t,n){return oh(s,t,n)},s.fitSize=function(t,n){return uh(s,t,n)},s.fitWidth=function(t,n){return ah(s,t,n)},s.fitHeight=function(t,n){return ch(s,t,n)},s.scale(1070)};function xh(t){return function(n,e){var r=vc(n),i=vc(e),o=t(r*i);return[o*i*xc(n),o*xc(e)]}}function wh(t){return function(n,e){var r=Mc(n*n+e*e),i=t(r),o=xc(i),u=vc(i);return[gc(n*o,r*u),Sc(r&&e*o/r)]}}var Mh=xh(function(t){return Mc(2/(1+t))});Mh.invert=wh(function(t){return 2*Sc(t/2)});var kh=function(){return ph(Mh).scale(124.75).clipAngle(179.999)},Nh=xh(function(t){return(t=Nc(t))&&t/xc(t)});Nh.invert=wh(function(t){return t});var Sh=function(){return ph(Nh).scale(79.4188).clipAngle(179.999)};function Th(t,n){return[t,mc(kc((cc+n)/2))]}Th.invert=function(t,n){return[t,2*pc(bc(n))-cc]};var Ah=function(){return Ch(Th).scale(961/sc)};function Ch(t){var n,e,r,i=ph(t),o=i.center,u=i.scale,a=i.translate,c=i.clipExtent,f=null;function s(){var o=ac*u(),a=i(us(i.rotate()).invert([0,0]));return c(null==f?[[a[0]-o,a[1]-o],[a[0]+o,a[1]+o]]:t===Th?[[Math.max(a[0]-o,f),n],[Math.min(a[0]+o,e),r]]:[[f,Math.max(a[1]-o,n)],[e,Math.min(a[1]+o,r)]])}return i.scale=function(t){return arguments.length?(u(t),s()):u()},i.translate=function(t){return arguments.length?(a(t),s()):a()},i.center=function(t){return arguments.length?(o(t),s()):o()},i.clipExtent=function(t){return arguments.length?(null==t?f=n=e=r=null:(f=+t[0][0],n=+t[0][1],e=+t[1][0],r=+t[1][1]),s()):null==f?null:[[f,n],[e,r]]},s()}function Eh(t){return kc((cc+t)/2)}function Lh(t,n){var e=vc(t),r=t===n?xc(t):mc(e/vc(n))/mc(Eh(n)/Eh(t)),i=e*_c(Eh(t),r)/r;if(!r)return Th;function o(t,n){i>0?n<-cc+uc&&(n=-cc+uc):n>cc-uc&&(n=cc-uc);var e=i/_c(Eh(n),r);return[e*xc(r*t),i-e*vc(r*t)]}return o.invert=function(t,n){var e=i-n,o=wc(r)*Mc(t*t+e*e);return[gc(t,dc(e))/r*wc(e),2*pc(_c(i/o,1/r))-cc]},o}var Rh=function(){return vh(Lh).scale(109.5).parallels([30,30])};function Dh(t,n){return[t,n]}Dh.invert=Dh;var Ph=function(){return ph(Dh).scale(152.63)};function zh(t,n){var e=vc(t),r=t===n?xc(t):(e-vc(n))/(n-t),i=e/r+t;if(dc(r)<uc)return Dh;function o(t,n){var e=i-n,o=r*t;return[e*xc(o),i-e*vc(o)]}return o.invert=function(t,n){var e=i-n;return[gc(t,dc(e))/r*wc(e),i-wc(r)*Mc(t*t+e*e)]},o}var Oh=function(){return vh(zh).scale(131.154).center([0,13.9389])},Uh=1.340264,qh=-.081106,Ih=893e-6,Bh=.003796,Fh=Mc(3)/2;function jh(t,n){var e=Sc(Fh*xc(n)),r=e*e,i=r*r*r;return[t*vc(e)/(Fh*(Uh+3*qh*r+i*(7*Ih+9*Bh*r))),e*(Uh+qh*r+i*(Ih+Bh*r))]}jh.invert=function(t,n){for(var e,r=n,i=r*r,o=i*i*i,u=0;u<12&&(o=(i=(r-=e=(r*(Uh+qh*i+o*(Ih+Bh*i))-n)/(Uh+3*qh*i+o*(7*Ih+9*Bh*i)))*r)*i*i,!(dc(e)<1e-12));++u);return[Fh*t*(Uh+3*qh*i+o*(7*Ih+9*Bh*i))/vc(r),Sc(xc(r)/Fh)]};var Yh=function(){return ph(jh).scale(177.158)};function Hh(t,n){var e=vc(n),r=vc(t)*e;return[e*xc(t)/r,xc(n)/r]}Hh.invert=wh(pc);var Vh=function(){return ph(Hh).scale(144.049).clipAngle(60)};function Xh(t,n,e,r){return 1===t&&1===n&&0===e&&0===r?rl:eh({point:function(i,o){this.stream.point(i*t+e,o*n+r)}})}var Gh=function(){var t,n,e,r,i,o,u=1,a=0,c=0,f=1,s=1,l=rl,h=null,d=rl;function p(){return r=i=null,o}return o={stream:function(t){return r&&i===t?r:r=l(d(i=t))},postclip:function(r){return arguments.length?(d=r,h=t=n=e=null,p()):d},clipExtent:function(r){return arguments.length?(d=null==r?(h=t=n=e=null,rl):Ns(h=+r[0][0],t=+r[0][1],n=+r[1][0],e=+r[1][1]),p()):null==h?null:[[h,t],[n,e]]},scale:function(t){return arguments.length?(l=Xh((u=+t)*f,u*s,a,c),p()):u},translate:function(t){return arguments.length?(l=Xh(u*f,u*s,a=+t[0],c=+t[1]),p()):[a,c]},reflectX:function(t){return arguments.length?(l=Xh(u*(f=t?-1:1),u*s,a,c),p()):f<0},reflectY:function(t){return arguments.length?(l=Xh(u*f,u*(s=t?-1:1),a,c),p()):s<0},fitExtent:function(t,n){return oh(o,t,n)},fitSize:function(t,n){return uh(o,t,n)},fitWidth:function(t,n){return ah(o,t,n)},fitHeight:function(t,n){return ch(o,t,n)}}};function Wh(t,n){var e=n*n,r=e*e;return[t*(.8707-.131979*e+r*(r*(.003971*e-.001529*r)-.013791)),n*(1.007226+e*(.015085+r*(.028874*e-.044475-.005916*r)))]}Wh.invert=function(t,n){var e,r=n,i=25;do{var o=r*r,u=o*o;r-=e=(r*(1.007226+o*(.015085+u*(.028874*o-.044475-.005916*u)))-n)/(1.007226+o*(.045255+u*(.259866*o-.311325-.005916*11*u)))}while(dc(e)>uc&&--i>0);return[t/(.8707+(o=r*r)*(o*(o*o*o*(.003971-.001529*o)-.013791)-.131979)),r]};var $h=function(){return ph(Wh).scale(175.295)};function Zh(t,n){return[vc(n)*xc(t),xc(n)]}Zh.invert=wh(Sc);var Qh=function(){return ph(Zh).scale(249.5).clipAngle(90+uc)};function Jh(t,n){var e=vc(n),r=1+vc(t)*e;return[e*xc(t)/r,xc(n)/r]}Jh.invert=wh(function(t){return 2*pc(t)});var Kh=function(){return ph(Jh).scale(250).clipAngle(142)};function td(t,n){return[mc(kc((cc+n)/2)),-t]}td.invert=function(t,n){return[-n,2*pc(bc(t))-cc]};var nd=function(){var t=Ch(td),n=t.center,e=t.rotate;return t.center=function(t){return arguments.length?n([-t[1],t[0]]):[(t=n())[1],-t[0]]},t.rotate=function(t){return arguments.length?e([t[0],t[1],t.length>2?t[2]+90:90]):[(t=e())[0],t[1],t[2]-90]},e([0,0,90]).scale(159.155)};function ed(t,n){return t.parent===n.parent?1:2}function rd(t,n){return t+n.x}function id(t,n){return Math.max(t,n.y)}var od=function(){var t=ed,n=1,e=1,r=!1;function i(i){var o,u=0;i.eachAfter(function(n){var e=n.children;e?(n.x=function(t){return t.reduce(rd,0)/t.length}(e),n.y=function(t){return 1+t.reduce(id,0)}(e)):(n.x=o?u+=t(n,o):0,n.y=0,o=n)});var a=function(t){for(var n;n=t.children;)t=n[0];return t}(i),c=function(t){for(var n;n=t.children;)t=n[n.length-1];return t}(i),f=a.x-t(a,c)/2,s=c.x+t(c,a)/2;return i.eachAfter(r?function(t){t.x=(t.x-i.x)*n,t.y=(i.y-t.y)*e}:function(t){t.x=(t.x-f)/(s-f)*n,t.y=(1-(i.y?t.y/i.y:1))*e})}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i};function ud(t){var n=0,e=t.children,r=e&&e.length;if(r)for(;--r>=0;)n+=e[r].value;else n=1;t.value=n}function ad(t,n){var e,r,i,o,u,a=new ld(t),c=+t.value&&(a.value=t.value),f=[a];for(null==n&&(n=cd);e=f.pop();)if(c&&(e.value=+e.data.value),(i=n(e.data))&&(u=i.length))for(e.children=new Array(u),o=u-1;o>=0;--o)f.push(r=e.children[o]=new ld(i[o])),r.parent=e,r.depth=e.depth+1;return a.eachBefore(sd)}function cd(t){return t.children}function fd(t){t.data=t.data.data}function sd(t){var n=0;do{t.height=n}while((t=t.parent)&&t.height<++n)}function ld(t){this.data=t,this.depth=this.height=0,this.parent=null}ld.prototype=ad.prototype={constructor:ld,count:function(){return this.eachAfter(ud)},each:function(t){var n,e,r,i,o=this,u=[o];do{for(n=u.reverse(),u=[];o=n.pop();)if(t(o),e=o.children)for(r=0,i=e.length;r<i;++r)u.push(e[r])}while(u.length);return this},eachAfter:function(t){for(var n,e,r,i=this,o=[i],u=[];i=o.pop();)if(u.push(i),n=i.children)for(e=0,r=n.length;e<r;++e)o.push(n[e]);for(;i=u.pop();)t(i);return this},eachBefore:function(t){for(var n,e,r=this,i=[r];r=i.pop();)if(t(r),n=r.children)for(e=n.length-1;e>=0;--e)i.push(n[e]);return this},sum:function(t){return this.eachAfter(function(n){for(var e=+t(n.data)||0,r=n.children,i=r&&r.length;--i>=0;)e+=r[i].value;n.value=e})},sort:function(t){return this.eachBefore(function(n){n.children&&n.children.sort(t)})},path:function(t){for(var n=this,e=function(t,n){if(t===n)return t;var e=t.ancestors(),r=n.ancestors(),i=null;for(t=e.pop(),n=r.pop();t===n;)i=t,t=e.pop(),n=r.pop();return i}(n,t),r=[n];n!==e;)n=n.parent,r.push(n);for(var i=r.length;t!==e;)r.splice(i,0,t),t=t.parent;return r},ancestors:function(){for(var t=this,n=[t];t=t.parent;)n.push(t);return n},descendants:function(){var t=[];return this.each(function(n){t.push(n)}),t},leaves:function(){var t=[];return this.eachBefore(function(n){n.children||t.push(n)}),t},links:function(){var t=this,n=[];return t.each(function(e){e!==t&&n.push({source:e.parent,target:e})}),n},copy:function(){return ad(this).eachBefore(fd)}};var hd=Array.prototype.slice;var dd=function(t){for(var n,e,r=0,i=(t=function(t){for(var n,e,r=t.length;r;)e=Math.random()*r--|0,n=t[r],t[r]=t[e],t[e]=n;return t}(hd.call(t))).length,o=[];r<i;)n=t[r],e&&vd(e,n)?++r:(e=bd(o=pd(o,n)),r=0);return e};function pd(t,n){var e,r;if(yd(n,t))return[n];for(e=0;e<t.length;++e)if(gd(n,t[e])&&yd(md(t[e],n),t))return[t[e],n];for(e=0;e<t.length-1;++e)for(r=e+1;r<t.length;++r)if(gd(md(t[e],t[r]),n)&&gd(md(t[e],n),t[r])&&gd(md(t[r],n),t[e])&&yd(_d(t[e],t[r],n),t))return[t[e],t[r],n];throw new Error}function gd(t,n){var e=t.r-n.r,r=n.x-t.x,i=n.y-t.y;return e<0||e*e<r*r+i*i}function vd(t,n){var e=t.r-n.r+1e-6,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function yd(t,n){for(var e=0;e<n.length;++e)if(!vd(t,n[e]))return!1;return!0}function bd(t){switch(t.length){case 1:return{x:(n=t[0]).x,y:n.y,r:n.r};case 2:return md(t[0],t[1]);case 3:return _d(t[0],t[1],t[2])}var n}function md(t,n){var e=t.x,r=t.y,i=t.r,o=n.x,u=n.y,a=n.r,c=o-e,f=u-r,s=a-i,l=Math.sqrt(c*c+f*f);return{x:(e+o+c/l*s)/2,y:(r+u+f/l*s)/2,r:(l+i+a)/2}}function _d(t,n,e){var r=t.x,i=t.y,o=t.r,u=n.x,a=n.y,c=n.r,f=e.x,s=e.y,l=e.r,h=r-u,d=r-f,p=i-a,g=i-s,v=c-o,y=l-o,b=r*r+i*i-o*o,m=b-u*u-a*a+c*c,_=b-f*f-s*s+l*l,x=d*p-h*g,w=(p*_-g*m)/(2*x)-r,M=(g*v-p*y)/x,k=(d*m-h*_)/(2*x)-i,N=(h*y-d*v)/x,S=M*M+N*N-1,T=2*(o+w*M+k*N),A=w*w+k*k-o*o,C=-(S?(T+Math.sqrt(T*T-4*S*A))/(2*S):A/T);return{x:r+w+M*C,y:i+k+N*C,r:C}}function xd(t,n,e){var r,i,o,u,a=t.x-n.x,c=t.y-n.y,f=a*a+c*c;f?(i=n.r+e.r,i*=i,u=t.r+e.r,i>(u*=u)?(r=(f+u-i)/(2*f),o=Math.sqrt(Math.max(0,u/f-r*r)),e.x=t.x-r*a-o*c,e.y=t.y-r*c+o*a):(r=(f+i-u)/(2*f),o=Math.sqrt(Math.max(0,i/f-r*r)),e.x=n.x+r*a-o*c,e.y=n.y+r*c+o*a)):(e.x=n.x+e.r,e.y=n.y)}function wd(t,n){var e=t.r+n.r-1e-6,r=n.x-t.x,i=n.y-t.y;return e>0&&e*e>r*r+i*i}function Md(t){var n=t._,e=t.next._,r=n.r+e.r,i=(n.x*e.r+e.x*n.r)/r,o=(n.y*e.r+e.y*n.r)/r;return i*i+o*o}function kd(t){this._=t,this.next=null,this.previous=null}function Nd(t){if(!(i=t.length))return 0;var n,e,r,i,o,u,a,c,f,s,l;if((n=t[0]).x=0,n.y=0,!(i>1))return n.r;if(e=t[1],n.x=-e.r,e.x=n.r,e.y=0,!(i>2))return n.r+e.r;xd(e,n,r=t[2]),n=new kd(n),e=new kd(e),r=new kd(r),n.next=r.previous=e,e.next=n.previous=r,r.next=e.previous=n;t:for(a=3;a<i;++a){xd(n._,e._,r=t[a]),r=new kd(r),c=e.next,f=n.previous,s=e._.r,l=n._.r;do{if(s<=l){if(wd(c._,r._)){e=c,n.next=e,e.previous=n,--a;continue t}s+=c._.r,c=c.next}else{if(wd(f._,r._)){(n=f).next=e,e.previous=n,--a;continue t}l+=f._.r,f=f.previous}}while(c!==f.next);for(r.previous=n,r.next=e,n.next=e.previous=e=r,o=Md(n);(r=r.next)!==e;)(u=Md(r))<o&&(n=r,o=u);e=n.next}for(n=[e._],r=e;(r=r.next)!==e;)n.push(r._);for(r=dd(n),a=0;a<i;++a)(n=t[a]).x-=r.x,n.y-=r.y;return r.r}var Sd=function(t){return Nd(t),t};function Td(t){if("function"!=typeof t)throw new Error;return t}function Ad(){return 0}var Cd=function(t){return function(){return t}};function Ed(t){return Math.sqrt(t.value)}var Ld=function(){var t=null,n=1,e=1,r=Ad;function i(i){return i.x=n/2,i.y=e/2,t?i.eachBefore(Rd(t)).eachAfter(Dd(r,.5)).eachBefore(Pd(1)):i.eachBefore(Rd(Ed)).eachAfter(Dd(Ad,1)).eachAfter(Dd(r,i.r/Math.min(n,e))).eachBefore(Pd(Math.min(n,e)/(2*i.r))),i}return i.radius=function(n){return arguments.length?(t=null==(e=n)?null:Td(e),i):t;var e},i.size=function(t){return arguments.length?(n=+t[0],e=+t[1],i):[n,e]},i.padding=function(t){return arguments.length?(r="function"==typeof t?t:Cd(+t),i):r},i};function Rd(t){return function(n){n.children||(n.r=Math.max(0,+t(n)||0))}}function Dd(t,n){return function(e){if(r=e.children){var r,i,o,u=r.length,a=t(e)*n||0;if(a)for(i=0;i<u;++i)r[i].r+=a;if(o=Nd(r),a)for(i=0;i<u;++i)r[i].r-=a;e.r=o+a}}}function Pd(t){return function(n){var e=n.parent;n.r*=t,e&&(n.x=e.x+t*n.x,n.y=e.y+t*n.y)}}var zd=function(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)},Od=function(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,f=t.value&&(r-n)/t.value;++a<c;)(o=u[a]).y0=e,o.y1=i,o.x0=n,o.x1=n+=o.value*f},Ud=function(){var t=1,n=1,e=0,r=!1;function i(i){var o=i.height+1;return i.x0=i.y0=e,i.x1=t,i.y1=n/o,i.eachBefore(function(t,n){return function(r){r.children&&Od(r,r.x0,t*(r.depth+1)/n,r.x1,t*(r.depth+2)/n);var i=r.x0,o=r.y0,u=r.x1-e,a=r.y1-e;u<i&&(i=u=(i+u)/2),a<o&&(o=a=(o+a)/2),r.x0=i,r.y0=o,r.x1=u,r.y1=a}}(n,o)),r&&i.eachBefore(zd),i}return i.round=function(t){return arguments.length?(r=!!t,i):r},i.size=function(e){return arguments.length?(t=+e[0],n=+e[1],i):[t,n]},i.padding=function(t){return arguments.length?(e=+t,i):e},i},qd="$",Id={depth:-1},Bd={};function Fd(t){return t.id}function jd(t){return t.parentId}var Yd=function(){var t=Fd,n=jd;function e(e){var r,i,o,u,a,c,f,s=e.length,l=new Array(s),h={};for(i=0;i<s;++i)r=e[i],a=l[i]=new ld(r),null!=(c=t(r,i,e))&&(c+="")&&(h[f=qd+(a.id=c)]=f in h?Bd:a);for(i=0;i<s;++i)if(a=l[i],null!=(c=n(e[i],i,e))&&(c+="")){if(!(u=h[qd+c]))throw new Error("missing: "+c);if(u===Bd)throw new Error("ambiguous: "+c);u.children?u.children.push(a):u.children=[a],a.parent=u}else{if(o)throw new Error("multiple roots");o=a}if(!o)throw new Error("no root");if(o.parent=Id,o.eachBefore(function(t){t.depth=t.parent.depth+1,--s}).eachBefore(sd),o.parent=null,s>0)throw new Error("cycle");return o}return e.id=function(n){return arguments.length?(t=Td(n),e):t},e.parentId=function(t){return arguments.length?(n=Td(t),e):n},e};function Hd(t,n){return t.parent===n.parent?1:2}function Vd(t){var n=t.children;return n?n[0]:t.t}function Xd(t){var n=t.children;return n?n[n.length-1]:t.t}function Gd(t,n,e){var r=e/(n.i-t.i);n.c-=r,n.s+=e,t.c+=r,n.z+=e,n.m+=e}function Wd(t,n,e){return t.a.parent===n.parent?t.a:e}function $d(t,n){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=n}$d.prototype=Object.create(ld.prototype);var Zd=function(){var t=Hd,n=1,e=1,r=null;function i(i){var c=function(t){for(var n,e,r,i,o,u=new $d(t,0),a=[u];n=a.pop();)if(r=n._.children)for(n.children=new Array(o=r.length),i=o-1;i>=0;--i)a.push(e=n.children[i]=new $d(r[i],i)),e.parent=n;return(u.parent=new $d(null,0)).children=[u],u}(i);if(c.eachAfter(o),c.parent.m=-c.z,c.eachBefore(u),r)i.eachBefore(a);else{var f=i,s=i,l=i;i.eachBefore(function(t){t.x<f.x&&(f=t),t.x>s.x&&(s=t),t.depth>l.depth&&(l=t)});var h=f===s?1:t(f,s)/2,d=h-f.x,p=n/(s.x+h+d),g=e/(l.depth||1);i.eachBefore(function(t){t.x=(t.x+d)*p,t.y=t.depth*g})}return i}function o(n){var e=n.children,r=n.parent.children,i=n.i?r[n.i-1]:null;if(e){!function(t){for(var n,e=0,r=0,i=t.children,o=i.length;--o>=0;)(n=i[o]).z+=e,n.m+=e,e+=n.s+(r+=n.c)}(n);var o=(e[0].z+e[e.length-1].z)/2;i?(n.z=i.z+t(n._,i._),n.m=n.z-o):n.z=o}else i&&(n.z=i.z+t(n._,i._));n.parent.A=function(n,e,r){if(e){for(var i,o=n,u=n,a=e,c=o.parent.children[0],f=o.m,s=u.m,l=a.m,h=c.m;a=Xd(a),o=Vd(o),a&&o;)c=Vd(c),(u=Xd(u)).a=n,(i=a.z+l-o.z-f+t(a._,o._))>0&&(Gd(Wd(a,n,r),n,i),f+=i,s+=i),l+=a.m,f+=o.m,h+=c.m,s+=u.m;a&&!Xd(u)&&(u.t=a,u.m+=l-s),o&&!Vd(c)&&(c.t=o,c.m+=f-h,r=n)}return r}(n,i,n.parent.A||r[0])}function u(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function a(t){t.x*=n,t.y=t.depth*e}return i.separation=function(n){return arguments.length?(t=n,i):t},i.size=function(t){return arguments.length?(r=!1,n=+t[0],e=+t[1],i):r?null:[n,e]},i.nodeSize=function(t){return arguments.length?(r=!0,n=+t[0],e=+t[1],i):r?[n,e]:null},i},Qd=function(t,n,e,r,i){for(var o,u=t.children,a=-1,c=u.length,f=t.value&&(i-e)/t.value;++a<c;)(o=u[a]).x0=n,o.x1=r,o.y0=e,o.y1=e+=o.value*f},Jd=(1+Math.sqrt(5))/2;function Kd(t,n,e,r,i,o){for(var u,a,c,f,s,l,h,d,p,g,v,y=[],b=n.children,m=0,_=0,x=b.length,w=n.value;m<x;){c=i-e,f=o-r;do{s=b[_++].value}while(!s&&_<x);for(l=h=s,v=s*s*(g=Math.max(f/c,c/f)/(w*t)),p=Math.max(h/v,v/l);_<x;++_){if(s+=a=b[_].value,a<l&&(l=a),a>h&&(h=a),v=s*s*g,(d=Math.max(h/v,v/l))>p){s-=a;break}p=d}y.push(u={value:s,dice:c<f,children:b.slice(m,_)}),u.dice?Od(u,e,r,i,w?r+=f*s/w:o):Qd(u,e,r,w?e+=c*s/w:i,o),w-=s,m=_}return y}var tp=function t(n){function e(t,e,r,i,o){Kd(n,t,e,r,i,o)}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(Jd),np=function(){var t=tp,n=!1,e=1,r=1,i=[0],o=Ad,u=Ad,a=Ad,c=Ad,f=Ad;function s(t){return t.x0=t.y0=0,t.x1=e,t.y1=r,t.eachBefore(l),i=[0],n&&t.eachBefore(zd),t}function l(n){var e=i[n.depth],r=n.x0+e,s=n.y0+e,l=n.x1-e,h=n.y1-e;l<r&&(r=l=(r+l)/2),h<s&&(s=h=(s+h)/2),n.x0=r,n.y0=s,n.x1=l,n.y1=h,n.children&&(e=i[n.depth+1]=o(n)/2,r+=f(n)-e,s+=u(n)-e,(l-=a(n)-e)<r&&(r=l=(r+l)/2),(h-=c(n)-e)<s&&(s=h=(s+h)/2),t(n,r,s,l,h))}return s.round=function(t){return arguments.length?(n=!!t,s):n},s.size=function(t){return arguments.length?(e=+t[0],r=+t[1],s):[e,r]},s.tile=function(n){return arguments.length?(t=Td(n),s):t},s.padding=function(t){return arguments.length?s.paddingInner(t).paddingOuter(t):s.paddingInner()},s.paddingInner=function(t){return arguments.length?(o="function"==typeof t?t:Cd(+t),s):o},s.paddingOuter=function(t){return arguments.length?s.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):s.paddingTop()},s.paddingTop=function(t){return arguments.length?(u="function"==typeof t?t:Cd(+t),s):u},s.paddingRight=function(t){return arguments.length?(a="function"==typeof t?t:Cd(+t),s):a},s.paddingBottom=function(t){return arguments.length?(c="function"==typeof t?t:Cd(+t),s):c},s.paddingLeft=function(t){return arguments.length?(f="function"==typeof t?t:Cd(+t),s):f},s},ep=function(t,n,e,r,i){var o,u,a=t.children,c=a.length,f=new Array(c+1);for(f[0]=u=o=0;o<c;++o)f[o+1]=u+=a[o].value;!function t(n,e,r,i,o,u,c){if(n>=e-1){var s=a[n];return s.x0=i,s.y0=o,s.x1=u,void(s.y1=c)}var l=f[n],h=r/2+l,d=n+1,p=e-1;for(;d<p;){var g=d+p>>>1;f[g]<h?d=g+1:p=g}h-f[d-1]<f[d]-h&&n+1<d&&--d;var v=f[d]-l,y=r-v;if(u-i>c-o){var b=(i*y+u*v)/r;t(n,d,v,i,o,b,c),t(d,e,y,b,o,u,c)}else{var m=(o*y+c*v)/r;t(n,d,v,i,o,u,m),t(d,e,y,i,m,u,c)}}(0,c,t.value,n,e,r,i)},rp=function(t,n,e,r,i){(1&t.depth?Qd:Od)(t,n,e,r,i)},ip=function t(n){function e(t,e,r,i,o){if((u=t._squarify)&&u.ratio===n)for(var u,a,c,f,s,l=-1,h=u.length,d=t.value;++l<h;){for(c=(a=u[l]).children,f=a.value=0,s=c.length;f<s;++f)a.value+=c[f].value;a.dice?Od(a,e,r,i,r+=(o-r)*a.value/d):Qd(a,e,r,e+=(i-e)*a.value/d,o),d-=a.value}else t._squarify=u=Kd(n,t,e,r,i,o),u.ratio=n}return e.ratio=function(n){return t((n=+n)>1?n:1)},e}(Jd),op=function(t){for(var n,e=-1,r=t.length,i=t[r-1],o=0;++e<r;)n=i,i=t[e],o+=n[1]*i[0]-n[0]*i[1];return o/2},up=function(t){for(var n,e,r=-1,i=t.length,o=0,u=0,a=t[i-1],c=0;++r<i;)n=a,a=t[r],c+=e=n[0]*a[1]-a[0]*n[1],o+=(n[0]+a[0])*e,u+=(n[1]+a[1])*e;return[o/(c*=3),u/c]},ap=function(t,n,e){return(n[0]-t[0])*(e[1]-t[1])-(n[1]-t[1])*(e[0]-t[0])};function cp(t,n){return t[0]-n[0]||t[1]-n[1]}function fp(t){for(var n=t.length,e=[0,1],r=2,i=2;i<n;++i){for(;r>1&&ap(t[e[r-2]],t[e[r-1]],t[i])<=0;)--r;e[r++]=i}return e.slice(0,r)}var sp=function(t){if((e=t.length)<3)return null;var n,e,r=new Array(e),i=new Array(e);for(n=0;n<e;++n)r[n]=[+t[n][0],+t[n][1],n];for(r.sort(cp),n=0;n<e;++n)i[n]=[r[n][0],-r[n][1]];var o=fp(r),u=fp(i),a=u[0]===o[0],c=u[u.length-1]===o[o.length-1],f=[];for(n=o.length-1;n>=0;--n)f.push(t[r[o[n]][2]]);for(n=+a;n<u.length-c;++n)f.push(t[r[u[n]][2]]);return f},lp=function(t,n){for(var e,r,i=t.length,o=t[i-1],u=n[0],a=n[1],c=o[0],f=o[1],s=!1,l=0;l<i;++l)e=(o=t[l])[0],(r=o[1])>a!=f>a&&u<(c-e)*(a-r)/(f-r)+e&&(s=!s),c=e,f=r;return s},hp=function(t){for(var n,e,r=-1,i=t.length,o=t[i-1],u=o[0],a=o[1],c=0;++r<i;)n=u,e=a,n-=u=(o=t[r])[0],e-=a=o[1],c+=Math.sqrt(n*n+e*e);return c},dp=function(){return Math.random()},pp=function t(n){function e(t,e){return t=null==t?0:+t,e=null==e?1:+e,1===arguments.length?(e=t,t=0):e-=t,function(){return n()*e+t}}return e.source=t,e}(dp),gp=function t(n){function e(t,e){var r,i;return t=null==t?0:+t,e=null==e?1:+e,function(){var o;if(null!=r)o=r,r=null;else do{r=2*n()-1,o=2*n()-1,i=r*r+o*o}while(!i||i>1);return t+e*o*Math.sqrt(-2*Math.log(i)/i)}}return e.source=t,e}(dp),vp=function t(n){function e(){var t=gp.source(n).apply(this,arguments);return function(){return Math.exp(t())}}return e.source=t,e}(dp),yp=function t(n){function e(t){return function(){for(var e=0,r=0;r<t;++r)e+=n();return e}}return e.source=t,e}(dp),bp=function t(n){function e(t){var e=yp.source(n)(t);return function(){return e()/t}}return e.source=t,e}(dp),mp=function t(n){function e(t){return function(){return-Math.log(1-n())/t}}return e.source=t,e}(dp);function _p(t,n){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(n).domain(t)}return this}function xp(t,n){switch(arguments.length){case 0:break;case 1:this.interpolator(t);break;default:this.interpolator(n).domain(t)}return this}var wp=Array.prototype,Mp=wp.map,kp=wp.slice,Np={name:"implicit"};function Sp(){var t=Zo(),n=[],e=[],r=Np;function i(i){var o=i+"",u=t.get(o);if(!u){if(r!==Np)return r;t.set(o,u=n.push(i))}return e[(u-1)%e.length]}return i.domain=function(e){if(!arguments.length)return n.slice();n=[],t=Zo();for(var r,o,u=-1,a=e.length;++u<a;)t.has(o=(r=e[u])+"")||t.set(o,n.push(r));return i},i.range=function(t){return arguments.length?(e=kp.call(t),i):e.slice()},i.unknown=function(t){return arguments.length?(r=t,i):r},i.copy=function(){return Sp(n,e).unknown(r)},_p.apply(i,arguments),i}function Tp(){var t,n,e=Sp().unknown(void 0),r=e.domain,i=e.range,o=[0,1],u=!1,a=0,c=0,f=.5;function s(){var e=r().length,s=o[1]<o[0],l=o[s-0],h=o[1-s];t=(h-l)/Math.max(1,e-a+2*c),u&&(t=Math.floor(t)),l+=(h-l-t*(e-a))*f,n=t*(1-a),u&&(l=Math.round(l),n=Math.round(n));var d=w(e).map(function(n){return l+t*n});return i(s?d.reverse():d)}return delete e.unknown,e.domain=function(t){return arguments.length?(r(t),s()):r()},e.range=function(t){return arguments.length?(o=[+t[0],+t[1]],s()):o.slice()},e.rangeRound=function(t){return o=[+t[0],+t[1]],u=!0,s()},e.bandwidth=function(){return n},e.step=function(){return t},e.round=function(t){return arguments.length?(u=!!t,s()):u},e.padding=function(t){return arguments.length?(a=Math.min(1,c=+t),s()):a},e.paddingInner=function(t){return arguments.length?(a=Math.min(1,t),s()):a},e.paddingOuter=function(t){return arguments.length?(c=+t,s()):c},e.align=function(t){return arguments.length?(f=Math.max(0,Math.min(1,t)),s()):f},e.copy=function(){return Tp(r(),o).round(u).paddingInner(a).paddingOuter(c).align(f)},_p.apply(s(),arguments)}function Ap(){return function t(n){var e=n.copy;return n.padding=n.paddingOuter,delete n.paddingInner,delete n.paddingOuter,n.copy=function(){return t(e())},n}(Tp.apply(null,arguments).paddingInner(1))}var Cp=function(t){return function(){return t}},Ep=function(t){return+t},Lp=[0,1];function Rp(t){return t}function Dp(t,n){return(n-=t=+t)?function(e){return(e-t)/n}:Cp(isNaN(n)?NaN:.5)}function Pp(t){var n,e=t[0],r=t[t.length-1];return e>r&&(n=e,e=r,r=n),function(t){return Math.max(e,Math.min(r,t))}}function zp(t,n,e){var r=t[0],i=t[1],o=n[0],u=n[1];return i<r?(r=Dp(i,r),o=e(u,o)):(r=Dp(r,i),o=e(o,u)),function(t){return o(r(t))}}function Op(t,n,e){var r=Math.min(t.length,n.length)-1,i=new Array(r),o=new Array(r),u=-1;for(t[r]<t[0]&&(t=t.slice().reverse(),n=n.slice().reverse());++u<r;)i[u]=Dp(t[u],t[u+1]),o[u]=e(n[u],n[u+1]);return function(n){var e=c(t,n,1,r)-1;return o[e](i[e](n))}}function Up(t,n){return n.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function qp(){var t,n,e,r,i,o,u=Lp,a=Lp,c=We,f=Rp;function s(){return r=Math.min(u.length,a.length)>2?Op:zp,i=o=null,l}function l(n){return isNaN(n=+n)?e:(i||(i=r(u.map(t),a,c)))(t(f(n)))}return l.invert=function(e){return f(n((o||(o=r(a,u.map(t),Ie)))(e)))},l.domain=function(t){return arguments.length?(u=Mp.call(t,Ep),f===Rp||(f=Pp(u)),s()):u.slice()},l.range=function(t){return arguments.length?(a=kp.call(t),s()):a.slice()},l.rangeRound=function(t){return a=kp.call(t),c=Qe,s()},l.clamp=function(t){return arguments.length?(f=t?Pp(u):Rp,l):f!==Rp},l.interpolate=function(t){return arguments.length?(c=t,s()):c},l.unknown=function(t){return arguments.length?(e=t,l):e},function(e,r){return t=e,n=r,s()}}function Ip(t,n){return qp()(t,n)}var Bp=function(t,n,e,r){var i,o=A(t,n,e);switch((r=Ba(null==r?",f":r)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(n));return null!=r.precision||isNaN(i=tc(o,u))||(r.precision=i),Va(r,u);case"":case"e":case"g":case"p":case"r":null!=r.precision||isNaN(i=nc(o,Math.max(Math.abs(t),Math.abs(n))))||(r.precision=i-("e"===r.type));break;case"f":case"%":null!=r.precision||isNaN(i=Ka(o))||(r.precision=i-2*("%"===r.type))}return Ha(r)};function Fp(t){var n=t.domain;return t.ticks=function(t){var e=n();return S(e[0],e[e.length-1],null==t?10:t)},t.tickFormat=function(t,e){var r=n();return Bp(r[0],r[r.length-1],null==t?10:t,e)},t.nice=function(e){null==e&&(e=10);var r,i=n(),o=0,u=i.length-1,a=i[o],c=i[u];return c<a&&(r=a,a=c,c=r,r=o,o=u,u=r),(r=T(a,c,e))>0?r=T(a=Math.floor(a/r)*r,c=Math.ceil(c/r)*r,e):r<0&&(r=T(a=Math.ceil(a*r)/r,c=Math.floor(c*r)/r,e)),r>0?(i[o]=Math.floor(a/r)*r,i[u]=Math.ceil(c/r)*r,n(i)):r<0&&(i[o]=Math.ceil(a*r)/r,i[u]=Math.floor(c*r)/r,n(i)),t},t}function jp(){var t=Ip(Rp,Rp);return t.copy=function(){return Up(t,jp())},_p.apply(t,arguments),Fp(t)}function Yp(t){var n;function e(t){return isNaN(t=+t)?n:t}return e.invert=e,e.domain=e.range=function(n){return arguments.length?(t=Mp.call(n,Ep),e):t.slice()},e.unknown=function(t){return arguments.length?(n=t,e):n},e.copy=function(){return Yp(t).unknown(n)},t=arguments.length?Mp.call(t,Ep):[0,1],Fp(e)}var Hp=function(t,n){var e,r=0,i=(t=t.slice()).length-1,o=t[r],u=t[i];return u<o&&(e=r,r=i,i=e,e=o,o=u,u=e),t[r]=n.floor(o),t[i]=n.ceil(u),t};function Vp(t){return Math.log(t)}function Xp(t){return Math.exp(t)}function Gp(t){return-Math.log(-t)}function Wp(t){return-Math.exp(-t)}function $p(t){return isFinite(t)?+("1e"+t):t<0?0:t}function Zp(t){return function(n){return-t(-n)}}function Qp(t){var n,e,r=t(Vp,Xp),i=r.domain,o=10;function u(){return n=function(t){return t===Math.E?Math.log:10===t&&Math.log10||2===t&&Math.log2||(t=Math.log(t),function(n){return Math.log(n)/t})}(o),e=function(t){return 10===t?$p:t===Math.E?Math.exp:function(n){return Math.pow(t,n)}}(o),i()[0]<0?(n=Zp(n),e=Zp(e),t(Gp,Wp)):t(Vp,Xp),r}return r.base=function(t){return arguments.length?(o=+t,u()):o},r.domain=function(t){return arguments.length?(i(t),u()):i()},r.ticks=function(t){var r,u=i(),a=u[0],c=u[u.length-1];(r=c<a)&&(h=a,a=c,c=h);var f,s,l,h=n(a),d=n(c),p=null==t?10:+t,g=[];if(!(o%1)&&d-h<p){if(h=Math.round(h)-1,d=Math.round(d)+1,a>0){for(;h<d;++h)for(s=1,f=e(h);s<o;++s)if(!((l=f*s)<a)){if(l>c)break;g.push(l)}}else for(;h<d;++h)for(s=o-1,f=e(h);s>=1;--s)if(!((l=f*s)<a)){if(l>c)break;g.push(l)}}else g=S(h,d,Math.min(d-h,p)).map(e);return r?g.reverse():g},r.tickFormat=function(t,i){if(null==i&&(i=10===o?".0e":","),"function"!=typeof i&&(i=Ha(i)),t===1/0)return i;null==t&&(t=10);var u=Math.max(1,o*t/r.ticks().length);return function(t){var r=t/e(Math.round(n(t)));return r*o<o-.5&&(r*=o),r<=u?i(t):""}},r.nice=function(){return i(Hp(i(),{floor:function(t){return e(Math.floor(n(t)))},ceil:function(t){return e(Math.ceil(n(t)))}}))},r}function Jp(){var t=Qp(qp()).domain([1,10]);return t.copy=function(){return Up(t,Jp()).base(t.base())},_p.apply(t,arguments),t}function Kp(t){return function(n){return Math.sign(n)*Math.log1p(Math.abs(n/t))}}function tg(t){return function(n){return Math.sign(n)*Math.expm1(Math.abs(n))*t}}function ng(t){var n=1,e=t(Kp(n),tg(n));return e.constant=function(e){return arguments.length?t(Kp(n=+e),tg(n)):n},Fp(e)}function eg(){var t=ng(qp());return t.copy=function(){return Up(t,eg()).constant(t.constant())},_p.apply(t,arguments)}function rg(t){return function(n){return n<0?-Math.pow(-n,t):Math.pow(n,t)}}function ig(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function og(t){return t<0?-t*t:t*t}function ug(t){var n=t(Rp,Rp),e=1;return n.exponent=function(n){return arguments.length?1===(e=+n)?t(Rp,Rp):.5===e?t(ig,og):t(rg(e),rg(1/e)):e},Fp(n)}function ag(){var t=ug(qp());return t.copy=function(){return Up(t,ag()).exponent(t.exponent())},_p.apply(t,arguments),t}function cg(){return ag.apply(null,arguments).exponent(.5)}function fg(){var t,n=[],e=[],i=[];function o(){var t=0,r=Math.max(1,e.length);for(i=new Array(r-1);++t<r;)i[t-1]=L(n,t/r);return u}function u(n){return isNaN(n=+n)?t:e[c(i,n)]}return u.invertExtent=function(t){var r=e.indexOf(t);return r<0?[NaN,NaN]:[r>0?i[r-1]:n[0],r<i.length?i[r]:n[n.length-1]]},u.domain=function(t){if(!arguments.length)return n.slice();n=[];for(var e,i=0,u=t.length;i<u;++i)null==(e=t[i])||isNaN(e=+e)||n.push(e);return n.sort(r),o()},u.range=function(t){return arguments.length?(e=kp.call(t),o()):e.slice()},u.unknown=function(n){return arguments.length?(t=n,u):t},u.quantiles=function(){return i.slice()},u.copy=function(){return fg().domain(n).range(e).unknown(t)},_p.apply(u,arguments)}function sg(){var t,n=0,e=1,r=1,i=[.5],o=[0,1];function u(n){return n<=n?o[c(i,n,0,r)]:t}function a(){var t=-1;for(i=new Array(r);++t<r;)i[t]=((t+1)*e-(t-r)*n)/(r+1);return u}return u.domain=function(t){return arguments.length?(n=+t[0],e=+t[1],a()):[n,e]},u.range=function(t){return arguments.length?(r=(o=kp.call(t)).length-1,a()):o.slice()},u.invertExtent=function(t){var u=o.indexOf(t);return u<0?[NaN,NaN]:u<1?[n,i[0]]:u>=r?[i[r-1],e]:[i[u-1],i[u]]},u.unknown=function(n){return arguments.length?(t=n,u):u},u.thresholds=function(){return i.slice()},u.copy=function(){return sg().domain([n,e]).range(o).unknown(t)},_p.apply(Fp(u),arguments)}function lg(){var t,n=[.5],e=[0,1],r=1;function i(i){return i<=i?e[c(n,i,0,r)]:t}return i.domain=function(t){return arguments.length?(n=kp.call(t),r=Math.min(n.length,e.length-1),i):n.slice()},i.range=function(t){return arguments.length?(e=kp.call(t),r=Math.min(n.length,e.length-1),i):e.slice()},i.invertExtent=function(t){var r=e.indexOf(t);return[n[r-1],n[r]]},i.unknown=function(n){return arguments.length?(t=n,i):t},i.copy=function(){return lg().domain(n).range(e).unknown(t)},_p.apply(i,arguments)}var hg=new Date,dg=new Date;function pg(t,n,e,r){function i(n){return t(n=new Date(+n)),n}return i.floor=i,i.ceil=function(e){return t(e=new Date(e-1)),n(e,1),t(e),e},i.round=function(t){var n=i(t),e=i.ceil(t);return t-n<e-t?n:e},i.offset=function(t,e){return n(t=new Date(+t),null==e?1:Math.floor(e)),t},i.range=function(e,r,o){var u,a=[];if(e=i.ceil(e),o=null==o?1:Math.floor(o),!(e<r&&o>0))return a;do{a.push(u=new Date(+e)),n(e,o),t(e)}while(u<e&&e<r);return a},i.filter=function(e){return pg(function(n){if(n>=n)for(;t(n),!e(n);)n.setTime(n-1)},function(t,r){if(t>=t)if(r<0)for(;++r<=0;)for(;n(t,-1),!e(t););else for(;--r>=0;)for(;n(t,1),!e(t););})},e&&(i.count=function(n,r){return hg.setTime(+n),dg.setTime(+r),t(hg),t(dg),Math.floor(e(hg,dg))},i.every=function(t){return t=Math.floor(t),isFinite(t)&&t>0?t>1?i.filter(r?function(n){return r(n)%t==0}:function(n){return i.count(0,n)%t==0}):i:null}),i}var gg=pg(function(){},function(t,n){t.setTime(+t+n)},function(t,n){return n-t});gg.every=function(t){return t=Math.floor(t),isFinite(t)&&t>0?t>1?pg(function(n){n.setTime(Math.floor(n/t)*t)},function(n,e){n.setTime(+n+e*t)},function(n,e){return(e-n)/t}):gg:null};var vg=gg,yg=gg.range,bg=6e4,mg=6048e5,_g=pg(function(t){t.setTime(t-t.getMilliseconds())},function(t,n){t.setTime(+t+1e3*n)},function(t,n){return(n-t)/1e3},function(t){return t.getUTCSeconds()}),xg=_g,wg=_g.range,Mg=pg(function(t){t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},function(t,n){t.setTime(+t+n*bg)},function(t,n){return(n-t)/bg},function(t){return t.getMinutes()}),kg=Mg,Ng=Mg.range,Sg=pg(function(t){t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-t.getMinutes()*bg)},function(t,n){t.setTime(+t+36e5*n)},function(t,n){return(n-t)/36e5},function(t){return t.getHours()}),Tg=Sg,Ag=Sg.range,Cg=pg(function(t){t.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+n)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*bg)/864e5},function(t){return t.getDate()-1}),Eg=Cg,Lg=Cg.range;function Rg(t){return pg(function(n){n.setDate(n.getDate()-(n.getDay()+7-t)%7),n.setHours(0,0,0,0)},function(t,n){t.setDate(t.getDate()+7*n)},function(t,n){return(n-t-(n.getTimezoneOffset()-t.getTimezoneOffset())*bg)/mg})}var Dg=Rg(0),Pg=Rg(1),zg=Rg(2),Og=Rg(3),Ug=Rg(4),qg=Rg(5),Ig=Rg(6),Bg=Dg.range,Fg=Pg.range,jg=zg.range,Yg=Og.range,Hg=Ug.range,Vg=qg.range,Xg=Ig.range,Gg=pg(function(t){t.setDate(1),t.setHours(0,0,0,0)},function(t,n){t.setMonth(t.getMonth()+n)},function(t,n){return n.getMonth()-t.getMonth()+12*(n.getFullYear()-t.getFullYear())},function(t){return t.getMonth()}),Wg=Gg,$g=Gg.range,Zg=pg(function(t){t.setMonth(0,1),t.setHours(0,0,0,0)},function(t,n){t.setFullYear(t.getFullYear()+n)},function(t,n){return n.getFullYear()-t.getFullYear()},function(t){return t.getFullYear()});Zg.every=function(t){return isFinite(t=Math.floor(t))&&t>0?pg(function(n){n.setFullYear(Math.floor(n.getFullYear()/t)*t),n.setMonth(0,1),n.setHours(0,0,0,0)},function(n,e){n.setFullYear(n.getFullYear()+e*t)}):null};var Qg=Zg,Jg=Zg.range,Kg=pg(function(t){t.setUTCSeconds(0,0)},function(t,n){t.setTime(+t+n*bg)},function(t,n){return(n-t)/bg},function(t){return t.getUTCMinutes()}),tv=Kg,nv=Kg.range,ev=pg(function(t){t.setUTCMinutes(0,0,0)},function(t,n){t.setTime(+t+36e5*n)},function(t,n){return(n-t)/36e5},function(t){return t.getUTCHours()}),rv=ev,iv=ev.range,ov=pg(function(t){t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+n)},function(t,n){return(n-t)/864e5},function(t){return t.getUTCDate()-1}),uv=ov,av=ov.range;function cv(t){return pg(function(n){n.setUTCDate(n.getUTCDate()-(n.getUTCDay()+7-t)%7),n.setUTCHours(0,0,0,0)},function(t,n){t.setUTCDate(t.getUTCDate()+7*n)},function(t,n){return(n-t)/mg})}var fv=cv(0),sv=cv(1),lv=cv(2),hv=cv(3),dv=cv(4),pv=cv(5),gv=cv(6),vv=fv.range,yv=sv.range,bv=lv.range,mv=hv.range,_v=dv.range,xv=pv.range,wv=gv.range,Mv=pg(function(t){t.setUTCDate(1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCMonth(t.getUTCMonth()+n)},function(t,n){return n.getUTCMonth()-t.getUTCMonth()+12*(n.getUTCFullYear()-t.getUTCFullYear())},function(t){return t.getUTCMonth()}),kv=Mv,Nv=Mv.range,Sv=pg(function(t){t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},function(t,n){t.setUTCFullYear(t.getUTCFullYear()+n)},function(t,n){return n.getUTCFullYear()-t.getUTCFullYear()},function(t){return t.getUTCFullYear()});Sv.every=function(t){return isFinite(t=Math.floor(t))&&t>0?pg(function(n){n.setUTCFullYear(Math.floor(n.getUTCFullYear()/t)*t),n.setUTCMonth(0,1),n.setUTCHours(0,0,0,0)},function(n,e){n.setUTCFullYear(n.getUTCFullYear()+e*t)}):null};var Tv=Sv,Av=Sv.range;function Cv(t){if(0<=t.y&&t.y<100){var n=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return n.setFullYear(t.y),n}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function Ev(t){if(0<=t.y&&t.y<100){var n=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return n.setUTCFullYear(t.y),n}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function Lv(t){return{y:t,m:0,d:1,H:0,M:0,S:0,L:0}}function Rv(t){var n=t.dateTime,e=t.date,r=t.time,i=t.periods,o=t.days,u=t.shortDays,a=t.months,c=t.shortMonths,f=Hv(i),s=Vv(i),l=Hv(o),h=Vv(o),d=Hv(u),p=Vv(u),g=Hv(a),v=Vv(a),y=Hv(c),b=Vv(c),m={a:function(t){return u[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return a[t.getMonth()]},c:null,d:ly,e:ly,f:vy,H:hy,I:dy,j:py,L:gy,m:yy,M:by,p:function(t){return i[+(t.getHours()>=12)]},Q:Xy,s:Gy,S:my,u:_y,U:xy,V:wy,w:My,W:ky,x:null,X:null,y:Ny,Y:Sy,Z:Ty,"%":Vy},_={a:function(t){return u[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return a[t.getUTCMonth()]},c:null,d:Ay,e:Ay,f:Dy,H:Cy,I:Ey,j:Ly,L:Ry,m:Py,M:zy,p:function(t){return i[+(t.getUTCHours()>=12)]},Q:Xy,s:Gy,S:Oy,u:Uy,U:qy,V:Iy,w:By,W:Fy,x:null,X:null,y:jy,Y:Yy,Z:Hy,"%":Vy},x={a:function(t,n,e){var r=d.exec(n.slice(e));return r?(t.w=p[r[0].toLowerCase()],e+r[0].length):-1},A:function(t,n,e){var r=l.exec(n.slice(e));return r?(t.w=h[r[0].toLowerCase()],e+r[0].length):-1},b:function(t,n,e){var r=y.exec(n.slice(e));return r?(t.m=b[r[0].toLowerCase()],e+r[0].length):-1},B:function(t,n,e){var r=g.exec(n.slice(e));return r?(t.m=v[r[0].toLowerCase()],e+r[0].length):-1},c:function(t,e,r){return k(t,n,e,r)},d:ny,e:ny,f:ay,H:ry,I:ry,j:ey,L:uy,m:ty,M:iy,p:function(t,n,e){var r=f.exec(n.slice(e));return r?(t.p=s[r[0].toLowerCase()],e+r[0].length):-1},Q:fy,s:sy,S:oy,u:Gv,U:Wv,V:$v,w:Xv,W:Zv,x:function(t,n,r){return k(t,e,n,r)},X:function(t,n,e){return k(t,r,n,e)},y:Jv,Y:Qv,Z:Kv,"%":cy};function w(t,n){return function(e){var r,i,o,u=[],a=-1,c=0,f=t.length;for(e instanceof Date||(e=new Date(+e));++a<f;)37===t.charCodeAt(a)&&(u.push(t.slice(c,a)),null!=(i=qv[r=t.charAt(++a)])?r=t.charAt(++a):i="e"===r?" ":"0",(o=n[r])&&(r=o(e,i)),u.push(r),c=a+1);return u.push(t.slice(c,a)),u.join("")}}function M(t,n){return function(e){var r,i,o=Lv(1900);if(k(o,t,e+="",0)!=e.length)return null;if("Q"in o)return new Date(o.Q);if("p"in o&&(o.H=o.H%12+12*o.p),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(i=(r=Ev(Lv(o.y))).getUTCDay(),r=i>4||0===i?sv.ceil(r):sv(r),r=uv.offset(r,7*(o.V-1)),o.y=r.getUTCFullYear(),o.m=r.getUTCMonth(),o.d=r.getUTCDate()+(o.w+6)%7):(i=(r=n(Lv(o.y))).getDay(),r=i>4||0===i?Pg.ceil(r):Pg(r),r=Eg.offset(r,7*(o.V-1)),o.y=r.getFullYear(),o.m=r.getMonth(),o.d=r.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:"W"in o?1:0),i="Z"in o?Ev(Lv(o.y)).getUTCDay():n(Lv(o.y)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,Ev(o)):n(o)}}function k(t,n,e,r){for(var i,o,u=0,a=n.length,c=e.length;u<a;){if(r>=c)return-1;if(37===(i=n.charCodeAt(u++))){if(i=n.charAt(u++),!(o=x[i in qv?n.charAt(u++):i])||(r=o(t,e,r))<0)return-1}else if(i!=e.charCodeAt(r++))return-1}return r}return m.x=w(e,m),m.X=w(r,m),m.c=w(n,m),_.x=w(e,_),_.X=w(r,_),_.c=w(n,_),{format:function(t){var n=w(t+="",m);return n.toString=function(){return t},n},parse:function(t){var n=M(t+="",Cv);return n.toString=function(){return t},n},utcFormat:function(t){var n=w(t+="",_);return n.toString=function(){return t},n},utcParse:function(t){var n=M(t,Ev);return n.toString=function(){return t},n}}}var Dv,Pv,zv,Ov,Uv,qv={"-":"",_:" ",0:"0"},Iv=/^\s*\d+/,Bv=/^%/,Fv=/[\\^$*+?|[\]().{}]/g;function jv(t,n,e){var r=t<0?"-":"",i=(r?-t:t)+"",o=i.length;return r+(o<e?new Array(e-o+1).join(n)+i:i)}function Yv(t){return t.replace(Fv,"\\$&")}function Hv(t){return new RegExp("^(?:"+t.map(Yv).join("|")+")","i")}function Vv(t){for(var n={},e=-1,r=t.length;++e<r;)n[t[e].toLowerCase()]=e;return n}function Xv(t,n,e){var r=Iv.exec(n.slice(e,e+1));return r?(t.w=+r[0],e+r[0].length):-1}function Gv(t,n,e){var r=Iv.exec(n.slice(e,e+1));return r?(t.u=+r[0],e+r[0].length):-1}function Wv(t,n,e){var r=Iv.exec(n.slice(e,e+2));return r?(t.U=+r[0],e+r[0].length):-1}function $v(t,n,e){var r=Iv.exec(n.slice(e,e+2));return r?(t.V=+r[0],e+r[0].length):-1}function Zv(t,n,e){var r=Iv.exec(n.slice(e,e+2));return r?(t.W=+r[0],e+r[0].length):-1}function Qv(t,n,e){var r=Iv.exec(n.slice(e,e+4));return r?(t.y=+r[0],e+r[0].length):-1}function Jv(t,n,e){var r=Iv.exec(n.slice(e,e+2));return r?(t.y=+r[0]+(+r[0]>68?1900:2e3),e+r[0].length):-1}function Kv(t,n,e){var r=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(n.slice(e,e+6));return r?(t.Z=r[1]?0:-(r[2]+(r[3]||"00")),e+r[0].length):-1}function ty(t,n,e){var r=Iv.exec(n.slice(e,e+2));return r?(t.m=r[0]-1,e+r[0].length):-1}function ny(t,n,e){var r=Iv.exec(n.slice(e,e+2));return r?(t.d=+r[0],e+r[0].length):-1}function ey(t,n,e){var r=Iv.exec(n.slice(e,e+3));return r?(t.m=0,t.d=+r[0],e+r[0].length):-1}function ry(t,n,e){var r=Iv.exec(n.slice(e,e+2));return r?(t.H=+r[0],e+r[0].length):-1}function iy(t,n,e){var r=Iv.exec(n.slice(e,e+2));return r?(t.M=+r[0],e+r[0].length):-1}function oy(t,n,e){var r=Iv.exec(n.slice(e,e+2));return r?(t.S=+r[0],e+r[0].length):-1}function uy(t,n,e){var r=Iv.exec(n.slice(e,e+3));return r?(t.L=+r[0],e+r[0].length):-1}function ay(t,n,e){var r=Iv.exec(n.slice(e,e+6));return r?(t.L=Math.floor(r[0]/1e3),e+r[0].length):-1}function cy(t,n,e){var r=Bv.exec(n.slice(e,e+1));return r?e+r[0].length:-1}function fy(t,n,e){var r=Iv.exec(n.slice(e));return r?(t.Q=+r[0],e+r[0].length):-1}function sy(t,n,e){var r=Iv.exec(n.slice(e));return r?(t.Q=1e3*+r[0],e+r[0].length):-1}function ly(t,n){return jv(t.getDate(),n,2)}function hy(t,n){return jv(t.getHours(),n,2)}function dy(t,n){return jv(t.getHours()%12||12,n,2)}function py(t,n){return jv(1+Eg.count(Qg(t),t),n,3)}function gy(t,n){return jv(t.getMilliseconds(),n,3)}function vy(t,n){return gy(t,n)+"000"}function yy(t,n){return jv(t.getMonth()+1,n,2)}function by(t,n){return jv(t.getMinutes(),n,2)}function my(t,n){return jv(t.getSeconds(),n,2)}function _y(t){var n=t.getDay();return 0===n?7:n}function xy(t,n){return jv(Dg.count(Qg(t),t),n,2)}function wy(t,n){var e=t.getDay();return t=e>=4||0===e?Ug(t):Ug.ceil(t),jv(Ug.count(Qg(t),t)+(4===Qg(t).getDay()),n,2)}function My(t){return t.getDay()}function ky(t,n){return jv(Pg.count(Qg(t),t),n,2)}function Ny(t,n){return jv(t.getFullYear()%100,n,2)}function Sy(t,n){return jv(t.getFullYear()%1e4,n,4)}function Ty(t){var n=t.getTimezoneOffset();return(n>0?"-":(n*=-1,"+"))+jv(n/60|0,"0",2)+jv(n%60,"0",2)}function Ay(t,n){return jv(t.getUTCDate(),n,2)}function Cy(t,n){return jv(t.getUTCHours(),n,2)}function Ey(t,n){return jv(t.getUTCHours()%12||12,n,2)}function Ly(t,n){return jv(1+uv.count(Tv(t),t),n,3)}function Ry(t,n){return jv(t.getUTCMilliseconds(),n,3)}function Dy(t,n){return Ry(t,n)+"000"}function Py(t,n){return jv(t.getUTCMonth()+1,n,2)}function zy(t,n){return jv(t.getUTCMinutes(),n,2)}function Oy(t,n){return jv(t.getUTCSeconds(),n,2)}function Uy(t){var n=t.getUTCDay();return 0===n?7:n}function qy(t,n){return jv(fv.count(Tv(t),t),n,2)}function Iy(t,n){var e=t.getUTCDay();return t=e>=4||0===e?dv(t):dv.ceil(t),jv(dv.count(Tv(t),t)+(4===Tv(t).getUTCDay()),n,2)}function By(t){return t.getUTCDay()}function Fy(t,n){return jv(sv.count(Tv(t),t),n,2)}function jy(t,n){return jv(t.getUTCFullYear()%100,n,2)}function Yy(t,n){return jv(t.getUTCFullYear()%1e4,n,4)}function Hy(){return"+0000"}function Vy(){return"%"}function Xy(t){return+t}function Gy(t){return Math.floor(+t/1e3)}function Wy(t){return Dv=Rv(t),Pv=Dv.format,zv=Dv.parse,Ov=Dv.utcFormat,Uv=Dv.utcParse,Dv}Wy({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]});var $y=Date.prototype.toISOString?function(t){return t.toISOString()}:Ov("%Y-%m-%dT%H:%M:%S.%LZ");var Zy=+new Date("2000-01-01T00:00:00.000Z")?function(t){var n=new Date(t);return isNaN(n)?null:n}:Uv("%Y-%m-%dT%H:%M:%S.%LZ"),Qy=1e3,Jy=60*Qy,Ky=60*Jy,tb=24*Ky,nb=7*tb,eb=30*tb,rb=365*tb;function ib(t){return new Date(t)}function ob(t){return t instanceof Date?+t:+new Date(+t)}function ub(t,n,e,r,o,u,a,c,f){var s=Ip(Rp,Rp),l=s.invert,h=s.domain,d=f(".%L"),p=f(":%S"),g=f("%I:%M"),v=f("%I %p"),y=f("%a %d"),b=f("%b %d"),m=f("%B"),_=f("%Y"),x=[[a,1,Qy],[a,5,5*Qy],[a,15,15*Qy],[a,30,30*Qy],[u,1,Jy],[u,5,5*Jy],[u,15,15*Jy],[u,30,30*Jy],[o,1,Ky],[o,3,3*Ky],[o,6,6*Ky],[o,12,12*Ky],[r,1,tb],[r,2,2*tb],[e,1,nb],[n,1,eb],[n,3,3*eb],[t,1,rb]];function w(i){return(a(i)<i?d:u(i)<i?p:o(i)<i?g:r(i)<i?v:n(i)<i?e(i)<i?y:b:t(i)<i?m:_)(i)}function M(n,e,r,o){if(null==n&&(n=10),"number"==typeof n){var u=Math.abs(r-e)/n,a=i(function(t){return t[2]}).right(x,u);a===x.length?(o=A(e/rb,r/rb,n),n=t):a?(o=(a=x[u/x[a-1][2]<x[a][2]/u?a-1:a])[1],n=a[0]):(o=Math.max(A(e,r,n),1),n=c)}return null==o?n:n.every(o)}return s.invert=function(t){return new Date(l(t))},s.domain=function(t){return arguments.length?h(Mp.call(t,ob)):h().map(ib)},s.ticks=function(t,n){var e,r=h(),i=r[0],o=r[r.length-1],u=o<i;return u&&(e=i,i=o,o=e),e=(e=M(t,i,o,n))?e.range(i,o+1):[],u?e.reverse():e},s.tickFormat=function(t,n){return null==n?w:f(n)},s.nice=function(t,n){var e=h();return(t=M(t,e[0],e[e.length-1],n))?h(Hp(e,t)):s},s.copy=function(){return Up(s,ub(t,n,e,r,o,u,a,c,f))},s}var ab=function(){return _p.apply(ub(Qg,Wg,Dg,Eg,Tg,kg,xg,vg,Pv).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)},cb=function(){return _p.apply(ub(Tv,kv,fv,uv,rv,tv,xg,vg,Ov).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)};function fb(){var t,n,e,r,i,o=0,u=1,a=Rp,c=!1;function f(n){return isNaN(n=+n)?i:a(0===e?.5:(n=(r(n)-t)*e,c?Math.max(0,Math.min(1,n)):n))}return f.domain=function(i){return arguments.length?(t=r(o=+i[0]),n=r(u=+i[1]),e=t===n?0:1/(n-t),f):[o,u]},f.clamp=function(t){return arguments.length?(c=!!t,f):c},f.interpolator=function(t){return arguments.length?(a=t,f):a},f.unknown=function(t){return arguments.length?(i=t,f):i},function(i){return r=i,t=i(o),n=i(u),e=t===n?0:1/(n-t),f}}function sb(t,n){return n.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function lb(){var t=Fp(fb()(Rp));return t.copy=function(){return sb(t,lb())},xp.apply(t,arguments)}function hb(){var t=Qp(fb()).domain([1,10]);return t.copy=function(){return sb(t,hb()).base(t.base())},xp.apply(t,arguments)}function db(){var t=ng(fb());return t.copy=function(){return sb(t,db()).constant(t.constant())},xp.apply(t,arguments)}function pb(){var t=ug(fb());return t.copy=function(){return sb(t,pb()).exponent(t.exponent())},xp.apply(t,arguments)}function gb(){return pb.apply(null,arguments).exponent(.5)}function vb(){var t=[],n=Rp;function e(e){if(!isNaN(e=+e))return n((c(t,e)-1)/(t.length-1))}return e.domain=function(n){if(!arguments.length)return t.slice();t=[];for(var i,o=0,u=n.length;o<u;++o)null==(i=n[o])||isNaN(i=+i)||t.push(i);return t.sort(r),e},e.interpolator=function(t){return arguments.length?(n=t,e):n},e.copy=function(){return vb(n).domain(t)},xp.apply(e,arguments)}function yb(){var t,n,e,r,i,o,u,a=0,c=.5,f=1,s=Rp,l=!1;function h(t){return isNaN(t=+t)?u:(t=.5+((t=+o(t))-n)*(t<n?r:i),s(l?Math.max(0,Math.min(1,t)):t))}return h.domain=function(u){return arguments.length?(t=o(a=+u[0]),n=o(c=+u[1]),e=o(f=+u[2]),r=t===n?0:.5/(n-t),i=n===e?0:.5/(e-n),h):[a,c,f]},h.clamp=function(t){return arguments.length?(l=!!t,h):l},h.interpolator=function(t){return arguments.length?(s=t,h):s},h.unknown=function(t){return arguments.length?(u=t,h):u},function(u){return o=u,t=u(a),n=u(c),e=u(f),r=t===n?0:.5/(n-t),i=n===e?0:.5/(e-n),h}}function bb(){var t=Fp(yb()(Rp));return t.copy=function(){return sb(t,bb())},xp.apply(t,arguments)}function mb(){var t=Qp(yb()).domain([.1,1,10]);return t.copy=function(){return sb(t,mb()).base(t.base())},xp.apply(t,arguments)}function _b(){var t=ng(yb());return t.copy=function(){return sb(t,_b()).constant(t.constant())},xp.apply(t,arguments)}function xb(){var t=ug(yb());return t.copy=function(){return sb(t,xb()).exponent(t.exponent())},xp.apply(t,arguments)}function wb(){return xb.apply(null,arguments).exponent(.5)}var Mb=function(t){for(var n=t.length/6|0,e=new Array(n),r=0;r<n;)e[r]="#"+t.slice(6*r,6*++r);return e},kb=Mb("1f77b4ff7f0e2ca02cd627289467bd8c564be377c27f7f7fbcbd2217becf"),Nb=Mb("7fc97fbeaed4fdc086ffff99386cb0f0027fbf5b17666666"),Sb=Mb("1b9e77d95f027570b3e7298a66a61ee6ab02a6761d666666"),Tb=Mb("a6cee31f78b4b2df8a33a02cfb9a99e31a1cfdbf6fff7f00cab2d66a3d9affff99b15928"),Ab=Mb("fbb4aeb3cde3ccebc5decbe4fed9a6ffffcce5d8bdfddaecf2f2f2"),Cb=Mb("b3e2cdfdcdaccbd5e8f4cae4e6f5c9fff2aef1e2cccccccc"),Eb=Mb("e41a1c377eb84daf4a984ea3ff7f00ffff33a65628f781bf999999"),Lb=Mb("66c2a5fc8d628da0cbe78ac3a6d854ffd92fe5c494b3b3b3"),Rb=Mb("8dd3c7ffffb3bebadafb807280b1d3fdb462b3de69fccde5d9d9d9bc80bdccebc5ffed6f"),Db=function(t){return ze(t[t.length-1])},Pb=new Array(3).concat("d8b365f5f5f55ab4ac","a6611adfc27d80cdc1018571","a6611adfc27df5f5f580cdc1018571","8c510ad8b365f6e8c3c7eae55ab4ac01665e","8c510ad8b365f6e8c3f5f5f5c7eae55ab4ac01665e","8c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e","8c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e","5430058c510abf812ddfc27df6e8c3c7eae580cdc135978f01665e003c30","5430058c510abf812ddfc27df6e8c3f5f5f5c7eae580cdc135978f01665e003c30").map(Mb),zb=Db(Pb),Ob=new Array(3).concat("af8dc3f7f7f77fbf7b","7b3294c2a5cfa6dba0008837","7b3294c2a5cff7f7f7a6dba0008837","762a83af8dc3e7d4e8d9f0d37fbf7b1b7837","762a83af8dc3e7d4e8f7f7f7d9f0d37fbf7b1b7837","762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b7837","762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b7837","40004b762a839970abc2a5cfe7d4e8d9f0d3a6dba05aae611b783700441b","40004b762a839970abc2a5cfe7d4e8f7f7f7d9f0d3a6dba05aae611b783700441b").map(Mb),Ub=Db(Ob),qb=new Array(3).concat("e9a3c9f7f7f7a1d76a","d01c8bf1b6dab8e1864dac26","d01c8bf1b6daf7f7f7b8e1864dac26","c51b7de9a3c9fde0efe6f5d0a1d76a4d9221","c51b7de9a3c9fde0eff7f7f7e6f5d0a1d76a4d9221","c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221","c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221","8e0152c51b7dde77aef1b6dafde0efe6f5d0b8e1867fbc414d9221276419","8e0152c51b7dde77aef1b6dafde0eff7f7f7e6f5d0b8e1867fbc414d9221276419").map(Mb),Ib=Db(qb),Bb=new Array(3).concat("998ec3f7f7f7f1a340","5e3c99b2abd2fdb863e66101","5e3c99b2abd2f7f7f7fdb863e66101","542788998ec3d8daebfee0b6f1a340b35806","542788998ec3d8daebf7f7f7fee0b6f1a340b35806","5427888073acb2abd2d8daebfee0b6fdb863e08214b35806","5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b35806","2d004b5427888073acb2abd2d8daebfee0b6fdb863e08214b358067f3b08","2d004b5427888073acb2abd2d8daebf7f7f7fee0b6fdb863e08214b358067f3b08").map(Mb),Fb=Db(Bb),jb=new Array(3).concat("ef8a62f7f7f767a9cf","ca0020f4a58292c5de0571b0","ca0020f4a582f7f7f792c5de0571b0","b2182bef8a62fddbc7d1e5f067a9cf2166ac","b2182bef8a62fddbc7f7f7f7d1e5f067a9cf2166ac","b2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac","b2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac","67001fb2182bd6604df4a582fddbc7d1e5f092c5de4393c32166ac053061","67001fb2182bd6604df4a582fddbc7f7f7f7d1e5f092c5de4393c32166ac053061").map(Mb),Yb=Db(jb),Hb=new Array(3).concat("ef8a62ffffff999999","ca0020f4a582bababa404040","ca0020f4a582ffffffbababa404040","b2182bef8a62fddbc7e0e0e09999994d4d4d","b2182bef8a62fddbc7ffffffe0e0e09999994d4d4d","b2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d","b2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d","67001fb2182bd6604df4a582fddbc7e0e0e0bababa8787874d4d4d1a1a1a","67001fb2182bd6604df4a582fddbc7ffffffe0e0e0bababa8787874d4d4d1a1a1a").map(Mb),Vb=Db(Hb),Xb=new Array(3).concat("fc8d59ffffbf91bfdb","d7191cfdae61abd9e92c7bb6","d7191cfdae61ffffbfabd9e92c7bb6","d73027fc8d59fee090e0f3f891bfdb4575b4","d73027fc8d59fee090ffffbfe0f3f891bfdb4575b4","d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4","d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4","a50026d73027f46d43fdae61fee090e0f3f8abd9e974add14575b4313695","a50026d73027f46d43fdae61fee090ffffbfe0f3f8abd9e974add14575b4313695").map(Mb),Gb=Db(Xb),Wb=new Array(3).concat("fc8d59ffffbf91cf60","d7191cfdae61a6d96a1a9641","d7191cfdae61ffffbfa6d96a1a9641","d73027fc8d59fee08bd9ef8b91cf601a9850","d73027fc8d59fee08bffffbfd9ef8b91cf601a9850","d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850","d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850","a50026d73027f46d43fdae61fee08bd9ef8ba6d96a66bd631a9850006837","a50026d73027f46d43fdae61fee08bffffbfd9ef8ba6d96a66bd631a9850006837").map(Mb),$b=Db(Wb),Zb=new Array(3).concat("fc8d59ffffbf99d594","d7191cfdae61abdda42b83ba","d7191cfdae61ffffbfabdda42b83ba","d53e4ffc8d59fee08be6f59899d5943288bd","d53e4ffc8d59fee08bffffbfe6f59899d5943288bd","d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd","d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd","9e0142d53e4ff46d43fdae61fee08be6f598abdda466c2a53288bd5e4fa2","9e0142d53e4ff46d43fdae61fee08bffffbfe6f598abdda466c2a53288bd5e4fa2").map(Mb),Qb=Db(Zb),Jb=new Array(3).concat("e5f5f999d8c92ca25f","edf8fbb2e2e266c2a4238b45","edf8fbb2e2e266c2a42ca25f006d2c","edf8fbccece699d8c966c2a42ca25f006d2c","edf8fbccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45005824","f7fcfde5f5f9ccece699d8c966c2a441ae76238b45006d2c00441b").map(Mb),Kb=Db(Jb),tm=new Array(3).concat("e0ecf49ebcda8856a7","edf8fbb3cde38c96c688419d","edf8fbb3cde38c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68856a7810f7c","edf8fbbfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d6e016b","f7fcfde0ecf4bfd3e69ebcda8c96c68c6bb188419d810f7c4d004b").map(Mb),nm=Db(tm),em=new Array(3).concat("e0f3dba8ddb543a2ca","f0f9e8bae4bc7bccc42b8cbe","f0f9e8bae4bc7bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc443a2ca0868ac","f0f9e8ccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe08589e","f7fcf0e0f3dbccebc5a8ddb57bccc44eb3d32b8cbe0868ac084081").map(Mb),rm=Db(em),im=new Array(3).concat("fee8c8fdbb84e34a33","fef0d9fdcc8afc8d59d7301f","fef0d9fdcc8afc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59e34a33b30000","fef0d9fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301f990000","fff7ecfee8c8fdd49efdbb84fc8d59ef6548d7301fb300007f0000").map(Mb),om=Db(im),um=new Array(3).concat("ece2f0a6bddb1c9099","f6eff7bdc9e167a9cf02818a","f6eff7bdc9e167a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf1c9099016c59","f6eff7d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016450","fff7fbece2f0d0d1e6a6bddb67a9cf3690c002818a016c59014636").map(Mb),am=Db(um),cm=new Array(3).concat("ece7f2a6bddb2b8cbe","f1eef6bdc9e174a9cf0570b0","f1eef6bdc9e174a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf2b8cbe045a8d","f1eef6d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0034e7b","fff7fbece7f2d0d1e6a6bddb74a9cf3690c00570b0045a8d023858").map(Mb),fm=Db(cm),sm=new Array(3).concat("e7e1efc994c7dd1c77","f1eef6d7b5d8df65b0ce1256","f1eef6d7b5d8df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0dd1c77980043","f1eef6d4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125691003f","f7f4f9e7e1efd4b9dac994c7df65b0e7298ace125698004367001f").map(Mb),lm=Db(sm),hm=new Array(3).concat("fde0ddfa9fb5c51b8a","feebe2fbb4b9f768a1ae017e","feebe2fbb4b9f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1c51b8a7a0177","feebe2fcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a0177","fff7f3fde0ddfcc5c0fa9fb5f768a1dd3497ae017e7a017749006a").map(Mb),dm=Db(hm),pm=new Array(3).concat("edf8b17fcdbb2c7fb8","ffffcca1dab441b6c4225ea8","ffffcca1dab441b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c42c7fb8253494","ffffccc7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea80c2c84","ffffd9edf8b1c7e9b47fcdbb41b6c41d91c0225ea8253494081d58").map(Mb),gm=Db(pm),vm=new Array(3).concat("f7fcb9addd8e31a354","ffffccc2e69978c679238443","ffffccc2e69978c67931a354006837","ffffccd9f0a3addd8e78c67931a354006837","ffffccd9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443005a32","ffffe5f7fcb9d9f0a3addd8e78c67941ab5d238443006837004529").map(Mb),ym=Db(vm),bm=new Array(3).concat("fff7bcfec44fd95f0e","ffffd4fed98efe9929cc4c02","ffffd4fed98efe9929d95f0e993404","ffffd4fee391fec44ffe9929d95f0e993404","ffffd4fee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c028c2d04","ffffe5fff7bcfee391fec44ffe9929ec7014cc4c02993404662506").map(Mb),mm=Db(bm),_m=new Array(3).concat("ffeda0feb24cf03b20","ffffb2fecc5cfd8d3ce31a1c","ffffb2fecc5cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cf03b20bd0026","ffffb2fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cb10026","ffffccffeda0fed976feb24cfd8d3cfc4e2ae31a1cbd0026800026").map(Mb),xm=Db(_m),wm=new Array(3).concat("deebf79ecae13182bd","eff3ffbdd7e76baed62171b5","eff3ffbdd7e76baed63182bd08519c","eff3ffc6dbef9ecae16baed63182bd08519c","eff3ffc6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b5084594","f7fbffdeebf7c6dbef9ecae16baed64292c62171b508519c08306b").map(Mb),Mm=Db(wm),km=new Array(3).concat("e5f5e0a1d99b31a354","edf8e9bae4b374c476238b45","edf8e9bae4b374c47631a354006d2c","edf8e9c7e9c0a1d99b74c47631a354006d2c","edf8e9c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45005a32","f7fcf5e5f5e0c7e9c0a1d99b74c47641ab5d238b45006d2c00441b").map(Mb),Nm=Db(km),Sm=new Array(3).concat("f0f0f0bdbdbd636363","f7f7f7cccccc969696525252","f7f7f7cccccc969696636363252525","f7f7f7d9d9d9bdbdbd969696636363252525","f7f7f7d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525","fffffff0f0f0d9d9d9bdbdbd969696737373525252252525000000").map(Mb),Tm=Db(Sm),Am=new Array(3).concat("efedf5bcbddc756bb1","f2f0f7cbc9e29e9ac86a51a3","f2f0f7cbc9e29e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8756bb154278f","f2f0f7dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a34a1486","fcfbfdefedf5dadaebbcbddc9e9ac8807dba6a51a354278f3f007d").map(Mb),Cm=Db(Am),Em=new Array(3).concat("fee0d2fc9272de2d26","fee5d9fcae91fb6a4acb181d","fee5d9fcae91fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4ade2d26a50f15","fee5d9fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181d99000d","fff5f0fee0d2fcbba1fc9272fb6a4aef3b2ccb181da50f1567000d").map(Mb),Lm=Db(Em),Rm=new Array(3).concat("fee6cefdae6be6550d","feeddefdbe85fd8d3cd94701","feeddefdbe85fd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3ce6550da63603","feeddefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d948018c2d04","fff5ebfee6cefdd0a2fdae6bfd8d3cf16913d94801a636037f2704").map(Mb),Dm=Db(Rm),Pm=vr(Me(300,.5,0),Me(-240,.5,1)),zm=vr(Me(-100,.75,.35),Me(80,1.5,.8)),Om=vr(Me(260,.75,.35),Me(80,1.5,.8)),Um=Me(),qm=function(t){(t<0||t>1)&&(t-=Math.floor(t));var n=Math.abs(t-.5);return Um.h=360*t-100,Um.s=1.5-1.5*n,Um.l=.8-.9*n,Um+""},Im=Yn(),Bm=Math.PI/3,Fm=2*Math.PI/3,jm=function(t){var n;return t=(.5-t)*Math.PI,Im.r=255*(n=Math.sin(t))*n,Im.g=255*(n=Math.sin(t+Bm))*n,Im.b=255*(n=Math.sin(t+Fm))*n,Im+""};function Ym(t){var n=t.length;return function(e){return t[Math.max(0,Math.min(n-1,Math.floor(e*n)))]}}var Hm=Ym(Mb("44015444025645045745055946075a46085c460a5d460b5e470d60470e6147106347116447136548146748166848176948186a481a6c481b6d481c6e481d6f481f70482071482173482374482475482576482677482878482979472a7a472c7a472d7b472e7c472f7d46307e46327e46337f463480453581453781453882443983443a83443b84433d84433e85423f854240864241864142874144874045884046883f47883f48893e49893e4a893e4c8a3d4d8a3d4e8a3c4f8a3c508b3b518b3b528b3a538b3a548c39558c39568c38588c38598c375a8c375b8d365c8d365d8d355e8d355f8d34608d34618d33628d33638d32648e32658e31668e31678e31688e30698e306a8e2f6b8e2f6c8e2e6d8e2e6e8e2e6f8e2d708e2d718e2c718e2c728e2c738e2b748e2b758e2a768e2a778e2a788e29798e297a8e297b8e287c8e287d8e277e8e277f8e27808e26818e26828e26828e25838e25848e25858e24868e24878e23888e23898e238a8d228b8d228c8d228d8d218e8d218f8d21908d21918c20928c20928c20938c1f948c1f958b1f968b1f978b1f988b1f998a1f9a8a1e9b8a1e9c891e9d891f9e891f9f881fa0881fa1881fa1871fa28720a38620a48621a58521a68522a78522a88423a98324aa8325ab8225ac8226ad8127ad8128ae8029af7f2ab07f2cb17e2db27d2eb37c2fb47c31b57b32b67a34b67935b77937b87838b9773aba763bbb753dbc743fbc7340bd7242be7144bf7046c06f48c16e4ac16d4cc26c4ec36b50c46a52c56954c56856c66758c7655ac8645cc8635ec96260ca6063cb5f65cb5e67cc5c69cd5b6ccd5a6ece5870cf5773d05675d05477d1537ad1517cd2507fd34e81d34d84d44b86d54989d5488bd6468ed64590d74393d74195d84098d83e9bd93c9dd93ba0da39a2da37a5db36a8db34aadc32addc30b0dd2fb2dd2db5de2bb8de29bade28bddf26c0df25c2df23c5e021c8e020cae11fcde11dd0e11cd2e21bd5e21ad8e219dae319dde318dfe318e2e418e5e419e7e419eae51aece51befe51cf1e51df4e61ef6e620f8e621fbe723fde725")),Vm=Ym(Mb("00000401000501010601010802010902020b02020d03030f03031204041405041606051806051a07061c08071e0907200a08220b09240c09260d0a290e0b2b100b2d110c2f120d31130d34140e36150e38160f3b180f3d19103f1a10421c10441d11471e114920114b21114e22115024125325125527125829115a2a115c2c115f2d11612f116331116533106734106936106b38106c390f6e3b0f703d0f713f0f72400f74420f75440f764510774710784910784a10794c117a4e117b4f127b51127c52137c54137d56147d57157e59157e5a167e5c167f5d177f5f187f601880621980641a80651a80671b80681c816a1c816b1d816d1d816e1e81701f81721f817320817521817621817822817922827b23827c23827e24828025828125818326818426818627818827818928818b29818c29818e2a81902a81912b81932b80942c80962c80982d80992d809b2e7f9c2e7f9e2f7fa02f7fa1307ea3307ea5317ea6317da8327daa337dab337cad347cae347bb0357bb2357bb3367ab5367ab73779b83779ba3878bc3978bd3977bf3a77c03a76c23b75c43c75c53c74c73d73c83e73ca3e72cc3f71cd4071cf4070d0416fd2426fd3436ed5446dd6456cd8456cd9466bdb476adc4869de4968df4a68e04c67e24d66e34e65e44f64e55064e75263e85362e95462ea5661eb5760ec5860ed5a5fee5b5eef5d5ef05f5ef1605df2625df2645cf3655cf4675cf4695cf56b5cf66c5cf66e5cf7705cf7725cf8745cf8765cf9785df9795df97b5dfa7d5efa7f5efa815ffb835ffb8560fb8761fc8961fc8a62fc8c63fc8e64fc9065fd9266fd9467fd9668fd9869fd9a6afd9b6bfe9d6cfe9f6dfea16efea36ffea571fea772fea973feaa74feac76feae77feb078feb27afeb47bfeb67cfeb77efeb97ffebb81febd82febf84fec185fec287fec488fec68afec88cfeca8dfecc8ffecd90fecf92fed194fed395fed597fed799fed89afdda9cfddc9efddea0fde0a1fde2a3fde3a5fde5a7fde7a9fde9aafdebacfcecaefceeb0fcf0b2fcf2b4fcf4b6fcf6b8fcf7b9fcf9bbfcfbbdfcfdbf")),Xm=Ym(Mb("00000401000501010601010802010a02020c02020e03021004031204031405041706041907051b08051d09061f0a07220b07240c08260d08290e092b10092d110a30120a32140b34150b37160b39180c3c190c3e1b0c411c0c431e0c451f0c48210c4a230c4c240c4f260c51280b53290b552b0b572d0b592f0a5b310a5c320a5e340a5f3609613809623909633b09643d09653e0966400a67420a68440a68450a69470b6a490b6a4a0c6b4c0c6b4d0d6c4f0d6c510e6c520e6d540f6d550f6d57106e59106e5a116e5c126e5d126e5f136e61136e62146e64156e65156e67166e69166e6a176e6c186e6d186e6f196e71196e721a6e741a6e751b6e771c6d781c6d7a1d6d7c1d6d7d1e6d7f1e6c801f6c82206c84206b85216b87216b88226a8a226a8c23698d23698f24699025689225689326679526679727669827669a28659b29649d29649f2a63a02a63a22b62a32c61a52c60a62d60a82e5fa92e5eab2f5ead305dae305cb0315bb1325ab3325ab43359b63458b73557b93556ba3655bc3754bd3853bf3952c03a51c13a50c33b4fc43c4ec63d4dc73e4cc83f4bca404acb4149cc4248ce4347cf4446d04545d24644d34743d44842d54a41d74b3fd84c3ed94d3dda4e3cdb503bdd513ade5238df5337e05536e15635e25734e35933e45a31e55c30e65d2fe75e2ee8602de9612bea632aeb6429eb6628ec6726ed6925ee6a24ef6c23ef6e21f06f20f1711ff1731df2741cf3761bf37819f47918f57b17f57d15f67e14f68013f78212f78410f8850ff8870ef8890cf98b0bf98c0af98e09fa9008fa9207fa9407fb9606fb9706fb9906fb9b06fb9d07fc9f07fca108fca309fca50afca60cfca80dfcaa0ffcac11fcae12fcb014fcb216fcb418fbb61afbb81dfbba1ffbbc21fbbe23fac026fac228fac42afac62df9c72ff9c932f9cb35f8cd37f8cf3af7d13df7d340f6d543f6d746f5d949f5db4cf4dd4ff4df53f4e156f3e35af3e55df2e661f2e865f2ea69f1ec6df1ed71f1ef75f1f179f2f27df2f482f3f586f3f68af4f88ef5f992f6fa96f8fb9af9fc9dfafda1fcffa4")),Gm=Ym(Mb("0d088710078813078916078a19068c1b068d1d068e20068f2206902406912605912805922a05932c05942e05952f059631059733059735049837049938049a3a049a3c049b3e049c3f049c41049d43039e44039e46039f48039f4903a04b03a14c02a14e02a25002a25102a35302a35502a45601a45801a45901a55b01a55c01a65e01a66001a66100a76300a76400a76600a76700a86900a86a00a86c00a86e00a86f00a87100a87201a87401a87501a87701a87801a87a02a87b02a87d03a87e03a88004a88104a78305a78405a78606a68707a68808a68a09a58b0aa58d0ba58e0ca48f0da4910ea3920fa39410a29511a19613a19814a099159f9a169f9c179e9d189d9e199da01a9ca11b9ba21d9aa31e9aa51f99a62098a72197a82296aa2395ab2494ac2694ad2793ae2892b02991b12a90b22b8fb32c8eb42e8db52f8cb6308bb7318ab83289ba3388bb3488bc3587bd3786be3885bf3984c03a83c13b82c23c81c33d80c43e7fc5407ec6417dc7427cc8437bc9447aca457acb4679cc4778cc4977cd4a76ce4b75cf4c74d04d73d14e72d24f71d35171d45270d5536fd5546ed6556dd7566cd8576bd9586ada5a6ada5b69db5c68dc5d67dd5e66de5f65de6164df6263e06363e16462e26561e26660e3685fe4695ee56a5de56b5de66c5ce76e5be76f5ae87059e97158e97257ea7457eb7556eb7655ec7754ed7953ed7a52ee7b51ef7c51ef7e50f07f4ff0804ef1814df1834cf2844bf3854bf3874af48849f48948f58b47f58c46f68d45f68f44f79044f79143f79342f89441f89540f9973ff9983ef99a3efa9b3dfa9c3cfa9e3bfb9f3afba139fba238fca338fca537fca636fca835fca934fdab33fdac33fdae32fdaf31fdb130fdb22ffdb42ffdb52efeb72dfeb82cfeba2cfebb2bfebd2afebe2afec029fdc229fdc328fdc527fdc627fdc827fdca26fdcb26fccd25fcce25fcd025fcd225fbd324fbd524fbd724fad824fada24f9dc24f9dd25f8df25f8e125f7e225f7e425f6e626f6e826f5e926f5eb27f4ed27f3ee27f3f027f2f227f1f426f1f525f0f724f0f921")),Wm=function(t){return function(){return t}},$m=Math.abs,Zm=Math.atan2,Qm=Math.cos,Jm=Math.max,Km=Math.min,t_=Math.sin,n_=Math.sqrt,e_=1e-12,r_=Math.PI,i_=r_/2,o_=2*r_;function u_(t){return t>=1?i_:t<=-1?-i_:Math.asin(t)}function a_(t){return t.innerRadius}function c_(t){return t.outerRadius}function f_(t){return t.startAngle}function s_(t){return t.endAngle}function l_(t){return t&&t.padAngle}function h_(t,n,e,r,i,o,u){var a=t-e,c=n-r,f=(u?o:-o)/n_(a*a+c*c),s=f*c,l=-f*a,h=t+s,d=n+l,p=e+s,g=r+l,v=(h+p)/2,y=(d+g)/2,b=p-h,m=g-d,_=b*b+m*m,x=i-o,w=h*g-p*d,M=(m<0?-1:1)*n_(Jm(0,x*x*_-w*w)),k=(w*m-b*M)/_,N=(-w*b-m*M)/_,S=(w*m+b*M)/_,T=(-w*b+m*M)/_,A=k-v,C=N-y,E=S-v,L=T-y;return A*A+C*C>E*E+L*L&&(k=S,N=T),{cx:k,cy:N,x01:-s,y01:-l,x11:k*(i/x-1),y11:N*(i/x-1)}}var d_=function(){var t=a_,n=c_,e=Wm(0),r=null,i=f_,o=s_,u=l_,a=null;function c(){var c,f,s,l=+t.apply(this,arguments),h=+n.apply(this,arguments),d=i.apply(this,arguments)-i_,p=o.apply(this,arguments)-i_,g=$m(p-d),v=p>d;if(a||(a=c=Fo()),h<l&&(f=h,h=l,l=f),h>e_)if(g>o_-e_)a.moveTo(h*Qm(d),h*t_(d)),a.arc(0,0,h,d,p,!v),l>e_&&(a.moveTo(l*Qm(p),l*t_(p)),a.arc(0,0,l,p,d,v));else{var y,b,m=d,_=p,x=d,w=p,M=g,k=g,N=u.apply(this,arguments)/2,S=N>e_&&(r?+r.apply(this,arguments):n_(l*l+h*h)),T=Km($m(h-l)/2,+e.apply(this,arguments)),A=T,C=T;if(S>e_){var E=u_(S/l*t_(N)),L=u_(S/h*t_(N));(M-=2*E)>e_?(x+=E*=v?1:-1,w-=E):(M=0,x=w=(d+p)/2),(k-=2*L)>e_?(m+=L*=v?1:-1,_-=L):(k=0,m=_=(d+p)/2)}var R=h*Qm(m),D=h*t_(m),P=l*Qm(w),z=l*t_(w);if(T>e_){var O,U=h*Qm(_),q=h*t_(_),I=l*Qm(x),B=l*t_(x);if(g<r_&&(O=function(t,n,e,r,i,o,u,a){var c=e-t,f=r-n,s=u-i,l=a-o,h=l*c-s*f;if(!(h*h<e_))return[t+(h=(s*(n-o)-l*(t-i))/h)*c,n+h*f]}(R,D,I,B,U,q,P,z))){var F=R-O[0],j=D-O[1],Y=U-O[0],H=q-O[1],V=1/t_(((s=(F*Y+j*H)/(n_(F*F+j*j)*n_(Y*Y+H*H)))>1?0:s<-1?r_:Math.acos(s))/2),X=n_(O[0]*O[0]+O[1]*O[1]);A=Km(T,(l-X)/(V-1)),C=Km(T,(h-X)/(V+1))}}k>e_?C>e_?(y=h_(I,B,R,D,h,C,v),b=h_(U,q,P,z,h,C,v),a.moveTo(y.cx+y.x01,y.cy+y.y01),C<T?a.arc(y.cx,y.cy,C,Zm(y.y01,y.x01),Zm(b.y01,b.x01),!v):(a.arc(y.cx,y.cy,C,Zm(y.y01,y.x01),Zm(y.y11,y.x11),!v),a.arc(0,0,h,Zm(y.cy+y.y11,y.cx+y.x11),Zm(b.cy+b.y11,b.cx+b.x11),!v),a.arc(b.cx,b.cy,C,Zm(b.y11,b.x11),Zm(b.y01,b.x01),!v))):(a.moveTo(R,D),a.arc(0,0,h,m,_,!v)):a.moveTo(R,D),l>e_&&M>e_?A>e_?(y=h_(P,z,U,q,l,-A,v),b=h_(R,D,I,B,l,-A,v),a.lineTo(y.cx+y.x01,y.cy+y.y01),A<T?a.arc(y.cx,y.cy,A,Zm(y.y01,y.x01),Zm(b.y01,b.x01),!v):(a.arc(y.cx,y.cy,A,Zm(y.y01,y.x01),Zm(y.y11,y.x11),!v),a.arc(0,0,l,Zm(y.cy+y.y11,y.cx+y.x11),Zm(b.cy+b.y11,b.cx+b.x11),v),a.arc(b.cx,b.cy,A,Zm(b.y11,b.x11),Zm(b.y01,b.x01),!v))):a.arc(0,0,l,w,x,v):a.lineTo(P,z)}else a.moveTo(0,0);if(a.closePath(),c)return a=null,c+""||null}return c.centroid=function(){var e=(+t.apply(this,arguments)+ +n.apply(this,arguments))/2,r=(+i.apply(this,arguments)+ +o.apply(this,arguments))/2-r_/2;return[Qm(r)*e,t_(r)*e]},c.innerRadius=function(n){return arguments.length?(t="function"==typeof n?n:Wm(+n),c):t},c.outerRadius=function(t){return arguments.length?(n="function"==typeof t?t:Wm(+t),c):n},c.cornerRadius=function(t){return arguments.length?(e="function"==typeof t?t:Wm(+t),c):e},c.padRadius=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:Wm(+t),c):r},c.startAngle=function(t){return arguments.length?(i="function"==typeof t?t:Wm(+t),c):i},c.endAngle=function(t){return arguments.length?(o="function"==typeof t?t:Wm(+t),c):o},c.padAngle=function(t){return arguments.length?(u="function"==typeof t?t:Wm(+t),c):u},c.context=function(t){return arguments.length?(a=null==t?null:t,c):a},c};function p_(t){this._context=t}p_.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:this._context.lineTo(t,n)}}};var g_=function(t){return new p_(t)};function v_(t){return t[0]}function y_(t){return t[1]}var b_=function(){var t=v_,n=y_,e=Wm(!0),r=null,i=g_,o=null;function u(u){var a,c,f,s=u.length,l=!1;for(null==r&&(o=i(f=Fo())),a=0;a<=s;++a)!(a<s&&e(c=u[a],a,u))===l&&((l=!l)?o.lineStart():o.lineEnd()),l&&o.point(+t(c,a,u),+n(c,a,u));if(f)return o=null,f+""||null}return u.x=function(n){return arguments.length?(t="function"==typeof n?n:Wm(+n),u):t},u.y=function(t){return arguments.length?(n="function"==typeof t?t:Wm(+t),u):n},u.defined=function(t){return arguments.length?(e="function"==typeof t?t:Wm(!!t),u):e},u.curve=function(t){return arguments.length?(i=t,null!=r&&(o=i(r)),u):i},u.context=function(t){return arguments.length?(null==t?r=o=null:o=i(r=t),u):r},u},m_=function(){var t=v_,n=null,e=Wm(0),r=y_,i=Wm(!0),o=null,u=g_,a=null;function c(c){var f,s,l,h,d,p=c.length,g=!1,v=new Array(p),y=new Array(p);for(null==o&&(a=u(d=Fo())),f=0;f<=p;++f){if(!(f<p&&i(h=c[f],f,c))===g)if(g=!g)s=f,a.areaStart(),a.lineStart();else{for(a.lineEnd(),a.lineStart(),l=f-1;l>=s;--l)a.point(v[l],y[l]);a.lineEnd(),a.areaEnd()}g&&(v[f]=+t(h,f,c),y[f]=+e(h,f,c),a.point(n?+n(h,f,c):v[f],r?+r(h,f,c):y[f]))}if(d)return a=null,d+""||null}function f(){return b_().defined(i).curve(u).context(o)}return c.x=function(e){return arguments.length?(t="function"==typeof e?e:Wm(+e),n=null,c):t},c.x0=function(n){return arguments.length?(t="function"==typeof n?n:Wm(+n),c):t},c.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:Wm(+t),c):n},c.y=function(t){return arguments.length?(e="function"==typeof t?t:Wm(+t),r=null,c):e},c.y0=function(t){return arguments.length?(e="function"==typeof t?t:Wm(+t),c):e},c.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:Wm(+t),c):r},c.lineX0=c.lineY0=function(){return f().x(t).y(e)},c.lineY1=function(){return f().x(t).y(r)},c.lineX1=function(){return f().x(n).y(e)},c.defined=function(t){return arguments.length?(i="function"==typeof t?t:Wm(!!t),c):i},c.curve=function(t){return arguments.length?(u=t,null!=o&&(a=u(o)),c):u},c.context=function(t){return arguments.length?(null==t?o=a=null:a=u(o=t),c):o},c},__=function(t,n){return n<t?-1:n>t?1:n>=t?0:NaN},x_=function(t){return t},w_=function(){var t=x_,n=__,e=null,r=Wm(0),i=Wm(o_),o=Wm(0);function u(u){var a,c,f,s,l,h=u.length,d=0,p=new Array(h),g=new Array(h),v=+r.apply(this,arguments),y=Math.min(o_,Math.max(-o_,i.apply(this,arguments)-v)),b=Math.min(Math.abs(y)/h,o.apply(this,arguments)),m=b*(y<0?-1:1);for(a=0;a<h;++a)(l=g[p[a]=a]=+t(u[a],a,u))>0&&(d+=l);for(null!=n?p.sort(function(t,e){return n(g[t],g[e])}):null!=e&&p.sort(function(t,n){return e(u[t],u[n])}),a=0,f=d?(y-h*m)/d:0;a<h;++a,v=s)c=p[a],s=v+((l=g[c])>0?l*f:0)+m,g[c]={data:u[c],index:a,value:l,startAngle:v,endAngle:s,padAngle:b};return g}return u.value=function(n){return arguments.length?(t="function"==typeof n?n:Wm(+n),u):t},u.sortValues=function(t){return arguments.length?(n=t,e=null,u):n},u.sort=function(t){return arguments.length?(e=t,n=null,u):e},u.startAngle=function(t){return arguments.length?(r="function"==typeof t?t:Wm(+t),u):r},u.endAngle=function(t){return arguments.length?(i="function"==typeof t?t:Wm(+t),u):i},u.padAngle=function(t){return arguments.length?(o="function"==typeof t?t:Wm(+t),u):o},u},M_=N_(g_);function k_(t){this._curve=t}function N_(t){function n(n){return new k_(t(n))}return n._curve=t,n}function S_(t){var n=t.curve;return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t.curve=function(t){return arguments.length?n(N_(t)):n()._curve},t}k_.prototype={areaStart:function(){this._curve.areaStart()},areaEnd:function(){this._curve.areaEnd()},lineStart:function(){this._curve.lineStart()},lineEnd:function(){this._curve.lineEnd()},point:function(t,n){this._curve.point(n*Math.sin(t),n*-Math.cos(t))}};var T_=function(){return S_(b_().curve(M_))},A_=function(){var t=m_().curve(M_),n=t.curve,e=t.lineX0,r=t.lineX1,i=t.lineY0,o=t.lineY1;return t.angle=t.x,delete t.x,t.startAngle=t.x0,delete t.x0,t.endAngle=t.x1,delete t.x1,t.radius=t.y,delete t.y,t.innerRadius=t.y0,delete t.y0,t.outerRadius=t.y1,delete t.y1,t.lineStartAngle=function(){return S_(e())},delete t.lineX0,t.lineEndAngle=function(){return S_(r())},delete t.lineX1,t.lineInnerRadius=function(){return S_(i())},delete t.lineY0,t.lineOuterRadius=function(){return S_(o())},delete t.lineY1,t.curve=function(t){return arguments.length?n(N_(t)):n()._curve},t},C_=function(t,n){return[(n=+n)*Math.cos(t-=Math.PI/2),n*Math.sin(t)]},E_=Array.prototype.slice;function L_(t){return t.source}function R_(t){return t.target}function D_(t){var n=L_,e=R_,r=v_,i=y_,o=null;function u(){var u,a=E_.call(arguments),c=n.apply(this,a),f=e.apply(this,a);if(o||(o=u=Fo()),t(o,+r.apply(this,(a[0]=c,a)),+i.apply(this,a),+r.apply(this,(a[0]=f,a)),+i.apply(this,a)),u)return o=null,u+""||null}return u.source=function(t){return arguments.length?(n=t,u):n},u.target=function(t){return arguments.length?(e=t,u):e},u.x=function(t){return arguments.length?(r="function"==typeof t?t:Wm(+t),u):r},u.y=function(t){return arguments.length?(i="function"==typeof t?t:Wm(+t),u):i},u.context=function(t){return arguments.length?(o=null==t?null:t,u):o},u}function P_(t,n,e,r,i){t.moveTo(n,e),t.bezierCurveTo(n=(n+r)/2,e,n,i,r,i)}function z_(t,n,e,r,i){t.moveTo(n,e),t.bezierCurveTo(n,e=(e+i)/2,r,e,r,i)}function O_(t,n,e,r,i){var o=C_(n,e),u=C_(n,e=(e+i)/2),a=C_(r,e),c=C_(r,i);t.moveTo(o[0],o[1]),t.bezierCurveTo(u[0],u[1],a[0],a[1],c[0],c[1])}function U_(){return D_(P_)}function q_(){return D_(z_)}function I_(){var t=D_(O_);return t.angle=t.x,delete t.x,t.radius=t.y,delete t.y,t}var B_={draw:function(t,n){var e=Math.sqrt(n/r_);t.moveTo(e,0),t.arc(0,0,e,0,o_)}},F_={draw:function(t,n){var e=Math.sqrt(n/5)/2;t.moveTo(-3*e,-e),t.lineTo(-e,-e),t.lineTo(-e,-3*e),t.lineTo(e,-3*e),t.lineTo(e,-e),t.lineTo(3*e,-e),t.lineTo(3*e,e),t.lineTo(e,e),t.lineTo(e,3*e),t.lineTo(-e,3*e),t.lineTo(-e,e),t.lineTo(-3*e,e),t.closePath()}},j_=Math.sqrt(1/3),Y_=2*j_,H_={draw:function(t,n){var e=Math.sqrt(n/Y_),r=e*j_;t.moveTo(0,-e),t.lineTo(r,0),t.lineTo(0,e),t.lineTo(-r,0),t.closePath()}},V_=Math.sin(r_/10)/Math.sin(7*r_/10),X_=Math.sin(o_/10)*V_,G_=-Math.cos(o_/10)*V_,W_={draw:function(t,n){var e=Math.sqrt(.8908130915292852*n),r=X_*e,i=G_*e;t.moveTo(0,-e),t.lineTo(r,i);for(var o=1;o<5;++o){var u=o_*o/5,a=Math.cos(u),c=Math.sin(u);t.lineTo(c*e,-a*e),t.lineTo(a*r-c*i,c*r+a*i)}t.closePath()}},$_={draw:function(t,n){var e=Math.sqrt(n),r=-e/2;t.rect(r,r,e,e)}},Z_=Math.sqrt(3),Q_={draw:function(t,n){var e=-Math.sqrt(n/(3*Z_));t.moveTo(0,2*e),t.lineTo(-Z_*e,-e),t.lineTo(Z_*e,-e),t.closePath()}},J_=Math.sqrt(3)/2,K_=1/Math.sqrt(12),tx=3*(K_/2+1),nx={draw:function(t,n){var e=Math.sqrt(n/tx),r=e/2,i=e*K_,o=r,u=e*K_+e,a=-o,c=u;t.moveTo(r,i),t.lineTo(o,u),t.lineTo(a,c),t.lineTo(-.5*r-J_*i,J_*r+-.5*i),t.lineTo(-.5*o-J_*u,J_*o+-.5*u),t.lineTo(-.5*a-J_*c,J_*a+-.5*c),t.lineTo(-.5*r+J_*i,-.5*i-J_*r),t.lineTo(-.5*o+J_*u,-.5*u-J_*o),t.lineTo(-.5*a+J_*c,-.5*c-J_*a),t.closePath()}},ex=[B_,F_,H_,$_,W_,Q_,nx],rx=function(){var t=Wm(B_),n=Wm(64),e=null;function r(){var r;if(e||(e=r=Fo()),t.apply(this,arguments).draw(e,+n.apply(this,arguments)),r)return e=null,r+""||null}return r.type=function(n){return arguments.length?(t="function"==typeof n?n:Wm(n),r):t},r.size=function(t){return arguments.length?(n="function"==typeof t?t:Wm(+t),r):n},r.context=function(t){return arguments.length?(e=null==t?null:t,r):e},r},ix=function(){};function ox(t,n,e){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+n)/6,(t._y0+4*t._y1+e)/6)}function ux(t){this._context=t}ux.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:ox(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:ox(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}};var ax=function(t){return new ux(t)};function cx(t){this._context=t}cx.prototype={areaStart:ix,areaEnd:ix,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x2=t,this._y2=n;break;case 1:this._point=2,this._x3=t,this._y3=n;break;case 2:this._point=3,this._x4=t,this._y4=n,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+n)/6);break;default:ox(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}};var fx=function(t){return new cx(t)};function sx(t){this._context=t}sx.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var e=(this._x0+4*this._x1+t)/6,r=(this._y0+4*this._y1+n)/6;this._line?this._context.lineTo(e,r):this._context.moveTo(e,r);break;case 3:this._point=4;default:ox(this,t,n)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n}};var lx=function(t){return new sx(t)};function hx(t,n){this._basis=new ux(t),this._beta=n}hx.prototype={lineStart:function(){this._x=[],this._y=[],this._basis.lineStart()},lineEnd:function(){var t=this._x,n=this._y,e=t.length-1;if(e>0)for(var r,i=t[0],o=n[0],u=t[e]-i,a=n[e]-o,c=-1;++c<=e;)r=c/e,this._basis.point(this._beta*t[c]+(1-this._beta)*(i+r*u),this._beta*n[c]+(1-this._beta)*(o+r*a));this._x=this._y=null,this._basis.lineEnd()},point:function(t,n){this._x.push(+t),this._y.push(+n)}};var dx=function t(n){function e(t){return 1===n?new ux(t):new hx(t,n)}return e.beta=function(n){return t(+n)},e}(.85);function px(t,n,e){t._context.bezierCurveTo(t._x1+t._k*(t._x2-t._x0),t._y1+t._k*(t._y2-t._y0),t._x2+t._k*(t._x1-n),t._y2+t._k*(t._y1-e),t._x2,t._y2)}function gx(t,n){this._context=t,this._k=(1-n)/6}gx.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:px(this,this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2,this._x1=t,this._y1=n;break;case 2:this._point=3;default:px(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var vx=function t(n){function e(t){return new gx(t,n)}return e.tension=function(n){return t(+n)},e}(0);function yx(t,n){this._context=t,this._k=(1-n)/6}yx.prototype={areaStart:ix,areaEnd:ix,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:px(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var bx=function t(n){function e(t){return new yx(t,n)}return e.tension=function(n){return t(+n)},e}(0);function mx(t,n){this._context=t,this._k=(1-n)/6}mx.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:px(this,t,n)}this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var _x=function t(n){function e(t){return new mx(t,n)}return e.tension=function(n){return t(+n)},e}(0);function xx(t,n,e){var r=t._x1,i=t._y1,o=t._x2,u=t._y2;if(t._l01_a>e_){var a=2*t._l01_2a+3*t._l01_a*t._l12_a+t._l12_2a,c=3*t._l01_a*(t._l01_a+t._l12_a);r=(r*a-t._x0*t._l12_2a+t._x2*t._l01_2a)/c,i=(i*a-t._y0*t._l12_2a+t._y2*t._l01_2a)/c}if(t._l23_a>e_){var f=2*t._l23_2a+3*t._l23_a*t._l12_a+t._l12_2a,s=3*t._l23_a*(t._l23_a+t._l12_a);o=(o*f+t._x1*t._l23_2a-n*t._l12_2a)/s,u=(u*f+t._y1*t._l23_2a-e*t._l12_2a)/s}t._context.bezierCurveTo(r,i,o,u,t._x2,t._y2)}function wx(t,n){this._context=t,this._alpha=n}wx.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x2,this._y2);break;case 3:this.point(this._x2,this._y2)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3;default:xx(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Mx=function t(n){function e(t){return n?new wx(t,n):new gx(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function kx(t,n){this._context=t,this._alpha=n}kx.prototype={areaStart:ix,areaEnd:ix,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._x5=this._y0=this._y1=this._y2=this._y3=this._y4=this._y5=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x3,this._y3),this._context.closePath();break;case 2:this._context.lineTo(this._x3,this._y3),this._context.closePath();break;case 3:this.point(this._x3,this._y3),this.point(this._x4,this._y4),this.point(this._x5,this._y5)}},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1,this._x3=t,this._y3=n;break;case 1:this._point=2,this._context.moveTo(this._x4=t,this._y4=n);break;case 2:this._point=3,this._x5=t,this._y5=n;break;default:xx(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Nx=function t(n){function e(t){return n?new kx(t,n):new yx(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function Sx(t,n){this._context=t,this._alpha=n}Sx.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._x2=this._y0=this._y1=this._y2=NaN,this._l01_a=this._l12_a=this._l23_a=this._l01_2a=this._l12_2a=this._l23_2a=this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){if(t=+t,n=+n,this._point){var e=this._x2-t,r=this._y2-n;this._l23_a=Math.sqrt(this._l23_2a=Math.pow(e*e+r*r,this._alpha))}switch(this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3,this._line?this._context.lineTo(this._x2,this._y2):this._context.moveTo(this._x2,this._y2);break;case 3:this._point=4;default:xx(this,t,n)}this._l01_a=this._l12_a,this._l12_a=this._l23_a,this._l01_2a=this._l12_2a,this._l12_2a=this._l23_2a,this._x0=this._x1,this._x1=this._x2,this._x2=t,this._y0=this._y1,this._y1=this._y2,this._y2=n}};var Tx=function t(n){function e(t){return n?new Sx(t,n):new mx(t,0)}return e.alpha=function(n){return t(+n)},e}(.5);function Ax(t){this._context=t}Ax.prototype={areaStart:ix,areaEnd:ix,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,n){t=+t,n=+n,this._point?this._context.lineTo(t,n):(this._point=1,this._context.moveTo(t,n))}};var Cx=function(t){return new Ax(t)};function Ex(t){return t<0?-1:1}function Lx(t,n,e){var r=t._x1-t._x0,i=n-t._x1,o=(t._y1-t._y0)/(r||i<0&&-0),u=(e-t._y1)/(i||r<0&&-0),a=(o*i+u*r)/(r+i);return(Ex(o)+Ex(u))*Math.min(Math.abs(o),Math.abs(u),.5*Math.abs(a))||0}function Rx(t,n){var e=t._x1-t._x0;return e?(3*(t._y1-t._y0)/e-n)/2:n}function Dx(t,n,e){var r=t._x0,i=t._y0,o=t._x1,u=t._y1,a=(o-r)/3;t._context.bezierCurveTo(r+a,i+a*n,o-a,u-a*e,o,u)}function Px(t){this._context=t}function zx(t){this._context=new Ox(t)}function Ox(t){this._context=t}function Ux(t){return new Px(t)}function qx(t){return new zx(t)}function Ix(t){this._context=t}function Bx(t){var n,e,r=t.length-1,i=new Array(r),o=new Array(r),u=new Array(r);for(i[0]=0,o[0]=2,u[0]=t[0]+2*t[1],n=1;n<r-1;++n)i[n]=1,o[n]=4,u[n]=4*t[n]+2*t[n+1];for(i[r-1]=2,o[r-1]=7,u[r-1]=8*t[r-1]+t[r],n=1;n<r;++n)e=i[n]/o[n-1],o[n]-=e,u[n]-=e*u[n-1];for(i[r-1]=u[r-1]/o[r-1],n=r-2;n>=0;--n)i[n]=(u[n]-i[n+1])/o[n];for(o[r-1]=(t[r]+i[r-1])/2,n=0;n<r-1;++n)o[n]=2*t[n+1]-i[n+1];return[i,o]}Px.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:Dx(this,this._t0,Rx(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,n){var e=NaN;if(n=+n,(t=+t)!==this._x1||n!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;break;case 2:this._point=3,Dx(this,Rx(this,e=Lx(this,t,n)),e);break;default:Dx(this,this._t0,e=Lx(this,t,n))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=n,this._t0=e}}},(zx.prototype=Object.create(Px.prototype)).point=function(t,n){Px.prototype.point.call(this,n,t)},Ox.prototype={moveTo:function(t,n){this._context.moveTo(n,t)},closePath:function(){this._context.closePath()},lineTo:function(t,n){this._context.lineTo(n,t)},bezierCurveTo:function(t,n,e,r,i,o){this._context.bezierCurveTo(n,t,r,e,o,i)}},Ix.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,n=this._y,e=t.length;if(e)if(this._line?this._context.lineTo(t[0],n[0]):this._context.moveTo(t[0],n[0]),2===e)this._context.lineTo(t[1],n[1]);else for(var r=Bx(t),i=Bx(n),o=0,u=1;u<e;++o,++u)this._context.bezierCurveTo(r[0][o],i[0][o],r[1][o],i[1][o],t[u],n[u]);(this._line||0!==this._line&&1===e)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,n){this._x.push(+t),this._y.push(+n)}};var Fx=function(t){return new Ix(t)};function jx(t,n){this._context=t,this._t=n}jx.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,n){switch(t=+t,n=+n,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,n):this._context.moveTo(t,n);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,n),this._context.lineTo(t,n);else{var e=this._x*(1-this._t)+t*this._t;this._context.lineTo(e,this._y),this._context.lineTo(e,n)}}this._x=t,this._y=n}};var Yx=function(t){return new jx(t,.5)};function Hx(t){return new jx(t,0)}function Vx(t){return new jx(t,1)}var Xx=function(t,n){if((i=t.length)>1)for(var e,r,i,o=1,u=t[n[0]],a=u.length;o<i;++o)for(r=u,u=t[n[o]],e=0;e<a;++e)u[e][1]+=u[e][0]=isNaN(r[e][1])?r[e][0]:r[e][1]},Gx=function(t){for(var n=t.length,e=new Array(n);--n>=0;)e[n]=n;return e};function Wx(t,n){return t[n]}var $x=function(){var t=Wm([]),n=Gx,e=Xx,r=Wx;function i(i){var o,u,a=t.apply(this,arguments),c=i.length,f=a.length,s=new Array(f);for(o=0;o<f;++o){for(var l,h=a[o],d=s[o]=new Array(c),p=0;p<c;++p)d[p]=l=[0,+r(i[p],h,p,i)],l.data=i[p];d.key=h}for(o=0,u=n(s);o<f;++o)s[u[o]].index=o;return e(s,u),s}return i.keys=function(n){return arguments.length?(t="function"==typeof n?n:Wm(E_.call(n)),i):t},i.value=function(t){return arguments.length?(r="function"==typeof t?t:Wm(+t),i):r},i.order=function(t){return arguments.length?(n=null==t?Gx:"function"==typeof t?t:Wm(E_.call(t)),i):n},i.offset=function(t){return arguments.length?(e=null==t?Xx:t,i):e},i},Zx=function(t,n){if((r=t.length)>0){for(var e,r,i,o=0,u=t[0].length;o<u;++o){for(i=e=0;e<r;++e)i+=t[e][o][1]||0;if(i)for(e=0;e<r;++e)t[e][o][1]/=i}Xx(t,n)}},Qx=function(t,n){if((a=t.length)>0)for(var e,r,i,o,u,a,c=0,f=t[n[0]].length;c<f;++c)for(o=u=0,e=0;e<a;++e)(i=(r=t[n[e]][c])[1]-r[0])>=0?(r[0]=o,r[1]=o+=i):i<0?(r[1]=u,r[0]=u+=i):r[0]=o},Jx=function(t,n){if((e=t.length)>0){for(var e,r=0,i=t[n[0]],o=i.length;r<o;++r){for(var u=0,a=0;u<e;++u)a+=t[u][r][1]||0;i[r][1]+=i[r][0]=-a/2}Xx(t,n)}},Kx=function(t,n){if((i=t.length)>0&&(r=(e=t[n[0]]).length)>0){for(var e,r,i,o=0,u=1;u<r;++u){for(var a=0,c=0,f=0;a<i;++a){for(var s=t[n[a]],l=s[u][1]||0,h=(l-(s[u-1][1]||0))/2,d=0;d<a;++d){var p=t[n[d]];h+=(p[u][1]||0)-(p[u-1][1]||0)}c+=l,f+=h*l}e[u-1][1]+=e[u-1][0]=o,c&&(o-=f/c)}e[u-1][1]+=e[u-1][0]=o,Xx(t,n)}},tw=function(t){var n=t.map(nw);return Gx(t).sort(function(t,e){return n[t]-n[e]})};function nw(t){for(var n,e=-1,r=0,i=t.length,o=-1/0;++e<i;)(n=+t[e][1])>o&&(o=n,r=e);return r}var ew=function(t){var n=t.map(rw);return Gx(t).sort(function(t,e){return n[t]-n[e]})};function rw(t){for(var n,e=0,r=-1,i=t.length;++r<i;)(n=+t[r][1])&&(e+=n);return e}var iw=function(t){return ew(t).reverse()},ow=function(t){var n,e,r=t.length,i=t.map(rw),o=tw(t),u=0,a=0,c=[],f=[];for(n=0;n<r;++n)e=o[n],u<a?(u+=i[e],c.push(e)):(a+=i[e],f.push(e));return f.reverse().concat(c)},uw=function(t){return Gx(t).reverse()},aw=function(t){return function(){return t}};function cw(t){return t[0]}function fw(t){return t[1]}function sw(){this._=null}function lw(t){t.U=t.C=t.L=t.R=t.P=t.N=null}function hw(t,n){var e=n,r=n.R,i=e.U;i?i.L===e?i.L=r:i.R=r:t._=r,r.U=i,e.U=r,e.R=r.L,e.R&&(e.R.U=e),r.L=e}function dw(t,n){var e=n,r=n.L,i=e.U;i?i.L===e?i.L=r:i.R=r:t._=r,r.U=i,e.U=r,e.L=r.R,e.L&&(e.L.U=e),r.R=e}function pw(t){for(;t.L;)t=t.L;return t}sw.prototype={constructor:sw,insert:function(t,n){var e,r,i;if(t){if(n.P=t,n.N=t.N,t.N&&(t.N.P=n),t.N=n,t.R){for(t=t.R;t.L;)t=t.L;t.L=n}else t.R=n;e=t}else this._?(t=pw(this._),n.P=null,n.N=t,t.P=t.L=n,e=t):(n.P=n.N=null,this._=n,e=null);for(n.L=n.R=null,n.U=e,n.C=!0,t=n;e&&e.C;)e===(r=e.U).L?(i=r.R)&&i.C?(e.C=i.C=!1,r.C=!0,t=r):(t===e.R&&(hw(this,e),e=(t=e).U),e.C=!1,r.C=!0,dw(this,r)):(i=r.L)&&i.C?(e.C=i.C=!1,r.C=!0,t=r):(t===e.L&&(dw(this,e),e=(t=e).U),e.C=!1,r.C=!0,hw(this,r)),e=t.U;this._.C=!1},remove:function(t){t.N&&(t.N.P=t.P),t.P&&(t.P.N=t.N),t.N=t.P=null;var n,e,r,i=t.U,o=t.L,u=t.R;if(e=o?u?pw(u):o:u,i?i.L===t?i.L=e:i.R=e:this._=e,o&&u?(r=e.C,e.C=t.C,e.L=o,o.U=e,e!==u?(i=e.U,e.U=t.U,t=e.R,i.L=t,e.R=u,u.U=e):(e.U=i,i=e,t=e.R)):(r=t.C,t=e),t&&(t.U=i),!r)if(t&&t.C)t.C=!1;else{do{if(t===this._)break;if(t===i.L){if((n=i.R).C&&(n.C=!1,i.C=!0,hw(this,i),n=i.R),n.L&&n.L.C||n.R&&n.R.C){n.R&&n.R.C||(n.L.C=!1,n.C=!0,dw(this,n),n=i.R),n.C=i.C,i.C=n.R.C=!1,hw(this,i),t=this._;break}}else if((n=i.L).C&&(n.C=!1,i.C=!0,dw(this,i),n=i.L),n.L&&n.L.C||n.R&&n.R.C){n.L&&n.L.C||(n.R.C=!1,n.C=!0,hw(this,n),n=i.L),n.C=i.C,i.C=n.L.C=!1,dw(this,i),t=this._;break}n.C=!0,t=i,i=i.U}while(!t.C);t&&(t.C=!1)}}};var gw=sw;function vw(t,n,e,r){var i=[null,null],o=Bw.push(i)-1;return i.left=t,i.right=n,e&&bw(i,t,n,e),r&&bw(i,n,t,r),qw[t.index].halfedges.push(o),qw[n.index].halfedges.push(o),i}function yw(t,n,e){var r=[n,e];return r.left=t,r}function bw(t,n,e,r){t[0]||t[1]?t.left===e?t[1]=r:t[0]=r:(t[0]=r,t.left=n,t.right=e)}function mw(t,n,e,r,i){var o,u=t[0],a=t[1],c=u[0],f=u[1],s=0,l=1,h=a[0]-c,d=a[1]-f;if(o=n-c,h||!(o>0)){if(o/=h,h<0){if(o<s)return;o<l&&(l=o)}else if(h>0){if(o>l)return;o>s&&(s=o)}if(o=r-c,h||!(o<0)){if(o/=h,h<0){if(o>l)return;o>s&&(s=o)}else if(h>0){if(o<s)return;o<l&&(l=o)}if(o=e-f,d||!(o>0)){if(o/=d,d<0){if(o<s)return;o<l&&(l=o)}else if(d>0){if(o>l)return;o>s&&(s=o)}if(o=i-f,d||!(o<0)){if(o/=d,d<0){if(o>l)return;o>s&&(s=o)}else if(d>0){if(o<s)return;o<l&&(l=o)}return!(s>0||l<1)||(s>0&&(t[0]=[c+s*h,f+s*d]),l<1&&(t[1]=[c+l*h,f+l*d]),!0)}}}}}function _w(t,n,e,r,i){var o=t[1];if(o)return!0;var u,a,c=t[0],f=t.left,s=t.right,l=f[0],h=f[1],d=s[0],p=s[1],g=(l+d)/2,v=(h+p)/2;if(p===h){if(g<n||g>=r)return;if(l>d){if(c){if(c[1]>=i)return}else c=[g,e];o=[g,i]}else{if(c){if(c[1]<e)return}else c=[g,i];o=[g,e]}}else if(a=v-(u=(l-d)/(p-h))*g,u<-1||u>1)if(l>d){if(c){if(c[1]>=i)return}else c=[(e-a)/u,e];o=[(i-a)/u,i]}else{if(c){if(c[1]<e)return}else c=[(i-a)/u,i];o=[(e-a)/u,e]}else if(h<p){if(c){if(c[0]>=r)return}else c=[n,u*n+a];o=[r,u*r+a]}else{if(c){if(c[0]<n)return}else c=[r,u*r+a];o=[n,u*n+a]}return t[0]=c,t[1]=o,!0}function xw(t,n){var e=t.site,r=n.left,i=n.right;return e===i&&(i=r,r=e),i?Math.atan2(i[1]-r[1],i[0]-r[0]):(e===r?(r=n[1],i=n[0]):(r=n[0],i=n[1]),Math.atan2(r[0]-i[0],i[1]-r[1]))}function ww(t,n){return n[+(n.left!==t.site)]}function Mw(t,n){return n[+(n.left===t.site)]}var kw,Nw=[];function Sw(){lw(this),this.x=this.y=this.arc=this.site=this.cy=null}function Tw(t){var n=t.P,e=t.N;if(n&&e){var r=n.site,i=t.site,o=e.site;if(r!==o){var u=i[0],a=i[1],c=r[0]-u,f=r[1]-a,s=o[0]-u,l=o[1]-a,h=2*(c*l-f*s);if(!(h>=-jw)){var d=c*c+f*f,p=s*s+l*l,g=(l*d-f*p)/h,v=(c*p-s*d)/h,y=Nw.pop()||new Sw;y.arc=t,y.site=i,y.x=g+u,y.y=(y.cy=v+a)+Math.sqrt(g*g+v*v),t.circle=y;for(var b=null,m=Iw._;m;)if(y.y<m.y||y.y===m.y&&y.x<=m.x){if(!m.L){b=m.P;break}m=m.L}else{if(!m.R){b=m;break}m=m.R}Iw.insert(b,y),b||(kw=y)}}}}function Aw(t){var n=t.circle;n&&(n.P||(kw=n.N),Iw.remove(n),Nw.push(n),lw(n),t.circle=null)}var Cw=[];function Ew(){lw(this),this.edge=this.site=this.circle=null}function Lw(t){var n=Cw.pop()||new Ew;return n.site=t,n}function Rw(t){Aw(t),Uw.remove(t),Cw.push(t),lw(t)}function Dw(t){var n=t.circle,e=n.x,r=n.cy,i=[e,r],o=t.P,u=t.N,a=[t];Rw(t);for(var c=o;c.circle&&Math.abs(e-c.circle.x)<Fw&&Math.abs(r-c.circle.cy)<Fw;)o=c.P,a.unshift(c),Rw(c),c=o;a.unshift(c),Aw(c);for(var f=u;f.circle&&Math.abs(e-f.circle.x)<Fw&&Math.abs(r-f.circle.cy)<Fw;)u=f.N,a.push(f),Rw(f),f=u;a.push(f),Aw(f);var s,l=a.length;for(s=1;s<l;++s)f=a[s],c=a[s-1],bw(f.edge,c.site,f.site,i);c=a[0],(f=a[l-1]).edge=vw(c.site,f.site,null,i),Tw(c),Tw(f)}function Pw(t){for(var n,e,r,i,o=t[0],u=t[1],a=Uw._;a;)if((r=zw(a,u)-o)>Fw)a=a.L;else{if(!((i=o-Ow(a,u))>Fw)){r>-Fw?(n=a.P,e=a):i>-Fw?(n=a,e=a.N):n=e=a;break}if(!a.R){n=a;break}a=a.R}!function(t){qw[t.index]={site:t,halfedges:[]}}(t);var c=Lw(t);if(Uw.insert(n,c),n||e){if(n===e)return Aw(n),e=Lw(n.site),Uw.insert(c,e),c.edge=e.edge=vw(n.site,c.site),Tw(n),void Tw(e);if(e){Aw(n),Aw(e);var f=n.site,s=f[0],l=f[1],h=t[0]-s,d=t[1]-l,p=e.site,g=p[0]-s,v=p[1]-l,y=2*(h*v-d*g),b=h*h+d*d,m=g*g+v*v,_=[(v*b-d*m)/y+s,(h*m-g*b)/y+l];bw(e.edge,f,p,_),c.edge=vw(f,t,null,_),e.edge=vw(t,p,null,_),Tw(n),Tw(e)}else c.edge=vw(n.site,c.site)}}function zw(t,n){var e=t.site,r=e[0],i=e[1],o=i-n;if(!o)return r;var u=t.P;if(!u)return-1/0;var a=(e=u.site)[0],c=e[1],f=c-n;if(!f)return a;var s=a-r,l=1/o-1/f,h=s/f;return l?(-h+Math.sqrt(h*h-2*l*(s*s/(-2*f)-c+f/2+i-o/2)))/l+r:(r+a)/2}function Ow(t,n){var e=t.N;if(e)return zw(e,n);var r=t.site;return r[1]===n?r[0]:1/0}var Uw,qw,Iw,Bw,Fw=1e-6,jw=1e-12;function Yw(t,n){return n[1]-t[1]||n[0]-t[0]}function Hw(t,n){var e,r,i,o=t.sort(Yw).pop();for(Bw=[],qw=new Array(t.length),Uw=new gw,Iw=new gw;;)if(i=kw,o&&(!i||o[1]<i.y||o[1]===i.y&&o[0]<i.x))o[0]===e&&o[1]===r||(Pw(o),e=o[0],r=o[1]),o=t.pop();else{if(!i)break;Dw(i.arc)}if(function(){for(var t,n,e,r,i=0,o=qw.length;i<o;++i)if((t=qw[i])&&(r=(n=t.halfedges).length)){var u=new Array(r),a=new Array(r);for(e=0;e<r;++e)u[e]=e,a[e]=xw(t,Bw[n[e]]);for(u.sort(function(t,n){return a[n]-a[t]}),e=0;e<r;++e)a[e]=n[u[e]];for(e=0;e<r;++e)n[e]=a[e]}}(),n){var u=+n[0][0],a=+n[0][1],c=+n[1][0],f=+n[1][1];!function(t,n,e,r){for(var i,o=Bw.length;o--;)_w(i=Bw[o],t,n,e,r)&&mw(i,t,n,e,r)&&(Math.abs(i[0][0]-i[1][0])>Fw||Math.abs(i[0][1]-i[1][1])>Fw)||delete Bw[o]}(u,a,c,f),function(t,n,e,r){var i,o,u,a,c,f,s,l,h,d,p,g,v=qw.length,y=!0;for(i=0;i<v;++i)if(o=qw[i]){for(u=o.site,a=(c=o.halfedges).length;a--;)Bw[c[a]]||c.splice(a,1);for(a=0,f=c.length;a<f;)p=(d=Mw(o,Bw[c[a]]))[0],g=d[1],l=(s=ww(o,Bw[c[++a%f]]))[0],h=s[1],(Math.abs(p-l)>Fw||Math.abs(g-h)>Fw)&&(c.splice(a,0,Bw.push(yw(u,d,Math.abs(p-t)<Fw&&r-g>Fw?[t,Math.abs(l-t)<Fw?h:r]:Math.abs(g-r)<Fw&&e-p>Fw?[Math.abs(h-r)<Fw?l:e,r]:Math.abs(p-e)<Fw&&g-n>Fw?[e,Math.abs(l-e)<Fw?h:n]:Math.abs(g-n)<Fw&&p-t>Fw?[Math.abs(h-n)<Fw?l:t,n]:null))-1),++f);f&&(y=!1)}if(y){var b,m,_,x=1/0;for(i=0,y=null;i<v;++i)(o=qw[i])&&(_=(b=(u=o.site)[0]-t)*b+(m=u[1]-n)*m)<x&&(x=_,y=o);if(y){var w=[t,n],M=[t,r],k=[e,r],N=[e,n];y.halfedges.push(Bw.push(yw(u=y.site,w,M))-1,Bw.push(yw(u,M,k))-1,Bw.push(yw(u,k,N))-1,Bw.push(yw(u,N,w))-1)}}for(i=0;i<v;++i)(o=qw[i])&&(o.halfedges.length||delete qw[i])}(u,a,c,f)}this.edges=Bw,this.cells=qw,Uw=Iw=Bw=qw=null}Hw.prototype={constructor:Hw,polygons:function(){var t=this.edges;return this.cells.map(function(n){var e=n.halfedges.map(function(e){return ww(n,t[e])});return e.data=n.site.data,e})},triangles:function(){var t=[],n=this.edges;return this.cells.forEach(function(e,r){if(o=(i=e.halfedges).length)for(var i,o,u,a,c,f,s=e.site,l=-1,h=n[i[o-1]],d=h.left===s?h.right:h.left;++l<o;)u=d,d=(h=n[i[l]]).left===s?h.right:h.left,u&&d&&r<u.index&&r<d.index&&(c=u,f=d,((a=s)[0]-f[0])*(c[1]-a[1])-(a[0]-c[0])*(f[1]-a[1])<0)&&t.push([s.data,u.data,d.data])}),t},links:function(){return this.edges.filter(function(t){return t.right}).map(function(t){return{source:t.left.data,target:t.right.data}})},find:function(t,n,e){for(var r,i,o=this,u=o._found||0,a=o.cells.length;!(i=o.cells[u]);)if(++u>=a)return null;var c=t-i.site[0],f=n-i.site[1],s=c*c+f*f;do{i=o.cells[r=u],u=null,i.halfedges.forEach(function(e){var r=o.edges[e],a=r.left;if(a!==i.site&&a||(a=r.right)){var c=t-a[0],f=n-a[1],l=c*c+f*f;l<s&&(s=l,u=a.index)}})}while(null!==u);return o._found=r,null==e||s<=e*e?i.site:null}};var Vw=function(){var t=cw,n=fw,e=null;function r(r){return new Hw(r.map(function(e,i){var o=[Math.round(t(e,i,r)/Fw)*Fw,Math.round(n(e,i,r)/Fw)*Fw];return o.index=i,o.data=e,o}),e)}return r.polygons=function(t){return r(t).polygons()},r.links=function(t){return r(t).links()},r.triangles=function(t){return r(t).triangles()},r.x=function(n){return arguments.length?(t="function"==typeof n?n:aw(+n),r):t},r.y=function(t){return arguments.length?(n="function"==typeof t?t:aw(+t),r):n},r.extent=function(t){return arguments.length?(e=null==t?null:[[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]],r):e&&[[e[0][0],e[0][1]],[e[1][0],e[1][1]]]},r.size=function(t){return arguments.length?(e=null==t?null:[[0,0],[+t[0],+t[1]]],r):e&&[e[1][0]-e[0][0],e[1][1]-e[0][1]]},r},Xw=function(t){return function(){return t}};function Gw(t,n,e){this.target=t,this.type=n,this.transform=e}function Ww(t,n,e){this.k=t,this.x=n,this.y=e}Ww.prototype={constructor:Ww,scale:function(t){return 1===t?this:new Ww(this.k*t,this.x,this.y)},translate:function(t,n){return 0===t&0===n?this:new Ww(this.k,this.x+this.k*t,this.y+this.k*n)},apply:function(t){return[t[0]*this.k+this.x,t[1]*this.k+this.y]},applyX:function(t){return t*this.k+this.x},applyY:function(t){return t*this.k+this.y},invert:function(t){return[(t[0]-this.x)/this.k,(t[1]-this.y)/this.k]},invertX:function(t){return(t-this.x)/this.k},invertY:function(t){return(t-this.y)/this.k},rescaleX:function(t){return t.copy().domain(t.range().map(this.invertX,this).map(t.invert,t))},rescaleY:function(t){return t.copy().domain(t.range().map(this.invertY,this).map(t.invert,t))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var $w=new Ww(1,0,0);function Zw(t){return t.__zoom||$w}function Qw(){Ht.stopImmediatePropagation()}Zw.prototype=Ww.prototype;var Jw=function(){Ht.preventDefault(),Ht.stopImmediatePropagation()};function Kw(){return!Ht.button}function tM(){var t,n,e=this;return e instanceof SVGElement?(t=(e=e.ownerSVGElement||e).width.baseVal.value,n=e.height.baseVal.value):(t=e.clientWidth,n=e.clientHeight),[[0,0],[t,n]]}function nM(){return this.__zoom||$w}function eM(){return-Ht.deltaY*(Ht.deltaMode?120:1)/500}function rM(){return"ontouchstart"in this}function iM(t,n,e){var r=t.invertX(n[0][0])-e[0][0],i=t.invertX(n[1][0])-e[1][0],o=t.invertY(n[0][1])-e[0][1],u=t.invertY(n[1][1])-e[1][1];return t.translate(i>r?(r+i)/2:Math.min(0,r)||Math.max(0,i),u>o?(o+u)/2:Math.min(0,o)||Math.max(0,u))}var oM=function(){var t,n,e=Kw,r=tM,i=iM,o=eM,u=rM,a=[0,1/0],c=[[-1/0,-1/0],[1/0,1/0]],f=250,s=ur,l=[],h=ht("start","zoom","end"),d=500,p=150,g=0;function v(t){t.property("__zoom",nM).on("wheel.zoom",M).on("mousedown.zoom",k).on("dblclick.zoom",N).filter(u).on("touchstart.zoom",S).on("touchmove.zoom",T).on("touchend.zoom touchcancel.zoom",A).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(t,n){return(n=Math.max(a[0],Math.min(a[1],n)))===t.k?t:new Ww(n,t.x,t.y)}function b(t,n,e){var r=n[0]-e[0]*t.k,i=n[1]-e[1]*t.k;return r===t.x&&i===t.y?t:new Ww(t.k,r,i)}function m(t){return[(+t[0][0]+ +t[1][0])/2,(+t[0][1]+ +t[1][1])/2]}function _(t,n,e){t.on("start.zoom",function(){x(this,arguments).start()}).on("interrupt.zoom end.zoom",function(){x(this,arguments).end()}).tween("zoom",function(){var t=arguments,i=x(this,t),o=r.apply(this,t),u=e||m(o),a=Math.max(o[1][0]-o[0][0],o[1][1]-o[0][1]),c=this.__zoom,f="function"==typeof n?n.apply(this,t):n,l=s(c.invert(u).concat(a/c.k),f.invert(u).concat(a/f.k));return function(t){if(1===t)t=f;else{var n=l(t),e=a/n[2];t=new Ww(e,u[0]-n[0]*e,u[1]-n[1]*e)}i.zoom(null,t)}})}function x(t,n){for(var e,r=0,i=l.length;r<i;++r)if((e=l[r]).that===t)return e;return new w(t,n)}function w(t,n){this.that=t,this.args=n,this.index=-1,this.active=0,this.extent=r.apply(t,n)}function M(){if(e.apply(this,arguments)){var t=x(this,arguments),n=this.__zoom,r=Math.max(a[0],Math.min(a[1],n.k*Math.pow(2,o.apply(this,arguments)))),u=fn(this);if(t.wheel)t.mouse[0][0]===u[0]&&t.mouse[0][1]===u[1]||(t.mouse[1]=n.invert(t.mouse[0]=u)),clearTimeout(t.wheel);else{if(n.k===r)return;t.mouse=[u,n.invert(u)],Kr(this),t.start()}Jw(),t.wheel=setTimeout(function(){t.wheel=null,t.end()},p),t.zoom("mouse",i(b(y(n,r),t.mouse[0],t.mouse[1]),t.extent,c))}}function k(){if(!n&&e.apply(this,arguments)){var t=x(this,arguments),r=nn(Ht.view).on("mousemove.zoom",function(){if(Jw(),!t.moved){var n=Ht.clientX-u,e=Ht.clientY-a;t.moved=n*n+e*e>g}t.zoom("mouse",i(b(t.that.__zoom,t.mouse[0]=fn(t.that),t.mouse[1]),t.extent,c))},!0).on("mouseup.zoom",function(){r.on("mousemove.zoom mouseup.zoom",null),vn(Ht.view,t.moved),Jw(),t.end()},!0),o=fn(this),u=Ht.clientX,a=Ht.clientY;gn(Ht.view),Qw(),t.mouse=[o,this.__zoom.invert(o)],Kr(this),t.start()}}function N(){if(e.apply(this,arguments)){var t=this.__zoom,n=fn(this),o=t.invert(n),u=t.k*(Ht.shiftKey?.5:2),a=i(b(y(t,u),n,o),r.apply(this,arguments),c);Jw(),f>0?nn(this).transition().duration(f).call(_,a,n):nn(this).call(v.transform,a)}}function S(){if(e.apply(this,arguments)){var n,r,i,o,u=x(this,arguments),a=Ht.changedTouches,c=a.length;for(Qw(),r=0;r<c;++r)i=a[r],o=[o=ln(this,a,i.identifier),this.__zoom.invert(o),i.identifier],u.touch0?u.touch1||(u.touch1=o):(u.touch0=o,n=!0);if(t&&(t=clearTimeout(t),!u.touch1))return u.end(),void((o=nn(this).on("dblclick.zoom"))&&o.apply(this,arguments));n&&(t=setTimeout(function(){t=null},d),Kr(this),u.start())}}function T(){var n,e,r,o,u=x(this,arguments),a=Ht.changedTouches,f=a.length;for(Jw(),t&&(t=clearTimeout(t)),n=0;n<f;++n)e=a[n],r=ln(this,a,e.identifier),u.touch0&&u.touch0[2]===e.identifier?u.touch0[0]=r:u.touch1&&u.touch1[2]===e.identifier&&(u.touch1[0]=r);if(e=u.that.__zoom,u.touch1){var s=u.touch0[0],l=u.touch0[1],h=u.touch1[0],d=u.touch1[1],p=(p=h[0]-s[0])*p+(p=h[1]-s[1])*p,g=(g=d[0]-l[0])*g+(g=d[1]-l[1])*g;e=y(e,Math.sqrt(p/g)),r=[(s[0]+h[0])/2,(s[1]+h[1])/2],o=[(l[0]+d[0])/2,(l[1]+d[1])/2]}else{if(!u.touch0)return;r=u.touch0[0],o=u.touch0[1]}u.zoom("touch",i(b(e,r,o),u.extent,c))}function A(){var t,e,r=x(this,arguments),i=Ht.changedTouches,o=i.length;for(Qw(),n&&clearTimeout(n),n=setTimeout(function(){n=null},d),t=0;t<o;++t)e=i[t],r.touch0&&r.touch0[2]===e.identifier?delete r.touch0:r.touch1&&r.touch1[2]===e.identifier&&delete r.touch1;r.touch1&&!r.touch0&&(r.touch0=r.touch1,delete r.touch1),r.touch0?r.touch0[1]=this.__zoom.invert(r.touch0[0]):r.end()}return v.transform=function(t,n){var e=t.selection?t.selection():t;e.property("__zoom",nM),t!==e?_(t,n):e.interrupt().each(function(){x(this,arguments).start().zoom(null,"function"==typeof n?n.apply(this,arguments):n).end()})},v.scaleBy=function(t,n){v.scaleTo(t,function(){return this.__zoom.k*("function"==typeof n?n.apply(this,arguments):n)})},v.scaleTo=function(t,n){v.transform(t,function(){var t=r.apply(this,arguments),e=this.__zoom,o=m(t),u=e.invert(o),a="function"==typeof n?n.apply(this,arguments):n;return i(b(y(e,a),o,u),t,c)})},v.translateBy=function(t,n,e){v.transform(t,function(){return i(this.__zoom.translate("function"==typeof n?n.apply(this,arguments):n,"function"==typeof e?e.apply(this,arguments):e),r.apply(this,arguments),c)})},v.translateTo=function(t,n,e){v.transform(t,function(){var t=r.apply(this,arguments),o=this.__zoom,u=m(t);return i($w.translate(u[0],u[1]).scale(o.k).translate("function"==typeof n?-n.apply(this,arguments):-n,"function"==typeof e?-e.apply(this,arguments):-e),t,c)})},w.prototype={start:function(){return 1==++this.active&&(this.index=l.push(this)-1,this.emit("start")),this},zoom:function(t,n){return this.mouse&&"mouse"!==t&&(this.mouse[1]=n.invert(this.mouse[0])),this.touch0&&"touch"!==t&&(this.touch0[1]=n.invert(this.touch0[0])),this.touch1&&"touch"!==t&&(this.touch1[1]=n.invert(this.touch1[0])),this.that.__zoom=n,this.emit("zoom"),this},end:function(){return 0==--this.active&&(l.splice(this.index,1),this.index=-1,this.emit("end")),this},emit:function(t){$t(new Gw(v,t,this.that.__zoom),h.apply,h,[t,this.that,this.args])}},v.wheelDelta=function(t){return arguments.length?(o="function"==typeof t?t:Xw(+t),v):o},v.filter=function(t){return arguments.length?(e="function"==typeof t?t:Xw(!!t),v):e},v.touchable=function(t){return arguments.length?(u="function"==typeof t?t:Xw(!!t),v):u},v.extent=function(t){return arguments.length?(r="function"==typeof t?t:Xw([[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]]),v):r},v.scaleExtent=function(t){return arguments.length?(a[0]=+t[0],a[1]=+t[1],v):[a[0],a[1]]},v.translateExtent=function(t){return arguments.length?(c[0][0]=+t[0][0],c[1][0]=+t[1][0],c[0][1]=+t[0][1],c[1][1]=+t[1][1],v):[[c[0][0],c[0][1]],[c[1][0],c[1][1]]]},v.constrain=function(t){return arguments.length?(i=t,v):i},v.duration=function(t){return arguments.length?(f=+t,v):f},v.interpolate=function(t){return arguments.length?(s=t,v):s},v.on=function(){var t=h.on.apply(h,arguments);return t===h?v:t},v.clickDistance=function(t){return arguments.length?(g=(t=+t)*t,v):Math.sqrt(g)},v};e.d(n,"version",function(){return"5.9.2"}),e.d(n,"bisect",function(){return c}),e.d(n,"bisectRight",function(){return u}),e.d(n,"bisectLeft",function(){return a}),e.d(n,"ascending",function(){return r}),e.d(n,"bisector",function(){return i}),e.d(n,"cross",function(){return l}),e.d(n,"descending",function(){return h}),e.d(n,"deviation",function(){return g}),e.d(n,"extent",function(){return v}),e.d(n,"histogram",function(){return E}),e.d(n,"thresholdFreedmanDiaconis",function(){return R}),e.d(n,"thresholdScott",function(){return D}),e.d(n,"thresholdSturges",function(){return C}),e.d(n,"max",function(){return P}),e.d(n,"mean",function(){return z}),e.d(n,"median",function(){return O}),e.d(n,"merge",function(){return U}),e.d(n,"min",function(){return q}),e.d(n,"pairs",function(){return f}),e.d(n,"permute",function(){return I}),e.d(n,"quantile",function(){return L}),e.d(n,"range",function(){return w}),e.d(n,"scan",function(){return B}),e.d(n,"shuffle",function(){return F}),e.d(n,"sum",function(){return j}),e.d(n,"ticks",function(){return S}),e.d(n,"tickIncrement",function(){return T}),e.d(n,"tickStep",function(){return A}),e.d(n,"transpose",function(){return Y}),e.d(n,"variance",function(){return p}),e.d(n,"zip",function(){return V}),e.d(n,"axisTop",function(){return rt}),e.d(n,"axisRight",function(){return it}),e.d(n,"axisBottom",function(){return ot}),e.d(n,"axisLeft",function(){return ut}),e.d(n,"brush",function(){return No}),e.d(n,"brushX",function(){return Mo}),e.d(n,"brushY",function(){return ko}),e.d(n,"brushSelection",function(){return wo}),e.d(n,"chord",function(){return Do}),e.d(n,"ribbon",function(){return Go}),e.d(n,"nest",function(){return Qo}),e.d(n,"set",function(){return ou}),e.d(n,"map",function(){return Zo}),e.d(n,"keys",function(){return uu}),e.d(n,"values",function(){return au}),e.d(n,"entries",function(){return cu}),e.d(n,"color",function(){return In}),e.d(n,"rgb",function(){return Yn}),e.d(n,"hsl",function(){return Gn}),e.d(n,"lab",function(){return ae}),e.d(n,"hcl",function(){return ge}),e.d(n,"lch",function(){return pe}),e.d(n,"gray",function(){return ue}),e.d(n,"cubehelix",function(){return Me}),e.d(n,"contours",function(){return bu}),e.d(n,"contourDensity",function(){return ku}),e.d(n,"dispatch",function(){return ht}),e.d(n,"drag",function(){return Mn}),e.d(n,"dragDisable",function(){return gn}),e.d(n,"dragEnable",function(){return vn}),e.d(n,"dsvFormat",function(){return Pu}),e.d(n,"csvParse",function(){return Ou}),e.d(n,"csvParseRows",function(){return Uu}),e.d(n,"csvFormat",function(){return qu}),e.d(n,"csvFormatBody",function(){return Iu}),e.d(n,"csvFormatRows",function(){return Bu}),e.d(n,"tsvParse",function(){return ju}),e.d(n,"tsvParseRows",function(){return Yu}),e.d(n,"tsvFormat",function(){return Hu}),e.d(n,"tsvFormatBody",function(){return Vu}),e.d(n,"tsvFormatRows",function(){return Xu}),e.d(n,"autoType",function(){return Gu}),e.d(n,"easeLinear",function(){return fi}),e.d(n,"easeQuad",function(){return hi}),e.d(n,"easeQuadIn",function(){return si}),e.d(n,"easeQuadOut",function(){return li}),e.d(n,"easeQuadInOut",function(){return hi}),e.d(n,"easeCubic",function(){return gi}),e.d(n,"easeCubicIn",function(){return di}),e.d(n,"easeCubicOut",function(){return pi}),e.d(n,"easeCubicInOut",function(){return gi}),e.d(n,"easePoly",function(){return bi}),e.d(n,"easePolyIn",function(){return vi}),e.d(n,"easePolyOut",function(){return yi}),e.d(n,"easePolyInOut",function(){return bi}),e.d(n,"easeSin",function(){return Mi}),e.d(n,"easeSinIn",function(){return xi}),e.d(n,"easeSinOut",function(){return wi}),e.d(n,"easeSinInOut",function(){return Mi}),e.d(n,"easeExp",function(){return Si}),e.d(n,"easeExpIn",function(){return ki}),e.d(n,"easeExpOut",function(){return Ni}),e.d(n,"easeExpInOut",function(){return Si}),e.d(n,"easeCircle",function(){return Ci}),e.d(n,"easeCircleIn",function(){return Ti}),e.d(n,"easeCircleOut",function(){return Ai}),e.d(n,"easeCircleInOut",function(){return Ci}),e.d(n,"easeBounce",function(){return Fi}),e.d(n,"easeBounceIn",function(){return Bi}),e.d(n,"easeBounceOut",function(){return Fi}),e.d(n,"easeBounceInOut",function(){return ji}),e.d(n,"easeBack",function(){return Vi}),e.d(n,"easeBackIn",function(){return Yi}),e.d(n,"easeBackOut",function(){return Hi}),e.d(n,"easeBackInOut",function(){return Vi}),e.d(n,"easeElastic",function(){return Wi}),e.d(n,"easeElasticIn",function(){return Gi}),e.d(n,"easeElasticOut",function(){return Wi}),e.d(n,"easeElasticInOut",function(){return $i}),e.d(n,"blob",function(){return $u}),e.d(n,"buffer",function(){return Qu}),e.d(n,"dsv",function(){return na}),e.d(n,"csv",function(){return ea}),e.d(n,"tsv",function(){return ra}),e.d(n,"image",function(){return ia}),e.d(n,"json",function(){return ua}),e.d(n,"text",function(){return Ku}),e.d(n,"xml",function(){return ca}),e.d(n,"html",function(){return fa}),e.d(n,"svg",function(){return sa}),e.d(n,"forceCenter",function(){return la}),e.d(n,"forceCollide",function(){return ka}),e.d(n,"forceLink",function(){return Ta}),e.d(n,"forceManyBody",function(){return Da}),e.d(n,"forceRadial",function(){return Pa}),e.d(n,"forceSimulation",function(){return Ra}),e.d(n,"forceX",function(){return za}),e.d(n,"forceY",function(){return Oa}),e.d(n,"formatDefaultLocale",function(){return Ja}),e.d(n,"format",function(){return Ha}),e.d(n,"formatPrefix",function(){return Va}),e.d(n,"formatLocale",function(){return Qa}),e.d(n,"formatSpecifier",function(){return Ba}),e.d(n,"precisionFixed",function(){return Ka}),e.d(n,"precisionPrefix",function(){return tc}),e.d(n,"precisionRound",function(){return nc}),e.d(n,"geoArea",function(){return Gc}),e.d(n,"geoBounds",function(){return Bf}),e.d(n,"geoCentroid",function(){return Jf}),e.d(n,"geoCircle",function(){return fs}),e.d(n,"geoClipAntimeridian",function(){return _s}),e.d(n,"geoClipCircle",function(){return xs}),e.d(n,"geoClipExtent",function(){return Cs}),e.d(n,"geoClipRectangle",function(){return Ns}),e.d(n,"geoContains",function(){return Gs}),e.d(n,"geoDistance",function(){return qs}),e.d(n,"geoGraticule",function(){return Zs}),e.d(n,"geoGraticule10",function(){return Qs}),e.d(n,"geoInterpolate",function(){return el}),e.d(n,"geoLength",function(){return zs}),e.d(n,"geoPath",function(){return th}),e.d(n,"geoAlbers",function(){return mh}),e.d(n,"geoAlbersUsa",function(){return _h}),e.d(n,"geoAzimuthalEqualArea",function(){return kh}),e.d(n,"geoAzimuthalEqualAreaRaw",function(){return Mh}),e.d(n,"geoAzimuthalEquidistant",function(){return Sh}),e.d(n,"geoAzimuthalEquidistantRaw",function(){return Nh}),e.d(n,"geoConicConformal",function(){return Rh}),e.d(n,"geoConicConformalRaw",function(){return Lh}),e.d(n,"geoConicEqualArea",function(){return bh}),e.d(n,"geoConicEqualAreaRaw",function(){return yh}),e.d(n,"geoConicEquidistant",function(){return Oh}),e.d(n,"geoConicEquidistantRaw",function(){return zh}),e.d(n,"geoEqualEarth",function(){return Yh}),e.d(n,"geoEqualEarthRaw",function(){return jh}),e.d(n,"geoEquirectangular",function(){return Ph}),e.d(n,"geoEquirectangularRaw",function(){return Dh}),e.d(n,"geoGnomonic",function(){return Vh}),e.d(n,"geoGnomonicRaw",function(){return Hh}),e.d(n,"geoIdentity",function(){return Gh}),e.d(n,"geoProjection",function(){return ph}),e.d(n,"geoProjectionMutator",function(){return gh}),e.d(n,"geoMercator",function(){return Ah}),e.d(n,"geoMercatorRaw",function(){return Th}),e.d(n,"geoNaturalEarth1",function(){return $h}),e.d(n,"geoNaturalEarth1Raw",function(){return Wh}),e.d(n,"geoOrthographic",function(){return Qh}),e.d(n,"geoOrthographicRaw",function(){return Zh}),e.d(n,"geoStereographic",function(){return Kh}),e.d(n,"geoStereographicRaw",function(){return Jh}),e.d(n,"geoTransverseMercator",function(){return nd}),e.d(n,"geoTransverseMercatorRaw",function(){return td}),e.d(n,"geoRotation",function(){return us}),e.d(n,"geoStream",function(){return Ic}),e.d(n,"geoTransform",function(){return nh}),e.d(n,"cluster",function(){return od}),e.d(n,"hierarchy",function(){return ad}),e.d(n,"pack",function(){return Ld}),e.d(n,"packSiblings",function(){return Sd}),e.d(n,"packEnclose",function(){return dd}),e.d(n,"partition",function(){return Ud}),e.d(n,"stratify",function(){return Yd}),e.d(n,"tree",function(){return Zd}),e.d(n,"treemap",function(){return np}),e.d(n,"treemapBinary",function(){return ep}),e.d(n,"treemapDice",function(){return Od}),e.d(n,"treemapSlice",function(){return Qd}),e.d(n,"treemapSliceDice",function(){return rp}),e.d(n,"treemapSquarify",function(){return tp}),e.d(n,"treemapResquarify",function(){return ip}),e.d(n,"interpolate",function(){return We}),e.d(n,"interpolateArray",function(){return Ue}),e.d(n,"interpolateBasis",function(){return Se}),e.d(n,"interpolateBasisClosed",function(){return Te}),e.d(n,"interpolateDate",function(){return qe}),e.d(n,"interpolateDiscrete",function(){return $e}),e.d(n,"interpolateHue",function(){return Ze}),e.d(n,"interpolateNumber",function(){return Ie}),e.d(n,"interpolateObject",function(){return Be}),e.d(n,"interpolateRound",function(){return Qe}),e.d(n,"interpolateString",function(){return Ge}),e.d(n,"interpolateTransformCss",function(){return er}),e.d(n,"interpolateTransformSvg",function(){return rr}),e.d(n,"interpolateZoom",function(){return ur}),e.d(n,"interpolateRgb",function(){return De}),e.d(n,"interpolateRgbBasis",function(){return ze}),e.d(n,"interpolateRgbBasisClosed",function(){return Oe}),e.d(n,"interpolateHsl",function(){return cr}),e.d(n,"interpolateHslLong",function(){return fr}),e.d(n,"interpolateLab",function(){return sr}),e.d(n,"interpolateHcl",function(){return hr}),e.d(n,"interpolateHclLong",function(){return dr}),e.d(n,"interpolateCubehelix",function(){return gr}),e.d(n,"interpolateCubehelixLong",function(){return vr}),e.d(n,"piecewise",function(){return yr}),e.d(n,"quantize",function(){return _r}),e.d(n,"path",function(){return Fo}),e.d(n,"polygonArea",function(){return op}),e.d(n,"polygonCentroid",function(){return up}),e.d(n,"polygonHull",function(){return sp}),e.d(n,"polygonContains",function(){return lp}),e.d(n,"polygonLength",function(){return hp}),e.d(n,"quadtree",function(){return ba}),e.d(n,"randomUniform",function(){return pp}),e.d(n,"randomNormal",function(){return gp}),e.d(n,"randomLogNormal",function(){return vp}),e.d(n,"randomBates",function(){return bp}),e.d(n,"randomIrwinHall",function(){return yp}),e.d(n,"randomExponential",function(){return mp}),e.d(n,"scaleBand",function(){return Tp}),e.d(n,"scalePoint",function(){return Ap}),e.d(n,"scaleIdentity",function(){return Yp}),e.d(n,"scaleLinear",function(){return jp}),e.d(n,"scaleLog",function(){return Jp}),e.d(n,"scaleSymlog",function(){return eg}),e.d(n,"scaleOrdinal",function(){return Sp}),e.d(n,"scaleImplicit",function(){return Np}),e.d(n,"scalePow",function(){return ag}),e.d(n,"scaleSqrt",function(){return cg}),e.d(n,"scaleQuantile",function(){return fg}),e.d(n,"scaleQuantize",function(){return sg}),e.d(n,"scaleThreshold",function(){return lg}),e.d(n,"scaleTime",function(){return ab}),e.d(n,"scaleUtc",function(){return cb}),e.d(n,"scaleSequential",function(){return lb}),e.d(n,"scaleSequentialLog",function(){return hb}),e.d(n,"scaleSequentialPow",function(){return pb}),e.d(n,"scaleSequentialSqrt",function(){return gb}),e.d(n,"scaleSequentialSymlog",function(){return db}),e.d(n,"scaleSequentialQuantile",function(){return vb}),e.d(n,"scaleDiverging",function(){return bb}),e.d(n,"scaleDivergingLog",function(){return mb}),e.d(n,"scaleDivergingPow",function(){return xb}),e.d(n,"scaleDivergingSqrt",function(){return wb}),e.d(n,"scaleDivergingSymlog",function(){return _b}),e.d(n,"tickFormat",function(){return Bp}),e.d(n,"schemeCategory10",function(){return kb}),e.d(n,"schemeAccent",function(){return Nb}),e.d(n,"schemeDark2",function(){return Sb}),e.d(n,"schemePaired",function(){return Tb}),e.d(n,"schemePastel1",function(){return Ab}),e.d(n,"schemePastel2",function(){return Cb}),e.d(n,"schemeSet1",function(){return Eb}),e.d(n,"schemeSet2",function(){return Lb}),e.d(n,"schemeSet3",function(){return Rb}),e.d(n,"interpolateBrBG",function(){return zb}),e.d(n,"schemeBrBG",function(){return Pb}),e.d(n,"interpolatePRGn",function(){return Ub}),e.d(n,"schemePRGn",function(){return Ob}),e.d(n,"interpolatePiYG",function(){return Ib}),e.d(n,"schemePiYG",function(){return qb}),e.d(n,"interpolatePuOr",function(){return Fb}),e.d(n,"schemePuOr",function(){return Bb}),e.d(n,"interpolateRdBu",function(){return Yb}),e.d(n,"schemeRdBu",function(){return jb}),e.d(n,"interpolateRdGy",function(){return Vb}),e.d(n,"schemeRdGy",function(){return Hb}),e.d(n,"interpolateRdYlBu",function(){return Gb}),e.d(n,"schemeRdYlBu",function(){return Xb}),e.d(n,"interpolateRdYlGn",function(){return $b}),e.d(n,"schemeRdYlGn",function(){return Wb}),e.d(n,"interpolateSpectral",function(){return Qb}),e.d(n,"schemeSpectral",function(){return Zb}),e.d(n,"interpolateBuGn",function(){return Kb}),e.d(n,"schemeBuGn",function(){return Jb}),e.d(n,"interpolateBuPu",function(){return nm}),e.d(n,"schemeBuPu",function(){return tm}),e.d(n,"interpolateGnBu",function(){return rm}),e.d(n,"schemeGnBu",function(){return em}),e.d(n,"interpolateOrRd",function(){return om}),e.d(n,"schemeOrRd",function(){return im}),e.d(n,"interpolatePuBuGn",function(){return am}),e.d(n,"schemePuBuGn",function(){return um}),e.d(n,"interpolatePuBu",function(){return fm}),e.d(n,"schemePuBu",function(){return cm}),e.d(n,"interpolatePuRd",function(){return lm}),e.d(n,"schemePuRd",function(){return sm}),e.d(n,"interpolateRdPu",function(){return dm}),e.d(n,"schemeRdPu",function(){return hm}),e.d(n,"interpolateYlGnBu",function(){return gm}),e.d(n,"schemeYlGnBu",function(){return pm}),e.d(n,"interpolateYlGn",function(){return ym}),e.d(n,"schemeYlGn",function(){return vm}),e.d(n,"interpolateYlOrBr",function(){return mm}),e.d(n,"schemeYlOrBr",function(){return bm}),e.d(n,"interpolateYlOrRd",function(){return xm}),e.d(n,"schemeYlOrRd",function(){return _m}),e.d(n,"interpolateBlues",function(){return Mm}),e.d(n,"schemeBlues",function(){return wm}),e.d(n,"interpolateGreens",function(){return Nm}),e.d(n,"schemeGreens",function(){return km}),e.d(n,"interpolateGreys",function(){return Tm}),e.d(n,"schemeGreys",function(){return Sm}),e.d(n,"interpolatePurples",function(){return Cm}),e.d(n,"schemePurples",function(){return Am}),e.d(n,"interpolateReds",function(){return Lm}),e.d(n,"schemeReds",function(){return Em}),e.d(n,"interpolateOranges",function(){return Dm}),e.d(n,"schemeOranges",function(){return Rm}),e.d(n,"interpolateCubehelixDefault",function(){return Pm}),e.d(n,"interpolateRainbow",function(){return qm}),e.d(n,"interpolateWarm",function(){return zm}),e.d(n,"interpolateCool",function(){return Om}),e.d(n,"interpolateSinebow",function(){return jm}),e.d(n,"interpolateViridis",function(){return Hm}),e.d(n,"interpolateMagma",function(){return Vm}),e.d(n,"interpolateInferno",function(){return Xm}),e.d(n,"interpolatePlasma",function(){return Gm}),e.d(n,"create",function(){return en}),e.d(n,"creator",function(){return vt}),e.d(n,"local",function(){return on}),e.d(n,"matcher",function(){return xt}),e.d(n,"mouse",function(){return fn}),e.d(n,"namespace",function(){return gt}),e.d(n,"namespaces",function(){return pt}),e.d(n,"clientPoint",function(){return cn}),e.d(n,"select",function(){return nn}),e.d(n,"selectAll",function(){return sn}),e.d(n,"selection",function(){return tn}),e.d(n,"selector",function(){return bt}),e.d(n,"selectorAll",function(){return _t}),e.d(n,"style",function(){return Ct}),e.d(n,"touch",function(){return ln}),e.d(n,"touches",function(){return hn}),e.d(n,"window",function(){return At}),e.d(n,"event",function(){return Ht}),e.d(n,"customEvent",function(){return $t}),e.d(n,"arc",function(){return d_}),e.d(n,"area",function(){return m_}),e.d(n,"line",function(){return b_}),e.d(n,"pie",function(){return w_}),e.d(n,"areaRadial",function(){return A_}),e.d(n,"radialArea",function(){return A_}),e.d(n,"lineRadial",function(){return T_}),e.d(n,"radialLine",function(){return T_}),e.d(n,"pointRadial",function(){return C_}),e.d(n,"linkHorizontal",function(){return U_}),e.d(n,"linkVertical",function(){return q_}),e.d(n,"linkRadial",function(){return I_}),e.d(n,"symbol",function(){return rx}),e.d(n,"symbols",function(){return ex}),e.d(n,"symbolCircle",function(){return B_}),e.d(n,"symbolCross",function(){return F_}),e.d(n,"symbolDiamond",function(){return H_}),e.d(n,"symbolSquare",function(){return $_}),e.d(n,"symbolStar",function(){return W_}),e.d(n,"symbolTriangle",function(){return Q_}),e.d(n,"symbolWye",function(){return nx}),e.d(n,"curveBasisClosed",function(){return fx}),e.d(n,"curveBasisOpen",function(){return lx}),e.d(n,"curveBasis",function(){return ax}),e.d(n,"curveBundle",function(){return dx}),e.d(n,"curveCardinalClosed",function(){return bx}),e.d(n,"curveCardinalOpen",function(){return _x}),e.d(n,"curveCardinal",function(){return vx}),e.d(n,"curveCatmullRomClosed",function(){return Nx}),e.d(n,"curveCatmullRomOpen",function(){return Tx}),e.d(n,"curveCatmullRom",function(){return Mx}),e.d(n,"curveLinearClosed",function(){return Cx}),e.d(n,"curveLinear",function(){return g_}),e.d(n,"curveMonotoneX",function(){return Ux}),e.d(n,"curveMonotoneY",function(){return qx}),e.d(n,"curveNatural",function(){return Fx}),e.d(n,"curveStep",function(){return Yx}),e.d(n,"curveStepAfter",function(){return Vx}),e.d(n,"curveStepBefore",function(){return Hx}),e.d(n,"stack",function(){return $x}),e.d(n,"stackOffsetExpand",function(){return Zx}),e.d(n,"stackOffsetDiverging",function(){return Qx}),e.d(n,"stackOffsetNone",function(){return Xx}),e.d(n,"stackOffsetSilhouette",function(){return Jx}),e.d(n,"stackOffsetWiggle",function(){return Kx}),e.d(n,"stackOrderAppearance",function(){return tw}),e.d(n,"stackOrderAscending",function(){return ew}),e.d(n,"stackOrderDescending",function(){return iw}),e.d(n,"stackOrderInsideOut",function(){return ow}),e.d(n,"stackOrderNone",function(){return Gx}),e.d(n,"stackOrderReverse",function(){return uw}),e.d(n,"timeInterval",function(){return pg}),e.d(n,"timeMillisecond",function(){return vg}),e.d(n,"timeMilliseconds",function(){return yg}),e.d(n,"utcMillisecond",function(){return vg}),e.d(n,"utcMilliseconds",function(){return yg}),e.d(n,"timeSecond",function(){return xg}),e.d(n,"timeSeconds",function(){return wg}),e.d(n,"utcSecond",function(){return xg}),e.d(n,"utcSeconds",function(){return wg}),e.d(n,"timeMinute",function(){return kg}),e.d(n,"timeMinutes",function(){return Ng}),e.d(n,"timeHour",function(){return Tg}),e.d(n,"timeHours",function(){return Ag}),e.d(n,"timeDay",function(){return Eg}),e.d(n,"timeDays",function(){return Lg}),e.d(n,"timeWeek",function(){return Dg}),e.d(n,"timeWeeks",function(){return Bg}),e.d(n,"timeSunday",function(){return Dg}),e.d(n,"timeSundays",function(){return Bg}),e.d(n,"timeMonday",function(){return Pg}),e.d(n,"timeMondays",function(){return Fg}),e.d(n,"timeTuesday",function(){return zg}),e.d(n,"timeTuesdays",function(){return jg}),e.d(n,"timeWednesday",function(){return Og}),e.d(n,"timeWednesdays",function(){return Yg}),e.d(n,"timeThursday",function(){return Ug}),e.d(n,"timeThursdays",function(){return Hg}),e.d(n,"timeFriday",function(){return qg}),e.d(n,"timeFridays",function(){return Vg}),e.d(n,"timeSaturday",function(){return Ig}),e.d(n,"timeSaturdays",function(){return Xg}),e.d(n,"timeMonth",function(){return Wg}),e.d(n,"timeMonths",function(){return $g}),e.d(n,"timeYear",function(){return Qg}),e.d(n,"timeYears",function(){return Jg}),e.d(n,"utcMinute",function(){return tv}),e.d(n,"utcMinutes",function(){return nv}),e.d(n,"utcHour",function(){return rv}),e.d(n,"utcHours",function(){return iv}),e.d(n,"utcDay",function(){return uv}),e.d(n,"utcDays",function(){return av}),e.d(n,"utcWeek",function(){return fv}),e.d(n,"utcWeeks",function(){return vv}),e.d(n,"utcSunday",function(){return fv}),e.d(n,"utcSundays",function(){return vv}),e.d(n,"utcMonday",function(){return sv}),e.d(n,"utcMondays",function(){return yv}),e.d(n,"utcTuesday",function(){return lv}),e.d(n,"utcTuesdays",function(){return bv}),e.d(n,"utcWednesday",function(){return hv}),e.d(n,"utcWednesdays",function(){return mv}),e.d(n,"utcThursday",function(){return dv}),e.d(n,"utcThursdays",function(){return _v}),e.d(n,"utcFriday",function(){return pv}),e.d(n,"utcFridays",function(){return xv}),e.d(n,"utcSaturday",function(){return gv}),e.d(n,"utcSaturdays",function(){return wv}),e.d(n,"utcMonth",function(){return kv}),e.d(n,"utcMonths",function(){return Nv}),e.d(n,"utcYear",function(){return Tv}),e.d(n,"utcYears",function(){return Av}),e.d(n,"timeFormatDefaultLocale",function(){return Wy}),e.d(n,"timeFormat",function(){return Pv}),e.d(n,"timeParse",function(){return zv}),e.d(n,"utcFormat",function(){return Ov}),e.d(n,"utcParse",function(){return Uv}),e.d(n,"timeFormatLocale",function(){return Rv}),e.d(n,"isoFormat",function(){return $y}),e.d(n,"isoParse",function(){return Zy}),e.d(n,"now",function(){return Er}),e.d(n,"timer",function(){return Dr}),e.d(n,"timerFlush",function(){return Pr}),e.d(n,"timeout",function(){return qr}),e.d(n,"interval",function(){return Ir}),e.d(n,"transition",function(){return ui}),e.d(n,"active",function(){return Ki}),e.d(n,"interrupt",function(){return Kr}),e.d(n,"voronoi",function(){return Vw}),e.d(n,"zoom",function(){return oM}),e.d(n,"zoomTransform",function(){return Zw}),e.d(n,"zoomIdentity",function(){return $w})},function(t,n,e){"use strict";t.exports=function(t){var n=[];return n.toString=function(){return this.map(function(n){var e=function(t,n){var e=t[1]||"",r=t[3];if(!r)return e;if(n&&"function"==typeof btoa){var i=(u=r,"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(u))))+" */"),o=r.sources.map(function(t){return"/*# sourceURL="+r.sourceRoot+t+" */"});return[e].concat(o).concat([i]).join("\n")}var u;return[e].join("\n")}(n,t);return n[2]?"@media "+n[2]+"{"+e+"}":e}).join("")},n.i=function(t,e){"string"==typeof t&&(t=[[null,t,""]]);for(var r={},i=0;i<this.length;i++){var o=this[i][0];null!=o&&(r[o]=!0)}for(i=0;i<t.length;i++){var u=t[i];null!=u[0]&&r[u[0]]||(e&&!u[2]?u[2]=e:e&&(u[2]="("+u[2]+") and ("+e+")"),n.push(u))}},n}},function(t,n,e){"use strict";n.__esModule=!0;var r=function(){function t(){this.vertices=[],this.nodesTable={},this.edgesTable={},this.results=[],this.debug=!1}return t.prototype.init=function(t,n){var e=this;this.vertices=[],t.forEach(function(t){e.nodesTable[t.id]=t,e.vertices.push(t.id),e.initNode(t)});for(var r=0,i=n;r<i.length;r++){var o=i[r];this.edgesTable[o.source.id+"-"+o.target.id]=o,this.initLink(o)}},t.prototype.log=function(){!0===this.debug&&this.writeLog()},t}();n.default=r},function(t,n,e){var r;!function(i,o){if(t.exports){var u=e(0);t.exports=o(u)}else{try{u=e(0)}catch(t){u=i.d3}u.contextMenu=o(u),void 0===(r=function(){return u.contextMenu}.apply(n,[]))||(t.exports=r)}}(this,function(t){var n={noop:function(){},isFn:function(t){return"function"==typeof t},const:function(t){return function(){return t}},toFactory:function(t,e){return t=void 0===t?e:t,n.isFn(t)?t:n.const(t)}},e=null,r=function(){e&&(t.select(".d3-context-menu").remove(),t.select("body").on("mousedown.d3-context-menu",null),e.boundCloseCallback(),e=null)};return function(i,o){if("close"===i)return r();i=n.toFactory(i);var u=(o=n.isFn(o)?{onOpen:o}:o||{}).onOpen||n.noop,a=o.onClose||n.noop,c=n.toFactory(o.position),f=n.toFactory(o.theme,"d3-context-menu-theme");return function(o,s){if(r(),e={boundCloseCallback:a.bind(this,o,s)},t.selectAll(".d3-context-menu").data([1]).enter().append("div").attr("class","d3-context-menu "+f.bind(this)(o,s)),t.select("body").on("mousedown.d3-context-menu",r),t.selectAll(".d3-context-menu").on("contextmenu",function(){r(),t.event.preventDefault(),t.event.stopPropagation()}).append("ul").call(function e(u,a,c){if(!c)c=0;var f=function(t){return n.toFactory(t).call(a,o,s)};u.selectAll("li").data(function(t){var n=0===c?i:t.children;return f(n)}).enter().append("li").each(function(n){var i=!!f(n.divider),u=!!f(n.disabled),l=!!f(n.children),h=!!n.action,d=i?"<hr>":f(n.title),p=t.select(this).classed("is-divider",i).classed("is-disabled",u).classed("is-header",!l&&!h).classed("is-parent",l).html(d).on("click",function(){!u&&h&&(n.action.call(a,o,s),r())});if(l){var g=p.append("ul").classed("is-children",!0);e(g,a,++c)}})},this),!1!==u.bind(this)(o,s)){var l=c.bind(this)(o,s);t.select(".d3-context-menu").style("left",(l?l.left:t.event.pageX-2)+"px").style("top",(l?l.top:t.event.pageY-2)+"px").style("display","block"),t.event.preventDefault(),t.event.stopPropagation()}}}})},function(t,n,e){"use strict";n.__esModule=!0,n.bindEvent=function(t,n,e){var r=["on"+n];r in t?t[r]=e:"attachEvent"in t?t.attachEvent(r,e):t.addEventListener(n,e,!1)},n.extend=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];for(var r=0;r<n.length;r+=1){var i=n[r];for(var o in i)i.hasOwnProperty(o)&&(t[o]=i[o])}return t},n.deepCopy=function t(n,e){for(var r in n||(n={}),e)switch(Object.prototype.toString.call(e[r])){case"[object Object]":n[r]=t(n[r],e[r]);break;case"[object Array]":n[r]=e[r].slice();break;default:n[r]=e[r]}return n},n.findParent=function(t,n){for(var e=t.matches||t.webkitMatchesSelector||t.mozMatchesSelector||t.msMatchesSelector,r=t.parentElement;r&&!e.call(r,n);)r=r.parentElement;return r}},function(t,n,e){(t.exports=e(1)(!1)).push([t.i,'/* Layout\n------------ */\n\n.d3-context-menu {\n\tposition: absolute;\n\tmin-width: 150px;\n\tz-index: 1200;\n}\n\n.d3-context-menu ul,\n.d3-context-menu ul li {\n\tmargin: 0;\n\tpadding: 0;\n}\n\n.d3-context-menu ul {\n\tlist-style-type: none;\n\tcursor: default;\n}\n\n.d3-context-menu ul li {\n\t-webkit-touch-callout: none; /* iOS Safari */\n\t-webkit-user-select: none;   /* Chrome/Safari/Opera */\n\t-khtml-user-select: none;    /* Konqueror */\n\t-moz-user-select: none;      /* Firefox */\n\t-ms-user-select: none;       /* Internet Explorer/Edge */\n\tuser-select: none;\n}\n\n/*\n\tDisabled\n*/\n\n.d3-context-menu ul li.is-disabled,\n.d3-context-menu ul li.is-disabled:hover {\n\tcursor: not-allowed;\n}\n\n/*\n\tDivider\n*/\n\n.d3-context-menu ul li.is-divider {\n\tpadding: 0;\n}\n\n/* Theming\n------------ */\n\n.d3-context-menu-theme {\n\tbackground-color: #f2f2f2;\n\tborder-radius: 4px;\n\n\tfont-family: Arial, sans-serif;\n\tfont-size: 14px;\n\tborder: 1px solid #d4d4d4;\n}\n\n.d3-context-menu-theme ul {\n\tmargin: 4px 0;\n}\n\n.d3-context-menu-theme ul li {\n\tpadding: 4px 16px;\n}\n\n.d3-context-menu-theme ul li:hover {\n\tbackground-color: #4677f8;\n\tcolor: #fefefe;\n}\n\n/*\n\tHeader\n*/\n\n.d3-context-menu-theme ul li.is-header,\n.d3-context-menu-theme ul li.is-header:hover {\n\tbackground-color: #f2f2f2;\n\tcolor: #444;\n\tfont-weight: bold;\n\tfont-style: italic;\n}\n\n/*\n\tDisabled\n*/\n\n.d3-context-menu-theme ul li.is-disabled,\n.d3-context-menu-theme ul li.is-disabled:hover {\n\tbackground-color: #f2f2f2;\n\tcolor: #888;\n}\n\n/*\n\tDivider\n*/\n\n.d3-context-menu-theme ul li.is-divider:hover {\n\tbackground-color: #f2f2f2;\n}\n\n.d3-context-menu-theme ul hr {\n\tborder: 0;\n\theight: 0;\n\tborder-top: 1px solid rgba(0, 0, 0, 0.1);\n\tborder-bottom: 1px solid rgba(255, 255, 255, 0.3);\n}\n\n/*\n\tNested Menu\n*/\n.d3-context-menu-theme ul li.is-parent:after {\n\tborder-left: 7px solid transparent;\n\tborder-top: 7px solid red;\n\tcontent: "";\n\theight: 0;\n\tposition: absolute;\n\tright: 8px;\n\ttop: 35%;\n\ttransform: rotate(45deg);\n\twidth: 0;\n}\n\n.d3-context-menu-theme ul li.is-parent {\n\tpadding-right: 20px;\n\tposition: relative;\n}\n\n.d3-context-menu-theme ul.is-children {\n\tbackground-color: #f2f2f2;\n\tborder: 1px solid #d4d4d4;\n\tcolor: black;\n\tdisplay: none;\n\tleft: 100%;\n\tmargin: -5px 0;\n\tpadding: 4px 0;\n\tposition: absolute;\n\ttop: 0;\n\twidth: 100%;\n}\n\n.d3-context-menu-theme li.is-parent:hover > ul.is-children {\n\tdisplay: block;\n}\n',""])},function(t,n,e){var r,i,o={},u=(r=function(){return window&&document&&document.all&&!window.atob},function(){return void 0===i&&(i=r.apply(this,arguments)),i}),a=function(t){var n={};return function(t,e){if("function"==typeof t)return t();if(void 0===n[t]){var r=function(t,n){return n?n.querySelector(t):document.querySelector(t)}.call(this,t,e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}n[t]=r}return n[t]}}(),c=null,f=0,s=[],l=e(11);function h(t,n){for(var e=0;e<t.length;e++){var r=t[e],i=o[r.id];if(i){i.refs++;for(var u=0;u<i.parts.length;u++)i.parts[u](r.parts[u]);for(;u<r.parts.length;u++)i.parts.push(b(r.parts[u],n))}else{var a=[];for(u=0;u<r.parts.length;u++)a.push(b(r.parts[u],n));o[r.id]={id:r.id,refs:1,parts:a}}}}function d(t,n){for(var e=[],r={},i=0;i<t.length;i++){var o=t[i],u=n.base?o[0]+n.base:o[0],a={css:o[1],media:o[2],sourceMap:o[3]};r[u]?r[u].parts.push(a):e.push(r[u]={id:u,parts:[a]})}return e}function p(t,n){var e=a(t.insertInto);if(!e)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var r=s[s.length-1];if("top"===t.insertAt)r?r.nextSibling?e.insertBefore(n,r.nextSibling):e.appendChild(n):e.insertBefore(n,e.firstChild),s.push(n);else if("bottom"===t.insertAt)e.appendChild(n);else{if("object"!=typeof t.insertAt||!t.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var i=a(t.insertAt.before,e);e.insertBefore(n,i)}}function g(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t);var n=s.indexOf(t);n>=0&&s.splice(n,1)}function v(t){var n=document.createElement("style");if(void 0===t.attrs.type&&(t.attrs.type="text/css"),void 0===t.attrs.nonce){var r=function(){0;return e.nc}();r&&(t.attrs.nonce=r)}return y(n,t.attrs),p(t,n),n}function y(t,n){Object.keys(n).forEach(function(e){t.setAttribute(e,n[e])})}function b(t,n){var e,r,i,o;if(n.transform&&t.css){if(!(o="function"==typeof n.transform?n.transform(t.css):n.transform.default(t.css)))return function(){};t.css=o}if(n.singleton){var u=f++;e=c||(c=v(n)),r=x.bind(null,e,u,!1),i=x.bind(null,e,u,!0)}else t.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(e=function(t){var n=document.createElement("link");return void 0===t.attrs.type&&(t.attrs.type="text/css"),t.attrs.rel="stylesheet",y(n,t.attrs),p(t,n),n}(n),r=function(t,n,e){var r=e.css,i=e.sourceMap,o=void 0===n.convertToAbsoluteUrls&&i;(n.convertToAbsoluteUrls||o)&&(r=l(r));i&&(r+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */");var u=new Blob([r],{type:"text/css"}),a=t.href;t.href=URL.createObjectURL(u),a&&URL.revokeObjectURL(a)}.bind(null,e,n),i=function(){g(e),e.href&&URL.revokeObjectURL(e.href)}):(e=v(n),r=function(t,n){var e=n.css,r=n.media;r&&t.setAttribute("media",r);if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}.bind(null,e),i=function(){g(e)});return r(t),function(n){if(n){if(n.css===t.css&&n.media===t.media&&n.sourceMap===t.sourceMap)return;r(t=n)}else i()}}t.exports=function(t,n){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");(n=n||{}).attrs="object"==typeof n.attrs?n.attrs:{},n.singleton||"boolean"==typeof n.singleton||(n.singleton=u()),n.insertInto||(n.insertInto="head"),n.insertAt||(n.insertAt="bottom");var e=d(t,n);return h(e,n),function(t){for(var r=[],i=0;i<e.length;i++){var u=e[i];(a=o[u.id]).refs--,r.push(a)}t&&h(d(t,n),n);for(i=0;i<r.length;i++){var a;if(0===(a=r[i]).refs){for(var c=0;c<a.parts.length;c++)a.parts[c]();delete o[a.id]}}}};var m,_=(m=[],function(t,n){return m[t]=n,m.filter(Boolean).join("\n")});function x(t,n,e,r){var i=e?"":r.css;if(t.styleSheet)t.styleSheet.cssText=_(n,i);else{var o=document.createTextNode(i),u=t.childNodes;u[n]&&t.removeChild(u[n]),u.length?t.insertBefore(o,u[n]):t.appendChild(o)}}},function(t,n,e){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(t){for(var n,e=1,r=arguments.length;e<r;e++)for(var i in n=arguments[e])Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t}).apply(this,arguments)};n.__esModule=!0;var i,o=e(4),u=e(8),a=e(0),c=e(3),f=e(9),s=e(12),l=e(13),h=e(14),d=e(15);console.warn(f.default),function(t){t[t.normal=0]="normal",t[t.highlight=1]="highlight",t[t.resume=2]="resume"}(i||(i={}));var p,g,v=function(){function t(t,n,e){var r=n.nodes,u=n.links;void 0===e&&(e={}),this.draging=!1,this.initScale=1,this.dragLink={line:null,start:null,end:null},this.d3Dom={gNode:null,nodeWrapper:null,circleNode:null,imgNode:null,nodeText:null,line:null,marker:null,exceptionNodes:null},this.status=0,this.config={},this.noop=function(){},this.nodeRadius={},this.data={nodes:[],links:[]},this.curScale=1,this.highlightMode=i.normal,this.highlightNodes=[],this.highlightNodesMap={},this.highlightLinks=[],this.tranfs={x:0,y:0,k:1},a.selection.prototype.moveUp=function(){return this.each(function(){this.parentNode.appendChild(this)})},this.selector=t;var c={click:this.noop,dblclick:this.noop,contextMenu:[]},f={click:this.noop,dblclick:this.noop};this.config=o.deepCopy({height:document.documentElement.clientHeight,width:document.documentElement.clientWidth,linkDistance:150,chargeValue:-1500,puffUrl:"http://172.21.2.211:8081/chart/puff",enableZoom:!0,nodeConf:{default:{textShow:!1,textSize:12,textColor:"#000",min:13,max:100,step:1,fillColor:"rgb(255, 76, 10)",strokeWidth:0}},lineConf:{default:{strokeColor:"rgb(128, 194, 216)"}},events:{node:c,link:f}},e),this.initSimulation(r,u),this.data={nodes:r,links:u},this.refresh()}return t.prototype.updateWH=function(t,n){this.config.width=t,this.config.height=n,this.svg.attr("width",t),this.svg.attr("height",n),this.simulation.force("center",a.forceCenter(this.config.width/2,this.config.height/2))},t.prototype.setConfig=function(t){this.config=o.deepCopy(o.deepCopy({},this.config),t)},t.prototype.redraw=function(t){this.setConfig(t),this.svg.remove(),this.initSimulation(this.data.nodes,this.data.links),this.refresh()},t.prototype.showNodeText=function(t){this.config.nodeConf.default.textShow!=t&&(this.config.nodeConf.default.textShow=t,this.refresh())},t.prototype.setStatus=function(t){this.status=t,this.svg.style("cursor",1==this.status?"crosshair":"auto")},t.prototype.getStatus=function(){return this.status},t.prototype.zoomFn=function(){if(!1!==this.config.enableZoom){var t=a.event.transform;this.tranfs=t,this.curScale=t.k*this.initScale,this.container.attr("transform","translate("+t.x+","+t.y+")scale("+this.curScale+")")}},t.prototype.transform=function(t,n,e){this.curScale=e*this.initScale,this.tranfs.k=this.curScale,this.container.attr("transform","translate("+t+","+n+")scale("+this.curScale+")")},t.prototype.getRadius=function(t){return this.nodeRadius[t.id]},t.prototype.lineStyle=function(t,n){if(t.style&&null!=t.style[n])return t.style[n];var e=this.config.lineConf[t.type];return!e||"strokeDasharray"!=n&&null==e[n]?this.config.lineConf.default[n]:e[n]},t.prototype.nodeStyle=function(t,n){if(t.style&&null!=t.style[n])return t.style[n];var e=this.config.nodeConf[t.type];return e&&null!=e[n]?e[n]:this.config.nodeConf.default[n]},t.prototype.initSimulation=function(t,n){var e=this;this.simulation=a.forceSimulation(t).force("link",a.forceLink(n).id(function(t){return t.id}).distance(function(){return e.config.linkDistance})).force("charge",a.forceManyBody().strength(function(){return e.config.chargeValue})).force("center",a.forceCenter(this.config.width/2,this.config.height/2)).force("x",a.forceX(this.config.width/2)).force("y",a.forceY(this.config.height/2));var i=a.zoom().scaleExtent([.25,3]).on("zoom",this.zoomFn.bind(this));this.zoom=i,this.drag=a.drag().on("start",function(t){e.draging=!0,a.event.active||e.simulation.alphaTarget(.3).restart(),t.fx=t.x,t.fy=t.y}).on("drag",function(t){t.fx=a.event.x,t.fy=a.event.y}).on("end",function(t){e.draging=!1,a.event.active||e.simulation.alphaTarget(0),t.fx=null,t.fy=null});var o=this,u=a.select(this.selector).append("svg").attr("width",this.config.width).attr("height",this.config.height).call(i).on("dblclick.zoom",null).on("click",function(){if(c("close"),1==o.status&&o.config.events.node.add){var t=a.mouse(this);o.config.events.node.add().then(function(n){o.addNode(r({},n,{x:(t[0]-o.tranfs.x)/o.tranfs.k,y:(t[1]-o.tranfs.y)/o.tranfs.k})),o.refresh()})}2==o.status&&o.dragLink.start&&(o.dragLink.line.attr("stroke-width",0).style("marker-end",""),o.dragLink.start=null,o.dragLink.end=null)}).on("mousemove",function(){2==o.status&&o.dragLink.start&&o.dragLink.line.attr("d","M"+o.dragLink.start.x+","+o.dragLink.start.y+"L"+(a.mouse(this)[0]-o.tranfs.x)/o.tranfs.k+","+(a.mouse(this)[1]-o.tranfs.y)/o.tranfs.k)});this.svg=u,this.container=u.append("g").attr("transform","scale("+this.initScale+")").attr("class","container"),this.container.append("svg:defs").append("svg:marker").attr("id","link-arrow").attr("viewBox","0 -5 10 10").attr("refX",6).attr("markerWidth",3).attr("markerHeight",3).attr("orient","auto").append("svg:path").attr("d","M0,-5L10,0L0,5").attr("fill","#000"),this.dragLink.line=this.container.append("svg:path").attr("class","dragline hidden").attr("d","M0,0L0,0").attr("stroke","#000").attr("stroke-width",0),this.tagClass=new d.default(this.container,this,this.config.events.node.deleteTag)},t.prototype.addNode=function(t){Array.isArray(t)||(t=[t]),this.data.nodes=this.data.nodes.concat(t),this.simulation.nodes(this.data.nodes)},t.prototype.updateNodeInfo=function(t,n){if(n){var e=this.data.nodes.findIndex(function(n){return n.id===t});for(var r in n)this.data.nodes[e][r]=n[r];this.refresh()}},t.prototype.nodes=function(){return this.data.nodes},t.prototype.addLink=function(t){var n=this;Array.isArray(t)||(t=[t]),this.data.links=this.data.links.concat(t),this.simulation.force("link",a.forceLink(this.data.links).id(function(t){return t.id}).distance(function(){return n.config.linkDistance}))},t.prototype.updateLinkInfo=function(t,n){if(n){var e=this.data.links.findIndex(function(n){return n.id===t});for(var r in n)this.data.links[e][r]=n[r];this.refresh()}},t.prototype.links=function(){return this.data.links},t.prototype.removeNode=function(t){var n=this.data.links,e=this.data.nodes,r=e.indexOf(t);e.splice(r,1);for(var i=n.filter(function(n){return n.source===t||n.target===t}),o=0,a=i;o<a.length;o++){var c=a[o],f=n.indexOf(c);n.splice(f,1)}return Object.keys(u.linkMap).forEach(function(n){(n.startsWith(t.id+"-")||n.endsWith("-"+t.id))&&delete u.linkMap[n]}),i},t.prototype.removeLink=function(t){return this.data.links=this.data.links.filter(function(n){return n.id!=t.id}),Object.keys(u.linkMap).forEach(function(n){(n.startsWith(t.source.id+"-")||n.endsWith("-"+t.target.id))&&delete u.linkMap[n]}),this.refresh(),!0},t.prototype.batchUpdate=function(t,n){var e=this;this.data={nodes:t,links:n},this.simulation.nodes(t),this.simulation.force("link",a.forceLink(n).id(function(t){return t.id}).distance(function(){return e.config.linkDistance})),this.clearHighlightData()},t.prototype.storeCaculateCache=function(t,n){var e=this;this.nodeRadius={};for(var r=0,i=t;r<i.length;r++){var o=i[r];this.nodeRadius[o.id]=this.nodeStyle(o,"min")}for(var a=function(t){e.nodeRadius[t.id]=e.nodeRadius[t.id]+e.nodeStyle(t,"step"),e.nodeRadius[t.id]>e.nodeStyle(t,"max")&&(e.nodeRadius[t.id]=e.nodeStyle(t,"max"))},c=0,f=n;c<f.length;c++){var s=f[c];u.linkMap[s.source.id+"-"+s.target.id]=!0,a(s.source),a(s.target)}},t.prototype.findExceptionNodes=function(){return this.highlightNodes},t.prototype.findExceptionLinks=function(){return this.highlightLinks},t.prototype.findNormalLinks=function(){var t=this;return this.data.links.filter(function(n){return-1===t.highlightLinks.indexOf(n)})},t.prototype.search=function(t,n){if(void 0===n&&(n=null),t){var e=null;if(this.d3Dom.gNode.filter(function(n){n.title!=t||e||(e=n)}),e){if(!n)return this.moveToCenter(e,!1),void this.config.events.node.click(e);var r=null;this.d3Dom.line.filter(function(t){t.type!=n||r||t.source.id!=e.id&&t.target.id!=e.id||(r=t)}),r?(this.moveToCenter(r,!1,!0),this.config.events.link.click(r)):(this.moveToCenter(e,!1),this.config.events.node.click(e))}}},t.prototype.moveToCenter=function(t,n,e,r){void 0===e&&(e=!1),void 0===r&&(r=3);var i=t.x,o=t.y;e&&(i=(t.source.x+t.target.x)/2,o=(t.source.y+t.target.y)/2);var u=this.config.width/2-i*r,c=this.config.height/2-o*r;this.tranfs={x:u,y:c,k:r},this.container.transition().duration(2e3).attr("transform","translate("+u+","+c+")scale("+r+")"),this.svg.transition().duration(2e3).call(this.zoom.transform,a.zoomIdentity.translate(u,c).scale(r))},t.prototype.addTag=function(t,n){if(this.tagClass.hasTagById(t,n.id))return!1;this.tagClass.add(this.data.nodes,t,n);return this.updateNodes(this.data.nodes),!0},t.prototype.updateNodes=function(t){this.data.nodes=t,this.simulation.nodes(this.data.nodes),this.refresh()},t.prototype.refresh=function(){var t=this;if(this.storeCaculateCache(this.data.nodes,this.data.links),this.drawMarker(),this.drawExceptionGraph(),this.drawLine(),this.drawNode(),this.tagClass.draw(),this.drawNodeText(),this.d3Dom.exceptionNodes=this.container.selectAll(".exception-node"),this.d3Dom.marker=this.container.selectAll(".line-marker"),this.d3Dom.line=this.container.selectAll(".line-path,.inpath,.outpath"),this.d3Dom.gNode=this.container.selectAll(".g-node"),this.d3Dom.nodeWrapper=this.container.selectAll(".node-wrapper"),this.d3Dom.circleNode=this.container.selectAll(".circle-node"),this.d3Dom.imgNode=this.container.selectAll(".image-node"),this.d3Dom.nodeText=this.container.selectAll(".node-text"),this.highlightMode===i.highlight){var n={};this.highlightNodes.forEach(function(t){n[t.id]=!0,t.tags&&t.tags.forEach(function(t){return n[t.id]=!0})}),this.highlightLinks.forEach(function(t){n[t.id]=!0}),this.container.selectAll("*").style("opacity",function(t){return t?!0===n[t.id]?1:.3:null}),this.d3Dom.nodeText.style("opacity",function(e){return n[e.id]?1:t.config.nodeConf.default.textShow?.3:0}),this.tagClass.setTextOpacity(0)}else this.highlightMode==i.resume&&(this.highlightMode=i.normal,this.container.selectAll("*").style("opacity",1),this.d3Dom.nodeText.style("opacity",function(n){return t.config.nodeConf.default.textShow?1:0}),this.tagClass.setIconOpacity(1),this.tagClass.setTextOpacity(0));this.container.selectAll(".inpath").moveUp(),this.d3Dom.exceptionNodes.moveUp(),this.d3Dom.nodeText.moveUp(),this.d3Dom.gNode.moveUp(),this.bindNodeEvent(),this.tagClass.bindEvent(),this.simulation.on("tick",this.tick.bind(this)),this.simulation.alpha(1),this.simulation.restart()},t.prototype.drawMarker=function(){var t=this,n=this.container.selectAll(".mark").data(this.data.links,function(t){return t.id});return n.exit().remove(),n.attr("refX",function(n){return t.getRadius(n.target)+(n.target.tags&&n.target.tags.length||t.highlightNodesMap[n.target.id]?15:10)}),n.select("path").attr("fill",function(n){return t.lineStyle(n,"strokeColor")}),n.enter().append("svg:marker").attr("class","mark").attr("id",function(t){return"m-"+t.id}).attr("markerUnits","userSpaceOnUse").attr("viewBox","0 -5 10 10").attr("refX",function(n){return t.getRadius(n.target)+(n.target.tags&&n.target.tags.length||t.highlightNodesMap[n.target.id]?15:10)}).attr("refY",0).attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").attr("stroke-width",2).append("svg:path").attr("d","M2,0 L0,-5 L9,0 L0,5 M2,0 L0,-5").attr("fill",function(n){return t.lineStyle(n,"strokeColor")}).on("dblclick",function(n){t.moveToCenter(n.target,!0)}).on("contextmenu",c(this.config.events.link.contextMenu,{theme:function(){return"d3-context-menu-theme"},position:function(t,n){return{left:a.event.pageX+10,top:a.event.pageY-10}}}))},t.prototype.drawExceptionGraph=function(){var t=this,n=this.findExceptionLinks(),e=this.container.selectAll("inmark").data(n,function(t){return t.id});e.exit().remove(),e.enter().append("svg:marker").attr("class","inmark").attr("id",function(t){return"im-"+t.id}).attr("markerUnits","userSpaceOnUse").attr("viewBox","0 -5 10 10").attr("refX",function(n){return t.getRadius(n.target)+(n.target.tags&&n.target.tags.length||t.highlightNodesMap[n.target.id]?15:10)}).attr("refY",0).attr("markerWidth",12).attr("markerHeight",12).attr("orient","auto").attr("stroke-width",2).append("svg:path").attr("d","M3,0 L1,-4 L6,0 L1,4 M3,0 L1,-4").attr("fill","white");var r=this.container.selectAll(".outpath").data(n,function(t){return t.id});r.exit().remove(),r.enter().append("path").attr("class","outpath").style("cursor","pointer").style("stroke-width",function(t){return 4}).attr("marker-end",function(t){return"url(#m-"+t.id+")"}).attr("d",function(t){return u.genLinkPath(t)}).attr("id",function(t){return"outpath-"+t.id}).style("stroke",function(n){return t.lineStyle(n,"strokeColor")}).on("dblclick",function(n){t.moveToCenter(n,!0,!0)});var i=this.container.selectAll(".inpath").data(n,function(t){return t.id});i.exit().remove(),i.enter().append("path").attr("class","inpath").style("cursor","pointer").style("stroke-width",function(t){return 2}).attr("marker-end",function(t){return"url(#im-"+t.id+")"}).attr("d",function(t){return u.genLinkPath(t)}).attr("id",function(t){return"inpath-"+t.id}).style("stroke","#ffffff").on("dblclick",function(n){t.moveToCenter(n,!0,!0)});var o=this.container.selectAll(".exception-node").data(this.findExceptionNodes(),function(t){return t.id});o.exit().remove(),o.attr("width",function(n){return 2*t.getRadius(n)+20}).attr("height",function(n){return 2*t.getRadius(n)+20}),o.enter().insert("image").style("cursor","pointer").attr("class","exception-node").attr("width",function(n){return 2*t.getRadius(n)+20}).attr("height",function(n){return 2*t.getRadius(n)+20}).attr("xlink:href",function(n){return"http://172.21.2.211:8081/chart/puff?color="+encodeURIComponent(t.nodeStyle(n,"textColor"))})},t.prototype.drawLine=function(){var t=this,n=this.container.selectAll(".line-path").data(this.findNormalLinks(),function(t){return t.id});n.style("stroke-dasharray",function(n){return t.lineStyle(n,"strokeDasharray")}).style("stroke",function(n){return t.lineStyle(n,"strokeColor")}),n.exit().remove(),n.enter().append("path").attr("class","line-path").style("cursor","pointer").on("dblclick",this.config.events.link.dblclick).on("click",this.config.events.link.click).style("stroke-width",function(t){return t&&t.weight?t.weight:1}).style("stroke-dasharray",function(n){return t.lineStyle(n,"strokeDasharray")}).attr("marker-end",function(t){return"url(#m-"+t.id+")"}).attr("d",function(t){return u.genLinkPath(t)}).attr("id",function(t){return"link-"+t.id}).style("stroke",function(n){return t.lineStyle(n,"strokeColor")}).on("dblclick",function(n){t.moveToCenter(n,!0,!0)}).on("contextmenu",c(this.config.events.link.contextMenu,{theme:function(){return"d3-context-menu-theme"},position:function(t,n){return{left:a.event.pageX+10,top:a.event.pageY-10}}}))},t.prototype.drawNode=function(){var t=this,n=this.container.selectAll(".g-node").data(this.data.nodes,function(t){return t.id});n.exit().remove(),n.selectAll(".node-wrapper").style("display",function(t){return t.tags&&t.tags.length?"block":"none"}).style("fill",function(n){return t.highlightNodes.indexOf(n)>-1?"none":"#fff"}),n.selectAll(".circle-node").attr("r",function(n){return t.getRadius(n)}).style("fill",function(n){return t.nodeStyle(n,"fillColor")}).style("stroke",function(n){return t.nodeStyle(n,"strokeColor")}).style("stroke-width",function(n){return t.nodeStyle(n,"strokeWidth")}),n.selectAll(".node-wrapper").attr("r",function(n){return t.getRadius(n)+10});var e=n.enter().append("g").attr("class","g-node");e.append("circle").attr("class","node-wrapper").attr("r",function(n){return t.getRadius(n)+10}).style("cursor","pointer").style("fill","#fff").style("stroke",function(n){return t.nodeStyle(n,"textColor")}).style("display",function(t){return t.tags&&t.tags.length?"block":"none"}),e.append("circle").attr("class","node circle-node").attr("id",function(t){return"node-circle-"+t.id}).attr("r",function(n){return t.getRadius(n)}).style("cursor","pointer").style("fill",function(n){return t.nodeStyle(n,"fillColor")}).style("stroke",function(n){return t.nodeStyle(n,"strokeColor")}).style("stroke-width",function(n){return t.nodeStyle(n,"strokeWidth")});var r=this.container.selectAll(".g-node").filter(function(n){return!!t.nodeStyle(n,"url")}).selectAll(".image-node").data(function(t){return[t]},function(t){return t.id});r.exit().remove(),r.attr("width",function(n){return 1.2*t.getRadius(n)}).attr("height",function(n){return 1.2*t.getRadius(n)}).attr("alt",function(t){return t.title?t.title:""}).attr("xlink:href",function(n){return t.nodeStyle(n,"url")}),r.enter().append("image").style("cursor","pointer").attr("class","node image-node").attr("width",function(n){return 1.2*t.getRadius(n)}).attr("height",function(n){return 1.2*t.getRadius(n)}).attr("alt",function(t){return t.title?t.title:""}).attr("xlink:href",function(n){return t.nodeStyle(n,"url")})},t.prototype.drawNodeText=function(){var t=this,n=this.data.nodes.filter(function(t){return!!t.title}),e=this.container.selectAll(".node-text").data(n,function(t){return t.id});e.exit().remove(),e.text(function(t){return t.title}).style("font-size",function(n){return t.nodeStyle(n,"textSize")}).style("fill",function(n){return t.nodeStyle(n,"textColor")}).attr("dy",function(n){return+t.nodeStyle(n,"textSize")+(n.tags&&n.tags.length||t.highlightNodesMap[n.id]?13:3)}).style("opacity",this.config.nodeConf.default.textShow?1:0),e.enter().append("text").text(function(t){return t.title}).attr("class","node-text").attr("id",function(t){return"node-text-"+t.id}).style("font-size",function(n){return t.nodeStyle(n,"textSize")}).style("font-weight",200).style("fill",function(n){return t.nodeStyle(n,"textColor")}).style("opacity",this.config.nodeConf.default.textShow?1:0).attr("text-anchor","middle").attr("dy",function(n){return+t.nodeStyle(n,"textSize")+(n.tags&&n.tags.length||t.highlightNodesMap[n.id]?13:3)})},t.prototype.bindNodeEvent=function(){var t=this;this.d3Dom.gNode.on("dblclick",function(n){t.moveToCenter(n,!0),t.config.events.node.dblclick()}).on("click",function(n){if(a.event.cancelBubble=!0,2==t.status)if(t.dragLink.start){if(t.dragLink.line.attr("stroke-width",0).style("marker-end",""),t.dragLink.end=n,t.dragLink.start==t.dragLink.end||u.linkMap[t.dragLink.start.id+"-"+t.dragLink.end.id]||u.linkMap[t.dragLink.end.id+"-"+t.dragLink.start.id]||!t.config.events.link.add)return t.dragLink.start=null,void(t.dragLink.end=null);u.linkMap[t.dragLink.start.id+"-"+t.dragLink.end.id]=!0,t.config.events.link.add(t.dragLink.start.id,t.dragLink.end.id).then(function(n){t.addLink(n),t.dragLink.start=null,t.dragLink.end=null,t.refresh()})}else t.dragLink.start=n,t.dragLink.line.attr("stroke-width",3).style("marker-end","url(#link-arrow)").attr("d","M"+n.x+","+n.y+"L"+n.x+","+n.y);else t.config.events.node.click(n)}).on("contextmenu",c(this.config.events.node.contextMenu,{theme:function(){return"d3-context-menu-theme"},position:function(t,n){return{left:a.event.pageX+16,top:a.event.pageY-10}}})).on("mouseenter",function(n){t.draging||t.highlightMode!=i.normal||(u.toggleNode(t.d3Dom.exceptionNodes,n,!0),u.toggleNode(t.d3Dom.gNode,n,!0),u.toggleNodeText(t.d3Dom.nodeText,n,!0,t.config.nodeConf.default.textShow),u.toggleLine(t.d3Dom.line,n,!0),u.toggleMarker(t.d3Dom.marker,n,t.nodeRadius[n.id]+10,!0))}).on("mouseleave",function(n){t.draging||t.highlightMode!=i.normal||(u.toggleNode(t.d3Dom.exceptionNodes,n,!1),u.toggleNode(t.d3Dom.gNode,n,!1),u.toggleNodeText(t.d3Dom.nodeText,n,!1,t.config.nodeConf.default.textShow),u.toggleLine(t.d3Dom.line,n,!1),u.toggleMarker(t.d3Dom.marker,n,t.nodeRadius[n.id]+10,!1))}).call(this.drag)},t.prototype.tick=function(){var t=this;this.d3Dom.circleNode.attr("cx",function(t){return t.x}).attr("cy",function(t){return t.y}),this.d3Dom.nodeWrapper.attr("cx",function(t){return t.x}).attr("cy",function(t){return t.y}),this.d3Dom.imgNode.attr("x",function(n){return n.x-.6*t.nodeRadius[n.id]}).attr("y",function(n){return n.y-.6*t.nodeRadius[n.id]}),this.tagClass.updatePos(),this.d3Dom.exceptionNodes.attr("x",function(n){return n.x-t.nodeRadius[n.id]-10}).attr("y",function(n){return n.y-t.nodeRadius[n.id]-10}),this.d3Dom.line.attr("d",function(t){return u.genLinkPath(t)}),this.d3Dom.nodeText.attr("x",function(t){return t.x}).attr("y",function(n){return n.y+t.nodeRadius[n.id]})},t.prototype.getPaths=function(t,n){var e=new h.default(this.data.nodes,this.data.links);return e.setVerts(t,n),e.debug=!0,e.caculate(),e.results.length&&this.highlight(e.results[0].chosenNodes,e.results[0].chosenLinks),e.results},t.prototype.getCycle=function(){var t=new s.default(this.data.nodes,this.data.links);return t.debug=!0,t.caculate(),t.results.length&&this.highlight(t.results[0].chosenNodes,t.results[0].chosenLinks),t.results},t.prototype.getSpecialNodes=function(){var t=new l.default(this.data.nodes,this.data.links);return t.debug=!0,t.caculate(),this.highlight(t.results,[]),t.results},t.prototype.highlight=function(t,n){this.highlightMode=i.highlight,this.highlightNodes=t,this.highlightLinks=n;for(var e=0,r=this.highlightNodes;e<r.length;e++){var o=r[e];this.highlightNodesMap[o.id]=!0}this.refresh()},t.prototype.HasHightlight=function(){return this.highlightNodes.length>0},t.prototype.clearHighlightData=function(){this.highlightNodes=[],this.highlightNodesMap={},this.highlightLinks=[],this.highlightMode==i.highlight&&(this.highlightMode=i.resume)},t.prototype.setHighlightMode=function(t){return void 0===t&&(t=!0),!(!this.highlightNodes.length&&!this.highlightLinks.length)&&(this.highlightMode=t?i.highlight:i.resume,this.refresh(),!0)},t.prototype.cancelHighlightMode=function(){return!(this.highlightMode!==i.highlight&&!this.highlightNodes.length&&!this.highlightLinks.length)&&(this.highlightMode=i.resume,this.highlightNodes=[],this.highlightNodesMap={},this.highlightLinks=[],this.refresh(),!0)},t}();n.default=v,g=v,"object"==typeof(p=this).exports&&void 0!==p.module?p.module.exports=g:(p=self).RelationChart=g},function(t,n,e){"use strict";n.__esModule=!0;var r={};function i(t,n){return n.source.id==t.id||n.target.id==t.id}function o(t,n){return t.id===n.id||(r[t.id+"-"+n.id]||r[n.id+"-"+t.id])}n.linkMap=r,n.genLinkPath=function(t){var n=t.source.x||0,e=t.target.x||0;return"M"+n+","+(t.source.y||0)+" L"+e+","+(t.target.y||0)},n.getLineAngle=function(t,n,e,r){var i=e-t,o=r-n,u=i/(1|Math.sqrt(Math.pow(i,2)+Math.pow(o,2))),a=Math.acos(u),c=180/(Math.PI/a);return o<0?c=-c:0==o&&i<0&&(c=180),c},n.isLinkLine=i,n.isLinkNode=o,n.getLineTextDx=function(t,n,e){var r=t.source.x||0,i=t.source.y||0,o=t.target.x||0,u=t.target.y||0;return(Math.sqrt(Math.pow(o-r,2)+Math.pow(u-i,2))-n-t.title.length*e)/2+8},n.getLineTextAngle=function(t,n){if(t.target.x<t.source.x){var e=n.x,r=n.y;return"rotate(180 "+(e+n.width/2)+" "+(r+n.height/2)+")"}return"rotate(0)"},n.toggleNode=function(t,n,e){e?t.style("opacity",.1).filter(function(t){return o(n,t)}).style("opacity",1):t.style("opacity",1)},n.toggleNodeText=function(t,n,e,r){e?(t.sort(function(t,e){return t.id===n.id?1:-1}),t.style("opacity",r?.1:0).filter(function(t){return o(n,t)}).style("opacity",1)):t.style("opacity",r?1:0)},n.toggleLine=function(t,n,e){e?t.style("opacity",.1).filter(function(t){return i(n,t)}).style("opacity",1).classed("link-active",!0):t.style("opacity",1).classed("link-active",!1)},n.toggleLineText=function(t,n,e){e?t.style("fill-opacity",function(t){return i(n,t)?1:0}):t.style("fill-opacity","1.0")},n.toggleMarker=function(t,n,e,r){r?t.filter(function(t){return i(n,t)}).style("transform","scale(1.2)"):t.style("transform","scale(1)")},n.round=function(t,n){void 0===n&&(n=2);var e=Math.pow(10,n);return Math.round(t*e)/e}},function(t,n,e){var r=e(10);"string"==typeof r&&(r=[[t.i,r,""]]);var i={hmr:!0,transform:void 0,insertInto:void 0};e(6)(r,i);r.locals&&(t.exports=r.locals)},function(t,n,e){(n=t.exports=e(1)(!1)).i(e(5),""),n.push([t.i,"\n",""])},function(t,n){t.exports=function(t){var n="undefined"!=typeof window&&window.location;if(!n)throw new Error("fixUrls requires window.location");if(!t||"string"!=typeof t)return t;var e=n.protocol+"//"+n.host,r=e+n.pathname.replace(/\/[^\/]*$/,"/");return t.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(t,n){var i,o=n.trim().replace(/^"(.*)"$/,function(t,n){return n}).replace(/^'(.*)'$/,function(t,n){return n});return/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(o)?t:(i=0===o.indexOf("//")?o:0===o.indexOf("/")?e+o:r+o.replace(/^\.\//,""),"url("+JSON.stringify(i)+")")})}},function(t,n,e){"use strict";var r,i=this&&this.__extends||(r=function(t,n){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)n.hasOwnProperty(e)&&(t[e]=n[e])})(t,n)},function(t,n){function e(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)});n.__esModule=!0;var o=function(t){function n(n,e){var r=t.call(this)||this;return r.adj={},r.marked={},r.onStack={},r.cycle=[],r.edgeTo={},t.prototype.init.call(r,n,e),r}return i(n,t),n.prototype.initNode=function(t){this.adj[t.id]=[],this.marked[t.id]=!1},n.prototype.initLink=function(t){this.adj[t.source.id].push(t.target.id)},n.prototype.caculate=function(){for(var t=0,n=this.vertices;t<n.length;t++){var e=n[t];this.marked[e]||this.dfs(e)}},n.prototype.writeLog=function(){var t=this;console.log(this.cycle.map(function(n){return t.nodesTable[n].title}).join("--\x3e"))},n.prototype.appendCycle=function(){var t=this,n=[];this.cycle.reduce(function(e,r){return n.push(t.edgesTable[e+"-"+r]),r}),this.results.push({chosenNodes:this.cycle.map(function(n){return t.nodesTable[n]}),chosenLinks:n})},n.prototype.dfs=function(t){this.onStack[t]=!0,this.marked[t]=!0;for(var n=0,e=this.adj[t];n<e.length;n++){var r=e[n];if(this.cycle.length>0)return;if(this.edgeTo[t]=r,this.marked[r]){if(this.onStack[r]){this.cycle=[r];for(var i=this.edgeTo[r];i&&i!=r;)this.cycle.push(i),i=this.edgeTo[i];i&&this.cycle.push(i),this.log(),this.appendCycle(),this.cycle=[]}}else this.dfs(r)}this.onStack[t]=!1},n}(e(2).default);n.default=o},function(t,n,e){"use strict";var r,i=this&&this.__extends||(r=function(t,n){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)n.hasOwnProperty(e)&&(t[e]=n[e])})(t,n)},function(t,n){function e(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)});n.__esModule=!0;var o=function(t){function n(n,e){var r=t.call(this)||this;return r.inDegree={},t.prototype.init.call(r,n,e),r}return i(n,t),n.prototype.initNode=function(t){this.inDegree[t.id]=0},n.prototype.initLink=function(t){this.inDegree[t.target.id]=this.inDegree[t.target.id]+1},n.prototype.caculate=function(){var t=this;this.vertices.filter(function(n){return 0===t.inDegree[n]}).map(function(n){t.results.push(t.nodesTable[n])}),this.log()},n.prototype.writeLog=function(){console.log(this.results.map(function(t){return t.title}).join("   "))},n}(e(2).default);n.default=o},function(t,n,e){"use strict";var r,i=this&&this.__extends||(r=function(t,n){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,n){t.__proto__=n}||function(t,n){for(var e in n)n.hasOwnProperty(e)&&(t[e]=n[e])})(t,n)},function(t,n){function e(){this.constructor=t}r(t,n),t.prototype=null===n?Object.create(n):(e.prototype=n.prototype,new e)});n.__esModule=!0;var o=function(t){function n(n,e){var r=t.call(this)||this;return r.adj={},r.edgeFrom={},r.s=new Set,r.u=new Set,r.distances={},t.prototype.init.call(r,n,e),r}return i(n,t),n.prototype.setVerts=function(t,n){this.startVert=t,this.endVert=n},n.prototype.initNode=function(t){this.distances[t.id]=Number.MAX_VALUE,this.adj[t.id]=[]},n.prototype.initLink=function(t){this.adj[t.source.id].push(t.target.id)},n.prototype.caculate=function(){var t=this;this.s.clear(),this.u.clear(),this.distances[this.startVert]=0,this.s.add(this.startVert);for(var n=0,e=this.vertices;n<e.length;n++){var r=e[n];r!=this.startVert&&this.u.add(r)}if(this.traverse(this.startVert),this.log(),this.endVert!==this.startVert&&null!=this.edgeFrom[this.endVert]){for(var i=this.endVert,o=[this.nodesTable[i]],u=[];i=this.edgeFrom[i];)o.push(this.nodesTable[i]);o.reverse(),o.reduce(function(n,e){return u.push(t.edgesTable[n.id+"-"+e.id]),e}),this.results.push({chosenNodes:o,chosenLinks:u})}},n.prototype.writeLog=function(){console.log("查询开始节点："+this.nodesTable[this.startVert].title+"，结束节点："+this.nodesTable[this.endVert].title);for(var t=0,n=this.vertices;t<n.length;t++){var e=n[t];if(e!==this.startVert)if(null!=this.edgeFrom[e]){for(var r=e,i=[this.nodesTable[r].title];r=this.edgeFrom[r];)i.push(this.nodesTable[r].title);i.reverse(),console.log(i.join("--\x3e")+"，距离是"+this.distances[e])}else console.warn(this.nodesTable[this.startVert].title+"无法到达"+this.nodesTable[e].title)}},n.prototype.traverse=function(t){for(var n=0,e=this.adj[t];n<e.length;n++){var r=e[n],i=this.distances[t]+1;this.distances[r]>i&&(this.distances[r]=i,this.edgeFrom[r]=t),this.s.has(r)||(this.u.delete(r),this.s.add(r),this.traverse(r))}},n}(e(2).default);n.default=o},function(t,n,e){"use strict";var r=this&&this.__awaiter||function(t,n,e,r){return new(e||(e=Promise))(function(i,o){function u(t){try{c(r.next(t))}catch(t){o(t)}}function a(t){try{c(r.throw(t))}catch(t){o(t)}}function c(t){t.done?i(t.value):new e(function(n){n(t.value)}).then(u,a)}c((r=r.apply(t,n||[])).next())})},i=this&&this.__generator||function(t,n){var e,r,i,o,u={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(e)throw new TypeError("Generator is already executing.");for(;u;)try{if(e=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!(i=(i=u.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){u=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){u.label=o[1];break}if(6===o[0]&&u.label<i[1]){u.label=i[1],i=o;break}if(i&&u.label<i[2]){u.label=i[2],u.ops.push(o);break}i[2]&&u.ops.pop(),u.trys.pop();continue}o=n.call(t,u)}catch(t){o=[6,t],r=0}finally{e=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}};n.__esModule=!0;var o=e(3),u=e(0),a=function(){function t(t,n,e){this.container=t,this.chart=n,this.deleteFn=e}return t.prototype.delete=function(t,n,e){return r(this,void 0,void 0,function(){var r;return i(this,function(i){switch(i.label){case 0:return r=!0,this.deleteFn?[4,this.deleteFn(n,e)]:[3,2];case 1:r=i.sent(),i.label=2;case 2:return r?[2,t.map(function(t){return t.id==n.id&&t.tags&&(t.tags=t.tags.filter(function(t){return t.id!=e})),t})]:[2,!1]}})})},t.prototype.add=function(t,n,e){return t.map(function(t){return t.id==n.id&&(t.tags?t.tags.push(e):t.tags=[e]),t})},t.prototype.hasTagById=function(t,n){return!!this.hasTags(t)&&!!t.tags.find(function(t){return t.id==n})},t.prototype.draw=function(){var t=this.container.selectAll(".g-node").filter(function(t){return!!t.tags}).selectAll(".node-tag-icon").data(function(t){return t.tags},function(t){return t.id});t.exit().remove(),t.property("index",function(t,n){return n}),t.enter().append("image").style("cursor","pointer").attr("class","node-tag node-tag-icon").attr("width",12).attr("height",12).attr("alt",function(t){return t.name}).attr("xlink:href",function(t){return t.icon}).property("index",function(t,n){return n});var n=this.container.selectAll(".g-node").filter(function(t){return!!t.tags}).selectAll(".node-tag-text").data(function(t){return t.tags},function(t){return t.id});n.exit().remove(),n.property("index",function(t,n){return n}),n.enter().append("text").text(function(t){return t.name}).attr("class","node-tag node-tag-text").style("font-size",8).style("font-weight",200).style("fill","#000").style("opacity",0).attr("dx",function(t){return 6}).attr("text-anchor","middle").attr("dy","-2").property("index",function(t,n){return n})},t.prototype.bindEvent=function(){var t=this;this.getIconDom().on("contextmenu",o([{title:"删除",action:function(n,e){return r(this,void 0,void 0,function(){var e;return i(this,function(r){switch(r.label){case 0:return[4,t.delete(t.chart.nodes(),u.select(this.parentNode).datum(),n.id)];case 1:return(e=r.sent())&&t.chart.updateNodes(e),[2]}})})}}],{theme:function(){return"d3-context-menu-theme"},position:function(t,n){var e=this.getBoundingClientRect();return{left:e.left+e.width,top:e.top}}})).on("mouseenter",function(n){t.toggleText(n,u.select(this.parentNode).datum(),!0)}).on("mouseleave",function(n){t.toggleText(n,u.select(this.parentNode).datum(),!1)})},t.prototype.toggleText=function(t,n,e){e?this.getTextDom().style("opacity",0).filter(function(e){return u.select(this.parentNode).datum().id==n.id&&e.id==t.id}).style("opacity",1):this.getTextDom().style("opacity",0)},t.prototype.getIconDom=function(){return this.container.selectAll(".node-tag")},t.prototype.getTextDom=function(){return this.container.selectAll(".g-node").selectAll(".node-tag-text")},t.prototype.hasTags=function(t){return t.tags&&t.tags.length},t.prototype.setIconOpacity=function(t){this.getIconDom().style("opacity",t)},t.prototype.setTextOpacity=function(t){this.getTextDom().style("opacity",t)},t.prototype.updatePos=function(){var t=this;this.getIconDom().attr("x",function(t){return u.select(this.parentNode).datum().x-6}).attr("y",function(n){return u.select(this.parentNode).datum().y-t.chart.getRadius(u.select(this.parentNode).datum())-16}).attr("transform",function(t){return"rotate("+45*u.select(this).property("index")+", "+u.select(this.parentNode).datum().x+", "+u.select(this.parentNode).datum().y+")"})},t}();n.default=a}]);