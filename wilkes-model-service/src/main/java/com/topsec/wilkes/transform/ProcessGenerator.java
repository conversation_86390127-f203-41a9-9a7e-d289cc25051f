package com.topsec.wilkes.transform;

import static com.topsec.common.utils.ToolUtils.OBJECT_MAPPER;
import static com.topsec.minsky.conf.ProcessConfig.ALARM_EVENT;
import static com.topsec.minsky.conf.ProcessConfig.SECURITY_LOG;
import static com.topsec.wilkes.dto.ActionConstants.*;
import static com.topsec.wilkes.util.ProcessUtil.*;

import com.google.common.collect.Maps;
import com.topsec.common.utils.ToolUtils;
import com.topsec.dm.domain.DataModel;
import com.topsec.dm.domain.FactTableStorageInfo;
import com.topsec.dm.domain.FieldInfo;
import com.topsec.dm.domain.StorageType;
import com.topsec.dm.domain.ThreatIntelligenceType;
import com.topsec.dm.domain.aas.DomainRangeDesc;
import com.topsec.dm.service.FactTableService;
import com.topsec.dm.service.FieldInfoService;
import com.topsec.dm.service.IntelligenceService;
import com.topsec.dm.util.HttpHeaderUtil;
import com.topsec.minsky.domain.Expression;
import com.topsec.minsky.domain.Operator;
import com.topsec.minsky.domain.Process;
import com.topsec.minsky.domain.Process.Connection;
import com.topsec.wilkes.common.dto.Action;
import com.topsec.wilkes.common.dto.Mapping;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.operator.util.FormatUtil;
import com.topsec.wilkes.transform.action.ActionTransform;
import com.topsec.wilkes.transform.alarm.AlarmTransform;
import com.topsec.wilkes.transform.filter.FilterTransform;
import com.topsec.wilkes.transform.params.ParamTransformFactory;
import com.topsec.wilkes.transform.sink.SinkTransform;
import com.topsec.wilkes.transform.source.SourceTransform;
import com.topsec.wilkes.util.IpUtil;
import com.topsec.wilkes.util.ModelUtil;
import com.topsec.wilkes.util.ProcessUtil;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.lang.reflect.Array;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ProcessGenerator {

  private static final String ADD_INTELLIGENCE = "ADD_INTELLIGENCE";
  private static final String ADD_KNOWLEDGE = "ADD_KNOWLEDGE";
  private static final String DELETE_KNOWLEDGE = "DELETE_KNOWLEDGE";

  private static final List<String> IP_FIELDS = Lists.newArrayList("src_ip", "dst_ip", "src_ip6",
      "dst_ip6");
  private static final Pattern pattern = Pattern.compile("(\\w+)\\s*(=)\\s*\"([^\"]+)\"");

  @Autowired
  private AlarmTransform alarmTransform;

  @Autowired
  private IntelligenceService intelligenceService;
  @Autowired
  private FactTableService factTableService;

  @Autowired
  private ActionTransform actionTransform;

  @Autowired
  private SinkTransform sinkTransform;

  @Autowired
  private SourceTransform sourceTransform;

  @Autowired
  private FilterTransform filterTransform;

  @Autowired
  private ActionGenerator actionGenerator;

  @Autowired
  private FieldInfoService fieldInfoService;

  @Autowired
  private ParamTransformFactory paramTransformFactory;

  @Value("${operator.not-before.impl:stream}")
  private String notBeforeImpl;

  @Value("${topic:security_log_debug}")
  private String debugTopic;

  @Value("${filters.enable:true}")
  private boolean enable;

  @Value("${domain.filter.enable:true}")
  private boolean domainFilterEnable;

  @Value("${wilkes.local_ip:127.0.0.1}")
  private String host;

  @Value("${soar.topic:disposal_event}")
  private String soarTopic;

  @Value("${wilkes.tag.sink:tags_log}")
  private String sinkName;


  /**
   * 获取转化最终的Process
   *
   * @param oldProcess
   * @return
   */
  public Process getCompleteProcess(String oldProcess, Model oldModel,
      Map<String, Boolean> configMap) throws Exception {
    Process process = ProcessUtil.toProcess(oldProcess);
    Model model = (Model) oldModel.clone();
    if (model.getId() == null) {
      model.setId(UUID.randomUUID().toString());
    }
    sourceTransform.transformSampleSource(process);
    // 添加共有操作[拆分数据源]
    Process outProcess = sourceTransform.separateMultipleSource(process, model.getNo());
    if (!(configMap != null && configMap.containsKey("securityEventEnabled") && !configMap.get(
        "securityEventEnabled"))) {
      Map<String, Action> ruleActionMap = actionGenerator.getCompleteActions(model).stream()
          .collect(Collectors.toMap(Action::getId, action -> action));
      if (configMap != null && configMap.containsKey("knowledgeEnabled") && configMap.get(
          "knowledgeEnabled")) {
        outProcess = addDetectionKnowledgeSink(outProcess, ruleActionMap.get(GENERATE_RESPONSE),
            model.getId());
      } else {
        Process addAction = addAction(outProcess, ruleActionMap.get(GENERATE_EVENT), model.getId());
        Process securityEventRateLimit = addRateLimitProcess(addAction,
            ruleActionMap.get(GENERATE_EVENT), model.getId());
        Process addSecurityEventProcess = addSecurityEventSink(securityEventRateLimit,
            ruleActionMap.get(GENERATE_SECURITY_EVENT));
        if (ruleActionMap.get(GENERATE_EVENT).getParams().containsKey("tagsGenerated")
            && ruleActionMap.get(GENERATE_EVENT).getParams().get("tagsGenerated").equals(true)) {
          addSecurityEventProcess = addTagsSink(addSecurityEventProcess);
        }
        //        Process addSecurityEventProcess = addSecurityEventSink(addAction, ruleActionMap.get(GENERATE_SECURITY_EVENT));
        Process addResponseProcess = addResponseSink(addSecurityEventProcess,
            ruleActionMap.get(GENERATE_RESPONSE), model.getId());
        outProcess = addAlarmEventSink(addResponseProcess, ruleActionMap.get(GENERATE_ALARM_EVENT),
            model);
        //加剧本算子
        if (ruleActionMap.containsKey(GENERATE_RESPONSE)) {
          outProcess = addSoarEvent(outProcess, ruleActionMap.get(GENERATE_RESPONSE),
              model.getId());
        }
        //加模型最近触发时间算子
        outProcess = addModelTriggeredTimer(outProcess, model);
      }
    }

    // 如果是河南移动项目，替换算子
    if (!"stream".equals(this.notBeforeImpl)) {
      changeOperator(outProcess);
    }
    // 添加过滤器算子
    if (enable && (oldModel.getModelType() == ModelType.CORRELATION
        || oldModel.getModelType() == ModelType.CORRELATION_TAG
        || oldModel.getModelType() == ModelType.BEHAVIOUR_TAG)) {
      addFilterOperator(oldModel, outProcess);
    }
    // 是否开启分权分域
    if (HttpHeaderUtil.isSaas && domainFilterEnable) {
      outProcess = addDomanFiledFilter(outProcess, model);
    }
    // 翻译表达式字段
    outProcess = translatePredication(outProcess);
    //转换表IPV6
    transformIpv6(outProcess);
    //转换参数
    paramTransform(outProcess, model.getNo());

    //告警降噪标签 添加名单库 自定义白名单
    if ((oldModel.getModelType() == ModelType.BEHAVIOUR_TAG
        || oldModel.getModelType() == ModelType.CORRELATION_TAG)) {
      List<Object> alarmNoiseCustomWhite = intelligenceService.getAlarmNoiseCustomWhite();
      filterTransform.addCustomWhiteFilter(outProcess, alarmNoiseCustomWhite);
    }
    //转换instanceId
    transformInstanceId(outProcess);

    return outProcess;
  }


  private Process addDomanFiledFilter(Process process, Model model) {
    List<Operator> operators = process.getOperators();
    List<Process.Connection> connectionList = process.getConnections();

    if (ModelUtil.IsDomainUser(model)) {
      List<DomainRangeDesc> domainRangeDescList = Lists.newArrayList();
      Matcher matcher = pattern.matcher(model.getDomainDesc());
      while (matcher.find()) {
        String field = matcher.group(1);   // 字段名
        String eq = matcher.group(2);      // 等号
        String value = matcher.group(3);
        DomainRangeDesc domainRangeDesc = new DomainRangeDesc();
        domainRangeDesc.setFiled(field);
        domainRangeDesc.setRelation(eq);
        domainRangeDesc.setValueRange(value);
        domainRangeDescList.add(domainRangeDesc);
      }

      if (!CollectionUtils.isEmpty(domainRangeDescList)) {
        List<Operator> sourceOperatorDescriptionList = operators.stream().filter(
                operatorDescription -> connectionList.stream().filter(
                    connection -> connection.getTo().split("\\.")[0].equalsIgnoreCase(
                        operatorDescription.getInstanceId())).count() == 0)
            .collect(Collectors.toList());
        for (Operator sourceOperatorDescription : sourceOperatorDescriptionList) {
          String fieldFilterInstanceId = UUID.randomUUID().toString() + "-domain1";
          Expression expression = new Expression();
          Expression.Node node = new Expression.Node();
          Expression childExpression;
          Expression.LeafNode leafNode;
          node.setOperator("or");
          List<Expression> expressions = new ArrayList<>();
          for (DomainRangeDesc domainRangeDesc : domainRangeDescList) {
            childExpression = new Expression();
            leafNode = new Expression.LeafNode();
            leafNode.setLeft(domainRangeDesc.getFiled());
            leafNode.setOperator(domainRangeDesc.getRelation());
            leafNode.setRight(domainRangeDesc.getValueRange());
            childExpression.setLeafNode(leafNode);
            expressions.add(childExpression);
          }
          node.setChildren(expressions);
          expression.setNode(node);
          Map<String, Object> paramsMap = new HashMap<>();
          paramsMap.put("expression", expression);
          paramsMap.put("type", "include");
          Operator filterFilterOperator = new Operator("field-filter", fieldFilterInstanceId,
              paramsMap);
          filterFilterOperator.setMergeMappings(Collections.singletonList(
              new Operator.MergeMapping(model.getNo(), filterFilterOperator.getInstanceId(),
                  new HashMap<String, Object>() {{
                    put("group", "field-filter");
                  }})));
          operators.add(filterFilterOperator);
          String sourceInstanceId = sourceOperatorDescription.getInstanceId();
          for (Process.Connection connection : connectionList) {
            if (connection.getFrom().split("\\.")[0].equals(sourceInstanceId)) {
              connection.setFrom(
                  connection.getFrom().replaceAll(sourceInstanceId, fieldFilterInstanceId));
            }
          }
          // 补全连线
          connectionList.add(new Process.Connection(String.format("%s.outport1", sourceInstanceId),
              String.format("%s.inport1", fieldFilterInstanceId)));
        }
      }
    }
    return new Process(operators, connectionList);
  }

  private void paramTransform(Process process, String modelNo) throws Exception {
    List<Operator> operators = process.getOperators();
    HashMap<String, String> sinkParentAndEventNameMap = getSinkParentAndEventNameMap(process);

    for (Operator operator : operators) {
      if (operator.getMergeMappings() == null || operator.getMergeMappings().size() == 0) {
        operator.setMergeMappings(Collections.singletonList(
            new Operator.MergeMapping(modelNo, operator.getInstanceId(),
                new HashMap<String, Object>() {{
                  put("group", operator.getId());
                }})));
      }

      if (sinkParentAndEventNameMap.containsKey(operator.getInstanceId())) {
        operator.setMergeMappings(Collections.singletonList(
            new Operator.MergeMapping(modelNo, operator.getInstanceId(),
                new HashMap<String, Object>() {{
                  put("event_type", sinkParentAndEventNameMap.get(operator.getInstanceId()));
                  put("is_sink_from", true);
                }})));
      }
      paramTransformFactory.getParamTransform(operator.getId()).apply(operator);
    }
  }

  private HashMap<String, String> getSinkParentAndEventNameMap(Process process) {
    HashMap<String, String> sinkParentInstanceIdAndEventNameMap = new HashMap<>();
    List<Process.Connection> connections = process.getConnections();
    List<Operator> operators = process.getOperators();
    for (Operator operator : operators) {
      if (operator.getId().equalsIgnoreCase("sink")) {
        String url = operator.getParams().get("url").toString();
        String type = url.split("://")[0];
        if (type.equalsIgnoreCase("kafka")) {
          String eventType = url.split("://")[1].split("/")[1].split("\\?")[0];
          String parentInstanceId = connections.stream()
              .filter(connection -> connection.getTo().startsWith(operator.getInstanceId()))
              .map(connection -> {
                return connection.getFrom().split("\\.")[0];
              }).collect(Collectors.toList()).get(0);
          sinkParentInstanceIdAndEventNameMap.put(parentInstanceId, eventType);
        }
      }
    }
    return sinkParentInstanceIdAndEventNameMap;
  }

  private Process addAction(Process process, Action action, String modelId) {
    if (action == null || !action.getId().equals(GENERATE_EVENT)) {
      return process;
    }
    Map<String, Object> params = new LinkedHashMap<>();
    action.setId(GENERATE_SECURITY_EVENT);

    Action securityAction = new Action();
    securityAction.setId(action.getId());
    Map<String, Object> securityParam = new HashMap<>();
    Map<String, Object> sourceParam = action.getParams();

    for (String key : sourceParam.keySet()) {
      if (key.equals("mappings")) {
        Map<String, Mapping> mappingMap = action.getMappingMap();
        List<Mapping> mappingList = new LinkedList<>();
        for (String mappingKey : mappingMap.keySet()) {
          if (mappingKey.equals("SECURITY_LOG_RATE_LIMIT") || mappingKey.equals("INDEXING_FIELD")
              || mappingKey.equals("CONFIDENCE_LEVEL") || mappingKey.equals("ALARM_LEVEL")
              || mappingKey.equals("mergePeriod") || mappingKey.equals("mergeCount")) {
            continue;
          }
          mappingList.add(mappingMap.get(mappingKey));
        }
        securityParam.put(key, mappingList);
      } else {
        securityParam.put(key, sourceParam.get(key));
      }
    }
    securityAction.setParams(securityParam);

    params.put("actions", Lists.newArrayList(translateAction(securityAction)));
    return actionTransform.addAction(process, params, modelId);
  }

  private Process addSecurityEventSink(Process process, Action action) {
    if (action == null || !action.getId().equals(GENERATE_SECURITY_EVENT)) {
      return process;
    }
    String securityUrl = factTableService.getFactTableStorageInfo(SECURITY_LOG,
        StorageType.DATABASE_KAFKA).toURL(true);
    return sinkTransform.addSink(process, securityUrl);
  }

  private Process addTagsSink(Process process) {
    String securityUrl = factTableService.getFactTableStorageInfo(sinkName,
        StorageType.DATABASE_KAFKA).toURL(true);
    return sinkTransform.addSink(process, securityUrl);
  }

  private Process addResponseSink(Process process, Action action, String modelId) {
    if (action == null || !action.getId().equals(GENERATE_RESPONSE)) {
      return process;
    }

    List<Mapping> mappings = ToolUtils.OBJECT_MAPPER.convertValue(
        action.getParams().get("mappings"), new TypeReference<List<Mapping>>() {
        });

    Map<String, Mapping> mappingMap = mappings.stream()
        .filter(mapping -> mapping.getValue() != null && mapping.getValue().toString().length() > 0)
        .collect(Collectors.toMap(Mapping::getName, Function.identity(), (m1, m2) -> m1));

    Process addThreatSink = addThreatSink(process, mappingMap.get(ADD_INTELLIGENCE), modelId);
    Process addKnowledgeSink = addKnowledgeSink(addThreatSink, mappingMap.get(ADD_KNOWLEDGE),
        modelId);
    process = addDeleteKnowledgeSink(addKnowledgeSink, mappingMap.get(DELETE_KNOWLEDGE), modelId);
    return process;
  }

  private Process addDetectionKnowledgeSink(Process process, Action action, String modelId) {
    if (action == null || !action.getId().equals(GENERATE_RESPONSE)) {
      return process;
    }
    List<Mapping> mappings = ToolUtils.OBJECT_MAPPER.convertValue(
        action.getParams().get("mappings"), new TypeReference<List<Mapping>>() {
        });
    Map<String, Mapping> mappingMap = mappings.stream()
        .filter(mapping -> mapping.getValue() != null && mapping.getValue().toString().length() > 0)
        .collect(Collectors.toMap(Mapping::getName, Function.identity(), (m1, m2) -> m1));
    process = addDetectionKnowledgeSink(process, mappingMap.get(ADD_KNOWLEDGE), modelId);
    return process;
  }


  private Process addRateLimitProcess(Process process, Action action, String id) {
    if (action == null) {
      return process;
    }
    Map<String, Object> params = new LinkedHashMap<>();
    Map<String, Mapping> map = action.getMappingMap();
    //todo 需要优化，action不需要做这些判断，从getCompleteActions处抑制
    if (!((map.get("INDEXING_FIELD") != null && map.get("INDEXING_FIELD").getValue() != null) && (
        map.get("SECURITY_LOG_RATE_LIMIT") != null
            && map.get("SECURITY_LOG_RATE_LIMIT").getValue() != null))) { //不存在指定的参数
      return process;
    }
    params.put("intervalTime", "");
    params.put("limitSize", "");
    params.put("indexingFieldNames", map.get("INDEXING_FIELD").getValue());
    List<Mapping> valueList = new LinkedList<>();
    try {
      String value = map.get("SECURITY_LOG_RATE_LIMIT").getValue().toString();
      valueList = new LinkedList<>(
          Arrays.asList(ToolUtils.OBJECT_MAPPER.readValue(value, Mapping[].class)));
    } catch (IOException e) {
      log.error("", e);
    }
    Map<String, Mapping> limitMap = valueList.stream()
        .collect(Collectors.toMap(Mapping::getName, Function.identity(), (o1, o2) -> o1));
    String intervalTime = limitMap.getOrDefault("interval-time",
        new Mapping("interval-time", "1 second")).getValue().toString();
    int limitSize = (Integer) limitMap.getOrDefault("limitSize", new Mapping("limitSize", 1))
        .getValue();
    params.put("intervalTime", intervalTime); //考虑对旧模型的兼容问题
    params.put("limitSize", limitSize);
    Process rateLimit = actionTransform.addRateLimit(process, params, id);
    return rateLimit;
  }

  private Process addAlarmEventSink(Process process, Action action, Model model) {
    if (action == null || !action.getId().equals(GENERATE_ALARM_EVENT)) {
      return process;
    }
    String modelId = model.getId();
    Map<String, Object> params = new LinkedHashMap<>();
    if (model.getModelType() == ModelType.BEHAVIOUR) {
      try {
        Map<String, Object> modelInfoMap = new LinkedHashMap<>();
        modelInfoMap.putAll((Map) ToolUtils.OBJECT_MAPPER.readValue(model.getContent(), Map.class)
            .get("behaviourDefinition"));
        params.put("modelInfo", modelInfoMap);
      } catch (Exception e) {
        log.error("", e);
      }
    }
    Map<String, Mapping> map = action.getMappingMap();
    params.put("indexingFieldNames", "");
    params.put("intervalTime", "");
    params.put("limitSize", "");
    if (map.get("INDEXING_FIELD") != null && map.get("INDEXING_FIELD").getValue() != null) {
      params.put("indexingFieldNames", map.get("INDEXING_FIELD").getValue());
    }
    boolean isOldModel = false;
    if (Strings.isNullOrEmpty(params.get("indexingFieldNames").toString())) {
      params.replace("indexingFieldNames", "S_IP,D_IP,NAME");
      isOldModel = true;
    } else if ("null".equals(params.get("indexingFieldNames").toString())) {
      params.replace("indexingFieldNames", "");
    }
    // 添加限制
    if (map.get("RATE_LIMIT") != null && map.get("RATE_LIMIT").getValue() != null) {
      List<Mapping> valueList = new LinkedList<>();
      try {
        String value = map.get("RATE_LIMIT").getValue().toString();
        valueList = new LinkedList<>(
            Arrays.asList(ToolUtils.OBJECT_MAPPER.readValue(value, Mapping[].class)));
      } catch (IOException e) {
        log.error("", e);
      }
      Map<String, Mapping> limitMap = valueList.stream()
          .collect(Collectors.toMap(Mapping::getName, Function.identity(), (o1, o2) -> o1));
      String intervalTime = limitMap.getOrDefault("interval-time",
          new Mapping("interval-time", "1 second")).getValue().toString();
      int limitSize = (Integer) limitMap.getOrDefault("limitSize", new Mapping("limitSize", 1))
          .getValue();
      // 默认值不可为0，兼容旧模型
      params.put("intervalTime",
          "0 second".equals(intervalTime) && isOldModel ? "1 second" : intervalTime);
      params.put("limitSize", limitSize == 0 && isOldModel ? 1 : limitSize);
    }
    // 添加告警评分
    if (map.get("THREATSCORE_ALARM") != null) {
      params.put("threatScoreAlarm", map.get("THREATSCORE_ALARM").getValue());
    }
    Process alarmProcess = alarmTransform.addAlarm(process, params, modelId);
    String alarmEventUrl = factTableService.getFactTableStorageInfo(ALARM_EVENT,
        StorageType.DATABASE_KAFKA).toURL(true);
    return sinkTransform.addAlarmSink(alarmProcess, alarmEventUrl, modelId);
  }

  private Process addThreatSink(Process process, Mapping mapping, String modelId) {
    if (mapping != null && ADD_INTELLIGENCE.equals(mapping.getName())) {
      process = sinkTransform.addThreatSink(process,
          ThreatIntelligenceType.valueOf(mapping.getValue().toString()), modelId);
    }
    return process;
  }

  private Process addKnowledgeSink(Process process, Mapping mapping, String modelId) {
    if (mapping != null && ADD_KNOWLEDGE.equals(mapping.getName())) {
      try {
        Map<String, Object> knowledgeParamMap = ToolUtils.OBJECT_MAPPER.readValue(
            mapping.getValue().toString(), Map.class);
        process = sinkTransform.addKnowledgeSink(process, knowledgeParamMap, modelId);
      } catch (JsonProcessingException e) {
        log.error("", e);
      }
    }
    return process;
  }

  private Process addDetectionKnowledgeSink(Process process, Mapping mapping, String modelId) {
    if (mapping != null && ADD_KNOWLEDGE.equals(mapping.getName())) {
      try {
        Map<String, Object> knowledgeParamMap = ToolUtils.OBJECT_MAPPER.readValue(
            mapping.getValue().toString(), Map.class);
        process = sinkTransform.addDetectionKnowledgeSink(process, knowledgeParamMap, modelId);
      } catch (JsonProcessingException e) {
        log.error("", e);
      }
    }
    return process;
  }

  private Process addDeleteKnowledgeSink(Process process, Mapping mapping, String modelId) {
    if (mapping != null && DELETE_KNOWLEDGE.equals(mapping.getName())) {
      try {
        Map<String, Object> deleteKnowledgeParamMap = ToolUtils.OBJECT_MAPPER.readValue(
            mapping.getValue().toString(), Map.class);
        process = sinkTransform.addDeleteKnowledgeSink(process, deleteKnowledgeParamMap, modelId);
      } catch (JsonProcessingException e) {
        log.error("", e);
      }
    }
    return process;
  }

  /**
   * 将算子not-before-coprocess替换成knowledge-base-filter
   *
   * @param processDescription
   */
  private void changeOperator(Process processDescription) {
    List<Operator> processDescriptionList = processDescription.getOperators();
    List<Process.Connection> connectionList = processDescription.getConnections();
    for (Operator operator : processDescriptionList) {
      if ("not-before-coprocess".equals(operator.getId())) {
        operator.setId("knowledge-base-filter");
        Map<String, Object> oldParamsMap = operator.getParams();
        Map<String, Object> newParamsMap = new HashMap<>();
        newParamsMap.put("type", "exclude");
        newParamsMap.put("relation_field", new Array[0]);
        newParamsMap.put("predication", getPredication(oldParamsMap));
        newParamsMap.put("knowledge_base_url",
            getURL(operator.getInstanceId(), processDescriptionList, connectionList));
        newParamsMap.put("filter_condition", null);
        operator.setParams(newParamsMap);
        adjust(operator.getInstanceId(), processDescriptionList, connectionList);
        break;
      }
    }
  }

  /**
   * 获取关联条件参数
   *
   * @param oldMap
   * @return
   */
  private Object getPredication(Map<String, Object> oldMap) {
    Expression expression = new Expression();
    Expression.Node node = new Expression.Node();
    List<Expression> childrenList = new LinkedList<>();
    if (oldMap.get("field") != null) {
      String[] fields = oldMap.get("field").toString().split(",");
      for (String field : fields) {
        addChildren(childrenList, field, field, "equal");
      }
    } else if (oldMap.get("diff_fields") != null) {
      List<String> diffFields = (ArrayList<String>) oldMap.get("diff_fields");
      List<String> leftList = split(diffFields.get(0), ",");
      List<String> rightList = split(diffFields.get(1), ",");
      for (int i = 0; i < Math.min(leftList.size(), rightList.size()); i++) {
        addChildren(childrenList, rightList.get(i), leftList.get(i), "equal");
      }
    }
    addChildren(childrenList, "TIME", "TIME", ">");
    node.setChildren(childrenList);
    node.setOperator("and");
    expression.setNode(node);
    return expression;
  }

  /**
   * 获取事件B的ES地址
   *
   * @param instanceId
   * @param processDescriptionList
   * @param connectionList
   * @return
   */
  private String getURL(String instanceId, List<Operator> processDescriptionList,
      List<Process.Connection> connectionList) {
    String esUrl = "";
    //默认中间没有别的算子
    for (Process.Connection connection : connectionList) {
      if ((instanceId + ".inport2").equals(connection.getTo())) {
        String secondSourceInstanceId = connection.getFrom().replace(".outport1", "");
        for (Operator operator : processDescriptionList) {
          if (secondSourceInstanceId.equals(operator.getInstanceId())) {
            List<Object> urlList = (List<Object>) operator.getParams().get("url");
            if (urlList == null) {
              throw new RuntimeException("事件B不能为安全事件！");
            }
            List<DataModel> sourceList = urlList.stream()
                .map(url -> OBJECT_MAPPER.convertValue(url, DataModel.class))
                .collect(Collectors.toList());
            String factTableName = sourceList.get(0).getId();
            FactTableStorageInfo factTableStorageInfo = factTableService.getFactTableStorageInfo(
                factTableName, StorageType.DATABASE_ES);
            esUrl = factTableStorageInfo != null ? factTableStorageInfo.getAddress() : "";
            break;
          }
        }
        break;
      }
    }
    return esUrl;
  }

  private void adjust(String instanceId, List<Operator> processDescriptionList,
      List<Process.Connection> connectionList) {
    String firstSourceInstanceId = null;
    String secondSourceInstanceId = null;
    for (Process.Connection connection : connectionList) {
      if ((instanceId + ".inport1").equals(connection.getTo())) {
        firstSourceInstanceId = connection.getFrom().replace(".outport1", "");
        break;
      }
    }
    for (Process.Connection connection : connectionList) {
      if ((instanceId + ".inport2").equals(connection.getTo())) {
        secondSourceInstanceId = connection.getFrom().replace(".outport1", "");
        //删除事件B到此的连线
        connectionList.remove(connection);
        break;
      }
    }
    //两个输入口是同一个输入源时不删除算子，否则删除第二个
    if (!firstSourceInstanceId.equals(secondSourceInstanceId)) {
      for (Operator operator : processDescriptionList) {
        if (secondSourceInstanceId.equals(operator.getInstanceId())) {
          //删除事件B算子
          processDescriptionList.remove(operator);
          break;
        }
      }
    }
  }

  private Map<String, String> getTranslateMap() {
    Map<String, String> translateMap = new HashMap<>();
    Map<String, List<FieldInfo>> fieldIdInfosMap = fieldInfoService.getFieldInfos();
    for (Map.Entry<String, List<FieldInfo>> entry : fieldIdInfosMap.entrySet()) {
      for (FieldInfo fieldInfo : entry.getValue()) {
        translateMap.put(fieldInfo.getNameEn(), fieldInfo.getName());
      }
    }
    return translateMap;
  }

  private List<String> getArrayFieldList() {
    List<String> arrayFieldList = new ArrayList<>();
    Map<String, List<FieldInfo>> fieldIdInfosMap = fieldInfoService.getFieldInfos();
    for (Map.Entry<String, List<FieldInfo>> entry : fieldIdInfosMap.entrySet()) {
      for (FieldInfo fieldInfo : entry.getValue()) {
        if ("array(string)".equals(fieldInfo.getType())) {
          arrayFieldList.add(fieldInfo.getName());
        }
      }
    }
    return arrayFieldList;
  }

  /**
   * 翻译action里的表达式
   *
   * @param action
   * @return
   */
  private Action translateAction(Action action) {
    //匹配${xxx}
    String regex = "\\$\\{([^}]*)\\}";
    Pattern pattern = Pattern.compile(regex);
    Map<String, String> translateMap = getTranslateMap();
    Map<String, Mapping> map = action.getMappingMap();
    List<Mapping> fieldParamsList = new LinkedList<>(map.values());

    Map<String, Mapping> reasonMappingMap = action.getReasonMappingMap();
    Map<String, Object> reasonMappingFieldMap = Maps.newHashMap();
    if (reasonMappingMap.size() > 0) {
      reasonMappingFieldMap = reasonMappingMap.values().stream().map(reasonMapping -> {
        String newName = reasonMapping.getName().split(FormatUtil.extendMark)[0];
        reasonMapping.setName(newName);
        return reasonMapping;
      }).collect(Collectors.toMap(Mapping::getName, Mapping::getValue, (o1, o2) -> o1));
    }
    for (Mapping mapping : fieldParamsList) {
      Object value = mapping.getValue();
      if (!(value instanceof String) || Strings.isNullOrEmpty((String) value)) {
        continue;
      }
      String strValue = (String) value;
      Matcher matcher = pattern.matcher(strValue);
      while (matcher.find()) {
        String matched = matcher.group();
        String matchedField = matched.substring(2, matched.length() - 1).trim();
        String factName = translateMap.get(matchedField);
        if (!Strings.isNullOrEmpty(factName)) {
          strValue = strValue.replace(matched, "${" + factName + "}");
        }
        //兼容处理引用知识库附加字段
        else if (reasonMappingFieldMap.containsKey(matchedField)) {
          strValue = strValue.replace(matched,
              "${" + reasonMappingFieldMap.get(matchedField) + "}");
        }
      }
      mapping.setValue(strValue);
    }
    Map<String, Object> params = action.getParams();
    params.put("mappings", fieldParamsList);
    action.setParams(params);
    return action;
  }

  /**
   * 添加过滤器算子
   *
   * @param model
   * @param process
   * @return
   */
  private Process addFilterOperator(Model model, Process process) {
    Map<String, Object> paramsMap = new HashMap<>();
    paramsMap.put("id", model.getId());
    paramsMap.put("type", ProcessUtil.getNamespace(model));
    return filterTransform.addFilterOperator(process, paramsMap, model.getNo());
  }


  /**
   * @Description 转换过滤器算子中用户输入的IPV6
   * <AUTHOR>
   * @Date 2023/3/3
   * @Param
   * @Return
   */
  private void transformIpv6(Process outProcess) {
    //获取过滤器算子
    List<Operator> processDescriptionList = outProcess.getOperators();
    List<Operator> fieldFilterOperators = processDescriptionList.stream()
        .filter(operator -> operator.getId().equals("field-filter")).collect(Collectors.toList());
    if (!CollectionUtils.isEmpty(fieldFilterOperators)) {
      for (Operator fieldFilterOperator : fieldFilterOperators) {
        if (fieldFilterOperator != null && StringUtils.isNotBlank(
            ObjectUtils.toString(fieldFilterOperator.getParams().get("expression")))) {
          processDescriptionList.remove(fieldFilterOperator);
          //获取表达式
          Expression expression = ToolUtils.OBJECT_MAPPER.convertValue(
              fieldFilterOperator.getParams().get("expression"), Expression.class);
          //转换ipv6
          expression = transformIpv6WithExpression(expression);
          fieldFilterOperator.getParams().put("expression", expression);
          processDescriptionList.add(fieldFilterOperator);
          outProcess.setOperators(processDescriptionList);
        }
      }
    }
  }

  //instanceId -> id_instanceId
  private void transformInstanceId(Process outProcess) {
    List<Connection> connections = outProcess.getConnections();
    List<Operator> operators = outProcess.getOperators();
    operators.stream().forEach(operator -> {
      String oldInstanceId = operator.getInstanceId();
      String id = operator.getId();
      if (!oldInstanceId.startsWith(id)) {
        String instanceId = String.format("%s__%s", id, oldInstanceId);
        operator.setInstanceId(instanceId);
        connections.stream().forEach(connection -> {
          if (connection.getFrom().startsWith(oldInstanceId)) {
            connection.setFrom(connection.getFrom().replace(oldInstanceId, instanceId));
          }
          if (connection.getTo().startsWith(oldInstanceId)) {
            connection.setTo(connection.getTo().replace(oldInstanceId, instanceId));
          }
        });
      }
    });
  }


  private Expression transformIpv6WithExpression(Expression expression) {
    if (ObjectUtils.isNotEmpty(expression)) {
      Expression.Node node = expression.getNode();
      Expression.LeafNode leafNode = expression.getLeafNode();
      if (node != null) {
        List<Expression> children = node.getChildren();
        if (!CollectionUtils.isEmpty(children)) {
          for (Expression child : children) {
            transformIpv6WithExpression(child);
          }
        }
      }
      if (leafNode != null) {
        if (IP_FIELDS.contains(leafNode.getLeft())) {
          if (StringUtils.isNotEmpty(leafNode.getRight()) && IpUtil.isValidIpv6Addr(
              leafNode.getRight())) {
            leafNode.setRight(IpUtil.getShortIPv6(leafNode.getRight()));
          }
        }
      }
    }
    return expression;
  }

  /**
   * 翻译高级编辑模式里的表达式
   *
   * @param process
   * @return
   */
  private Process translatePredication(Process process) {
    //匹配${xxx}
    String regex = "\\$\\{([^}]*)\\}";
    Pattern pattern = Pattern.compile(regex);
    List<String> needTranslateIds = Arrays.asList("field-filter", "field-aggregation");
    List<Operator> operatorList = process.getOperators();
    Map<String, String> translateMap = getTranslateMap();
    for (Operator operator : operatorList) {
      if (needTranslateIds.contains(operator.getId())
          && operator.getParams().get("predication") != null) {
        String value = operator.getParams().get("predication").toString();
        Matcher matcher = pattern.matcher(value);
        while (matcher.find()) {
          String matched = matcher.group();
          String factName = translateMap.get(matched.substring(2, matched.length() - 1).trim());
          if (!Strings.isNullOrEmpty(factName)) {
            value = value.replace(matched, factName);
            for (String ipv6 : IP_FIELDS) {
              if (value.contains(ipv6)) {
                value = translatePredicationWithIpv6(value);
                break;
              }
            }
          }
        }
        operator.getParams().put("predication", value);
      }
    }
    return process;
  }

  private String translatePredicationWithIpv6(String value) {
    Pattern patternIpv6 = Pattern.compile(
        "(src_ip|src_ip6|dst_ip|dst_ip6)\\s*(=|<>|!=)\\s*\\'(.*?)\\'+");
    Pattern patternValue = Pattern.compile("\\'(.*?)\\'+");
    Matcher matcher = patternIpv6.matcher(value);
    while (matcher.find()) {
      String group = matcher.group();
      if (StringUtils.isNotEmpty(group)) {
        Matcher matcher1 = patternValue.matcher(group);
        if (matcher1.find()) {
          String matched = matcher1.group().substring(1, matcher1.group().length() - 1).trim();
          if (StringUtils.isNotEmpty(matched) && IpUtil.isValidIpv6Addr(matched)) {
            value = value.replace(matched, IpUtil.getShortIPv6(matched));
          }
        }
      }
    }
    return value;
  }

  /**
   * 加剧本算子
   *
   * @param process
   * @param responseAction
   * @param modelId
   * @return
   */
  private Process addSoarEvent(Process process, Action responseAction, String modelId) {
    List<Operator> operatorList = process.getOperators();
    List<Process.Connection> connectionList = process.getConnections();
    List<Operator> generateAlarmOperatorDesList = operatorList.stream()
        .filter((operator) -> operator.getId().equals("generate-alarm-event"))
        .collect(Collectors.toList());
    //有告警事件并且选择剧本时才加算子
    Mapping playbookIdMapping = responseAction.getMappingMap().get("PLAYBOOKID");
    if (generateAlarmOperatorDesList.size() == 0 || playbookIdMapping == null
        || Strings.isNullOrEmpty(playbookIdMapping.getValue().toString())) {
      return process;
    }
    for (int i = 1; i <= generateAlarmOperatorDesList.size(); ++i) {
      Operator operatorDescription = generateAlarmOperatorDesList.get(i - 1);
      String InstanceId = UUID.randomUUID().toString();
      Map<String, Object> paramMap = new HashMap<>();
      paramMap.put("topic", soarTopic);
      paramMap.put("playbookIds", playbookIdMapping.getValue().toString().split(","));
      operatorList.add(new Operator("soar-event", InstanceId, paramMap));
      connectionList.add(
          new Process.Connection(String.format("%s.outport1", operatorDescription.getInstanceId()),
              String.format("%s.inport1", InstanceId)));
    }
    Process newProcess = new Process(operatorList, connectionList);
    String alarmEventUrl = factTableService.getFactTableStorageInfo(ALARM_EVENT,
        StorageType.DATABASE_KAFKA).toURL(true);
    return addSink(newProcess, "soar-event", alarmEventUrl.replace("kafka://", "commonkafka://"),
        modelId, "soar");
  }

  private Process addModelTriggeredTimer(Process process, Model model) {
    List<Operator> operatorList = process.getOperators();
    List<Process.Connection> connectionList = process.getConnections();
    List<Operator> generateSecurityOperatorList = operatorList.stream()
        .filter((operator) -> operator.getId().equals("action")).collect(Collectors.toList());
    if (generateSecurityOperatorList.size() < 1) {
      return process;
    }
    Map<String, Object> paramsMap = new HashMap<>();
    paramsMap.put("modelNo", model.getNo());
    String instanceId = UUID.randomUUID().toString();
    operatorList.add(new Operator("model-triggered-timer", instanceId, paramsMap));
    connectionList.add(new Process.Connection(
        String.format("%s.outport1", generateSecurityOperatorList.get(0).getInstanceId()),
        String.format("%s.inport1", instanceId)));
    Process newProcess = new Process(operatorList, connectionList);
    return addSink(newProcess, "model-triggered-timer",
        String.format("http://%s:22010/wilkes/models/triggeredTimer", host), model.getId(),
        "trigger");
  }

}