package com.topsec.wilkes.behaviour.service.impl.portrait;

import com.google.common.collect.Lists;
import com.topsec.wilkes.behaviour.dto.definition.AlgorithmType;
import com.topsec.wilkes.behaviour.dto.portrait.PortraitData;
import com.topsec.wilkes.behaviour.service.impl.util.StatisticTimeUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ColDevPortraitServiceImpl extends BehaviourPortraitServiceImpl {
  @Override
  public PortraitData getPortraitData(String no, Map<String, Object> conditionMap, String from, String to, int pageNo, int pageSize) {
    List<String> queryFields = Lists.newArrayList("e_t", "a_d", "d_v", "flag");
    List<Map<String, Object>> mapList = super.getResult(no, Lists.newArrayList(), queryFields, conditionMap);
    if (!mapList.isEmpty()) {
      PortraitData portraitData = new PortraitData();
      List<String> timeList = Lists.newArrayList();
      List<Object> aggDisList = Lists.newArrayList();
      List<Object> disList = Lists.newArrayList();
      List<Object> flagList = new ArrayList<>(128);
      for (int i = 1; i < mapList.size(); i++) {
        aggDisList.add((mapList.get(i - 1).get("a_d").toString()));
        disList.add((mapList.get(i).get("d_v").toString()));
        timeList.add(mapList.get(i).get("e_t").toString());
        flagList.add((mapList.get(i).get("flag")));
      }
      List<PortraitData.DataInfo> dataList = Lists.newArrayList();
      dataList.add(new PortraitData.DataInfo("距离值", disList));
      dataList.add(new PortraitData.DataInfo("平均距离值", aggDisList));
      dataList.add(new PortraitData.DataInfo("异常标志位", flagList));
      String intervalDesc = behaviourModelService.getBehaviourModel(no).getBehaviourAlgorithm().getParams().get("learn_interval").toString();
      Map<String, Object> extendConfigMap = new HashMap<>(8);
      extendConfigMap.put("nowTime", timeList.size() >= 1 ? timeList.get(timeList.size() - 1) : null);
      extendConfigMap.put("nextTime", StatisticTimeUtil.getNextTimeStr(intervalDesc, StatisticTimeUtil.getCurrentTimeStr()));
      portraitData.setTimeList(timeList);
      portraitData.setDataList(dataList);
      portraitData.setExtendConfig(extendConfigMap);
      return portraitData;
    } else {
      return null;
    }
  }

  @Override
  public String accept() {
    return AlgorithmType.COLLECTION_DEVIATION.getName();
  }

  @Data
  private static class ColStaValue {
    String name;
    String currentValue;
    String aggValue;
    String endTime;
    String beginTime;
    String flag;
  }
}
