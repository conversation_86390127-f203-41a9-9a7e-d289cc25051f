import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'treeFilter',
  pure: true,
})
export class TreeFilterPipe implements PipeTransform {
  transform(tree, name, value, childName): any {
    tree = JSON.parse(JSON.stringify(tree));
    !(tree instanceof Array) && (tree = [tree]);
    const fun = (arr) =>
      arr.filter((e) => {
        e.expanded = true;
        if (e[childName] && e[childName].length > 0) {
          e[childName] = fun(e[childName]);
        }
        return new RegExp(value, 'i').test(e[name]) || (e[childName] && e[childName].length > 0);
      });
    return value ? fun(tree) : tree;
  }
}
