package com.topsec.wilkes.util;

import com.topsec.dm.controller.DataModelController;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.commons.util.InetUtils;
import org.springframework.cloud.commons.util.InetUtilsProperties;
import org.springframework.web.bind.annotation.RequestMapping;

import java.lang.reflect.Method;
import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Optional;
import java.util.Set;
import javax.management.MBeanServer;
import javax.management.MBeanServerFactory;
import javax.management.ObjectName;

/**
 * <AUTHOR>
 */
@Slf4j
public class MvcURIUtil {

  public static URI fromControllerMethod(Class controllerClazz, String method, Object... args) {
    StringBuilder sb = new StringBuilder("http://");
    String host = new InetUtils(new InetUtilsProperties()).findFirstNonLoopbackHostInfo().getIpAddress();
    sb.append(host).append(":");

    try {
      String port = getPortByMBean();
      if (port == null) {
        port = "80";
      }
      sb.append(port)
          .append("/wilkes")//TODO 获取path
          .append(getURLPath(controllerClazz, method, args));
    } catch (Exception e) {
      throw new RuntimeException("can't generate url from " + controllerClazz, e);
    }
    return URI.create(sb.toString());
  }

  public static String getDataModelSchemaURI(String dataModelId) {
    String uri = "";
    try {
      uri = MvcURIUtil.getURLPath(DataModelController.class, "getDataModelSchema", dataModelId);
    } catch (NoSuchMethodException e) {
      log.error("", e);
    }
    return "wilkes" + uri;
  }

  public static String getURLPath(Class controllerClazz, String methodName, Object... args) throws NoSuchMethodException {
    RequestMapping clazzRequestMappingAnnotation = (RequestMapping) controllerClazz.getDeclaredAnnotation(RequestMapping.class);
    String path = clazzRequestMappingAnnotation.value()[0];
    Optional<Method> method = Arrays.stream(controllerClazz.getDeclaredMethods())
        .filter(m -> m.getName().equals(methodName) && m.getParameterCount() == args.length)
        .findFirst();

    RequestMapping methodRequestMappingAnnotation = method.get().getDeclaredAnnotation(RequestMapping.class);
    path += methodRequestMappingAnnotation.value()[0];

    path = path.replaceAll("\\{[^}]*\\}", "%s");
    return String.format(path, args);
  }

  private static String getPortByMBean() {
    String portStr = null;

    MBeanServer mBeanServer = null;
    ArrayList<MBeanServer> mBeanServers = MBeanServerFactory.findMBeanServer(null);
    if (mBeanServers.size() > 0) {
      mBeanServer = mBeanServers.get(0);
    }
    if (mBeanServer == null) {
      throw new IllegalStateException("没有发现JVM中关联的MBeanServer.");
    }
    Set<ObjectName> objectNames = null;
    try {
      objectNames = mBeanServer.queryNames(new ObjectName("Catalina:type=Connector,*"), null);
    } catch (Exception e) {
      log.error("", e);
    }
    if (objectNames == null || objectNames.size() <= 0) {
      throw new IllegalStateException("没有发现JVM中关联的MBeanServer : " + mBeanServer.getDefaultDomain() + " 中的对象名称.");
    }
    try {
      for (ObjectName objectName : objectNames) {
        String protocol = (String) mBeanServer.getAttribute(objectName, "protocol");
        if (protocol.equals("HTTP/1.1")) {
          portStr = String.valueOf(mBeanServer.getAttribute(objectName, "port"));
          break;
        }

      }
    } catch (Exception e) {
      log.error("", e);
    }
    return portStr;
  }
}
