package com.topsec.wilkes.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@ConditionalOnPropertyArrayContains(propertyName = "wilkes.run.module", expectedValue = "correlation", defaultValue = "correlation")
public @interface EnabledCorrelation {
}
