package com.topsec.wilkes.behaviour.service.impl.util;

import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.behaviour.service.impl.BehaviourAlgorithmServiceImpl;

import com.google.common.collect.Lists;
import com.google.common.io.ByteStreams;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class ResourcesUtil {
  private static List<Object> portraitInfoList;

  public static List<Object> getListFromResource(String name) {
    try {
      return Arrays.asList(ToolUtils.OBJECT_MAPPER.readValue(new String(ByteStreams
          .toByteArray(BehaviourAlgorithmServiceImpl.class.getResourceAsStream(name))), Object[].class));
    } catch (IOException e) {
      log.error("", e);
      return Lists.newArrayList();
    }
  }

  public static List<Object> getPortraitBaseInfo(String name) {
    if (portraitInfoList == null) {
      portraitInfoList = getListFromResource(name);
    }
    return portraitInfoList;
  }
}
