package com.topsec.wilkes.repository.entity;

import com.topsec.wilkes.dto.OfflineTask;
import com.topsec.wilkes.dto.task.ExecuteTaskType;
import com.topsec.wilkes.dto.task.StageType;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "offline_task")
@Data
@Slf4j
public class OfflineTaskEntity {

  @Id
  private String id;

  private String taskId;

  @Column
  @Enumerated(EnumType.STRING)
  private StageType stageType;

  @Column
  @Enumerated(EnumType.STRING)
  private ExecuteTaskType executeTaskType;

  @ManyToOne
  @JoinColumn(name = "offline_id")
  private OfflineEntity offlineEntity;

  public OfflineTaskEntity() {
  }

  public OfflineTask convertToOfflineTask() {
    OfflineTask offlineTask = new OfflineTask();
    offlineTask.setId(this.id);
    offlineTask.setTaskId(this.taskId);
    offlineTask.setOfflineId(this.offlineEntity.getId());
    offlineTask.setStageType(this.stageType);
    offlineTask.setExecuteTaskType(this.executeTaskType);
    return offlineTask;
  }
}
