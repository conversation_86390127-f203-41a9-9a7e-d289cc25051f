<t-dialog header="模型分发" [(visible)]="display" [baseZIndex]="1000000">
  <div style="width: 800px;height: 300px;">
    <div class="distribute-con">
      <div class="select-tages-con">
        <ul>
          <li *ngFor="let item of selectData;index as i;trackBy: trackByFn" [title]="item.name"
            [ngStyle]="{backgroundColor: item.backgroundColor}">
            {{item.name}}
            <i class="icon-smallcolse" *ngIf="!item.dis" (click)="delSelectData(i)"></i>
          </li>
        </ul>
      </div>
      <div class="distribute-tages-con">
        <div class="search-con">
          <input placeholder="请输入平台名称" type="text" size="30" style="width: 60%;" tInputText [(ngModel)]="searchText">
        </div>
        <div class="tages-con">
          <ul>
            <li *ngFor="let item of (platLists | secondFilter:true:searchText);index as i;trackBy: trackByFn"
              [title]="item.name">
              <t-checkbox name="platLists" [value]="item" [label]="item.name" [disabled]="item.dis"
                [(ngModel)]="selectData" (onChange)="changeData()"></t-checkbox>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
  <t-footer>
    <button type="button" tButton [disabled]="selectData?.length === 0" label="确定"
      (click)="distributeSave(true)"></button>
    <button type="button" tButton label="取消" class="ui-button-secondary" (click)="distributeSave()"></button>
  </t-footer>
</t-dialog>