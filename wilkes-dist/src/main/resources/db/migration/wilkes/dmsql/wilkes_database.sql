declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'config' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "config" (
                                      "name" varchar(255) NOT NULL,
                                      "value" text DEFAULT NULL,
                                      PRIMARY KEY ("name")
            );
            ';
        execute immediate
           '
           insert into "config"("name", "value") values (''knowledgebase.datasource.type'', ''mysql'');
           insert into "config"("name", "value") values (''knowledgebase.datasource.ip'', ''mysql'');
           insert into "config"("name", "value") values (''knowledgebase.datasource.port'', ''17306'');
           insert into "config"("name", "value") values (''knowledgebase.datasource.name'', ''knowledge'');
           insert into "config"("name", "value") values (''knowledgebase.datasource.username'', ''knowledge'');
           insert into "config"("name", "value") values (''knowledgebase.datasource.password'', ''EP6r!feN$W'');
           ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'data_model' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "data_model" (
                                          "id" varchar(255) NOT NULL,
                                          "alias" varchar(255) DEFAULT NULL,
                                          "description" varchar(255) DEFAULT NULL,
                                          "enrichments" text,
                                          "fact_table_name" varchar(255) DEFAULT NULL,
                                          "name" varchar(255) DEFAULT NULL,
                                          "type" varchar(255) DEFAULT NULL,
                                          "storage_type" varchar(255) DEFAULT NULL,
                                          PRIMARY KEY ("id")
            );
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'data_source' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "data_source" (
                                           "id" varchar(255) NOT NULL,
                                           "name" varchar(255) DEFAULT NULL,
                                           "properties" varchar(255) DEFAULT NULL,
                                           "type" varchar(255) DEFAULT NULL,
                                           "url" varchar(255) DEFAULT NULL,
                                           "fact_table_schema" text,
                                           "es_url" varchar(255) DEFAULT NULL,
                                           PRIMARY KEY ("id")
            );
            ';
        execute immediate
            '
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''http_log'', ''HTTP日志'', ''kafka://kafka:23092/http_log'', ''es://elasticsearch:23200/http_log/http_log'', ''数据'', '''', ''{}'');
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''ftp_log'', ''FTP日志'', ''kafka://kafka:23092/ftp_log'', ''es://elasticsearch:23200/ftp_log/ftp_log'', ''数据'', '''', ''{}'');
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''dns_log'', ''DNS日志'', ''kafka://kafka:23092/dns_log'', ''es://elasticsearch:23200/dns_log/dns_log'', ''数据'', '''', ''{}'');
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''mail_log'', ''MAIL日志'', ''kafka://kafka:23092/mail_log'', ''es://elasticsearch:23200/mail_log/mail_log'', ''数据'', '''', ''{}'');
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''platform'', ''PLATFORM日志'', ''kafka://kafka:23092/platform'', ''es://elasticsearch:23200/platform/platform'', ''数据'', '''', ''{}'');
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''os_log'', ''OS日志'', ''kafka://kafka:23092/os_log'', ''es://elasticsearch:23200/os_log/os_log'', ''数据'', '''', ''{}'');
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''netflow_log'', ''NETFLOW日志'', ''kafka://kafka:23092/netflow_log'', ''es://elasticsearch:23200/netflow_log/netflow_log'', ''数据'', '''', ''{}'');
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''sql_log'', ''SQL日志'', ''kafka://kafka:23092/sql_log'', ''es://elasticsearch:23200/sql_log/sql_log'', ''数据'', '''', ''{}'');
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''security_log'', ''安全事件'', ''kafka://kafka:23092/security_log'', ''es://elasticsearch:23200/security_log/security_log'', ''数据'', '''', ''{}'');
            insert into "data_source"("id", "name", "url", "es_url", "type", "fact_table_schema", "properties") values(''alarm_event'', ''告警事件'', ''kafka://kafka:23092/alarm_event'', ''es://elasticsearch:23200/alarm_event/alarm_event'', ''数据'', '''', ''{}'');
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'model' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "model" (
                                     "id" varchar(255) NOT NULL,
                                     "action" text,
                                     "content" text,
                                     "create_time" timestamp NOT NULL,
                                     "creator" varchar(255) DEFAULT NULL,
                                     "description" varchar(4096) DEFAULT NULL,
                                     "last_updated_time" timestamp DEFAULT NULL,
                                     "model_type" varchar(255) DEFAULT NULL,
                                     "name" varchar(255) DEFAULT NULL,
                                     "no" varchar(255) DEFAULT NULL,
                                     "source" varchar(255) DEFAULT NULL,
                                     "status" varchar(255) DEFAULT NULL,
                                     "abnormal" VARCHAR(4096) DEFAULT NULL,
                                     "tenant_id" VARCHAR(255) DEFAULT NULL,
                                     "rule_id" VARCHAR(255) DEFAULT NULL,
                                     "domain_desc" text,
                                     "reason" text,
                                     "history" varchar(255) DEFAULT NULL,
                                     "platforms" varchar(255) DEFAULT NULL,
                                     PRIMARY KEY ("id"),
                                     UNIQUE ("no"),
                                     UNIQUE ("name")
            );
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'model_task' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "model_task" (
                                          "id" varchar(255) NOT NULL,
                                          "job_id" varchar(255) DEFAULT NULL,
                                          "model_task_type" varchar(255) DEFAULT NULL,
                                          "stage" varchar(255) DEFAULT NULL,
                                          "model_id" varchar(255) DEFAULT NULL,
                                          "execute_task_type" varchar(255) DEFAULT NULL,
                                          "stage_type" varchar(255) DEFAULT NULL,
                                          "task_id" varchar(255) DEFAULT NULL,
                                          PRIMARY KEY ("id")
            );
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'tag' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "tag" (
                                   "id" varchar(255) NOT NULL,
                                   "description" varchar(255) DEFAULT NULL,
                                   "group_id" varchar(255) DEFAULT NULL,
                                   "icon" varchar(255) DEFAULT NULL,
                                   "label" varchar(255) DEFAULT NULL,
                                   "level_num" varchar(255) DEFAULT NULL,
                                   "name" varchar(255) DEFAULT NULL,
                                   "name_en" varchar(255) DEFAULT NULL,
                                   "parent_id" varchar(255) DEFAULT NULL,
                                   "flag" varchar(255) DEFAULT NULL,
                                   "order" varchar(255) DEFAULT NULL,
                                   "tenant_id" varchar(255) DEFAULT NULL,
                                   PRIMARY KEY ("id")
            );
            ';

    execute immediate
        '
        -- 杀伤链
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''559795470'', NULL, ''非杀伤链日志'', ''Non-KillChain'', ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''0'');
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''631202532'', NULL, ''侦察跟踪'', ''Reconnaissance'', ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''1'');
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''841176888'', NULL, ''武器构建'', ''Weaponization'', ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''2'');
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''1127474487'', NULL, ''载荷投递'', ''Delivery'', ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''3'');
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''874514510'', NULL, ''漏洞利用'', ''Exploit'', ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''4'');
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''732594516'', NULL, ''安装植入'', ''Installation'', ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''5'');
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''-878327146'', NULL, ''命令与控制'', ''Command&Control'', ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''6'');
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''933775019'', NULL, ''目标达成'', ''Accomplish'', ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''7'');
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''934967909'', NULL, ''痕迹清理'', ''Clear'',  ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''8'');
        INSERT INTO "tag"("id", "parent_id", "name", "name_en", "group_id", "level_num", "label", "icon", "description", "order") VALUES (''-1135940034'', NULL, ''未知杀伤链阶段'', ''Unknown'', ''KILL_CHAIN'', ''1'', ''false'', NULL, NULL, ''9'');
        -- 事件标签
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''隧道通讯'', NULL, ''隧道通讯'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''DNS'', NULL, ''DNS'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''异常'', NULL, ''异常'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''认证'', NULL, ''认证'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''域控制器'', NULL, ''域控制器'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''失陷主机'', NULL, ''失陷主机'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''扫描'', NULL, ''扫描'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''访问'', NULL, ''访问'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''观察表'', NULL, ''观察表'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''关键资产'', NULL, ''关键资产'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''异常浏览'', NULL, ''异常浏览'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''离职倾向'', NULL, ''离职倾向'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''病毒'', NULL, ''病毒'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''暴力破解'', NULL, ''暴力破解'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''邮箱'', NULL, ''邮箱'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''DDOS'', NULL, ''DDOS'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''撞库'', NULL, ''撞库'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''SQL注入'', NULL, ''SQL注入'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''XSS攻击'', NULL, ''XSS攻击'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''提权'', NULL, ''提权'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''泄露'', NULL, ''泄露'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''服务器'', NULL, ''服务器'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''攻击'', NULL, ''攻击'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''横向'', NULL, ''横向'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''横向移动'', NULL, ''横向移动'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''攻击倾向'', NULL, ''攻击倾向'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''违规'', NULL, ''违规'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''操作'', NULL, ''操作'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''特权'', NULL, ''特权'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''密码修改'', NULL, ''密码修改'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        INSERT INTO "tag"("id", "parent_id", "name", "group_id", "level_num", "label", "icon", "description") VALUES (''新账号'', NULL, ''新账号'', ''EVENT_TAG'', ''1'', ''false'', NULL, NULL);
        -- ATT&CK
		-- 初始访问
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''649474525'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''初始访问'', NULL, ''10'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''649474525_T1189'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''路过损害'', ''649474525'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''649474525_T1190'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''利用面向公众的应用程序'', ''649474525'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''649474525_T1133'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''外部远程服务'', ''649474525'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''649474525_T1200'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''硬件添加'', ''649474525'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''649474525_T1556'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''网络钓鱼'', ''649474525'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''649474525_T1091'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''通过移动存储进行复制'', ''649474525'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''649474525_T1195'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''供应链损害'', ''649474525'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''649474525_T1199'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''信任关系'', ''649474525'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''649474525_T1078'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''有效账号'', ''649474525'', NULL, NULL, NULL);

		-- 执行
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''815813'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''执行'', NULL, ''11'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1059'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''命令和脚本解释器'', ''815813'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1203'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''利用客户执行'', ''815813'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1559'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''进程间通讯'', ''815813'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1106'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''本机API'', ''815813'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1053'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''计划任务/工作'', ''815813'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1129'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''共享模块'', ''815813'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1072'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''软件部署工具'', ''815813'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1569'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''系统服务'', ''815813'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1204'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''用户执行'', ''815813'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''815813_T1047'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''Windows管理规范'', ''815813'', NULL, NULL, NULL);

		--持久化
        INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''24998962'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''持久化'', NULL, ''12'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1098'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''账号操作'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1197'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''位工作'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1547'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''引导或登录自动启动执行'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1037'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''引导或登录初始化脚本'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1176'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''浏览器扩展'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1554'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''损害客户端软件二进制'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1136'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''创建账号'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1543'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''创建或修改系统过程'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1546'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''事件触发执行'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1133'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''外部远程服务'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1574'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''劫持执行流程'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1525'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''植入容器图像'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1137'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''Office应用程序启动'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1542'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''操作系统前启动'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1053'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''计划任务/工作'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1505'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''服务器软件组件'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1205'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''交通信号'', ''24998962'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''24998962_T1078'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''有效账号'', ''24998962'', NULL, NULL, NULL);

		--权限提升
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''825197677'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''权限提升'', NULL, ''13'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1548'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''滥用海拔控制机制'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1134'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''访问令牌操纵'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1547'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''引导或登录自动启动执行'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1037'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''引导或登录初始化脚本'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1543'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''创建或修改系统过程'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1546'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''事件触发执行'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1068'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''利用特权升级'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1484'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''组策略修改'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1574'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''劫持执行流程'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1055'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''工艺注入'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1053'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''计划任务/工作'', ''825197677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''825197677_T1078'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''有效账号'', ''825197677'', NULL, NULL, NULL);

		--防御绕过
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''1170169988'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''防御绕过'', NULL, ''14'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1548'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''滥用海拔控制机制'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1134'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''访问令牌操纵'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1197'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''位工作'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1140'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''反混淆/解码文件或信息'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1006'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''直接卷访问'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1480'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''执行护栏'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1211'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''为逃避国防开发'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1222'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''文件和目录权限修改'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1484'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''组策略修改'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1564'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''隐藏文物'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1574'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''劫持执行流程'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1562'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''损害防御'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1070'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''主机上的指示灯卸下'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1202'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''间接命令执行'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1036'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''伪装'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1556'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''修改身份验证过程'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1578'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''修改云计算基础架构'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1112'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''修改注册表'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1027'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''混淆的文件或信息'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1542'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''操作系统前启动'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1055'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''工艺注入'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1207'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''流氓域控制器'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1014'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''根套件'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1218'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''签名的二进制代理执行'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1216'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''签名脚本代理执行'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1553'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''颠覆信任控制'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1221'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''模板注入'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1205'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''交通信号'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1127'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''受信任的开发人员实用程序代理执行'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1535'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''未使用/不受支持的云区域'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1550'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''使用备用身份验证材料'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1078'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''有效账号'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1497'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''虚拟化/沙盒规避'', ''1170169988'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1170169988_T1220'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''XSL脚本处理XSL Script Processing'', ''1170169988'', NULL, NULL, NULL);

		--凭据获取
        INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''650415344'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''凭据获取'', NULL, ''15'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1110'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''蛮力'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1555'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''密码存储中的凭证'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1212'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''利用凭据访问'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1187'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''强制认证'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1056'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''输入捕捉'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1557'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''中间人'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1556'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''修改身份验证过程'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1040'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''网络嗅探'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1003'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''操作系统凭据转储'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1528'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''窃取应用程序访问令牌'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1558'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''窃取或伪造Kerberos票证'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1539'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''窃取Web会话Cookie'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1111'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''两因素身份验证拦截'', ''650415344'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''650415344_T1552'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''无抵押凭证'', ''650415344'', NULL, NULL, NULL);

		--发现
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''694783'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''发现'', NULL, ''16'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1087'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''账号发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1010'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''应用程序窗口发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1217'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''浏览器书签发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1538'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''云服务仪表板'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1526'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''云服务发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1482'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''域信任发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1083'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''文件和目录发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1046'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''网络服务扫描'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1135'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''网络共享发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1040'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''网络嗅探'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1201'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''密码策略发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1120'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''周边设备发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1069'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''权限组发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1057'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''流程发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1012'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''查询注册表'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1018'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''远程系统发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1518'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''软件发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1082'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''系统信息发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1016'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''系统网络配置发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1049'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''系统网络连接发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1033'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''系统所有者/用户发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1007'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''系统服务发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1124'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''系统时间发现'', ''694783'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''694783_T1497'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''虚拟化/沙箱逃逸'', ''694783'', NULL, NULL, NULL);

		--横向移动
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''831330676'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''横向移动'', NULL, ''17'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''831330676_T1210'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''远程服务利用'', ''831330676'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''831330676_T1534'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''内部鱼叉'', ''831330676'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''831330676_T1570'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''横向工具转移'', ''831330676'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''831330676_T1563'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''远程服务会话劫持'', ''831330676'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''831330676_T1021'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''远程服务'', ''831330676'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''831330676_T1091'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''通过可移动媒体复制'', ''831330676'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''831330676_T1072'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''软件部署工具'', ''831330676'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''831330676_T1080'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''污染共享内容'', ''831330676'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''831330676_T1550'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''使用备用身份验证材料'', ''831330676'', NULL, NULL, NULL);

		--搜集
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''1195487'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''搜集'', NULL, ''18'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1560'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''存档收集的数据'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1123'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''音讯撷取'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1119'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''自动收集'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1115'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''剪贴板数据'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1530'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''来自云存储对象的数据'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1213'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''信息库中的数据'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1005'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''来自本地系统数据的数据'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1039'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''来自共享网络驱动器的数据'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1025'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''来自可移动媒体的数据'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1074'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''数据分段'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1114'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''邮件收集'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1056'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''输入捕捉'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1185'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''浏览器中的人'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1557'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''中间人'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1113'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''屏幕截图'', ''1195487'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''1195487_T1125'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''视频截取'', ''1195487'', NULL, NULL, NULL);

		--命令与控制
        INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''0650215677'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''命令与控制'', NULL, ''19'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1071'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''应用层协议'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1092'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''通过可移动媒体进行通信'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1132'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''数据编码'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1001'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''数据混淆'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1568'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''动态分辨率'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1573'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''加密频道'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1008'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''后备频道'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1105'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''入口工具转移'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1104'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''多阶段频道'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1095'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''非应用层协议'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1571'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''非标准端口'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1572'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''协议隧道'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1090'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''代理'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1219'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''远程访问软件'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1205'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''交通信号'', ''0650215677'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''0650215677_T1102'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''网络服务'', ''0650215677'', NULL, NULL, NULL);

		--数据渗出
        INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''798976086'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''数据渗出'', NULL, ''20'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''798976086_T1020'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''自动化渗出'', ''798976086'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''798976086_T1030'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''数据传输大小限制'', ''798976086'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''798976086_T1048'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''通过替代协议渗出'', ''798976086'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''798976086_T1041'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''通过C2通道渗出'', ''798976086'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''798976086_T1011'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''通过其他网络介质渗出'', ''798976086'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''798976086_T1052'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''通过物理介质渗出'', ''798976086'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''798976086_T1067'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''通过Web服务渗出'', ''798976086'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''798976086_T1029'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''计划转移'', ''798976086'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''798976086_T1537'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''数据转移到云账户'', ''798976086'', NULL, NULL, NULL);

		--影响
        INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "order", "name_en") VALUES (''756768041'', NULL, ''ATTCK_TAG'', NULL, ''true'', ''1'', ''影响'', NULL, ''21'', NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1531'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''账户访问权限删除'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1485'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''数据销毁'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1486'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''加密数据以产生影响'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1565'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''数据处理'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1491'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''污损'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1561'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''磁盘擦拭'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1499'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''端点拒绝服务'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1495'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''固件损坏'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1490'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''禁止系统恢复'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1498'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''网络拒绝服务'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1496'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''资源劫持'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1489'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''服务停止'', ''756768041'', NULL, NULL, NULL);
		INSERT INTO "tag"("id", "description", "group_id", "icon", "label", "level_num", "name", "parent_id", "flag", "order", "name_en") VALUES(''756768041_T1529'', NULL, ''ATTCK_TAG'', NULL, ''false'', ''2'', ''系统关机/重启'', ''756768041'', NULL, NULL, NULL);
        --attck改为知识库初始化
        DELETE FROM tag WHERE group_id = ''ATTCK_TAG'';
		';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'model_tags' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "model_tags" (
                                          "model_id" varchar(255) NOT NULL,
                                          "tag_id" varchar(255) NOT NULL,
                                          CONSTRAINT "FKp0uicchrjmhtsrhcqy6o66mhe" FOREIGN KEY ("tag_id") REFERENCES "tag" ("id"),
                                          CONSTRAINT "FKr1pc79fwyp8texb5n2kd1hd4u" FOREIGN KEY ("model_id") REFERENCES "model" ("id")
            );
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'monitor_info' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "monitor_info" (
                                            "id" varchar(255) NOT NULL,
                                            "model_no" varchar(255) DEFAULT NULL,
                                            "operator_id" varchar(255) DEFAULT NULL,
                                            "type" varchar(255) DEFAULT NULL,
                                            "value" varchar(255) DEFAULT NULL,
                                            "last_updated_time" timestamp DEFAULT NULL,
                                            PRIMARY KEY ("id")
            );
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'model_original' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE  "model_original" (
                                               "action" text,
                                               "content" text,
                                               "no" varchar(255),
                                               "name" varchar(255) DEFAULT NULL,
                                               PRIMARY KEY ("no"),
                                               UNIQUE ("name")
            );
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'offline' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "offline" (
                                       "id" varchar(255) NOT NULL,
                                       "model_no" varchar(255) DEFAULT NULL,
                                       "model_name" varchar(255) DEFAULT NULL,
                                       "model_type" varchar(255) DEFAULT NULL,
                                       "offline_status" varchar(255) DEFAULT NULL,
                                       "start_time" timestamp DEFAULT NULL,
                                       "end_time" timestamp DEFAULT NULL,
                                       "trigger_response" number(1) DEFAULT NULL,
                                       "create_time" timestamp NOT NULL,
                                       "creator" varchar(255) DEFAULT NULL,
                                       "action" text,
                                       "content" text,
                                       "offline_type" varchar(255) DEFAULT NULL,
                                       PRIMARY KEY ("id")
            );
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'offline_task' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "offline_task" (
                                            "id" varchar(255) NOT NULL,
                                            "offline_task_type" varchar(255) DEFAULT NULL,
                                            "stage" varchar(255) DEFAULT NULL,
                                            "offline_id" varchar(255) DEFAULT NULL,
                                            "execute_task_type" varchar(255) DEFAULT NULL,
                                            "stage_type" varchar(255) DEFAULT NULL,
                                            "task_id" varchar(255) DEFAULT NULL,
                                            PRIMARY KEY ("id"),
                                            CONSTRAINT "offline_id" FOREIGN KEY ("offline_id") REFERENCES "offline" ("id")
            );
            ';
    end if;
end$$


declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'model_distribute' ;
    if num = 0 then
        execute immediate
        '
            CREATE TABLE "model_distribute"(
                "id" varchar(255) NOT NULL,
                "model_no" varchar(255) NOT NULL,
                "platform_id" varchar(255) NOT NULL,
                PRIMARY KEY ("id")
            );
        ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'model_distribute_operation' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE  "model_distribute_operation"(
                                                          "id" varchar(255) NOT NULL,
                                                          "model_no" varchar(255) NOT NULL,
                                                          "platform_id" varchar(255) NOT NULL,
                                                          "operation_time" timestamp DEFAULT NULL,
                                                          "operation_num" varchar(255) DEFAULT NULL,
                                                          "operation" varchar(255) DEFAULT NULL,
                                                          "status" varchar(255) DEFAULT NULL,
                                                          "failure" VARCHAR(255) DEFAULT NULL,
                                                          PRIMARY KEY ("id")
            );
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_views where view_name = 'model_monitor_info' ;
    if num = 0 then
        execute immediate
            '
            create view model_monitor_info as select a.*,b."value" as last_trigger_time from model a left join monitor_info b on a."no" = b.model_no and b."type" = ''TRIGGER_TIME'';
            ';
    end if;
end$$

declare num  number;
begin
    select count(1) into num from user_tables where table_name = 'model_history' ;
    if num = 0 then
        execute immediate
            '
            CREATE TABLE "model_history" (
                                     "id" varchar(255) NOT NULL,
                                     "action" text,
                                     "content" text,
                                     "create_time" timestamp NOT NULL,
                                     "creator" varchar(255) DEFAULT NULL,
                                     "description" varchar(4096) DEFAULT NULL,
                                     "last_updated_time" timestamp DEFAULT NULL,
                                     "model_type" varchar(255) DEFAULT NULL,
                                     "name" varchar(255) DEFAULT NULL,
                                     "no" varchar(255) DEFAULT NULL,
                                     "source" varchar(255) DEFAULT NULL,
                                     "status" varchar(255) DEFAULT NULL,
                                     "abnormal" VARCHAR(255) DEFAULT NULL,
                                     "tenant_id" VARCHAR(255) DEFAULT NULL,
                                     "rule_id" VARCHAR(255) DEFAULT NULL,
                                     "domain_desc" text,
                                     "reason" text,
                                     "history" varchar(255) DEFAULT NULL,
                                     PRIMARY KEY ("id")
            );
            ';
    end if;
end$$