<div class="ai-con ai-main" (mousedown)="aiMoreClick(null)">
  <div class="ai-content">
    <t-scrollPanel [style]="{ width: '100%', height: '100%' }">
      <div class="sel-con">
        <div class="tag-con">
          <label>标签筛选</label>
          <ul #ul class="tag-ul-con" [ngStyle]="{ height: isUpDown ? 'auto' : '28px' }">
            <li *ngFor="let data of tageList; index as i" [ngStyle]="setLabelStyle(i, data.value)"
              (click)="getIndex(data.value)">
              {{ data.value }}
            </li>
          </ul>
          <!-- <button tButton  icon="icon-down" iconPos="right" class="ui-button-nav more-btn" type="button" label="更多" (click)="isUpDown = !isUpDown"></button> -->
          <span *ngIf="ul.clientWidth < tageList.length * 82" class="more-btn icon-down file-name"
            (click)="isUpDown = !isUpDown">更多</span>
        </div>
        <div class="name-con flex-ai">
          <label>模型名称</label>
          <input type="text" tInputText [(ngModel)]="selectObj.name" placeholder="请输入模型名称" />
          <button class="btn ui-button-primary" tButton type="button" icon="icon-search2" label="查询"
            (click)="getAIModules()"></button>
          <button tButton type="button" class="iconfont icon-loop ui-button-color-grey" label=""
            style="margin-left: 10px" (click)="setOperation()"></button>
        </div>
      </div>
      <div class="result-con">
        <t-nodata *ngIf="lodding" [noDataType]="lodding" [noDatatext]="loddingText"></t-nodata>
        <div class="ui-g" *ngIf="!lodding">
          <div class="ui-sm-12 ui-md-6 ui-lg-4 p-relative" *ngFor="let data of aiModules; index as i">
            <div class="details-con">
              <div class="ai-model-icon kin-color kin-ai-cr">
                <i class="icon-model-management-one"></i>
              </div>
              <div class="cove-con" *ngIf="data.moreIsNo"></div>
              <div class="ai-title" (click)="toDetailsRouter(data)">
                <p class="ai-title-name">
                  <span class="name" tTooltip="{{ data.name || '' }}" tooltipPosition="bottom">{{ data?.name }}</span>
                  <span class="time">{{ data?.lastTriggerTime }}</span>
                </p>
                <span class="ai-title-no">#{{ data?.no }}</span>
              </div>
              <ul class="tages-ul ai-tages">
                <li [ngStyle]="{
                    color: colorArr['normal'][i][0],
                    'border-color': colorArr['normal'][i][1]
                  }" class="tages-li" *ngFor="let tage of data?.tags; index as i">
                  {{ tage.value }}
                </li>
              </ul>
              <div class="ai-text" aas uri="/wilkes-ai" act="/wilkes/models/ai/edit/*" dpt="disable"
                tTooltip="{{ data.desc || '' }}" tooltipPosition="bottom" [ellipsis]="data?.desc"
                (click)="toDetailsRouter(data)">
                {{ data?.desc }}
              </div>
              <div class="ai-operation">
                <t-switch [(ngModel)]="data.isNO" [disabled]="'' | authority:data:isSaas" (change)="openOrClose(data)">
                </t-switch>
                <div class="float-r" *ngIf="data?.events?.length > 0">
                  <t-optionsButton label="查看事件" appendTo="body" icon="pi pi-check" [model]="data.events"
                    [style]="{ marginRight: '6px' }" styleClass="rule-splitButton"></t-optionsButton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </t-scrollPanel>
  </div>
</div>