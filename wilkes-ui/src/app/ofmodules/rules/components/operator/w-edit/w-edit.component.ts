import {
  Component,
  OnInit,
  Input,
  forwardRef,
  Output,
  EventEmitter,
  ViewChild,
  AfterViewInit,
} from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { Rule, RuleSet } from '@tui/component-library';
import { TeditService } from 'src/app/services/t-edit.service';
import { ComToComService } from '../../../services/com-to-com.service';

@Component({
  selector: 'w-edit',
  templateUrl: './w-edit.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => WEditComponent),
      multi: true,
    },
  ],
})
export class WEditComponent implements OnInit, AfterViewInit {
  @Input() id: string;
  @Input() disabled = false;
  @ViewChild('t_editContion') t_editContion: any;
  @Output() change = new EventEmitter<any>();
  @Output() errorMes = new EventEmitter<any>();
  value = new RuleSet();
  highValue: any;
  hiddenArr: string[];
  attributes: any;
  isError = false;
  _option = [];
  // 验证规则
  verifys = [
    {
      field: ['src_ip', 'dst_ip'],
      operator: [
        'rlike',
        'like',
        'ISNULL',
        'empty',
        'blank',
        'PARAMETER_POLLUTION_DETECT',
        'SQL_INJECTION_DETECT',
        'notRlike',
        'notLike',
        'ISNOTNULL',
        'notEmpty',
        'notBlank',
      ],
      reg: new RegExp(
        /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/
      ),
      mes: '输入的ip字段格式有误,可能会影响模型触发',
    },
    {
      field: ['src_ip6', 'dst_ip6'],
      operator: [
        'rlike',
        'like',
        'ISNULL',
        'empty',
        'blank',
        'PARAMETER_POLLUTION_DETECT',
        'SQL_INJECTION_DETECT',
        'notRlike',
        'notLike',
        'ISNOTNULL',
        'notEmpty',
        'notBlank',
      ],
      reg: new RegExp(
        /^([\da-fA-F]{1,4}:){6}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^::([\da-fA-F]{1,4}:){0,4}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:):([\da-fA-F]{1,4}:){0,3}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){2}:([\da-fA-F]{1,4}:){0,2}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){3}:([\da-fA-F]{1,4}:){0,1}((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){4}:((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$|^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^:((:[\da-fA-F]{1,4}){1,6}|:)$|^[\da-fA-F]{1,4}:((:[\da-fA-F]{1,4}){1,5}|:)$|^([\da-fA-F]{1,4}:){2}((:[\da-fA-F]{1,4}){1,4}|:)$|^([\da-fA-F]{1,4}:){3}((:[\da-fA-F]{1,4}){1,3}|:)$|^([\da-fA-F]{1,4}:){4}((:[\da-fA-F]{1,4}){1,2}|:)$|^([\da-fA-F]{1,4}:){5}:([\da-fA-F]{1,4})?$|^([\da-fA-F]{1,4}:){6}:$/
      ),
      mes: '输入的ipv6字段格式有误,可能会影响模型触发',
    },
  ];
  mes: string;

  @Input() set option(arr) {
    if (arr && arr.length) {
      this._option = arr;
      if (new RegExp('field-aggregation').test(this.id))
        this._option.unshift({
          label: '未分类',
          items: [{ value: '*', label: '*', type: 'number', items: '' }],
        });
    }
  }
  // 编译样式
  allStyle: any;
  public onModelChange: Function = () => { };
  public onModelTouched: Function = () => { };
  operators: any[] = [];
  groupHidden: boolean;
  attr: boolean = false;
  sels: any;
  constructor(
    private teditService: TeditService,
    private comToComService: ComToComService
  ) { }

  ngOnInit() {
    this.attributes = this.teditService.attributes;
    this.init();
  }

  ngAfterViewInit(): void { }

  init() {
    // 条件表达式样式
    this.allStyle = {
      dataIputStyle: { width: '170px', btnStyle: { marginRight: '5px' } },
      iconStyle: {},
      selStyle: {
        width: '146px',
        borderRadius: '1px',
        fontSize: '13px',
        marginRight: '5px',
      },
      btnStyle: { fontSize: '12px', margin: '0 5px' },
      inputStyle: {
        width: '146px',
        height: '32px',
        marginRight: '5px',
        borderRadius: '1px',
      },
      selStyleAndOR: { width: '200px' },
      selStyleCount: { width: '100px' },
      inputStyleCount: { width: '100px', verticalAlign: 'initial' },
      selStyleLast: {},
    };
    // 规则编译器隐藏值函数集合
    this.hiddenArr = [
      'workingDay',
      'nonWorkingDay',
      'nonWorkingHour',
      'workingHour',
      'ISNULL',
      'ISNOTNULL',
      'PARAMETER_POLLUTION_DETECT',
      'SQL_INJECTION_DETECT',
      'empty',
      'notEmpty',
      'blank',
      'notBlank',
    ];
    this.addRuleSet();
  }

  writeValue(v): void {
    if (v) {
      this.highValue = v.predication;
      this.value = !v.expression ? this.value : this.setRuleSet(v.expression);
    }
    this.isError = this.comToComService.postData.includes(this.id + ',t_edit');
  }

  // 保存
  save() {
    let expression: any = '';
    if (!this.t_editContion.highValue) {
      expression = this.getParamEntery();
      expression.node;
    } else {
      this.comToComService.postData = this.comToComService.postData.filter(
        (id) => !new RegExp(this.id).test(id)
      );
    }
    this.mes && this.errorMes.emit(this.mes);
    this.change.emit();
    this.onModelChange({
      predication: this.t_editContion.highValue,
      expression,
    });
  }

  registerOnChange(fn: any): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onModelTouched = fn;
  }

  // 解析数据
  setRuleSet(v) {
    const usedSel = [];
    const dataS = [JSON.parse(JSON.stringify(v))];
    const getRuleSet = () => {
      const d = new RuleSet();
      d.rules = [];
      return d;
    };
    const ruleSet = getRuleSet();
    // 递归函数
    const repeatFun = (d: any[], ruleSetData, isNo?: boolean) => {
      let ang: any;
      let p: any;
      d.forEach((item) => {
        let child: any;
        if (item.node) {
          child = isNo ? ruleSetData : getRuleSet();
          // tslint:disable-next-line: no-unused-expression
          !isNo && (child.parentId = ruleSetData.id);
          child.relation = item.node.operator.toLocaleUpperCase();
          repeatFun(item.node.children, child);
        } else {
          ang = item.leaf_node;
          const isNO = ang.operator === 'between';
          p = isNO ? ang.right.toString().split(',') : ang.right;
          child = new Rule(
            ang.left || '',
            ang.operator || '',
            (isNO ? p[0] : p) || ''
          );
          usedSel.push(isNO ? p[0] : p);
          child._operator = ang.operator || '';
          child.attr = ang.func || '';
          p[1] && (child.value1 = p[1]);
          // ang.aggType && (child.attr = ang.aggType);
        }
        !isNo && ruleSetData.rules.push(child);
      });
    };
    repeatFun(dataS, ruleSet, true);
    if (ruleSet.rules.length === 0) {
      ruleSet.rules.push(new Rule());
    }
    return ruleSet;
  }

  // 获得规则编译器的业务结构
  getParamEntery() {
    const dataS = JSON.parse(JSON.stringify(this.value));
    // if (!dataS.rules.every(e => e instanceof Rule ? e.field && e.operator && e.value : true)) return null;
    const isArr = [];
    this.mes = '';
    const isFun = (d) => d != null && d !== undefined && d !== '';
    // 保留/排除符合条件的事件', '知识库关联', '保留/排除符合知识库条件的事件'
    const contionFun = (d) => {
      const obj = {
        left: d.field,
        operator: d.operator,
        right: (d.operator === 'between'
          ? [d.value, d.value1].filter(
            (item) => item !== '' || item !== undefined
          )
          : d.value
        ).toString(),
      };
      if (d.func || d.attr) {
        obj['func'] = d.func || d.attr;
      }
      return { leaf_node: obj };
    };
    // 递归函数
    const repeatFun = (d: any[], sData?: any) => {
      let data: any;
      d.forEach((item) => {
        // 判断是否有确实的编译器参数
        if (item.relation) {
          isArr.push(item.rules.length > 0);
          data = {
            node: {
              operator: item.relation.toLocaleLowerCase(),
              children: [],
            },
          };
          repeatFun(item.rules, data.node.children);
        } else {
          isArr.push(
            isFun(item.field) &&
            isFun(item.operator) &&
            (this.hiddenArr.includes(item.operator)
              ? true
              : isFun(item.value))
          );
          if (isArr[isArr.length - 1] && new RegExp('field-filter').test(this.id)) {
            this.verifys.forEach((v) => {
              v.field.indexOf(item.field) !== -1 &&
                v.operator.indexOf(item.operator) == -1 &&
                // ['=', 'trim', 'EQUALIGNORECASE',].includes(item.operator) &&
                !v.reg.test(item.value) &&
                (this.mes = v.mes, isArr.push(false));
            });
          }
          data = contionFun(item);
        }
        sData && sData.push(data);
      });
      return data;
    };
    const d = repeatFun([dataS]);
    // 满足条件去掉，不满足添加入
    this.comToComService.postData = this.comToComService.postData.filter(
      (id) => !new RegExp(this.id).test(id)
    );
    !isArr.every((e) => e) &&
      !this.t_editContion.highValue &&
      this.comToComService.postData.push(this.id + ',t_edit');
    return d;
  }

  addRuleSet() {
    const n = ['field-filter', 'base-filter', 'field-aggregation'].findIndex(
      (e) => new RegExp(e).test(this.id)
    );
    const fun = (type: any, isNo: boolean, isNo1: boolean) => {
      this.sels = this.sels || type;
      this.attr = isNo;
      this.groupHidden = isNo1;
      this.allStyle.selStyleCount.width = isNo
        ? '100px'
        : this.allStyle.selStyle.width;
      this.allStyle.inputStyleCount.width = isNo
        ? '100px'
        : this.allStyle.inputStyle.width;
    };
    if (n === 0) {
      this.operators = this.teditService.getFunList();
      fun(null, false, false);
    } else if (n === 2) {
      this.operators = this.teditService.getFunList(2);
      fun(null, true, false);
    } else {
      this.operators = this.teditService.getFunList(1);
      fun([], false, true);
    }
  }

  nodeChange(e) {
    // e.field && ['severity', 'kill_chain'].includes(e.field) && (this.sels = (e.allItems || this.sels).map(s => ({...s, value: s.value || s.code }))); 
    this.change.emit(e);
  }
}
