<div class="node-detail animate__fadeInDown" *ngIf="isLoaded">
  <div class="list-box mr-10">
    <div class="list-title file-name">模型输入量</div>
    <div class="list-scroller">
      <ng-container *ngIf="modelInfo && modelInfo.input.length; else nullTemplate">
        <ul class="list-group">
          <li class="list-item" [tTooltip]="item.id + ':' + item.rate" *ngFor="let item of modelInfo.input">
            <span>{{ item.id }}:</span>
            <span>{{ item.rate }}</span>
          </li>
        </ul>
      </ng-container>
    </div>
  </div>
  <div class="list-box">
    <div class="list-title file-name">模型输出量</div>
    <div class="list-scroller">
      <ng-container *ngIf="modelInfo && modelInfo.output.length; else nullTemplate">
        <ul class="list-group">
          <li class="list-item" [tTooltip]="item.id + ':' + item.rate" *ngFor="let item of modelInfo.output">
            <span>{{ item.id }}:</span>
            <span>{{ item.rate }}</span>
          </li>
        </ul>
      </ng-container>
    </div>
  </div>
</div>

<ng-template #nullTemplate>
  <div class="is-null">暂无数据...</div>
</ng-template>