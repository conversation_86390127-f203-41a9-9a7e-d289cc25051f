import { Component, EventEmitter, forwardRef, Input, OnInit, Output } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { ComToComService } from '../../../services/com-to-com.service';
@Component({
  selector: 'app-aggregation-filed',
  templateUrl: './aggregation-filed.component.html',
  styleUrls: ['./aggregation-filed.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AggregationFiledComponent),
      multi: true,
    },
  ],
})
export class AggregationFiledComponent implements OnInit {
  @Input() disabled = false;
  value: any = {
    diff_fields: '',
    field: '',
  };
  // 事件关联特殊化展示数据处理
  allHappenCep = {
    arr: [],
    state: '1',
    disabledMap: {},
  };
  isError = false;

  @Input() id = '';
  @Input() options = [];
  @Input() anchor = 2;
  @Input() metadata = [];
  @Output() change = new EventEmitter<any>();
  placeHolder = '请选择字段';

  public onModelChange: Function = () => { };
  public onModelTouched: Function = () => { };
  constructor(private comToComService: ComToComService) { }

  ngOnInit() { }

  writeValue(v): void {
    if (v) {
      if (!v.field && v.diff_fields) {
        this.allHappenCep.state = '2';
        this.addFieldTable();
        // this.allHappenCep.arr[0].list.forEach((k, i) => {
        //   this.allHappenCep.disabledMap[k.field] = (v.diff_fields[i] || '').split(',');
        // });
        v.diff_fields.forEach((valus, indx) => {
          valus.split(',').forEach((item, i) => {
            indx === 0 && i !== 0 && this.addFieldTable(true);
            const k = String.fromCharCode('A'.charCodeAt(0) + indx);
            this.allHappenCep.arr[i][k] = item;
          });
        });
      } else {
        v.field = v.field ? v.field.split(',') : [];
      }
      this.value = v;
    }
    this.isError = this.comToComService.postData.includes(this.id);
  }

  registerOnChange(fn: any): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onModelTouched = fn;
  }

  save() {
    this.value.field = (this.value.field || '').toString();
    // 根据锚点数生成对应的数据结构
    this.value.diff_fields = new Array(this.anchor).fill('');
    // 生成对应的abcd字段
    const keys = this.value.diff_fields.slice().map((k, i) => this.getKey(i));
    // 判断是都一列都有数据有的话匹配加入
    this.allHappenCep.arr.forEach(e =>
      keys.every(k => e[k]) &&
      keys.forEach((k, i) =>
        this.value.diff_fields[i] = this.value.diff_fields[i].split(',').concat([e[k]]).filter(d => d).toString()
        )
    );
    // this.value.diff_fields = Object.values(this.allHappenCep.disabledMap).map((ee: any[]) => ee.toString());
    this.value[this.allHappenCep.state == '1' ? 'diff_fields' : 'field'] = null;
    this.change.emit(this.allHappenCep.state == '1' ? '相同字段' : '不同字段');
    const v = {
      diff_fields: (this.value.diff_fields || []).every((e) => !e) ? null : this.value.diff_fields,
      field: this.value.field,
    };
    this.comToComService.postData = this.comToComService.postData.filter((e) => !new RegExp(this.id).test(e));
    if (!(v.field || (v.diff_fields && v.diff_fields.length > 0))) {
      this.comToComService.postData.push(this.id);
    }
    this.onModelChange(v);
  }

  // 点击事件关联算子相同不相同时
  changeState() {
    this.allHappenCep.arr = [];
    this.value.field = '';
    this.addFieldTable();
  }

  delAllHapenCep(i) {
    this.allHappenCep.arr.splice(i, 1);
  }

  // 点击事件关联算子新增时
  addFieldTable(is?) {
    const o = { list: [], details: '' };
    for (let i = 0; i < this.anchor; i++) {
      const k = this.getKey(i);
      o.list.push({ field: k, header: k });
      o[k] = '';
    }

    this.allHappenCep.arr.push(o);
    if (!is && this.metadata && this.metadata.length > 0) {
      this.comToComService.getFiles(
        this.metadata.map((e) => e.join(this.allHappenCep.state === '2' ? '@' : ',')).toString(),
        (fildes) => {
          fildes.forEach((ee, i) => {
            const k = this.getKey(i);
            this.allHappenCep.arr.forEach((e) => (e[k + 'details'] = ee));
          });
        },
        this.allHappenCep.state === '2',
      );
    }
    // this.allHappenCep.arr.forEach(e => e.details = JSON.parse(JSON.stringify(this.comToComService.fildes)));
  }

  getKey(i) {
    return String.fromCharCode('A'.charCodeAt(0) + i);
  }

  // 关联算子选择字段时业务
  changeField(e, o, p, i) {
    setTimeout(() => {
      // const fun = (arr, disV, value) => {
      //   arr.forEach((item) => {
      //     if (item.value === undefined && item.items && item.items.length > 0) {
      //       // eslint-disable-next-line @typescript-eslint/no-unused-vars
      //       fun(item.items, disV, value);
      //     } else {
      //       arr.forEach((oo) => {
      //         if (oo.value === disV && value !== disV) {
      //           oo.disabled = true;
      //         } else {
      //           delete oo.disabled;
      //         }
      //       });
      //     }
      //   });
      // };
      if (e.value === null) {
        o[p] = '';
      } else {
        o[p] = e.value;
      }
      // this.allHappenCep.arr.forEach(ee => {
      //   if (ee !== o) {
      //     fun(ee.details, o[p], ee[p]);
      //   }
      // });
    }, 0);
  }
}
