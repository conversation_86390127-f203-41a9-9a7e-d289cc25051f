package com.topsec.wilkes.ai.service.impl;

import com.topsec.common.utils.FileUtil;
import com.topsec.common.utils.ToolUtils;
import com.topsec.dm.domain.aas.DomainRange;
import com.topsec.dm.util.HttpHeaderUtil;
import com.topsec.minsky.dto.Result;
import com.topsec.wilkes.ai.config.AIConfig;
import com.topsec.wilkes.ai.dto.AIModel;
import com.topsec.wilkes.ai.dto.AIModelTag;
import com.topsec.wilkes.ai.dto.AIModelTask;
import com.topsec.wilkes.ai.dto.DataModelFilterWrapper;
import com.topsec.wilkes.ai.service.AIModelService;
import com.topsec.wilkes.ai.transform.ModelTransform;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.common.dto.SourceType;
import com.topsec.wilkes.common.dto.Status;
import com.topsec.wilkes.config.AIAlarmConfig;
import com.topsec.wilkes.service.ModelService;
import com.topsec.wilkes.service.NoService;
import com.topsec.wilkes.service.ResoucreDomainService;
import com.topsec.wilkes.task.impl.HeartBeatTask;
import com.topsec.wilkes.util.ThreadPoolUtil;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
 * <AUTHOR> (<EMAIL>)
 */
@Service
@Slf4j
public class AIModelServiceImpl implements AIModelService {
  private static Map<String, List<AIModelTag>> tagMap = new HashMap<>();
  private static final String HEARTBEAT_MODEL_NAME = "心跳会话识别";

  @Autowired
  private AIConfig aiConfig;

  @Autowired
  private ModelService modelService;

  @Autowired
  private ModelTransform modelTransform;

  @Autowired
  private ResoucreDomainService resoucreDomainService;

  @Autowired
  private NoService noService;

  @Value("${model.exclude.ai:false}")
  private boolean excludeAI;

  @Value("${cpu.arch:X86}")
  private String arch;

  @Value("${deeplearn.filter:true}")
  private boolean isDeeplearn;

  private final ExecutorService executor = ThreadPoolUtil.getFixedThreadPool(10);


  @Override
  public AIModel createAIModel(AIModel aiModel) {
    Model model = modelService.createModel(modelTransform.convertToModel(aiModel));
    return modelTransform.converToAIModel(model);
  }

  @Override
  public AIModel getAIModel(String no) {
    return modelTransform.converToAIModel(modelService.getModel(no));
  }

  @Override
  public List<AIModel> getAIModels(List<String> tags, String name) {
    String tenantId = HttpHeaderUtil.getTenantId();
    if (excludeAI) {
      return Collections.emptyList();
    } else {
      return modelService.getModels(ModelType.AI, true).stream()
          .map(model -> modelTransform.converToAIModel(model))
          .map(aiModel -> {
            aiModel.setDataModel(new DataModelFilterWrapper());
            aiModel.setPrediction(new AIModelTask());
            aiModel.setTrain(new AIModelTask());
            aiModel.setActions(Lists.newArrayList());
            return aiModel;
          })
          .filter(alModel -> tags == null || !Collections.disjoint(alModel.getTags().stream().map(t -> t.getValue()).collect(Collectors.toList()), tags))
          .filter(alModel -> Strings.isNullOrEmpty(name) || alModel.getName().toLowerCase().contains(name.toLowerCase()))
          .filter(alModel -> AIAlarmConfig.deeplearnModels.contains(alModel.getNo()) || AIAlarmConfig.deeplearnModels.contains(alModel.getOriginNo()) || !isDeeplearn)
          .filter(alModel -> "".equals(tenantId) ? true : tenantId.equals(alModel.getTenantId()))
          .sorted(new Comparator<AIModel>() {
            @Override
            public int compare(AIModel o1, AIModel o2) {
              return o1.getNo().compareTo(o2.getNo());
            }
          })
          .collect(Collectors.toList());
    }
  }

  @Override
  public AIModel editAIModel(String no, AIModel aiModel) {
    aiModel.setTags(tagMap.getOrDefault(no, new ArrayList<>()));
    Model model = modelService.editModel(no, modelTransform.convertToModel(aiModel));
    return modelTransform.converToAIModel(model);
  }

  @Override
  public Model modifyAIModelSourceByNo(String no) {
    return modelService.modifyModelSourceByNo(no);
  }

  @Override
  public List<AIModel> editAIModel(List<String> ids, Status status) {
    Object aiModelObj = modelService.editModels(ids, status).get("models");
    List<AIModel> aiModelList = Lists.newArrayList();
    if (aiModelObj != null && aiModelObj instanceof List) {
      for (Object o : (List<?>) aiModelObj) {
        aiModelList.add(modelTransform.converToAIModel(Model.class.cast(o)));
      }
    }
    return aiModelList;
//    return modelService.editModels(ids, status).stream().map(model -> modelTransform.converToAIModel(model)).collect(Collectors.toList());
  }

  public Result testAIModel(String id, AIModel aiModel) {
    return modelService.testModel(modelTransform.convertToModel(aiModel));
  }

  @Override
  public Result testAIModel(String id) {
    File file = new File(aiConfig.getAiModelPath() + "/" + id);
    AIModel aiModel = null;
    try {
      aiModel = ToolUtils.OBJECT_MAPPER.readValue(new File(file.getPath() + "/model.json"), AIModel.class);
    } catch (IOException e) {
      e.printStackTrace();
    }
    return modelService.testModel(modelTransform.convertToModel(aiModel));
  }

  @Override
  public List<String> getHeartbeatModelNos() {
    List<String> nos = new ArrayList<>();
    List<Model> models = modelService.getModels(ModelType.AI, true);
    if (!CollectionUtils.isEmpty(models)) {
      nos = models.stream().filter(model -> model.getName().contains(HEARTBEAT_MODEL_NAME)).map(Model::getNo).collect(Collectors.toList());
    }
    return nos;
  }

  public void initAIModelBase() {
    log.info("------AI模型导入开始------");
    File[] files = FileUtil.listFiles(new File(aiConfig.getAiModelPath()), pathname -> !pathname.getName().equals("META-INF") && pathname.isDirectory());
    for (File file : files) {
      uploadAIModel(file);
    }
//    if (HttpHeaderUtil.isSaas && "B-90061".equals(noService.current(SourceType.BUILT_IN_AI))) {
//      initResourceDomainAIModelBase();
//    }
    log.info("------AI模型导入结束------");
    HeartBeatTask.HEARTBEAT_MODEL = getHeartbeatModelNos();
  }

  @Override
  public void uploadAIModel(File file) {
    try {
      AIModel aiModel = ToolUtils.OBJECT_MAPPER.readValue(new File(file.getPath() + "/model.json"), AIModel.class);
      Model model = modelService.getModel(aiModel.getNo().trim());
      if (Strings.isNullOrEmpty(model.getId())) {
        aiModel.setOriginNo(aiModel.getNo().trim());
        AIModel newAIModel = createAIModel(aiModel);
        tagMap.put(newAIModel.getId(), aiModel.getTags());
        tagMap.put(newAIModel.getNo(), aiModel.getTags());
      }
    } catch (Exception e) {
      log.error("", e);
      throw new RuntimeException(e);
    }
  }

  public void initResourceDomainAIModelBase() {
    File[] files = FileUtil.listFiles(new File(aiConfig.getAiModelPath()), pathname -> !pathname.getName().equals("META-INF") && pathname.isDirectory());
    List<DomainRange> domainRangeList = resoucreDomainService.getDomainRangeList();
    if (!CollectionUtils.isEmpty(domainRangeList)) {
      for (DomainRange domainRange : domainRangeList) {
        executor.execute(() -> {
          try {
            for (File file : files) {
              String no = noService.syncNext(SourceType.BUILT_IN_AI);
              AIModel aiModel = ToolUtils.OBJECT_MAPPER.readValue(new File(file.getPath() + "/model.json"), AIModel.class);
              aiModel.setOriginNo(aiModel.getNo().trim());
              aiModel.setName(domainRange.getField() + "-" + aiModel.getName());
              aiModel.setNo(no);
              aiModel.setTenantId(domainRange.getId());
              aiModel.setDomainDesc(domainRange.getDesc());
              Model model = modelService.getModel(aiModel.getNo().trim());
              if (Strings.isNullOrEmpty(model.getId())) {
                createAIModel(aiModel);
                AIAlarmConfig.ModelAlgorithmInfo.put(aiModel.getNo(), AIAlarmConfig.ModelAlgorithmInfo.get(aiModel.getOriginNo()));
              }
            }
          } catch (Error | Exception e) {
            log.error("{} 初始化AI模型任务运行异常,线程名{}", domainRange.getField(), Thread.currentThread().getName(), e);
          }
        });
      }
      ThreadPoolUtil.shutdown(executor);

    }
  }
}
