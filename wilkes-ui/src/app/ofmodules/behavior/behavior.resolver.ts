import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable } from 'rxjs';
import { BHHttpService } from './services/bhHttp.service';

@Injectable()
export class BehaviorResolver implements Resolve<any> {
  constructor(public bh: BHHttpService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> | Promise<any> | any {
    // 确实是否是平台跳入
    //@ts-ignore
    route.routeConfig.data.isPlatform = sessionStorage.getItem('wilkes-platform-id');
    sessionStorage.removeItem('wilkes-platform-id');

    return this.bh.queryBehaviour(route.paramMap.get('id'));
  }
}
