package com.topsec.wilkes.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.io.Files;

import com.topsec.common.utils.FileUtil;
import com.topsec.common.utils.ToolUtils;
import com.topsec.dm.util.HttpHeaderUtil;
import com.topsec.minsky.dto.Result;
import com.topsec.wilkes.common.dto.*;
import com.topsec.wilkes.dto.*;
import com.topsec.wilkes.repository.ModelOriginalRespository;
import com.topsec.wilkes.repository.ModelRepository;
import com.topsec.wilkes.repository.entity.ModelEntity;
import com.topsec.wilkes.repository.entity.ModelOriginalEntity;
import com.topsec.wilkes.runner.ModelUpdateRunner;
import com.topsec.wilkes.service.ModelImportCacheService;
import com.topsec.wilkes.service.ModelInteractiveService;
import com.topsec.wilkes.service.ModelService;
import com.topsec.wilkes.service.NoService;
import com.topsec.wilkes.util.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.topsec.wilkes.common.dto.SourceType.*;
import static com.topsec.wilkes.util.ModelUtil.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ModelInteractiveServiceImpl implements ModelInteractiveService {
  @Autowired
  private ModelService modelService;
  @Autowired
  private ModelImportCacheService modelImportCacheService;
  @Autowired
  private NoService noService;
  @Autowired
  private ModelRepository modelRepository;
  @Autowired
  private ModelOriginalRespository modelOriginalRespository;
  @Autowired
  private ModelUpdateRunner modelUpdateRunner;
  @Autowired
  private TagUtil tagUtil;


  @Override
  public Result importCancelModels() {
    modelImportCacheService.clearCache();
    return Result.failed(Result.Status.SUCCESS, "取消导入成功");
  }

  @Override
  public void exportModels(ServletOutputStream outputStream, String name, String source, Status status,
                           String[] categoryId, String[] killChainId, String[] subjectId, String[] dataTypeId, String[] attCkId, ModelType modelType, List<String> nos, String creator) {

    List<ModelExportTemplate> modelExportTemplates = findModelsDoExport(source, name, status, categoryId, killChainId, subjectId, dataTypeId, attCkId, 1, 99999, "no", Sort.Direction.ASC, modelType, nos, creator);
    File srcFile = Files.createTempDir();
    if (!srcFile.exists()) {
      srcFile.mkdir();
    }
    for (ModelExportTemplate ruleExportTemplate : modelExportTemplates) {
      String value = null;
      try {
        value = AESUtils.encrypt(ToolUtils.OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(ruleExportTemplate));
      } catch (Exception e) {
        log.error("", e);
      }
      FileUtil.write(srcFile.getAbsolutePath() + "/" + ruleExportTemplate.getModel().getNo() + "_" + protectModelName(ruleExportTemplate.getModel().getSource(), ruleExportTemplate.getModel().getName(), false) + ".json", value);
    }

    Metadata metadata = new Metadata();
    metadata.setCreator(HttpHeaderUtil.getUser());
    metadata.setCreateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
    metadata.setVersion(VersionManager.getCurrentVersion());
    metadata.setDataVersion(VersionManager.getDataVersion());
    //设置版本
    metadata.setCategoryVersion(getTagVersionName("category"));
    metadata.setSubjectVersion(getTagVersionName("subject"));
    try {
      FileUtil.write(srcFile.getAbsolutePath() + "/" + "metadata.json", ToolUtils.OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(metadata));
    } catch (JsonProcessingException e) {
      log.info("", e);
    }

    ZipUtil.zipFile(outputStream, srcFile);
  }

  protected List<ModelExportTemplate> findModelsDoExport(String source, String name, Status status, String[] categoryId, String[] killChainId, String[] topicId, String[] dataTypeId, String[] attCkId, int pageNo, int pageSize, String sort, Sort.Direction sortType, ModelType modelType, List<String> nos, String creator) {
    List<Model> models = new LinkedList<>();
    if (nos != null && nos.size() > 0) {
      models = modelService.getModels(nos, true);
    } else {
      models = modelService.findModelsDoExport(source, name, status, categoryId, killChainId, topicId, dataTypeId, attCkId, pageNo, pageSize, sort, sortType, modelType, creator);
    }

    List<ModelExportTemplate> modelExportTemplates = new LinkedList<>();
    for (Model model : models) {
      if (model.getModelType() == ModelType.AI) {
        continue;
      }
      modelExportTemplates.add(new ModelExportTemplate(model, Constants.DATE_FORMAT.format(new Date()), VersionManager.getCurrentVersion()));
    }
    return modelExportTemplates;
  }

  @Override
  public Boolean importDetectionValidateStatus() {
    return modelImportCacheService.getTaskRunStatus();
  }

  @Override
  public DetectionResult importDetectionValidateResult() {
    return modelImportCacheService.getTaskResult();
  }

  @Override
  public Result importDetectionUpload(MultipartFile multipartFile, ModelImportType importType) {
    Map<Metadata, List<Model>> listMap = ModelUtil.getModelsFromFiles(multipartFile);
    for (Metadata metadata : listMap.keySet()) {
      if (!VersionManager.getCompatibleVersion().contains(metadata.getVersion().substring(0, 3))) {
        throw new RuntimeException("当前模型版本" + metadata.getVersion() + "为不被兼容的模型版本，支持的模型版本为: " + Joiner.on(",").join(VersionManager.getCompatibleVersion()));
      }

      List<Model> modelList = listMap.get(metadata);
      for (Model model : modelList) {
        if (Strings.isNullOrEmpty(model.getModelType().getName())) {
          continue;
        }
        // 兼容旧版本模型
        if (!VersionManager.getCurrentVersion().equalsIgnoreCase(metadata.getVersion())) {
          updateOldModel(metadata.getVersion(), model);
        }
        //兼容22年-23年模型，此阶段对告警做了适配，但实际未使用，24年上线此功能，对当时的模型部分变量不兼容，由于未使用过，对于此阶段模型，直接移除与告警相关的信息
        ModelUtil.compatibleAlarmRule(model);

        if (model.getTags() != null) {
          // 同步tag
          List<Tag> tagList = tagUtil.getTags(model.getActions());
          Map<String, Tag> tagMap = model.getTags().stream().collect(Collectors.toMap(Tag::getId, Function.identity()));
          tagList.forEach(tag -> {
            if (!tagMap.containsKey(tag.getId())) {
              tagMap.put(tag.getId(), tag);
            }
          });
          model.setTags(tagMap.values().stream().collect(Collectors.toList()));
        }
        modelImportCacheService.addCache(model);
      }
      modelImportCacheService.addTask(() -> {
        DetectionResult detectionResult = new DetectionResult();
        List<DetectionModelInfo> conflictList = new ArrayList<>();
        List<Model> damageAndUnDamageList = new ArrayList<>();
        List<DetectionModelInfo> damageInfoList = new ArrayList<>();
        List<Model> builtInList = new ArrayList<>();
        List<Model> unbuiltInList = new ArrayList<>();
        List<Model> sampleList = new ArrayList<>();
        //初始化导入模型默认不测试
        Result result = Result.success();
        List<Model> modelCache = modelImportCacheService.getCache();
        for (Model model : modelCache) {
          if (ModelImportType.MANUAL == importType) {
            result = modelService.testModel(model);
          } else {
            //初始化模型将自定义模型转为内置，并且重新编号
            if (SELF_DEFINING.equals(model.getSource())) {
              model.setSource(BUILT_IN);
              model.setNo(noService.next(BUILT_IN));
            }
          }
          if (Result.Status.FAILED == result.getStatus()) {
            //设置错误信息，修改为关闭状态
            model.setAbnormal(result.getMessage());
            model.setStatus(Status.DISABLED);
            damageAndUnDamageList.add(model);
            damageInfoList.add(new DetectionModelInfo(model.getNo(), protectModelName(model.getSource(), model.getName(), true), model.getSource(), model.getModelType(), CauseType.DEFEATED, "", model.getAbnormal()));
            log.info("模型\"{}_{}\"测试失败: {}", model.getNo(), model.getName(), result.getMessage());
          } else if (Result.Status.SUCCESS == result.getStatus()) {
            damageAndUnDamageList.add(model);
            if (modelRepository.findByNo(model.getNo()).isPresent()) {
              conflictList.add(new DetectionModelInfo(model.getNo(), protectModelName(model.getSource(), model.getName(), true), model.getSource(), model.getModelType(), CauseType.NUMBER, "", ""));
            } else if (modelRepository.findByName(model.getName()).isPresent()) {
              conflictList.add(new DetectionModelInfo(model.getNo(), protectModelName(model.getSource(), model.getName(), true), model.getSource(), model.getModelType(), CauseType.NAME, "", ""));
            }
          }
          switch (model.getSource()) {
            case BUILT_IN:
              builtInList.add(model);
              break;
            case SELF_DEFINING:
              unbuiltInList.add(model);
              break;
            case SAMPLE:
              sampleList.add(model);
              break;
            default:
              break;
          }
        }
        String importId = UUID.randomUUID().toString();
        detectionResult.setImportId(importId);
        detectionResult.setMetadata(metadata);
        detectionResult.setTotal(modelList.size());
        detectionResult.setConflict(conflictList.size());
        detectionResult.setDamage(damageInfoList.size());   //此处异常数
        detectionResult.setBuiltIn(builtInList.size());
        detectionResult.setUnBuiltIn(unbuiltInList.size());
        detectionResult.setSample(sampleList.size());
        Map<String, String> tagVersion = Maps.newLinkedHashMap();
        tagVersion.put("subject", Strings.isNullOrEmpty(metadata.getSubjectVersion()) ? "subject_2021-07-19" : metadata.getSubjectVersion());
        tagVersion.put("category", Strings.isNullOrEmpty(metadata.getCategoryVersion()) ? "category_2020-09-07" : metadata.getCategoryVersion());
        detectionResult.setTagUnmatchDescription(checkTagsVersion(tagVersion));
        List<DetectionModelInfo> problemList = new ArrayList<>();
        problemList.addAll(conflictList);
        problemList.addAll(damageInfoList);
        detectionResult.setContent(problemList);

        if (damageAndUnDamageList.size() > 0) {
          log.info("模型锁定操作状态：{}", Constants.DATE_FORMAT.format(new Date()));
        }
        return detectionResult;
      });
    }
    return Result.success();
  }

  @Override
  public DetectionResult importExecuteModels(ExecuteInfo executeInfo, String domainName) {
    int builtInSize = 0;
    int unBuiltInSize = 0;
    int sampleSize = 0;
    //获取缓存数据
    ModelImportCacheService.isImport.set(true);
    List<Model> models = modelImportCacheService.getCache();
    if (models.isEmpty()) {
      throw new RuntimeException("模型导入会话超时，请重新上传模型包。");
    }
    Map<String, TreatmentType> treatmentTypeMap = new HashMap<>();
    for (Execute execute : executeInfo.executes) {
      treatmentTypeMap.put(execute.getNo(), execute.getTreatmentType());
    }
    //缓存模型 model
    for (Model importingModel : models) {
      boolean containsKey = treatmentTypeMap.containsKey(importingModel.getNo());
      TreatmentType treatmentType = treatmentTypeMap.get(importingModel.getNo());
      if (containsKey && (null == treatmentType || TreatmentType.OVERSKIP == treatmentType)) {
        continue;
      }
      if (!executeInfo.getKeepStatus() || (StringUtils.isEmpty(domainName) && HttpHeaderUtil.isSaas)) {
        importingModel.setStatus(Status.DISABLED);
      }
      //导入模型重新生成operator的instanceId
      if (!ModelType.AI.equals(importingModel.getModelType())) {
        importingModel.setContent(ProcessUtil.updateProcessDescriptionInstanceId(importingModel));
      }
      if (HttpHeaderUtil.isSaas) {
       importingModel.setTenantId(HttpHeaderUtil.getTenantId());
       importingModel.setDomainDesc(HttpHeaderUtil.getTenantConditions());
      }
      if (containsKey) {
        Optional<ModelEntity> modelEntityOptional = modelRepository.findByNo(importingModel.getNo());
        Model existsModel = null;
        //如果是正常模型，找到现存模型（也就是导致它出现冲突的原因）
        if (Strings.isNullOrEmpty(importingModel.getAbnormal())) {
          if (!modelEntityOptional.isPresent()) {
            modelEntityOptional = modelRepository.findByName(importingModel.getName());
          }
          if (!modelEntityOptional.isPresent()) {
            continue;
          }
          existsModel = modelEntityOptional.get().convertToModel();
        }
        switch (treatmentType) {
          case AGAINNO:
            importingModel.setNo(noService.syncNext(importingModel.getSource()));
            if (modelRepository.findByName(importingModel.getName()).isPresent()) {
              if (domainName == null) {
                importingModel.setName(importingModel.getName() + Constants.DATE_FORMAT_NOT_SYMBOL.format(new Date()));
              } else {
                importingModel.setName(domainName + "-" + importingModel.getName());
              }
            }
            if (ModelType.CORRELATION == importingModel.getModelType()) {
              importingModel.setContent(ProcessUtil.updateProcessDescriptionInstanceId(importingModel));
            }
            if (!Strings.isNullOrEmpty(importingModel.getRuleId())) { //相当于新建的情况的下，要新一个，且不影响之前的模型
              importingModel.setRuleId(null);
            }
            modelService.createModel(importingModel);
            break;
          case OVERWRITE:
            Optional<ModelOriginalEntity> modelOriginalEntityOptional = modelOriginalRespository.findById(existsModel.getNo());
            ModelOriginalEntity modelOriginalEntity = null;
            if (modelOriginalEntityOptional.isPresent()) {
              modelOriginalEntity = modelOriginalEntityOptional.get();
            }
            if (modelOriginalEntity != null) {
              modelService.deleteOriginalModel(existsModel.getNo());
            }
            importingModel.setCreator(Strings.isNullOrEmpty(importingModel.getCreator()) ? "" : "import:".concat(importingModel.getCreator()));
            modelService.editModel(existsModel.getNo(), importingModel);
            break;
          default:
            break;
        }
      } else {
        modelService.createModel(importingModel);
      }

      switch (importingModel.getSource()) {
        case BUILT_IN:
          builtInSize++;
          break;
        case SELF_DEFINING:
          unBuiltInSize++;
          break;
        case SAMPLE:
          sampleSize++;
          break;
        default:
          break;
      }
      //界面导入模型更新模型事件分类和默认值
      if (!executeInfo.getKeepStatus()) {
        modelUpdateRunner.updateEventType(Collections.singletonList(importingModel));
        modelUpdateRunner.updateDefaultValue(Collections.singletonList(importingModel));
      }
    }

    noService.init();
    modelImportCacheService.clearCache();
    ModelImportCacheService.isImport.remove();
    DetectionResult detectionResult = new DetectionResult();
    detectionResult.setBuiltIn(builtInSize);
    detectionResult.setUnBuiltIn(unBuiltInSize);
    detectionResult.setSample(sampleSize);
    return detectionResult;
  }

  @Override
  public Model modifyModelSourceByNo(String no) {
    return modelService.modifyModelSourceByNo(no);
  }

  private List<String> checkTagsVersion(Map<String, String> tagVersion) {
    List<String> result = Lists.newLinkedList();
    Map<String, String> tagType = Maps.newHashMap();
    tagType.put("category", "分类");
    tagType.put("subject", "专题");
    for (String type : tagType.keySet()) {
      String versionByFile = getTagVersionName(type);
      String versionByMetaData = tagVersion.get(type);
      if (!versionByMetaData.equals(versionByFile)) {
        result.add(String.format("此模型包使用的%s版本为%s，当前系统支持为%s，继续导入，可能导致模型绑定的%s丢失", tagType.get(type), versionByMetaData, versionByFile, tagType.get(type)));
      }
    }
    return result;
  }

  private String getTagVersionName(String type) {
    String catalinaBase = System.getProperty("catalina.base", "");
    String filePath = catalinaBase + "/init/tag/" + type;
    String[] fileNames = new File(filePath).list();
    String latestFileName = new String();
    if ("subject".equals(type)) {
      latestFileName = "subject_2021-07-19";
    } else if ("category".equals(type)) {
      latestFileName = "category_2020-09-07";
    }
    Optional<String> latestFileNameOptional = Arrays.stream(fileNames).max(((o1, o2) -> o1.compareTo(o2)));
    if (latestFileNameOptional.isPresent()) {
      latestFileName = latestFileNameOptional.get().substring(0, latestFileNameOptional.get().length() - 4);
    }
    return latestFileName;
  }
}
