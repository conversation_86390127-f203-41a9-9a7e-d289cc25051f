{"name": "union", "desc": "关联分析-多级计算", "options": {"transformations": ["ParallelismOptimize"], "sessionOptions": {"taskManagerMemory": "2g", "taskManagerCpu": 100, "name": "Wilkes Union", "taskManagerNum": 1, "taskManagerOpts": "-XX:MaxMetaspaceSize=512m  -XX:+HeapDumpOnOutOfMemoryError -XX:ErrorFile=${TOP_BASE_PATH}/serviceLogs/wilkes/jvm/union_hs_err_pid%p.log -XX:HeapDumpPath=${TOP_BASE_PATH}/serviceLogs/wilkes/jvm/union_oom_$RANDOM.hprof", "autoRestart": true, "params": null}, "jobOptions": {"mainClass": "com.topsec.wilkes.operator.WilkesProcessRunner", "additionalArgs": ["-monitor_url", "wilkes/models/monitorRate"], "distributeCache": null, "boundedStream": false, "defaultParallelism": 1, "useKafkaParallelism": false, "parallelism": {}}}, "jars": [{"namespace": "", "name": "", "hashCode": ""}]}