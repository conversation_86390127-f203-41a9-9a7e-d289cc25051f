<t-loading
  [position]="'absolute'"
  [visible]="isLoading"
  [ngStyle]="{ zIndex: isLoading ? '2' : '-2' }"
>
</t-loading>

<div class="tui-exp">
  <div class="tui-exp-search">
    <div class="tui-exp-lione clear clearfix">
      <div class="tui-exp-list clearfix clear">
        <span class="tui-exp-lable">群体</span>
        <span class="tui-exp-input">
          <!-- <t-dropdown
            [options]="groupList"
            [(ngModel)]="groupValue"
            [filter]="true"
            [loading]="loadGroup"
            [numberMoreLayout]="false"
            [ischeckbox]="false"
            placeholder="请选择群体"
            [style]="{ width: '170px' }"
            (onChange)="selectChange($event)"
            [showClear]="true"
          ></t-dropdown> -->
          <app-w-dropdown placeholder="请选择群体" [(ngModel)]="groupValue" [nodeWidth]="'100%'" [options]="groupList" (onChange)="selectChange($event)"></app-w-dropdown>
        </span>
      </div>
    </div>
  </div>
  <!--表格-->
  <div class="tui-exp-table clear clearfix">
    <div class="body" #chartBox></div>
    <div class="center">
      <span class="now-con"
        >当前画像产生时间：{{ currentTime }}
        <i
          class="icon-tps icon-con"
          tTooltip="以日志源的事件时间为准"
          tooltipPosition="bottom"
        ></i
      ></span>
      <span class="ml-10" *ngIf="forecastTime != ''"
        >预计下次检测时间：{{ forecastTime }}
        <i
          class="icon-tps icon-con"
          tTooltip="以系统时间为准"
          tooltipPosition="bottom"
        ></i
      ></span>
    </div>
    <ng-container *ngIf="!isLoading && isNodata">
      <div class="no-data">
        <div class="ui-data ng-star-inserted">
          <div class="ui-data-contentimg"></div>
          <div class="ui-data-text">暂无数据</div>
          <div class="ui-data-text">
            预计画像首次生成时间：{{ generateTime }}
          </div>
        </div>
      </div>
    </ng-container>
  </div>
</div>

<t-dialog
  header="个体画像信息"
  [(visible)]="showDialog"
  [style]="{ width: '800px', height: '460px' }"
  [baseZIndex]="99999999"
>
  <app-related-rule-info
    *ngIf="showDialog"
    [model]="model"
    [modelNo]="modelNo"
    [groupValue]="groupValue"
    [params]="pointParams"
  ></app-related-rule-info>
</t-dialog>
