<div class="interval-con">
    <t-spinner *ngIf="!numOptions" #spinner [disabled]="isOpen" (onFocus)="onFocus(spinner)" (onChange)="numChange()" [size]="30"
        [(ngModel)]="valueObj.numValue" inputId="num-interval" [inputStyle]="style?.inputStyle" [min]="min" [max]="max">
    </t-spinner>
    <t-dropdown *ngIf="numOptions" [disabled]="isOpen" [options]="numOptions" appendTo="body"
        [style]="style?.inputStyle" [panelStyle]="{width: '100px'}" placeholder="请选择时间" [(ngModel)]="valueObj.numValue" (onChange)="change1($event)">
    </t-dropdown>
    <t-dropdown [options]="options" [disabled]="isOpen" appendTo="body" [style]="style?.style"
        [placeholder]="placeholder" [panelStyle]="{width: '100px'}" [(ngModel)]="valueObj.selValue" (onChange)="change1($event)">
    </t-dropdown>
</div>