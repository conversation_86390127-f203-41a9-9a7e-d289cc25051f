package com.topsec.wilkes.behaviour.component;

import com.google.common.base.Joiner;
import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.behaviour.config.BehaviourModelConfig;
import com.topsec.wilkes.behaviour.dto.BehaviourModel;
import com.topsec.wilkes.behaviour.dto.definition.BehaviourAlgorithm;
import com.topsec.wilkes.behaviour.dto.definition.BehaviourDefinition;
import com.topsec.wilkes.config.WilkesConfig;
import com.topsec.wilkes.common.dto.GroupId;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.common.dto.Status;
import com.topsec.wilkes.common.dto.Tag;
import com.topsec.wilkes.util.EventUtil;
import com.topsec.wilkes.util.TagUtil;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import java.util.ArrayList;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class BehaviourModelTransform {
  private ObjectMapper objectMapper = ToolUtils.OBJECT_MAPPER;

  @Autowired
  private TagUtil tagUtil;

  @Autowired
  private WilkesConfig wilkesConfig;

  public Model convertToModel(BehaviourModel behaviourModel) {
    Model model = new Model();
    BeanUtils.copyProperties(behaviourModel, model);
    model.setSource(Strings.isNullOrEmpty(behaviourModel.getSource()) ? "自定义" : behaviourModel.getSource());
    model.setStatus(behaviourModel.getStatus() == null ? Status.DISABLED : behaviourModel.getStatus());
    // 设置标签
    List<Tag> tags = new LinkedList<>();
    if (behaviourModel.getTags() != null) {
      tags.addAll(behaviourModel.getTags());
    }
    if (behaviourModel.getSubjects() != null) {
      for (String subject : behaviourModel.getSubjects()) {
        tags.add(new Tag(subject, GroupId.SUBJECT));
      }
    }
    if (behaviourModel.getCategories() != null) {
      for (String category : behaviourModel.getCategories()) {
        tags.add(new Tag(category, GroupId.CATEGORY));
      }
    }
    if (!Strings.isNullOrEmpty(behaviourModel.getAttckTag())) {
      tags.add(new Tag(behaviourModel.getAttckTag(), GroupId.ATTCK_TAG));
    }
    tags.addAll(behaviourModel.getBehaviourDefinition().getDataIds().stream()
        .map(dataId -> new Tag(dataId, GroupId.DATA_TYPE)).collect(Collectors.toList()));
    tags.addAll(tagUtil.getTags(behaviourModel.getActions()));
    model.setTags(tags);

    try {
      Map map = Maps.newHashMap();
      map.put(BehaviourModelConfig.BEHAVIOUR_DEFINITION, behaviourModel.getBehaviourDefinition());
      map.put(BehaviourModelConfig.BEHAVIOUR_ALGORITHM, behaviourModel.getBehaviourAlgorithm());
      model.setContent(objectMapper.writeValueAsString(map));
    } catch (Exception e) {
      throw new IllegalArgumentException("参数格式化异常！", e);
    }
    if (behaviourModel.getModelType() == null){
      model.setModelType(ModelType.BEHAVIOUR);
    }
    model.setNo(behaviourModel.getNo());
    model.setRuleId(behaviourModel.getRuleId());
    return model;
  }

  public BehaviourModel convertToBehaviourModel(Model model) {
    BehaviourModel behaviourModel = new BehaviourModel();
    BeanUtils.copyProperties(model, behaviourModel);
    // 设置标签
    List<String> subjects = new LinkedList<>();
    List<String> categories = new LinkedList<>();
   List<String> attackTag = new ArrayList<>();
    for (Tag tag : model.getTags()) {
      switch (tag.getGroupId()) {
        case SUBJECT:
          subjects.add(tag.getId());
          break;
        case CATEGORY:
          categories.add(tag.getId());
          break;
        case ATTCK_TAG:
          attackTag.add(tag.getId());
          break;
        default:
      }
    }
    behaviourModel.setAttckTag(Joiner.on(",").join(attackTag));
    behaviourModel.setSubjects(subjects);
    behaviourModel.setCategories(categories);
    behaviourModel.setTags(model.getTags());
    behaviourModel.setEvents(EventUtil.getEvents(model.getActions()).stream().filter(event -> !wilkesConfig.getExcludeViewEvents().contains(event.getId())).collect(Collectors.toList()));
    try {
      if (model.getContent() != null) {
        Map<String, Object> contentMap = objectMapper.readValue(model.getContent(), Map.class);
        behaviourModel.setBehaviourDefinition(objectMapper.convertValue(contentMap.getOrDefault(BehaviourModelConfig.BEHAVIOUR_DEFINITION, new BehaviourDefinition()), BehaviourDefinition.class));
        behaviourModel.setBehaviourAlgorithm(objectMapper.convertValue(contentMap.getOrDefault(BehaviourModelConfig.BEHAVIOUR_ALGORITHM, new BehaviourAlgorithm()), BehaviourAlgorithm.class));
      }
    } catch (Exception e) {
      throw new IllegalArgumentException("格式化异常!", e);
    }
    if (behaviourModel.getBehaviourDefinition() != null) {
      // 兼容周期性不带单位大小的情况
      String periodic = behaviourModel.getBehaviourDefinition().getPeriodic();
      if (!periodic.contains(" ") && !"none".equalsIgnoreCase(periodic)) {
        behaviourModel.getBehaviourDefinition().setPeriodic(String.format("1 %s", periodic));
      }
    }
    behaviourModel.setAbnormal(model.getAbnormal());
    behaviourModel.setRuleId(model.getRuleId());
    return behaviourModel;
  }
}
