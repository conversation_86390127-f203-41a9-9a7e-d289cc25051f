{"id": "field-aggregation", "name": "在《一段时间》内，对于相同《字段》，符合《条件》的事件", "group": "AGGREGATION", "className": "com.topsec.wilkes.operator.AggregationOperator", "params": [{"name": "interval_time", "type": "interval_time"}, {"name": "field", "type": "field"}, {"name": "predication", "type": "predication"}], "inports": [{"type": "default"}], "outports": [{"type": "default", "name": "输出数据"}], "desc": "this is a test description"}