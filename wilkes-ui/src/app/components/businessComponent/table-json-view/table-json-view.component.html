<div class="table-json-view">
  <t-table [styleClass]="tableValue.length>0?'recall-result-table':''" [columns]="tableHeader" [data]="tableValue"
    dataKey="numIndex" [noData]="true" [noDataType]="'noData'" [scrollable]="true" [scrollHeight]="scrollHeight">
    <ng-template tTemplate="header" let-columns>
      <tr>
        <th></th>
        <th *ngFor="let col of columns" style="width: 16%">
          {{col.name}}
        </th>
      </tr>
    </ng-template>
    <ng-template tTemplate="body" let-rowData let-expanded="expanded" let-columns="columns">
      <tr>
        <td>
          <a href="#" [tRowToggler]="rowData">
            <i [ngClass]="expanded ? 'icon-down' : 'icon-left'" style="font-size: 18px!important;"></i>
          </a>
        </td>
        <td *ngFor="let col of columns" style="width: 16%">
          {{rowData['json'][col.name] || '-'}}
        </td>
      </tr>
    </ng-template>
    <ng-template tTemplate="rowexpansion" let-rowData let-columns="columns">
      <tr style="background-color: #f7f8fa !important;">
        <td [attr.colspan]="columns.length + 1">
          <div>
            <t-tabView styleClass="recall-result-tabview1">
              <t-tabPanel header="表格视图">
                <div class="table-view">
                  <div *ngFor="let table of rowData.tableArray" style="padding: 10px 0px">
                    <p>
                      <t-title [style]="{'font-size': '14px'}" [text]="table.title" [icon]="'default'"></t-title>
                    </p>
                    <ul class="table-view-item">
                      <li *ngFor="let tableItem of table.value">
                        <p class="tableItem-name">{{tableItem.nameCn}}</p>
                        <p class="tableItem-value">{{tableItem.value}}</p>
                      </li>
                    </ul>
                  </div>
                </div>
              </t-tabPanel>
              <t-tabPanel header="json视图">
                <!-- json视图 -->
                <div class="json-view">
                  <span style="color: #98a4af;">{{ '{' }}</span>
                  <ng-container *ngFor="let item of rowData.jsonArray">
                    <div class="json-include-box">
                      <div *ngIf="item.value | dataType">
                        <span class="item-json-key">"{{ item.key }}":</span>
                        <span style="color: #98a4af;">"</span>
                        <span class="item-json-value">{{ item.value | escape }}</span>
                        <span style="color: #98a4af;">"</span>
                      </div>
                      <div *ngIf="!(item.value | dataType)">
                        <span class="item-json-key">"{{ item.key }}":</span>
                        <span class="fold" (click)="item.fold = !item.fold">{{ item.fold? '+' : '-' }}</span>
                        <span *ngIf="item.fold">Array</span>
                        <span>[</span>
                        <span *ngIf="item.fold">{{ item.value?.length }}</span>
                        <ng-container *ngIf="!item.fold">
                          <p *ngFor="let arrValue of item.value" class="item-json-value arr-value">
                            <span style="color: #98a4af;">"</span>
                            <span>{{ arrValue }}</span>
                            <span style="color: #98a4af;">"</span>
                          </p>
                        </ng-container>
                        <span>]</span>
                      </div>
                    </div>
                  </ng-container>
                  <span style="color: #98a4af;">}</span>
                </div>
              </t-tabPanel>
            </t-tabView>
          </div>
        </td>
      </tr>
    </ng-template>
  </t-table>
</div>
<div class="page-con">
  <t-paginator [styleClass]="pageClass" [displayPages]="true" [displayPole]="false" [jumpToPage]="false"
    [rowsPerPageOptions]="[20, 30, 50]" [rows]="page.size" [totalRecords]="page.total" #paginator
    (onPageChange)="onPageChange($event)"></t-paginator>
</div>