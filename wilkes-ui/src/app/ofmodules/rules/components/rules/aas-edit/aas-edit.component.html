<div class="app-rules-con" style="
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
  ">
  <t-toast key="rules-rule" position="top-center" [style]="{ marginTop: '80px' }"></t-toast>
  <t-toast key="generate-knowledge" position="top-center" [style]="{ marginTop: '80px' }"></t-toast>
  <t-toast key="special-fields-search" position="top-center" [style]="{ marginTop: '80px' }"></t-toast>

  <div class="correlation-edit">
    <t-loading [zindex]="1200" [position]="'absolute'" [visible]="pageStatus.loading"
      [ngStyle]="{ zIndex: pageStatus.loading ? '999' : '-2' }">
    </t-loading>
    <div class="top-con">
      <div class="name-con">
        <span #topSpan class="top-span-w">{{ fromData.name}}</span>
        <span class="icon-lock-c icon-lock"></span>
      </div>
      <div class="act-con">
        <div class="flex">
          <t-splitButton label="查看事件" icon="pi pi-check" [model]="events" styleClass="rule-splitButton">
          </t-splitButton>
          <button tButton class="ui-button-secondary" type="button" icon="icon-program" label="测试" (click)="test()"
            [disabled]="pageStatus.loading"></button>
          <button tButton class="ui-button-secondary" type="button" icon="icon-floppy-disk" label="保存" (click)="save()"
            [disabled]="pageStatus.saveBtn"></button>
          <button tButton class="ui-button-secondary" type="button" icon="icon-return-left" label="返回"
            (click)="black()"></button>
          <button tButton class="ui-button-secondary" type="button" icon="icon-history" *ngIf="isReset" label="恢复默认"
            [disabled]="pageStatus.resetBtn" (click)="resetAll()"></button>
        </div>
      </div>
    </div>
    <div class="bottom-con">
      <div class="l-con">
        <t-scrollPanel [styleClass]="'aas-edit-con'" [style]="{ width: '100%', height: '100%' }" *ngIf="isNodesShow">
          <ul class="node-con">
            <li *ngFor="
                let item of nodeList;
                index as i;
                trackBy: trackByParamsFn
              " [ngClass]="{ 'hidden-node': item.group === 'source' }">
              <div class="node-des-con">
                <span class="node-des-result file-name">
                  {{ item.custom_desc || item.name }}
                </span>
                <button tButton type="button" style="height: auto; line-height: normal; width: 80px"
                  [label]="item?.backName" class="ui-button-rounded" (click)="blackNode(item)"
                  *ngIf="isReset && item.isReset"></button>
              </div>
              <div class="node-result">
                <app-node-from [fromData]="item"></app-node-from>
                <!-- <app-node-paramset [toolId]="item.typeId" [type]="item.params" [groupName]="item.name"
									[metadata]="item.metadata" [operatorName]="item.desc" (onchange)="nodeChange(item)"
									[anchor]="item.pointArr?.length - 1 || 1" [hiddenType]="item.hiddenType">
								</app-node-paramset> -->
              </div>
            </li>
          </ul>
        </t-scrollPanel>

        <t-nodata *ngIf="!isNodesShow" [noDataType]="'noData'" noDatatext="模型暂无可定制算子配置">
        </t-nodata>
      </div>
      <div class="r-con">
        <t-scrollPanel #scrollPanel [styleClass]="'edit-con'" [style]="{ width: '100%', height: '100%' }">
          <div class="rigth-detail-con">
            <t-accordion styleClass="modelsys-accordion" [multiple]="true">
              <t-accordionTab [selected]="true" [disabled]="true" [accordionIcon]="true" headerIcon="icon-enter-omitted">
                <t-header>
                  模型输出
                  <i class="icon-history" [title]="'恢复默认'" *ngIf="resetAction.indexOf('generate-event') != -1"
                    style="margin-left: calc(100% - 152px);cursor: pointer;" (click)="
                      $event.stopPropagation(); resetAll('generate-event')
                    "></i>
                </t-header>
                <app-security-event-set #securityE [pType]="'correlation'" [pScrollPanel]="scrollPanel"
                  [process]="nodeDetail?.process" [(ngModel)]="fromData.eventSetList"
                  [tipFields]="fromData.tipFields">
                </app-security-event-set>
              </t-accordionTab>

              <t-accordionTab [selected]="true" [disabled]="true" [accordionIcon]="true" headerIcon="icon-uishow">
                <t-header>
                  模型响应
                  <i class="icon-history" [title]="'恢复默认'" *ngIf="resetAction.indexOf('generate-response') != -1"
                    style="margin-left: calc(100% - 152px);cursor: pointer;" (click)="
                      $event.stopPropagation(); resetAll('generate-response')
                    "></i>
                </t-header>

                <form class="even-detial-con" style="width: 100%" [formGroup]="fromData.responSetList">
                  <div class="form-item" style="margin: 10px 0 12px 0">
                    <div class="form-label" style="width: 80px; text-align: left">
                      生成情报
                    </div>
                    <div class="form-control" style="width: calc(100% - 80px); float: right">
                      <t-dropdown [style]="{ width: '100%' }" [options]="fromData.responOptions.intelligenceList"
                        formControlName="ADD_INTELLIGENCE" placeholder="请选择" appendTo="body">
                      </t-dropdown>
                    </div>
                  </div>
                  <div class="form-item">
                    <div class="form-label" style="width: 80px; text-align: left">
                      生成知识
                    </div>
                    <div class="form-control" style="
                        width: calc(100% - 80px);
                        float: right;
                        position: relative;
                      ">
                      <div style="width: 100%">
                        <t-dropdown [style]="{
                            width: 'calc( 100% - 60px )',
                            'vertical-align': 'top'
                          }" [options]="intelligenceO.sel" formControlName="ADD_KNOWLEDGE" placeholder="请选择"
                          appendTo="body" [layoutModel]="'group-horizontal'" [filter]="true" filterBy="label,value"
                          [showClear]="true" (onChange)="valueChange($event, 0)">
                        </t-dropdown>
                        <button class="ui-button-primary" tButton type="button" label="配置" [style]="{
                            width: '50px',
                            'margin-left': '10px',
                            'vertical-align': 'top'
                          }" (click)="config(0)"></button>
                      </div>
                    </div>
                  </div>
                  <div class="form-item">
                    <div class="form-label" style="width: 80px; text-align: left">
                      删除知识
                    </div>
                    <div class="form-control" style="
                        width: calc(100% - 80px);
                        float: right;
                        position: relative;
                      ">
                      <div style="width: 100%">
                        <t-dropdown [style]="{
                            width: 'calc( 100% - 60px )',
                            'vertical-align': 'top'
                          }" [options]="intelligenceODel.sel" formControlName="DELETE_KNOWLEDGE" placeholder="请选择"
                          appendTo="body" [layoutModel]="'group-horizontal'" [filter]="true" filterBy="label,value"
                          [showClear]="true" (onChange)="valueChange($event, 1)">
                        </t-dropdown>
                        <button class="ui-button-primary" tButton type="button" label="配置" [style]="{
                            width: '50px',
                            'margin-left': '10px',
                            'vertical-align': 'top'
                          }" (click)="config(1)"></button>
                      </div>
                    </div>
                  </div>
                  <div class="form-item" *ngIf="securityE?.alarmObj.isAlarm">
                    <div class="form-label" style="width: 80px; text-align: left">
                      触发剧本
                    </div>
                    <div class="form-control" style="
                        width: calc(100% - 80px);
                        float: right;
                        position: relative;
                      ">
                      <div style="width: 100%">
                        <choose-playbook [isShow]="securityE?.alarmObj.isAlarm" [showNum]="false" [playbookDtos]="
                            fromData.responSetList.get('PLAYBOOKID').value
                          " (playbookDtosChange)="playbookDtosChange($event)"></choose-playbook>
                      </div>
                    </div>
                  </div>
                </form>
              </t-accordionTab>
            </t-accordion>
          </div>
        </t-scrollPanel>
      </div>
    </div>
  </div>
  <t-dialog header="知识情报配置" [(visible)]="knowledgeisDialog" [baseZIndex]="9" [closable]="false">
    <div class="g-k-dialog">
      <ul>
        <li *ngFor="let item of aConditionOfields">
          <div class="g-k-dialog-list g-k-dialog-c">
            <div *ngIf="!item.isAdd; else inputD" class="add-con">
              <span class="icon-editorial-team file-name" title="自定义"
                (click)="item.isAdd=true;item.assignment=''"></span>
              <t-dropdown [style]="{
                  width: '100%',
                  'border-color':
                    item.isNull == false &&
                    checkknowledgeisDialog &&
                    (item.assignment == '' || item.assignment == null)
                      ? '#f00'
                      : '#d9d9d9'
                }" [options]="fromData.alarmFields" [(ngModel)]="item.assignment" placeholder="请选择" appendTo="body"
                [layoutModel]="'group-horizontal'" [filter]="true" [showClear]="item.assignment !== ''"
                [baseZIndex]="100000000000000" (onChange)="assignmentV($event, item)">
              </t-dropdown>
            </div>
            <ng-template #inputD>
              <div class="add-con">
                <span class="icon-tui-newtabsvg file-name" title="恢复默认"
                  (click)="item.isAdd=false;item.assignment=''"></span>
                <input type="text" tInputText [(ngModel)]="item.assignment"
                  (ngModelChange)="validatorsInput($event, item)" [ngStyle]="{'border-color':
                item.isNull == false &&
                checkknowledgeisDialog &&
                (item.assignment == '' || item.assignment == null)
                  ? '#f00'
                  : '#d9d9d9'}" placeholder="自定义内容" style="width: 100%">
              </div>
            </ng-template>
            <!-- <div *ngIf="item.isAdd" class="add-con">
              
            </div> -->
            <span class="r-item" *ngIf="item.isNull == false">*</span>
          </div>
          <p class="icon-arrow-right g-k-dialog-list g-k-dialog-icon"></p>
          <p class="g-k-dialog-list g-k-dialog-tit">{{ item.label }}</p>
        </li>
      </ul>
    </div>
    <t-footer>
      <button class="ui-button-primary" type="button" tButton (click)="checkFun(0)" label="确定"></button>
      <button type="button" tButton (click)="cancelD(0)" label="取消" class="ui-button-secondary"></button>
    </t-footer>
  </t-dialog>
  <t-dialog header="删除知识情报配置" [(visible)]="knowledgeisDialogDel" [baseZIndex]="9" [closable]="false">
    <div class="g-k-dialog">
      <ul>
        <li *ngFor="let item of aConditionOfieldsDel">
          <div class="g-k-dialog-list g-k-dialog-c">
            <div *ngIf="!item.isAdd; else inputD" class="add-con">
              <span class="icon-editorial-team file-name" title="自定义"
                (click)="item.isAdd=true;item.assignment=''"></span>
              <t-dropdown [style]="{
                  width: '100%',
                  'border-color':
                    item.isNull == false &&
                    checkknowledgeisDialogDel &&
                    (item.assignment == '' || item.assignment == null)
                      ? '#f00'
                      : '#d9d9d9'
                }" [options]="fromData.alarmFields" [(ngModel)]="item.assignment" placeholder="请选择" appendTo="body"
                [layoutModel]="'group-horizontal'" [filter]="true" [showClear]="item.assignment !== ''"
                [baseZIndex]="100000000000000" (onChange)="assignmentV($event, item)">
              </t-dropdown>
            </div>
            <ng-template #inputD>
              <div class="add-con">
                <span class="icon-tui-newtabsvg file-name" title="恢复默认"
                  (click)="item.isAdd=false;item.assignment=''"></span>
                <input type="text" tInputText [(ngModel)]="item.assignment"
                  (ngModelChange)="validatorsInput($event, item)" [ngStyle]="{'border-color':
                item.isNull == false &&
                checkknowledgeisDialog &&
                (item.assignment == '' || item.assignment == null)
                  ? '#f00'
                  : '#d9d9d9'}" placeholder="自定义内容" style="width: 100%">
              </div>
            </ng-template>
            <!-- <div *ngIf="item.isAdd" class="add-con">
              
            </div> -->
            <span class="r-item" *ngIf="item.isNull == false">*</span>
          </div>
          <p class="icon-arrow-right g-k-dialog-list g-k-dialog-icon"></p>
          <p class="g-k-dialog-list g-k-dialog-tit">{{ item.label }}</p>
        </li>
      </ul>
    </div>
    <t-footer>
      <button class="ui-button-primary" type="button" tButton (click)="checkFun(1)" label="确定"></button>
      <button type="button" tButton (click)="cancelD(1)" label="取消" class="ui-button-secondary"></button>
    </t-footer>
  </t-dialog>

  <div class="tui-dialog-con">
    <t-dialog header="提示" [(visible)]="isDialog" [baseZIndex]="98" [style]="{ top: '1px', zIndex: '98' }">
      <div></div>
      <i class="icon-exclamation-mark" style="
          color: #fff;
          background: #faad14;
          border-radius: 50%;
          margin-right: 5px;
        "></i>
      恢复默认并保存退出。
      <t-footer>
        <button class="ui-button-primary" type="button" tButton style="float: right; margin: 0" (click)="saveData()"
          label="确定"></button>
        <button type="button" tButton style="float: right; margin-right: 10px" (click)="isDialog = false" label="取消"
          class="ui-button-secondary"></button>
      </t-footer>
    </t-dialog>
  </div>
</div>
<t-dialog header="控制台" [(visible)]="showConsole" appendTo="body" styleClass="fullfill" [maximizable]="true"
  [style]="{ width: '80%', height: '80%' }" [baseZIndex]="98000000" [closable]="true">
  <iframe *ngIf="showConsole" [src]="debugUrl" frameborder="0" style="width: 100%; height: 100%"></iframe>
</t-dialog>
<t-toast position="top-center" [style]="{ marginTop: '80px' }"></t-toast>