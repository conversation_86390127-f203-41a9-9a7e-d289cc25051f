import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ConditionEditorModule, SwitchModule } from '@firmament/component-ext-library';
import { MessageService } from '@tui/component-library';
import { EleModule } from '@firmament/component-secbizlib';
import { ComModule } from 'src/app/components/com-module';
import { SafePipe } from 'src/app/common/pipe/safe';
import { ComToComService } from '../rules/services/com-to-com.service';
import { AiComponent } from './aiModule.component';
import { AiRoutes } from './aiModule.routing';
import { AiAnalysisComponent } from './components/ai-analysis/ai-analysis.component';
import { AiDetailsComponent } from './components/ai-details/ai-details.component';

@NgModule({
  imports: [CommonModule, FormsModule, ReactiveFormsModule, ComModule, SwitchModule, EleModule, ConditionEditorModule, RouterModule.forChild(AiRoutes)],
  exports: [AiComponent, AiAnalysisComponent, AiDetailsComponent, RouterModule],
  declarations: [AiComponent, AiAnalysisComponent, AiDetailsComponent, SafePipe],
  schemas: [NO_ERRORS_SCHEMA, CUSTOM_ELEMENTS_SCHEMA],
  providers: [MessageService, ComToComService],
})
export class AiModule {}
