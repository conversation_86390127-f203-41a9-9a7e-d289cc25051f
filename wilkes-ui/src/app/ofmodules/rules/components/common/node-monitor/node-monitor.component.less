/* stylelint-disable keyframes-name-pattern */
/* stylelint-disable value-keyword-case */
/* stylelint-disable property-no-vendor-prefix */
:host {
  .animate__fadeInDown {
    -webkit-animation: 1s fadeInDownBig;
    animation: 1s fadeInDownBig;
  }

  @keyframes fadeInDownBig {
    0% {
      opacity: 0;
      -webkit-transform: translate3d(0, 100%, 0);
      transform: translate3d(0, 100%, 0);
    }

    100% {
      opacity: 1;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }
  }

  @keyframes fadeInDownBig {
    0% {
      opacity: 0;
      -webkit-transform: translate3d(0, 100%, 0);
      transform: translate3d(0, 100%, 0);
    }

    100% {
      opacity: 1;
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
    }
  }

  .node-detail {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 110;
    box-sizing: border-box;
    display: flex;
    width: 395px;
    padding: 10px;
    background: #fff;
    border: 1px solid #ffba01;
    border-radius: 5px;

    &:hover {
      z-index: 120;
      box-shadow: 0 0 5px #f3dfa1;
    }
  }

  .mr-10 {
    margin-right: 10px;
  }

  .list-box {
    display: flex;
    flex-direction: column;

    .list-title {
      flex: 0;
      font-size: 14px;
    }

    .list-scroller {
      position: relative;
      display: inline-block;
      flex: 1;
      width: 180px;
      min-height: 100px;
      max-height: 240px;
      overflow: auto;
      border: 1px solid #d9d9d9;
    }

    .list-group {
      padding: 0;
      margin: 0;
      font-size: 14px;
      line-height: 20px;
      list-style: none;

      .list-item {
        display: flex;
        padding: 4px 8px;
        margin: 0;

        span:first-child {
          margin-right: 8px;
        }
      }
    }
  }

  ::ng-deep .operator-label {
    padding: 4px 8px;
    pointer-events: none;
    background: #fff;
    border: 1px solid #ffba01 !important;
    border-radius: 5px;

    &:hover {
      z-index: 120;
      box-shadow: 0 0 5px #f3dfa1;
    }
  }

  .is-null {
    padding: 4px 8px;
  }
}
