import { Component, OnInit } from '@angular/core';
import { RulePortraitService } from './rule-portrait.service';
import { zip } from 'rxjs';
import { PortraitChartService } from './portrait-chart.service';
import { TuiAppService } from '@tui/frame';
import { Util } from 'src/app/utils/util';

@Component({
  selector: 'app-rule-portrait',
  templateUrl: './rule-portrait.component.html',
  styleUrls: ['./rule-portrait.component.less']
})
export class RulePortraitComponent implements OnInit {
  loading = false;
  time = {
    startDate: new Date(new Date().getTime() - 45 * 24 * 60 * 60 * 1000),
    endDate: new Date(),
  };

  // 监听框架主题 true为红色 false为其他颜色
  tuiTheme = true;
  // 统计数据 第一行数据
  totalDatas: any;
  // 事件数据 第二行
  eventDatas: any;
  // 事件标签 第三行
  tagsDatas: any;
  // 杀伤连   第三行
  killData: any;
  // 最后一行数据
  top5Datas: any;

  from = {
    modelType: '',
    aggSize: 5,
    startTime: '',
    endTime: ''
  };

  modelType: any = [
    { label: '关联分析', value: 'CORRELATION' },
    { label: '行为分析', value: 'BEHAVIOUR' },
    // { label: '联邦分析', value: 'UNION' },
    { label: '深度分析', value: 'AI' },
  ];

  switch = [
    { lable: '近一天', value: '1', checked: true },
    { lable: '近一周', value: '7' },
    { lable: '近一月', value: '30' },
  ];

  tagType = ['AI_TAG', 'ATTCK_TAG', 'CATEGORY', 'DATA_TYPE', 'EVENT_TAG', 'EVENT_TYPE', 'KILL_CHAIN', 'KNOWLEDGE_BASE', 'SUBJECT'];


  // top数据 最后一行
  top5Data: any = [{
    name: `模型安全事件TOP${this.from.aggSize}`,
    chartName: 'TrxHBarChart',
    dataCfg: {
      x: ['name'],
      y: ['value'],
      data: [

      ]
    },
    styleCfg: {
      global: {
        showNum: 5,
        liTop: 22
      },
    }
  }, {
    name: `模型类型TOP${this.from.aggSize}`,
    chartName: 'TrxHBarChart',
    dataCfg: {
      x: ['name'],
      y: ['value'],
      data: []
    },
    styleCfg: {
      global: {
        showNum: 5,
        liTop: 22
      },
    }
  }];

  constructor(
    private tui: TuiAppService,
    private ht: RulePortraitService,
    private chartBase: PortraitChartService
  ) { }

  ngOnInit(): void {
    this.tuiTheme = (window.localStorage.getItem('tui-theme') || 'defaultTheme') === 'defaultTheme';
    this.tui.tuiAppCfgChanged.on('skinSett.skin', (res) => {
      this.tuiTheme = 'defaultTheme' === res;
    });
    this.totalDatas = [
      {
        title: '模型来源',
        icon: 'icon-resourceDomain',
        type: 'text',
        list: [
          { name: '内置', value: 0, icon: 'icon-information', style: { width: '50%' } },
          { name: '自定义', value: 0, icon: 'icon-analysis04', style: { width: '50%' } }],
        style: { width: '33.33%' },
        padding: null
      },
      {
        title: '模型状态',
        icon: 'icon-status',
        type: 'text',
        list: [
          { name: '启用', key: 'enabled', value: 0, icon: 'icon-ddos_log', style: { width: '50%' } },
          { name: '停用', key: 'disabled', value: 0, icon: 'icon-ER-ascription', style: { width: '50%' } }],
        style: { width: '33.33%' },
        padding: null
      },
      {
        title: '模型活跃数',
        icon: 'icon-key-horizontal',
        type: 'chart',
        tTooltip: '0/0',
        list: [
          {
            chartStyle: this.chartBase.textStyle,
            chart: {
              x: ['name'],
              y: ['value'],
              data: [
                { name: '/0', value: 0 },
              ]
            },
            switch: true,
            startTime: Util.dateFormat(new Date(new Date().getTime() - 1 * 1000 * 60 * 60 * 24)),
            endTime: Util.dateFormat(new Date()),
            style: { width: '100%', paddingRight: '35%', height: '150px', position: 'relative' }
          },
        ],
        style: { width: '33.33%' },
        padding: { padding: '13px 0' }
      },
    ];
    this.eventDatas = [
      {
        title: '事件类型',
        icon: 'icon-netuser',
        type: 'table',
        style: { width: '50%' },
        padding: null
      },
      {
        title: '安全日志分布',
        icon: 'icon-collection',
        type: 'chart',
        switch: true,
        startTime: Util.dateFormat(new Date(new Date().getTime() - 1 * 1000 * 60 * 60 * 24)),
        endTime: Util.dateFormat(new Date()),
        style: { width: '50%' },
        chartName: 'TrxLineChart',
        chart: {
          x: ['name'],
          y: ['value'],
          data: []
        },
        padding: null
      },
    ];

    this.tagsDatas = [
      {
        title: '事件标签',
        icon: 'icon-label',
        type: 'chart',
        style: null,
        chartName: 'TrxBubbleChart',
        chart: {
          x: ['name'],
          y: ['value'],
          data: [{
            name: '飞客蠕虫',
            value: '15445645'
          }, {
            name: '永恒之蓝',
            value: '135'
          }, {
            name: '暗云木马',
            value: '124'
          }, {
            name: '欢乐时光',
            value: '113'
          }, {
            name: '虚拟货币挖矿',
            value: '98'
          }, {
            name: '电话窃取',
            value: '95'
          }, {
            name: '黑链',
            value: '87'
          }, {
            name: 'LOCKY',
            value: '82'
          }, {
            name: 'IP试探',
            value: '76'
          }, {
            name: '黑链',
            value: '60'
          }]
        },
        styleCfg: {
          "series": {
            "symbol": "rect",
          },
          "tooltip": {
            "show": true,
            "textStyle": {
              "color": "#999999",
              "fontSize": 16,
              "fontWeight": "normal"
            }
          }
        },
        padding: null
      },
      {
        title: '数据源分布',
        icon: 'icon-pie',
        type: 'chart',
        style: null,
        chartName: 'TrxPieChart',
        chart: {
          x: ['name'],
          y: ['value'],
          data: [
            { name: '河南省', value: 1000 },
            { name: '天津市', value: 2000 },
            { name: '上海市', value: 2000 },
            { name: '广东省', value: 2000 }
          ]
        },
        padding: null
      },
      {
        title: '安全事件Top5',
        icon: 'icon-security',
        type: 'chart',
        style: null,
        chartName: 'TrxHBarChart',
        chart: {
          x: ['name'],
          y: ['value'],
          data: [
            { name: '河南省', value: 1000 },
            { name: '天津市', value: 2000 },
            { name: '上海市', value: 2000 },
            { name: '广东省', value: 2000 }
          ]
        },
        styleCfg: {
          xAxis: {
            minInterval: 1
          }
        },
        padding: null
      },
    ];
    this.top5Datas = [
      {
        title: '模型类型 Top5',
        icon: 'icon-edit-correlation-model',
        type: 'chart',
        style: null,
        chartName: 'TrxHBarChart',
        chart: {
          x: ['name'],
          y: ['value'],
          data: []
        },
        styleCfg: {
          xAxis: {
            minInterval: 1
          }
        },
        padding: null
      },
      {
        title: '模型ATTCK Top5',
        icon: 'icon-wantpick',
        type: 'chart',
        style: null,
        chartName: 'TrxHBarChart',
        chart: {
          x: ['name'],
          y: ['value'],
          data: []
        },
        styleCfg: {
          xAxis: {
            minInterval: 1
          },
          grid: {
            "left": "18%",
          }
        },
        padding: null
      },
      {
        title: '模型专题 Top5',
        icon: 'icon-advanced',
        type: 'chart',
        style: null,
        chartName: 'TrxHBarChart',
        chart: {
          x: ['name'],
          y: ['value'],
          data: []
        },
        styleCfg: {
          xAxis: {
            minInterval: 1
          }
        },
        padding: null
      },
    ];
    // 默认标签选中第一个
    this.getIndex(0);
    this.query();
  }

  query() {
    // this.loading = true;
    // this.top5Data = [];
    this.getTotalDatas();
    this.getModelActiveNum();
    this.securityEvent();
    this.tagsAndSanE();
    // this.securityEventTop();
    // this.eventTypeActiveNumTop();
  }

  refresh(e) {
    e.endDate = e.startDate = null;
    e.endDateStr = e.startDateStr = '';
    e.inputFieldValue = '';
    this.from = {
      modelType: '',
      aggSize: 5,
      startTime: '',
      endTime: ''
    };
    this.query();
  }

  timeClick(e) {
    this.from.startTime = e.dateRange ? e.dateRange.startDateStr : '';
    this.from.endTime = e.dateRange ? e.dateRange.endDateStr : '';
  }

  getTotalDatas() {
    const modelType = this.modelType.find(e => e.isSelect).value;
    zip(
      this.ht.models({ modelType, statisticsType: 'SOURCE' }),
      this.ht.models({ modelType, statisticsType: 'STATUS' })
    ).subscribe(d => {
      this.totalDatas.forEach((e, i) =>
        i < 2 && e.list.forEach(
          item => item.value = (d[i].find(k => k.key == item.name || k.key == item.key) || { value: 0 }).value
        )
      );
    })
  }

  // 获取活跃模型数
  getModelActiveNum() {
    const data = this.totalDatas[2],
      modelType = this.modelType.find(e => e.isSelect).value,
      startTime = data.list[0].startTime,
      endTime = data.list[0].endTime;
    data.list[0].show = false;
    this.ht.activeNum({ modelType, startTime, endTime }).subscribe(d => {
      data.list[0].show = true;
      data.list[0].chart.data = [{ name: `/${d.total}`, value: d.activeNum }];
      data.tTooltip = `模型总数${d.total}，活跃模型数${d.activeNum}`;
      setTimeout(() => {
        const w = document.querySelector('.rule-portrait-con .inner-box').clientWidth + 12 + 'px';
        document.querySelector('.chart-title')['attributeStyleMap'].set('width', w);
      }, 3000)
    })
  }

  // 安全事件分布
  securityEvent() {
    const data = this.eventDatas[1],
      modelType = this.modelType.find(e => e.isSelect).value,
      startTime = data.startTime,
      endTime = data.endTime;
    data.show = false;
    this.ht.securityEvent({ modelType, startTime, endTime }).subscribe(d => {
      data.show = true;
      data.chart.data = d.map(e => ({ name: e.key, value: e.doc_count }));
    })
  }

  // 事件标签 数据源分布 安全事件Top5
  tagsAndSanE() {
    const modelType = this.modelType.find(e => e.isSelect).value, size = 5;
    const arr = [...this.tagsDatas, ...this.top5Datas];
    arr.forEach(e => {
      e.show = false;
    });
    this
    zip(
      this.ht.tagTop({ modelType, groupId: 'EVENT_TAG', size }),
      this.ht.tagTop({ modelType, groupId: 'DATA_TYPE', size }),
      this.ht.securityEventTop({ modelType, aggSize: size, startTime: '', endTime: '' }),
      this.ht.tagTop({ modelType, groupId: 'CATEGORY', size }),
      this.ht.tagTop({ modelType, groupId: 'ATTCK_TAG', size }),
      this.ht.tagTop({ modelType, groupId: 'SUBJECT', size }),
    ).subscribe(d => {
      arr.forEach((e, i) => {
        e.show = true;
        e.chart.data = d[i].map(item => ({name: item.key, value: item.value || item.doc_count || 0}))
      })

    })
  }


  // 模型安全事件TOP5
  securityEventTop() {
    const { startTime, endTime, ...p } = this.from;
    zip(this.ht.securityEventTop(this.from), this.ht.eventTypeActiveNumTop(p)).subscribe((d) => {
      this.top5Data[0] = {
        name: `模型安全事件TOP${this.from.aggSize}`,
        chartName: 'TrxHBarChart',
        dataCfg: {
          x: ['name'],
          y: ['value'],
          data: []
        },
        styleCfg: {
          global: {
            showNum: 5,
            liTop: 22
          },
          xAxis: {
            minInterval: 1
          }
        }
      };
      this.top5Data[0].dataCfg.data = d[0].length > 0 ? d[0].filter(e => e.doc_count).map(e => ({ name: e.key, value: e.doc_count })) : [];
      const arr = [];
      this.top5Data[1] = {
        name: `模型类型TOP${this.from.aggSize}`,
        chartName: 'TrxHBarChart_M',
        dataCfg: {
          x: ['name'],
          y: ['value'],
          type: ['type'],
          data: []
        },
        styleCfg: {
          global: {
            showNum: 5,
            liTop: 22
          },
          tooltip: {
            show: true,
            formatter: (e) => {
              const ns = arr.filter(vs => e.name == vs.name);
              return e.name + '<br>活跃模型个数:' + ns[0].total + '<br>总数:' + ns[1].total;
            },
          },
          xAxis: {
            minInterval: 1
          }
        }
      };
      d[1].length > 0 && d[1].filter(e => e.total).forEach(e => {
        arr.push({ name: e.name, value: e.activeNum, total: e.activeNum, type: '活跃模型个数' });
        arr.push({ name: e.name, value: e.total - e.activeNum, total: e.total, type: '总数' });
      });
      this.top5Data[1].dataCfg.data.push(...arr)
      this.loading = false
    })
  }

  // 模型类型TOP5
  eventTypeActiveNumTop() {
    // AnalyzeWhiteComponent;
  }

  // 一天一周一月点击事件
  onToggleHandler(e, item?, method?) {
    if (item) {
      const d = new Date();
      item.startTime = Util.dateFormat(new Date(d.getTime() - e.value * 1000 * 60 * 60 * 24)),
        item.endTime = Util.dateFormat(d);
      method && this[method]();
    }
  }

  // 切换模型事件
  getIndex(i) {
    // const colorArr = {
    //   normal: [
    //     ['#4dabca', '#95d8ee'],
    //     ['#ddb575', '#f4dbb3'],
    //     ['#b389e4', '#dcc2fa'],
    //     ['#f08a8a', '#ff9494'],
    //     ['#72c886', '#a2e1b1'],
    //     ['#639cde', '#a2cbfa'],
    //     ['#59c8c5', '#a7eae8'],
    //     ['#dac579', '#fff0b9'],
    //     ['#a3b0b9', '#d3dee6'],
    //   ],
    //   selected: [
    //     ['#588999', '#95d8ee'],
    //     ['#a98346', '#f4dbb3'],
    //     ['#804eb9', '#dcc2fa'],
    //     ['#af5151', '#ff9494'],
    //     ['#3d8d50', '#a2e1b1'],
    //     ['#4178b7', '#a2cbfa'],
    //     ['#3ca29f', '#a7eae8'],
    //     ['#b9a763', '#fff0b9'],
    //     ['#87959f', '#d3dee6'],
    //   ],
    // };
    this.modelType = this.modelType.map((e, mi) => {
      // const k = mi == i ? 'selected' : 'normal';
      e.isSelect = mi == i;
      // e.style = { color: colorArr[k][mi][0], borderColor: colorArr[k][mi][1], backgroundColor: e.isSelect ? colorArr[k][mi][1] : '#fff' }
      return e;
    });

    this.query();
  }

  // 切换页签
  onTabChange(e) {
    this.getIndex(e.index);
  }
}
