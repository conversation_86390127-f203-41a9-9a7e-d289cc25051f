<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.topsec.wilkes</groupId>
    <artifactId>wilkes-parent</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>wilkes-integrated-dist</artifactId>
  <name>Wilkes Integrated Distribution</name>
  <packaging>pom</packaging>

  <properties>
    <maven.deploy.skip>true</maven.deploy.skip>
  </properties>

  <build>
    <plugins>
      <plugin>
        <groupId>pl.project13.maven</groupId>
        <artifactId>git-commit-id-plugin</artifactId>
        <version>4.0.0</version>
        <executions>
          <execution>
            <id>get-the-git-infos</id>
            <goals>
              <goal>revision</goal>
            </goals>
            <phase>initialize</phase>
          </execution>
          <execution>
            <id>validate-the-git-infos</id>
            <goals>
              <goal>validateRevision</goal>
            </goals>
            <phase>compile</phase>
          </execution>
        </executions>
        <configuration>
          <dotGitDirectory>${project.basedir}/.git</dotGitDirectory>
          <prefix>wilkes</prefix>
          <dateFormat>yyyy.MMdd</dateFormat>
          <dateFormatTimeZone>${user.timezone}</dateFormatTimeZone>
          <verbose>false</verbose>
          <abbrevLength>8</abbrevLength>
          <generateGitPropertiesFile>true</generateGitPropertiesFile>
          <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties
          </generateGitPropertiesFilename>
          <format>properties</format>
          <gitDescribe>
            <skip>false</skip>
            <always>true</always>
            <dirty>-dirty</dirty>
            <tags>true</tags>
          </gitDescribe>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <version>3.1.2</version>
        <executions>
          <execution>
            <id>copy</id>
            <phase>prepare-package</phase>
            <goals>
              <goal>copy</goal>
            </goals>
            <configuration>
              <artifactItems>
                <artifactItem>
                  <groupId>com.topsec.wilkes</groupId>
                  <artifactId>wilkes-dist</artifactId>
                  <version>${project.version}</version>
                  <type>war</type>
                  <destFileName>wilkes.war</destFileName>
                </artifactItem>
                <artifactItem>
                  <groupId>com.topsec.wilkes</groupId>
                  <artifactId>wilkes-ai-model</artifactId>
                  <version>${wilkes-ai.version}</version>
                  <type>war</type>
                  <destFileName>wilkes-ai-model.war</destFileName>
                </artifactItem>
                <artifactItem>
                  <groupId>com.topsec.wilkes</groupId>
                  <artifactId>filkes-container</artifactId>
                  <version>${filkes.version}</version>
                  <classifier>correlation</classifier>
                  <destFileName>filkes-container-${filkes.version}-correlation.jar</destFileName>
                  <type>jar</type>
                </artifactItem>
                <artifactItem>
                  <groupId>com.topsec.wilkes</groupId>
                  <artifactId>filkes-container</artifactId>
                  <version>${filkes.version}</version>
                  <classifier>behaviour</classifier>
                  <destFileName>filkes-container-${filkes.version}-behaviour.jar</destFileName>
                  <type>jar</type>
                </artifactItem>
                <artifactItem>
                  <groupId>com.topsec.wilkes</groupId>
                  <artifactId>filkes-container</artifactId>
                  <version>${filkes.version}</version>
                  <destFileName>filkes-container-${filkes.version}-ai.jar</destFileName>
                  <classifier>ai</classifier>
                  <type>jar</type>
                </artifactItem>
              </artifactItems>
              <!-- other configurations here -->
            </configuration>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>1.8</version>
        <executions>
          <execution>
            <phase>prepare-package</phase>
            <goals>
              <goal>run</goal>
            </goals>
            <configuration>
              <target>
                <unwar src="${project.build.directory}/dependency/wilkes.war"
                       dest="${project.build.directory}/dependency/wilkes"/>
                <unwar src="${project.build.directory}/dependency/wilkes-ai-model.war"
                       dest="${project.build.directory}/dependency/wilkes-ai-model"/>
              </target>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>1.9.1</version>
        <executions>
          <execution>
            <id>timestamp-property</id>
            <goals>
              <goal>timestamp-property</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <name>wilkes.current.time</name>
          <pattern>yyyyMMdd.HHmm</pattern>
          <timeZone>GMT+8</timeZone>
          <regex/>
          <source/>
          <value/>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <version>3.2.0</version>
        <executions>
          <execution>
            <id>dist</id>
            <phase>package</phase>
            <goals>
              <goal>single</goal>
            </goals>
            <configuration>
              <finalName>wilkes_${wilkes_version_number}_${wilkes.current.time}
              </finalName>
              <descriptors>
                <descriptor>src/main/assembly/dist.xml</descriptor>
              </descriptors>
              <appendAssemblyId>false</appendAssemblyId>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>