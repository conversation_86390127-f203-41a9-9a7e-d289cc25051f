package com.topsec.wilkes.controller;

import com.topsec.wilkes.service.AIAlgorithmBaseService;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.InputStream;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> (<EMAIL>)
 */
@RestController
@RequestMapping("/aiAlgorithmBase")
@Slf4j
public class AIAlgorithmBaseController {
  @Autowired
  private AIAlgorithmBaseService aiAlgorithmBaseService;

  @ResponseBody
  @RequestMapping(value = "/{id}/download", method = RequestMethod.GET)
  public void downloadAIAlgorithm(@PathVariable("id") String id, HttpServletResponse response) {
    InputStream modelEntityInputStream = aiAlgorithmBaseService.getAIAlgorithmEntity(id);
    try {
      response.reset();
      IOUtils.copy(modelEntityInputStream, response.getOutputStream());
      response.setContentType("application/x-download");
      response.setHeader("Content-Disposition",
          "attachment;filename=\"" + id + ".zip\"");
      response.flushBuffer();
    } catch (IOException e) {
      log.error("", e);
    }
  }
}
