{"request": {"path": "", "mothed": "get", "parms": "", "active": false, "dataOnError": "local"}, "data": {"datas": [{"label": "安装植入", "value": "5", "selected": false}, {"label": "命令与控制", "value": "6", "selected": false}, {"label": "载荷投递", "value": "3", "selected": false}, {"label": "漏洞利用", "value": "4", "selected": false}, {"label": "侦察跟踪", "value": "1", "selected": false}, {"label": "目标达成", "value": "7", "selected": false}, {"label": "非杀伤链日志", "value": "9", "selected": false}, {"label": "未知杀伤链阶段", "value": "10", "selected": false}], "active": true}}