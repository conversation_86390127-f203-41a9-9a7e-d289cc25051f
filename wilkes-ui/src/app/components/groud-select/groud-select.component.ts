import { Component, OnInit, Input, Output, EventEmitter, forwardRef, ViewChild, ElementRef } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { MessageService } from '@tui/component-library';
import { RuleService } from 'src/app/ofmodules/rules/services/rule.service';
import { TreeFilterPipe } from 'src/app/common/pipe/treeFilter';
import { v4 as uuidv4 } from 'uuid';

@Component({
  selector: 'app-groud-select',
  templateUrl: './groud-select.component.html',
  styleUrls: ['./groud-select.component.less'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => GroudSelectComponent),
      multi: true,
    },
  ],
})
export class GroudSelectComponent implements OnInit {
  @ViewChild('tree') tree: any;
  @ViewChild('dom')
  dom!: ElementRef;
  @ViewChild('bgCover')
  bgCover!: ElementRef;
  @Input() key = 'data';
  @Input() outType = false;
  // 是否支持自定义字段
  @Input() custom = false;
  className: any;
  // 下拉数据
  _options = [];
  baseTreeNodes = [];
  // 双向绑定之
  private value: any;
  // 选择模式 single单选 checkbox多选
  @Input() selectionMode = 'checkbox';
  // 默认选中
  selection: any = [];
  @Input() height = '230px';
  @Input() disabled = false;
  @Input() isFilter = true;
  @Input() filterType = 'value';
  // 是否隐藏复选框
  @Input() isCheckbox = true;
  // 提示
  @Input() placeholder = '请选择数据';
  // 是否显示搜索功能
  @Input() searchRestrictions = true;
  @Output() radiosEvent = new EventEmitter<any>();
  // 选中事件
  @Output() select = new EventEmitter<any>();
  // 取消选中事件
  @Output() unselect = new EventEmitter<any>();
  @ViewChild('addInput')
  addInput!: ElementRef;
  treeHeigh = 200;
  // 双向绑定的值
  inputValue = '';
  // 新增输入框的值
  addValue = '';
  addList = [];
  // 筛选框的值
  filterName: any;
  // 下拉框出现的位置信息
  styleXY: any;
  // 下拉列表展示内容
  // 下拉列表展示内容
  isShow!: boolean;
  // 复选框内容
  radios: any;
  radmodel: any;
  // 示例参数
  fileDetailObj = {
    isFileDetail: false,
    data: [],
    key: null,
  };
  public onModelChange: Function = () => {};
  public onModelTouched: Function = () => {};
  trackFunc(index: any, item: any) {
    return item[this.key];
  }

  // 开启切换
  @Input() set _radios(v: string | any[]) {
    if (v && v.length > 0) {
      this.radios = v;
      this.radmodel = v[0];
      this.treeHeigh = parseInt(this.height) - 30 - 35;
    }
  }

  // 组件的下拉值
  @Input() set options(v: any[]) {
    if (v && v.length > 0) {
      if (this.custom && !v.find((ee) => ee.key === 'add')) {
        v.unshift({
          groupId: '',
          id: '',
          key: 'add',
          label: '新增',
          levelNum: '1',
          name: '新增',
          parent: undefined,
          total: 1,
          value: '',
          selectable: false,
        });
      }
      v.forEach((e) => {
        e.children && e.children.length > 0 && e.children.forEach((ee: { parent: any }) => delete ee.parent);
      });
      this.baseTreeNodes = JSON.parse(JSON.stringify(v));
      // @ts-ignore
      this._options = v;
      setTimeout(() => {
        this.setSelection();
      }, 0);
    }
  }
  constructor(private ruleService: RuleService, private messageService: MessageService) {}

  ngOnInit() {
    this.treeHeigh = parseInt(this.height) - 30 - (this.radmodel ? 35 : 0);
    this.className = 'g-' + uuidv4() + '-con';
  }

  writeValue(value: any): void {
    if (value !== undefined) {
      this.value = value || '';
      setTimeout(() => {
        this.setSelection();
      }, 0);
    }
  }

  registerOnChange(fn: any): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onModelTouched = fn;
  }

  // 切换数据源点击按钮
  radioClick(radio: any) {
    this.radiosEvent.emit(radio);
  }

  // 输入框获取焦点事件
  inputfocus(e: string, node?: { isadd: boolean }) {
    const fun = (
      arr: any[],
      o: (
        | {
            groupId: string;
            id: string;
            key: string;
            label: string;
            levelNum: string;
            name: string;
            parent: undefined;
            total: number;
            value: string;
          }
        | {
            groupId: string;
            id: string;
            key: string;
            label: string;
            levelNum: string;
            name: string;
            parent: undefined;
            total: number;
            value: string;
            selectable: boolean;
          }
      )[],
    ) => {
      arr.splice(0, 1);
      arr.unshift(...o);
    };
    node && (node.isadd = e === 'addClick');
    if (e === 'addClick') {
      setTimeout(() => {
        this.addInput.nativeElement.focus();
      }, 0);
    } else if (e === 'blur') {
      // 清除空格
      this.addValue = this.addValue.trim();
      if (
        this.addValue &&
        // @ts-ignore
        !this.querySelctNode(this._options, this.addValue, 'children', 'label')
      ) {
        const o = {
          groupId: '',
          id: this.addValue,
          key: this.addValue,
          label: this.addValue,
          levelNum: '1',
          name: this.addValue,
          parent: undefined,
          total: 1,
          value: this.addValue,
        };
        //@ts-ignore
        this.addList.push(o);
        fun(this.baseTreeNodes, [
          {
            groupId: '',
            id: '',
            key: 'add',
            label: '新增',
            levelNum: '1',
            name: '新增',
            parent: undefined,
            total: 1,
            value: '',
            selectable: false,
          },
          o,
        ]);
        this.baseTreeNodes !== this._options &&
          fun(this._options, [
            {
              groupId: '',
              id: '',
              key: 'add',
              label: '新增',
              levelNum: '1',
              name: '新增',
              parent: undefined,
              total: 1,
              value: '',
              selectable: false,
            },
            o,
          ]);
        this.selection.push(o);
        //@ts-ignore
        this.onNodeSelect({ node: o });
        this.addValue = '';
      } else if (this.addValue) {
        this.addValue = '';
        //@ts-ignore
        node.isadd = true;
        !!this.addValue && this.addInput.nativeElement.focus();
        this.messageService.add({
          key: 'rules-rule',
          severity: 'warn',
          summary: '提示',
          detail: '标签不合法或已存在！！！',
          isClear: true,
        });
      }
    }
  }

  // 示例详情展示
  fileClick(node: { key: any } | null) {
    this.fileDetailObj.isFileDetail = true;
    //@ts-ignore
    this.fileDetailObj.key = node;
    //@ts-ignore
    this.ruleService.getExampleTreeDetails(node.key).subscribe((data) => {
      this.fileDetailObj.data = data;
    });
  }

  fileSave(node: any) {
    // @ts-ignore
    node.data = [];
    node.isFileDetail = false;
    this.selection = Array.from(new Set([...(this.selection || []), node.key]));
  }

  // 鼠标事件判断是否是自定义字段
  keydown(e: { keyCode: number; key: string }) {
    if (e.keyCode === 13 || e.key === 'Enter') {
      this.addInput.nativeElement.blur();
    }
  }

  // 展开菜单
  toOpen(e: any) {
    const that = this;
    if (this.disabled) return;
    this.isShow = !this.isShow;
    if (this.isShow) {
      document.body.appendChild(this.dom.nativeElement);
      document.body.appendChild(this.bgCover.nativeElement);
      // document.body.appendChild(this.bgCover.nativeElement);
      //@ts-ignore
      document.querySelector('body').onclick = function (d) {
        // if (d.target) {
        //@ts-ignore
        that.coverClick();
        document.body.removeChild(that.dom.nativeElement);
        document.body.removeChild(that.bgCover.nativeElement);
        //@ts-ignore
        document.querySelector('body').onclick = null;
        // }
      };
    }
    this.filterName = '';
    this.initPost(e);
  }

  initPost(e: any) {
    this.position = e;
    window.onresize = () => {
      this.position = e;
    };
  }

  intval(v: string | number) {
    //@ts-ignore
    v = parseInt(v);
    return isNaN(v) ? 0 : v;
  }

  // 过滤实时变化逻辑
  filterChange(e: string) {
    if (e) {
      this.baseTreeNodes.forEach((e) => {
        //@ts-ignore
        e.children &&
          //@ts-ignore
          e.children.length > 0 &&
          //@ts-ignore
          e.children.forEach((ee: { parent: any }) => delete ee.parent);
      });
      this._options = new TreeFilterPipe().transform(this.baseTreeNodes, 'label', e, 'children');
    } else {
      this._options = JSON.parse(JSON.stringify(this.baseTreeNodes));
    }
    // 当查询时下拉列过滤特殊处理
    this.setSelection(true);
  }

  setSelection(is?: boolean | undefined) {
    const value = this.value;
    if ([undefined, null].includes(value)) return;
    if (value instanceof Array) {
      this.selection = (is ? this.selection : value)
        .map(
          (e: { [x: string]: any }) =>
            this.querySelctNode(
              // @ts-ignore
              this._options,
              is ? e[this.key] : e,
              e['items'] ? 'items' : 'children',
              this.key,
            ) || e,
        )
        .filter((e: any) => e);
      this.inputValue = this.selection.map((e: { label: any }) => e.label).toString();
    } else {
      this.selection = this.selection instanceof Array ? this.selection : [this.selection];
      this.selection = (is ? this.selection : value.split(','))
        .map(
          (e: { [x: string]: any }) =>
            this.querySelctNode(
              // @ts-ignore
              this._options,
              is ? e[this.key] : e,
              e['items'] ? 'items' : 'children',
              this.key,
            ) || e,
        )
        .filter((e: any) => e);
      if (this.selectionMode === 'checkbox') {
        this.inputValue = this.selection.map((e: { label: any }) => e.label).toString();
      } else {
        this.selection = this.selection[0];
        this.inputValue = (this.selection || { label: '' }).label;
      }
    }
  }

  // 迭代查询树组件的值
  querySelctNode(treeNodes: never[], node: string, childName: string, key: string) {
    let o = null;
    const fun = (arr: any[]) => {
      let is = false;
      if (arr instanceof Array) {
        arr.forEach((e) => {
          if (e[key] === node) {
            o = e;
            is = true;
          } else {
            // tslint:disable-next-line:no-unused-expression
            const isNo = e[childName] && e[childName].length > 0 && fun(e[childName]);
            e.expanded = e.expanded ? true : isNo;
          }
        });
      }
      return is;
    };
    fun(treeNodes);
    return o;
  }

  getPos(e: { offsetParent: any; offsetLeft: any; currentStyle: { borderLeftWidth: any; borderTopWidth: any }; offsetTop: any; scrollTop: any }) {
    // left and top value
    let x = 0,
      y = 0,
      top = 0; // offsetParent返回一个指向最近的定位元素,标准模式如果没有就返回html/body,表示迁移量都是相对其中来计算.
    while (e.offsetParent) {
      // 计算偏移量
      x += e.offsetLeft + (e.currentStyle ? this.intval(e.currentStyle.borderLeftWidth) : 0);
      y += e.offsetTop + (e.currentStyle ? this.intval(e.currentStyle.borderTopWidth) : 0); // 最近的定位元素或者body
      top += e.scrollTop || 0;
      e = e.offsetParent;
    }

    x += e.offsetLeft + (e.currentStyle ? this.intval(e.currentStyle.borderLeftWidth) : 0);
    y += e.offsetTop + (e.currentStyle ? this.intval(e.currentStyle.borderTopWidth) : 0);
    top += e.scrollTop || 0;
    return {
      x,
      y,
      top,
    };
  }

  // 位置
  set position(e: { [x: string]: number }) {
    // tslint:disable-next-line: radix
    //@ts-ignore
    const xy = this.getPos(document.querySelector('.' + this.className));
    //@ts-ignore
    const h = document.querySelector('body')['offsetHeight'];
    const domm = document.querySelector(`.${this.className}`);
    if (e['clientY'] * 1 + parseInt(this.height) + 10 > h) {
      this.styleXY = {
        top: xy.y - xy.top - parseInt(this.height) + 'px',
        left: xy.x + 'px',
        //@ts-ignore
        width: domm['offsetWidth'] + 'px',
        height: this.height,
        position: 'fixed',
        zIndex: '9999',
      };
    } else {
      this.styleXY = {
        top: xy.y - xy.top + 35 + 'px',
        left: xy.x + 'px',
        //@ts-ignore
        width: domm['offsetWidth'] + 'px',
        height: this.height,
        position: 'fixed',
        zIndex: '9999',
      };
    }
  }

  // 选中
  onNodeSelect(e: { node: any }) {
    if (e.node.key) {
      this.selection = this.selectionMode === 'checkbox' ? this.selection.filter((ee: { key: any }) => ee.key) : this.selection;
    } else {
      this.selection = this.selection.filter((ee: { key: any }) => !ee.key);
    }
    this.setInputValue(this.selectionMode !== 'single');
    this.select.emit(e);
  }

  // 取消
  onNodeUnselect(e: any) {
    this.setInputValue(true);
    this.unselect.emit(e);
  }

  setInputValue(is = false) {
    let key = '';
    if (this.selectionMode === 'single') {
      this.inputValue = this.selection.label;
      key = this.selection[this.key];
      this.isShow = false;
    } else {
      this.inputValue = (this.selection || []).map((e: { label: any }) => e.label).toString();
      key = (this.selection || []).map((e: { [x: string]: any }) => e[this.key]);
      !this.outType && (key = key.toString());
    }
    if (!is && this.value.toString() !== key.toString()) {
      this.value = key;
      this.onModelChange(key);
    }
  }

  coverClick() {
    this.isShow = false;
    this.filterChange('');
    this.setInputValue();
    window.onresize = () => {};
  }

  clickClear() {
    this.isShow = false;
    this.selection = this.selectionMode === 'single' ? { label: '', [this.key]: '' } : [];
    this.inputValue = '';
    this.filterChange('');
    // 当是新增字段是清除要过滤掉
    const options = this.custom
      ? this.baseTreeNodes.filter(
          //@ts-ignore
          (e) => !this.addList.find((ee) => ee.key === e.key),
        )
      : this.baseTreeNodes;
    this._options = JSON.parse(JSON.stringify(options));
    this.setInputValue();
    this.setSelection();
  }
}
