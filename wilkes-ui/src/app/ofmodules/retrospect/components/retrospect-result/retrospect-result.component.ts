import { AfterViewInit, Component, OnInit } from '@angular/core';
import { HttpRetrospect } from '../../service/http.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'app-retrospect-result',
  templateUrl: './retrospect-result.component.html',
  styleUrls: ['./retrospect-result.component.less'],
})
export class RetrospectResultComponent implements OnInit, AfterViewInit {
  public pageEvent = 'security';
  public tableHeader = [
    { name: '日志接收时间', value: '日志接收时间', width: '100px' },
    { name: '源IP地址', value: '源IP地址', width: '18%' },
    { name: '源端口', value: '源端口', width: '18%' },
    { name: '目标IP地址', value: '目标IP地址', width: '18%' },
    { name: '目标端口', value: '目标端口', width: '18%' },
    { name: '传输协议', value: '传输协议', width: '18%' },
  ];
  public tableList = [];
  public dataDetail = {};
  public type = 'table';

  // public security_log = [{"json":{"设备类型":"534443","日志类型":"vnc����Samba����PPplkjhjlh","采集器地址":"***********","日志模块分类":"recorder","采集器编号":"5900afdsfga1615900","事件分类1":"��ʬ","杀伤链阶段":"�Զ���K_Cfugai","目标责任人ID":"test222,*******","源物理地址":"00:0c:29:2a:c2:a5","目标IP地址":"*******,***********","源IP地址":"127.0.0.1,*******","事件时间":"2021/1/29 10:44","日志接收时间":"1611888278633"},"table":{"设备组":[{"nameCn":"设备类型","nameEn":"device_type","value":"534443"},{"nameCn":"日志类型","nameEn":"log_type","value":"vnc����Samba����PPplkjhjlh"},{"nameCn":"采集器地址","nameEn":"collector_addr","value":"***********"},{"nameCn":"日志模块分类","nameEn":"log_module_type","value":"recorder"},{"nameCn":"采集器编号","nameEn":"collector_id","value":"5900afdsfga1615900"},{"nameCn":"日志接收时间","nameEn":"log_receive_time","value":"1611888278633"}],"事件组":[{"nameCn":"事件分类1","nameEn":"first_event_type","value":"��ʬ"},{"nameCn":"杀伤链阶段","nameEn":"kill_chain_stage","value":"�Զ���K_Cfugai"}],"目标组":[{"nameCn":"目标责任人ID","nameEn":"dest_userid","value":"test222,*******"},{"nameCn":"目标IP地址","nameEn":"dest_ip","value":"*******,***********"}],"源组":[{"nameCn":"源物理地址","nameEn":"source_mac","value":"00:0c:29:2a:c2:a5"},{"nameCn":"源IP地址","nameEn":"source_ip","value":"127.0.0.1,*******"}],"事件字段组":[{"nameCn":"事件时间","nameEn":"event_time","value":"2021/1/29 10:44"}]}},{"json":{"设备类型":"231312","日志类型":"vnc����Samba����PPplkjhjlh","采集器地址":"***********","日志模块分类":"recorder","采集器编号":"5900afdsfga1615922","事件分类1":"��ʬ","杀伤链阶段":"�Զ���K_Cfugai","目标责任人ID":"test333,222","源物理地址":"00:0c:29:2a:c2:a5","目标IP地址":"*******,***********","源IP地址":"127.0.0.1,*******","事件时间":"2021/1/29 10:44","日志接收时间":"1.61189E+12"},"table":{"设备组":[{"nameCn":"设备类型","nameEn":"device_type","value":"231312"},{"nameCn":"日志类型","nameEn":"log_type","value":"vnc����Samba����PPplkjhjlh"},{"nameCn":"采集器地址","nameEn":"collector_addr","value":"***********"},{"nameCn":"日志模块分类","nameEn":"log_module_type","value":"recorder"},{"nameCn":"采集器编号","nameEn":"collector_id","value":"5900afdsfga1615922"},{"nameCn":"日志接收时间","nameEn":"log_receive_time","value":"1.61189E+12"}],"事件组":[{"nameCn":"事件分类1","nameEn":"first_event_type","value":"��ʬ"},{"nameCn":"杀伤链阶段","nameEn":"kill_chain_stage","value":"�Զ���K_Cfugai"}],"目标组":[{"nameCn":"目标责任人ID","nameEn":"dest_userid","value":"test333,222"},{"nameCn":"目标IP地址","nameEn":"dest_ip","value":"*******,***********"}],"源组":[{"nameCn":"源物理地址","nameEn":"source_mac","value":"00:0c:29:2a:c2:a5"},{"nameCn":"源IP地址","nameEn":"source_ip","value":"127.0.0.1,*******"}],"事件字段组":[{"nameCn":"事件时间","nameEn":"event_time","value":"2021/1/29 10:44"}]}}];
  public security_log: any = {
    list: [],
    page: {
      page: 1,
      size: 20,
    },
    scrollId: '',
  };
  // public alarm_event = [];
  loading = false;
  constructor(private router: Router, private http: HttpRetrospect, private activatedRoute: ActivatedRoute) {}
  ngAfterViewInit(): void {
    const searchData = {
      parameter: {
        id: this.activatedRoute.snapshot.params.id,
        modelName: '',
        modelType: '',
        offlineStatus: '',
        pageNo: 1,
        pageSize: 20,
        sort: 'offlineStatus',
        sortType: 'DESC',
      },
      total: 0,
    };
    this.searchModels(searchData);
  }

  ngOnInit() {
    this.searchResult({
      page: this.security_log.page['page'],
      size: this.security_log.page['size'],
    });
  }

  onPageChange(e) {
    this.searchResult(e);
  }

  getEvent(event: { target: { getAttribute: (arg0: string) => any } }) {
    const name = event.target.getAttribute('eventName');
    name && (this.pageEvent = name);
  }

  searchResult(p) {
    this.loading = true;
    this.http.getResult(this.activatedRoute.snapshot.params.id, { ...p, scrollId: this.security_log.scrollId }).subscribe(
      (success: any) => {
        this.security_log = {
          list: success.list,
          page: {
            total: success.size,
            page: success.page,
          },
          scrollId: success.scrollId,
        };
      },
      (error) => {
        this.loading = false;
      },
      () => {
        this.loading = false;
      },
    );
  }

  searchModels(params?: { parameter: any; total?: number }) {
    const p = {};
    //@ts-ignore
    for (const k in params.parameter) {
      //@ts-ignore
      if (params.parameter.hasOwnProperty(k)) {
        //@ts-ignore
        p[k] = encodeURIComponent(params.parameter[k]);
      }
    }
    this.http.getModels(p).subscribe((data) => {
      const d = (this.dataDetail = data.content[0]);
      //@ts-ignore
      this.dataDetail['text'] = `${d.modelName}在${d.startTime}-${d.endTime}内的调试分析结果`;
    });
  }

  backList() {
    const locationUrl = this.router.url.split('/result')[0];

    // 适配盘古修改
    // this.tuiNavService.closeNavByUri(this.router.url.replace('/', ''));
    this.router.navigateByUrl(locationUrl);
  }
}
