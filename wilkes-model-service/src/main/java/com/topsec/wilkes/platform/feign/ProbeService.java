package com.topsec.wilkes.platform.feign;

import static com.topsec.wilkes.config.WilkesConfig.PROBEMANAGER;

import com.topsec.minsky.domain.Process;
import com.topsec.minsky.dto.ProcessMetadata;
import com.topsec.wilkes.common.dto.*;
import com.topsec.wilkes.dto.Platform;
import com.topsec.wilkes.dto.RestPage;
import com.topsec.wilkes.platform.feign.fallbackFactory.ProbeFallbackFactory;

import lombok.Data;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(name = PROBEMANAGER, fallbackFactory = ProbeFallbackFactory.class, url = "${feign.platform.url:}")
public interface ProbeService {

  @Data
  public static class Platforms {

    List<Platform> content;
  }

  @GetMapping(value = "/probemanager/probemanager/getProbe")
  Platforms getPlatforms(@RequestParam(value = "isUp") String isUp,
      @RequestParam(value = "pageIndex") String pageIndex,
      @RequestParam(value = "pageSize") String pageSize,
      @RequestParam(value = "status") String status);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes/models/{nos}", method = RequestMethod.DELETE)
  int deleteModels(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @PathVariable(value = "nos") List<String> nos,
      @RequestHeader("platformId") String platformIdHeader);

  //  @RequestParam Map<String, Object> paramMap
  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes/models", method = RequestMethod.GET)
  RestPage getModelsByCondition(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @RequestParam(value = "source", required = false) String source,
      @RequestParam(value = "name", required = false) String name,
      @RequestParam(value = "status", required = false) Status status,
      @RequestParam(value = "categoryId", required = false, defaultValue = "") String[] categoryId,
      @RequestParam(value = "killChainId", required = false, defaultValue = "") String[] killChainId,
      @RequestParam(value = "subjectId", required = false, defaultValue = "") String[] subjectId,
      @RequestParam(value = "dataTypeId", required = false, defaultValue = "") String[] dataTypeId,
      @RequestParam(value = "modelIds", required = false, defaultValue = "") String[] modelNos,
      @RequestParam(value = "pageNo", required = false, defaultValue = "1") int pageNo,
      @RequestParam(value = "pageSize", required = false, defaultValue = "20") int pageSize,
      @RequestParam(value = "sort", required = false, defaultValue = "status") String sort,
      @RequestParam(value = "sortType", required = false, defaultValue = "DESC") Sort.Direction sortType,
      @RequestParam(value = "modelType", required = false) ModelType modelType,
      @RequestParam(value = "creator", required = false) String creator,
      @RequestHeader(value = "platformId") String platformId);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes/models/{no}", method = RequestMethod.PUT)
  Model editModel(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @PathVariable("no") String no, @RequestBody Model model);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes/models/{nos}/{status}", method = RequestMethod.PUT)
  Map<String, Object> editModelStatus(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @PathVariable(value = "nos") List<String> nos,
      @PathVariable(value = "status") Status status);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes/models", method = RequestMethod.POST)
  Model createModel(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @RequestBody Model model, @RequestHeader("platformId") String platformIdHeader);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes/rules/{no}", method = RequestMethod.GET)
  Object getModelWithRules(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @PathVariable(value = "no") String modelNo);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes/behaviours/{no}", method = RequestMethod.GET)
  Object getModelWithBehaviours(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @PathVariable(value = "no") String modelNo);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes/tags/list", method = RequestMethod.GET)
  List<Tag> getTags(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @RequestParam(value = "parentId", required = false) String parentId,
      @RequestParam(value = "groupId", required = false) GroupId groupId,
      @RequestParam(value = "modelType", required = false) ModelType modelType,
      @RequestParam(value = "modelNos", required = false) List<String> modelNos,
      @RequestHeader("platformId") String platformIdHeader);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes/tags/list/{parentId}", method = RequestMethod.GET)
  List<Tag> getTagsWithParentId(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @PathVariable(value = "parentId", required = false) String parentId,
      @RequestParam(value = "groupId", required = false) GroupId groupId,
      @RequestParam(value = "modelType", required = false) ModelType modelType,
      @RequestParam(value = "modelNos", required = false) List<String> modelNos,
      @RequestHeader("platformId") String platformIdHeader);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/minsky/namespaces/{namespace}", method = RequestMethod.POST)
  ProcessMetadata addProcess(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @PathVariable("namespace") String namespace, @RequestBody Process process);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/minsky/processes/{ids}", method = RequestMethod.DELETE)
  List<ProcessMetadata> deleteProcess(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @PathVariable(value = "ids") List<String> ids);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/wilkes//getKafkaURI/{topic}", method = RequestMethod.POST)
  String getKafkaURI(@PathVariable(value = "platformIdUrl") String platformIdUrl,
      @PathVariable(value = "topic") String topic);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/pensieve/resources/types/typesStorage", method = RequestMethod.GET)
  List<Map<String, Object>> getKafkaInfoFromPensieve(
      @PathVariable(value = "platformIdUrl") String platformIdUrl,
      @RequestParam("dataSourceType") String var1);

  @RequestMapping(value = "/probemanager/proxy/{platformIdUrl}/pensieve/resources/types/typesStorage", method = RequestMethod.GET)
  List<Map<String, Object>> getFactTableAddress(
      @PathVariable(value = "platformIdUrl") String platformIdUrl,
      @RequestParam("typesAliasName") String var1,
      @RequestParam("dataSourceType") String var2);


}
