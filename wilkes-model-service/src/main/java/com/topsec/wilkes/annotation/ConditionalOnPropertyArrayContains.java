package com.topsec.wilkes.annotation;

import org.springframework.context.annotation.Conditional;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Conditional(OnPropertyArrayContainsCondition.class)
public @interface ConditionalOnPropertyArrayContains {
    String propertyName();

    String expectedValue();

    String defaultValue();
}
