package com.topsec.wilkes.util;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR>
 * java com.topsec.wilkes.util.SessionStop url params connectionTimeOut ReadTimeout
 */
public class SessionStop {
  public static void main(String[] args) {
    try {
      URL url = new URL(args[0]);
      HttpURLConnection connection = (HttpURLConnection) url.openConnection();
      connection.setRequestMethod("POST");
      connection.setConnectTimeout(Integer.parseInt(args[2]));
      connection.setReadTimeout(Integer.parseInt(args[3]));
      connection.setDoOutput(true);
      connection.setDoInput(true);
      connection.setUseCaches(false);
      connection.setInstanceFollowRedirects(true);
      connection.setRequestProperty("Content-Type", "application/json");
      connection.setRequestProperty("Connection", "Keep-Alive");
      String params = args[1];
      OutputStream out = connection.getOutputStream();
      out.write(params.getBytes());
      out.flush();
      out.close();
      connection.connect();
      int code = connection.getResponseCode();
      connection.disconnect();
      Thread.sleep(2 * 1000);
    } catch (Exception e) {

    }
  }
}