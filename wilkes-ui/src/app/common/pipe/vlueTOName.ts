import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'vlueTOName',
  pure: true,
})
export class VlueTOName implements PipeTransform {
  transform(value: any, obj: any, p = 'value', name = 'name'): any {
    let text = '';
    const reg = new RegExp(value, 'i');
    if (obj instanceof Array) {
      text = obj.find((item) => {
        return reg.test(item[p]) && item[p].length === value.length;
      })[name];
    } else if (obj instanceof Object) {
      text = obj[name];
    }
    return text;
  }
}
