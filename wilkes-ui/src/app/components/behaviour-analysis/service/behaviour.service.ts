import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';;
import { Observable } from 'rxjs';
import { TuiAppService } from '@tui/frame'
import { environment } from 'src/environments/environment';
import { Util } from 'src/app/utils/util';

export interface ConditionMap {
  // 行为主体
  subjectValue: Array<string>;
  // 行为客体
  objectValue: Array<string>;
  // 分组
  groupValue: Array<string>;

  [key: string]: any;
}

@Injectable({
  providedIn: 'root'
})
export class BehaviourService {
  baseUrl;
  curUser: any;

  constructor(private tuiAppService: TuiAppService, private http: HttpClient) {
    this.baseUrl = environment.root;
    // this.tuiChartService.behaviourUrl;
  }

  // 获取画像列表
  getNodes(subjectId: string): Observable<any> {
    const url = `${this.baseUrl}/${subjectId}`;
    return this.http.get(url);
  }

  // 获取行为主体的列表
  getSubjectList(no: string): Observable<any> {
    const url = `${this.baseUrl}/behaviours/${no}/portrait/subject`;
    return this.http.get(url);
  }

  // 获取行为客体的列表
  getCustomerList(no: string): Observable<any> {
    const url = `${this.baseUrl}/behaviours/${no}/portrait/object`;
    return this.http.get(url);
  }

  // 根据行为主体获取行为客体
  getCustomerListBySubjectId(d: { no: string, subjectValue: any }): Observable<any> {
    const url = `${this.baseUrl}/behaviours/${d.no}/portrait/object`;
    return this.http.post(url, d);
  }

  // 根据行为客体获取行为主体no编号,objectValue行为客体值
  getSubjectListByCustomerId(d: { no: string, objectValue: any }): Observable<any> {
    const url = `${this.baseUrl}/behaviours/${d.no}/portrait/subject`;
    return this.http.post(url, d);
  }

  // 请求行为画像信息
  getPortraitInfo(no: string): Observable<any> {
    const url = `${this.baseUrl}/behaviours/${no}/portrait`;
    return this.http.get(url);
  }

  // 请求行为画像信息
  getbehaviourorData(no: string, d: { subjectValue, objectValue, from?, to?, pageNo?, pageSize? }, time?): Observable<any> {
    let s = 1;
    const e = 0;
    if (new RegExp('hour', 'i').test(time)) {
      s = 1;
    } else {
      s = time.split(' ')[0] * 1;
    }
    // if (time === 'week') {
    //   s = new Date().getDay();
    //   s = s < 2 ? 7 : s;
    // } else if (time === 'month') {
    //   s = new Date().getDay();
    //   s = s < 2 ? new Date(new Date().getFullYear(), (new Date().getMonth() + 1), -1).getDate() : s;
    // } else { s = 2; }
    d.from = Util.dateFormat(new Date(new Date().getTime() - 1000 * 60 * 60 * 24 * s));
    d.to = Util.dateFormat(new Date());
    const url = `${this.baseUrl}/behaviours/${no}/portrait/data`;
    return this.http.post(url,  d);
  }

  // 请求行为画像信息
  getBehaviourorData(no: string, d: { conditionMap: ConditionMap,  from?, to?, pageNo?, pageSize? }, time?): Observable<any> {
    let s = 1;
    const e = 0;
    if (new RegExp('hour', 'i').test(time)) {
      s = 1;
    } else {
      s = time.split(' ')[0] * 1;
    }
    // if (time === 'week') {
    //   s = new Date().getDay();
    //   s = s < 2 ? 7 : s;
    // } else if (time === 'month') {
    //   s = new Date().getDay();
    //   s = s < 2 ? new Date(new Date().getFullYear(), (new Date().getMonth() + 1), -1).getDate() : s;
    // } else { s = 2; }
    d.from = Util.dateFormat(new Date(new Date().getTime() - 1000 * 60 * 60 * 24 * s));
    d.to = Util.dateFormat(new Date());
    const url = `${this.baseUrl}/behaviours/${no}/portrait/data`;
    // 这里暂时删除第三个参数
    return this.http.post(url, d);
  }

  // 获取分组的列表
  getGroupList(no: string): Observable<any> {
    const url = `${this.baseUrl}/behaviours/${no}/portrait/group`;
    return this.http.get(url);
  }

}
