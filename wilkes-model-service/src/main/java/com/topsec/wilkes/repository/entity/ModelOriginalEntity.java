package com.topsec.wilkes.repository.entity;

import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.common.dto.Action;
import com.topsec.wilkes.common.dto.Model;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@Entity
@Table(name = "model_original", uniqueConstraints = @UniqueConstraint(columnNames = {"no", "name"}))
public class ModelOriginalEntity {

  @Id
  private String no;

  @Column
  private String name;

  @Column(columnDefinition = "text")
  private String content;

  @Column(columnDefinition = "text")
  private String action;

  public ModelOriginalEntity() {
  }

  public Model convertToModel() {
    Model model = convertToBaseModel();
    model.setContent(this.content);
    try {
      model.setActions(Arrays.asList(ToolUtils.OBJECT_MAPPER.readValue(this.action, Action[].class)));
    } catch (Exception e) {
      log.error("", e);
    }
    return model;
  }

  public Model convertToBaseModel() {
    Model model = new Model();
    model.setNo(this.no);
    model.setName(this.name);
    return model;
  }
}

