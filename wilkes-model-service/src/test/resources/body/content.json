{"operators": [{"id": "source", "name": "选择事件数据", "params": {"url": [{"id": "dns_log"}]}, "instanceId": "fbd1555d-1b74-4e8e-b9b6-7181806083a4", "typeId": "1——source", "operatorName": "选择<span class=\"aaa file-name\"><span class=\"aaa file-name\">DNS日志</span></span>数据", "PositionX": 88, "PositionY": 12, "pointArr": [[0.5, 1, 0, 1, 0, 0, "outport1"]]}, {"id": "field-filter", "name": "过滤条件筛选", "params": {"type": "include", "predication": "", "expression": {"node": {"operator": "and", "children": [{"leaf_node": {"left": "DVC_ID", "operator": "=", "right": "10011"}}]}}}, "instanceId": "29ed5b44-fa73-4e6f-bdd8-bf1e7840e862", "typeId": "3——field-filter", "operatorName": "<span class=\"aaa file-name\"><span class=\"aaa file-name\">保留</span></span>符合<span class=\"aaa file-name\">过滤条件</span>的事件", "PositionX": 10, "PositionY": 215, "pointArr": [[0.5, 0, 0, -1, 0, 0, "inport1"], [0.5, 1, 0, 1, 0, 0, "outport1"]]}, {"id": "source", "name": "选择事件数据", "params": {"url": [{"id": "http_log"}]}, "instanceId": "ab21d886-4814-4007-a104-15386ae3c6ab", "typeId": "4——source", "operatorName": "选择<span class=\"aaa file-name\"><span class=\"aaa file-name\">HTTP日志</span></span>数据", "PositionX": 397, "PositionY": 12, "pointArr": [[0.5, 1, 0, 1, 0, 0, "outport1"]]}, {"id": "follow-by-cep", "name": "事件A发生后，发生事件B", "params": {"interval_time": "1 minute", "field": "EVT_T", "follow_by_predication": {"number_a": 1, "type_a": "least", "number_b": 1, "type_b": "least"}}, "instanceId": "fe3d2af6-2e00-435b-b071-635ce4ac2f09", "typeId": "5——follow-by-cep", "operatorName": "在<span class=\"aaa file-name\"><span class=\"aaa file-name\">1分钟</span></span>内，对于指定的<span class=\"aaa file-name\"><span class=\"aaa file-name\">日志类型(log_type)</span></span>，事件A发生<span class=\"aaa file-name\"><span class=\"aaa file-name\">1</span></span>次后事件B发生<span class=\"aaa file-name\"><span class=\"aaa file-name\"><span class=\"aaa file-name\">1</span></span></span>次", "PositionX": 210, "PositionY": 363, "pointArr": [[0.3333333333333333, 0, 0, -1, 0, 0, "inport1"], [0.6666666666666666, 0, 0, -1, 0, 0, "inport2"], [0.5, 1, 0, 1, 0, 0, "outport1"]]}, {"id": "field-aggregation", "name": "维度统计筛选", "params": {"interval_time": "1 minute", "field": "DVC_T", "predication": "", "expression": {"node": {"operator": "and", "children": [{"leaf_node": {"left": "DVC_T", "operator": "less", "right": "5", "func": "COUNT"}}]}}}, "instanceId": "9c8eaf99-02e0-42a9-8800-86aeae0c325e", "typeId": "6——field-aggregation", "operatorName": "在<span class=\"aaa file-name\"><span class=\"aaa file-name\">1分钟</span></span>内，对于<span class=\"aaa file-name\"><span class=\"aaa file-name\">设备类型(device_type)</span></span>，符合<span class=\"aaa file-name\"><span class=\"aaa file-name\">统计条件</span></span>的事件", "PositionX": 179, "PositionY": 514, "pointArr": [[0.5, 0, 0, -1, 0, 0, "inport1"], [0.5, 1, 0, 1, 0, 0, "outport1"]]}], "connections": [{"ConId": "con_19", "from": "fbd1555d-1b74-4e8e-b9b6-7181806083a4.outport1", "to": "29ed5b44-fa73-4e6f-bdd8-bf1e7840e862.inport1"}, {"ConId": "con_20", "from": "29ed5b44-fa73-4e6f-bdd8-bf1e7840e862.outport1", "to": "fe3d2af6-2e00-435b-b071-635ce4ac2f09.inport1"}, {"ConId": "con_21", "from": "ab21d886-4814-4007-a104-15386ae3c6ab.outport1", "to": "fe3d2af6-2e00-435b-b071-635ce4ac2f09.inport2"}, {"ConId": "con_22", "from": "fe3d2af6-2e00-435b-b071-635ce4ac2f09.outport1", "to": "9c8eaf99-02e0-42a9-8800-86aeae0c325e.inport1"}]}