:host {
  .con {
    position: relative;
    z-index: 8;
  }

  .data-model {
    // margin: 0 10px;

    & > li {
      padding: 7px 0;

      .p-l-20 {
        padding-left: 20px;
      }

      .icon-con {
        margin-left: 10px;
        font-size: 16px;
        color: #83de12;
        vertical-align: middle;
      }

      .d-m-header {
        min-width: 100px;
        height: 30px;
        font-size: 14px;
        line-height: 30px;
        color: #505050;
        text-align: justify;

        i {
          display: inline-block;

          /* padding-left: 100%; */
          width: 100%;
        }
      }

      .d-m-c {
        display: block;
        text-align: left;

        .w-balse {
          width: 390px;
        }

        .p-5 {
          padding: 5px 0;
        }

        app-interval-time {
          display: inline-block;
        }

        .d-m-c-sHeader {
          padding: 0 10px;
        }
      }

      .line-c {
        height: 27px;
        line-height: 29px;
      }

      .d-m-c-button {
        width: 86px;
        height: 30px;
        margin-bottom: 5px;
        line-height: 30px;
        text-align: center;
        background-size: 100%;
      }
    }
  }

  .node-con {
    width: 525px !important;
    padding-left: 0 !important;
  }
}
