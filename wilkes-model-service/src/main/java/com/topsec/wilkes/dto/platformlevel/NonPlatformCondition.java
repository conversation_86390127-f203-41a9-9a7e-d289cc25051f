package com.topsec.wilkes.dto.platformlevel;

import com.google.common.base.Strings;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * 非平台部署
 */
@Component
public class NonPlatformCondition implements Condition {

  private static final String SERVICEORDER = System.getenv("SERVICE_ORDER");

  @Override
  public boolean matches(ConditionContext conditionContext, AnnotatedTypeMetadata annotatedTypeMetadata) {
    if (Strings.isNullOrEmpty(SERVICEORDER)){
      return true;
    }
    return false;
  }
}
