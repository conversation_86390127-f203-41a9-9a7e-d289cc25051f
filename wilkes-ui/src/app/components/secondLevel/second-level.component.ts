import { Component, OnInit, Input, Output, EventEmitter, forwardRef, OnDestroy } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { RuleService } from 'src/app/ofmodules/rules/services/rule.service';
import { Util } from 'src/app/utils/util';

@Component({
  selector: 'app-second-level',
  templateUrl: './second-level.component.html',
  styleUrls: ['./second-level.component.less'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SecondLeveLComponent),
      multi: true,
    },
  ],
})
export class SecondLeveLComponent implements OnInit, OnDestroy, ControlValueAccessor {
  @Input() fromeName = '事件类型';
  @Output() changeData = new EventEmitter<any>();
  @Input() type = 'two'; // one为点击显示二级 two为左右显示一级二级默认one
  hostDom = 'body';
  @Input() height = '220px';
  @Input() placeholder: any;
  // 私有的下拉内容
  _options = [];
  modelKeys = ['cat1', 'cat1_id', 'cat2', 'cat2_id', 'k_c', 'k_c_id'];
  oneLable!: string;
  modelValue: any;
  // inputValue
  inputValue = '';
  // 过滤参数
  // 过滤参数
  filterName!: string;
  // ngModel绑定值
  value: any;
  // 子集数据
  // 子集数据
  children!: any[];
  // 覆盖层
  isShow = false;
  // 一级二级区分true一级
  oneOrTwo = true;
  // 二级弹窗的位置
  styleXY: any;
  public onModelChange: Function = () => {};
  public onModelTouched: Function = () => {};

  // = [{label: '我的你的', value: '123', item: [{label: '你的', value: '123'}, {label: '的', value: '123'}]}];
  @Input() set options(v: any[]) {
    if (v && v.length > 0) {
      //@ts-ignore
      this._options = v;
      setTimeout(() => {
        this.modelValue && this.setParams();
      }, 0);
    }
  }
  constructor(private ruleService: RuleService) {}

  ngOnInit() {
    this.initParams();
    const fun = (d: any, i: any) => ({
      ...d,
      label: d['cat' + i],
      value: d['cat' + i + '_id'],
    });
    if (!this._options) {
      this._options = [];
      this.ruleService.getEventType().subscribe((res: any[]) => {
        //@ts-ignore
        this._options = res.map((event) => {
          const o = fun(event, 1);
          //@ts-ignore
          o.item = event.cat2s.map((cat2) => fun(cat2, 2));
          return o;
        });
      });
    }
  }

  initParams() {
    this.modelValue = {};
    this.children = [];
    this.inputValue = '';
    this.modelKeys.forEach((e) => (this.modelValue[e.toLocaleUpperCase()] = ''));
  }

  ngOnDestroy() {
    window.onresize = () => {};
  }

  writeValue(value: any): void {
    if (value && Object.values(value).every((e) => !!e)) {
      this.modelValue = value;
      setTimeout(() => {
        this._options && this._options.length > 0 && this.setParams();
      }, 0);
    } else {
      this.onModelChange(null);
    }
  }

  setParams() {
    const value = this.modelValue;
    if (!value || Object.values(value).filter((e) => e).length === 0) {
      this.initParams();
      return;
    }
    this._options.forEach((dd) => {
      //@ts-ignore
      if (dd.cat1_id === value.CAT1_ID) {
        //@ts-ignore
        this.modelValue.cat1 = dd.cat1;
        this.oneOrTwo = false;
        //@ts-ignore
        dd.isClick = true;
        //@ts-ignore
        this.children = dd.item;
        //@ts-ignore
        dd.cat2s.forEach((o) => {
          if (value.CAT2_ID === o.cat2_id) {
            this.modelValue.cat2 = o.cat2;
          }
        });
      }
    });
    this.inputValue = this.modelValue.cat1 + ' / ' + this.modelValue.cat2;
    this.changeData.emit(this.modelValue);
  }

  registerOnChange(fn: any): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onModelTouched = fn;
  }

  initPost(e: any) {
    const fun = () => {
      this.position = e;
    };
    fun();
    window.onresize = () => {
      fun();
    };
  }

  // 展开菜单
  toOpen(e: any) {
    this.isShow = !this.isShow;
    this.initPost(e);
  }

  // 一级菜单展示
  oneClick() {
    this.oneOrTwo = true;
  }

  // 二级菜单展示
  twoClick() {
    this.oneOrTwo = false;
  }

  intval(v: any) {
    v = parseInt(v);
    return isNaN(v) ? 0 : v;
  }

  getPos(e: any) {
    // left and top value
    let left = 0;
    let top = 0; // offsetParent返回一个指向最近的定位元素,标准模式如果没有就返回html/body,表示迁移量都是相对其中来计算.
    while (e.offsetParent) {
      // 计算偏移量
      left += e.offsetLeft + (e.currentStyle ? this.intval(e.currentStyle.borderLeftWidth) : 0);
      top += e.offsetTop + (e.currentStyle ? this.intval(e.currentStyle.borderTopWidth) : 0); // 最近的定位元素或者body
      e = e.offsetParent;
    }

    left += e.offsetLeft + (e.currentStyle ? this.intval(e.currentStyle.borderLeftWidth) : 0);
    top += e.offsetTop + (e.currentStyle ? this.intval(e.currentStyle.borderTopWidth) : 0);
    return {
      x: left,
      y: top,
    };
  }

  // 位置
  set position(e: any) {
    // tslint:disable-next-line: radix
    const xy = this.getPos(document.querySelector('.second-level-con'));
    //@ts-ignore
    const h = document.querySelector('body')['offsetHeight'];
    const domm = document.querySelector('.second-level-con');
    //@ts-ignore
    const top: number = document.querySelector('.edit-con .ui-scrollpanel-content').scrollTop;
    if (e['clientY'] * 1 + parseInt(this.height) + 40 > h) {
      this.styleXY = {
        top: xy.y - top - parseInt(this.height) - 40 + 'px',
        left: xy.x + 'px',
        //@ts-ignore
        width: domm['offsetWidth'] + 'px',
        height: parseInt(this.height) + 40 + 'px',
        position: 'fixed',
        zIndex: '9999',
      };
    } else {
      this.styleXY = {
        top: 30 + 'px',
        left: '0px',
        height: parseInt(this.height) + 40 + 'px',
      };
    }
  }

  // 下拉点击事件
  listClick(e: any, i: any, arr: any[]) {
    arr.forEach((item) => delete item.isClick);
    e.isClick = true;
    if (e.item) {
      //@ts-ignore
      e.item.forEach((item) => delete item.isClick);
      this.children = e.item;
      this.inputValue = e.label + ' / ';
      this.oneLable = e.label;
      this.oneOrTwo = false;
      this.modelValue['cat1'] = e['cat1'];
      Util.setKeyFromOther(this.modelValue, e);
    } else if (!this.oneOrTwo && !e.item) {
      this.inputValue = this.oneLable + '/' + e.label;
      this.isShow = false;
      Util.setKeyFromOther(this.modelValue, e);
      // this.modelValue['CAT2_S'] = this.inputValue;
      this.onModelChange(this.modelValue);
      this.changeData.emit(this.modelValue);
      window.onresize = () => {};
    }
  }

  coverClick() {
    this.isShow = false;
    window.onresize = () => {};
  }
}
