import { Component, OnInit, AfterViewInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { ModelsysService } from '../../service/modelsys.service';
import { Router, ActivatedRoute } from '@angular/router';
import { IntegrationPermissionService } from 'src/app/services/integration-permission.service';
import { WebSocketService } from 'src/app/services/web-socket.service';
import { RefreshListService } from 'src/app/services/refresh-list.service';
import { CreatRetrospectService } from '../../../retrospect/components/creat-retrospect/creat-retrospect.common';
import { Routingname } from 'src/app/routing/routingname.enum';
import { MessageService, ConfirmationService } from '@tui/component-library';
import { TuiAppService, TuiNavService, TuiUserService } from '@tui/frame';
import { SessionService } from 'src/app/services/session.service';
import { PaginatorComponent } from '@tui/component-library/src/app/ngtui/components/paginator/paginator/paginator.component';

@Component({
  selector: 'app-modelsys-hn',
  templateUrl: './modelsys-hn.component.html',
  styleUrls: ['./modelsys-hn.component.less'],
})
export class ModelsysHMComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('paginator') paginator: PaginatorComponent;
  @ViewChild('uploadDom') uploadDom;
  private listType = [
    { label: '模型分类', code: 'CATEGORY' },
    { label: '杀伤链阶段', code: 'KILL_CHAIN' },
    { label: '专题', code: 'SUBJECT' },
  ];
  // type为对话框的key避免同一组件用两次之后弹窗重复弹出
  public modelType = {
    ai: '深度分析',
    correlation: '关联分析',
    behaviour: '安全异常行为分析',
    type: '',
  };
  public modelUploadType = {
    number: '编号/名称冲突',
    defeated: '测试失败',
    name: '名称冲突',
  };

  // 模型列表搜索条件  sortType ASC/ DESC
  public searchConditions = {
    parameter: {
      source: '',
      name: '',
      status: '',
      categoryId: '',
      subjectId: '',
      killChainId: '',
      pageNo: 1,
      pageSize: 20,
      sort: 'status',
      sortType: 'DESC',
      modelType: '',
      dataTypeId: '',
    },
    total: 0,
    // , { label: '示例', value: '示例' }
    modelType: [
      { label: '内置', value: '内置' },
      { label: '自定义', value: '自定义' },
    ],
    modelStatus: [
      { label: '启用', value: 'ENABLED' },
      { label: '停用', value: 'DISABLED' },
    ],
    condition: [
      { label: '关联分析', value: 'CORRELATION' },
      { label: '深度分析', value: 'AI' },
      { label: '安全异常行为分析', value: 'BEHAVIOUR' },
    ],
  };

  // 检索条件个数
  searcConditionsNumber = {
    selectedLists: 0,
    selectedTopics: 0,
    selectedKillValue: 0,
    selectDatas: 0,
  };

  public tableHeader = [
    { header: '编号', field: 'no', sortType: '', width: 100, selected: false },
    {
      header: '名称',
      field: 'name',
      sortType: '',
      width: 280,
      selected: false,
    },
    {
      header: '来源',
      field: 'source',
      sortType: '',
      width: 100,
      selected: false,
    },
    {
      header: '状态',
      field: 'status',
      sortType: '',
      width: 100,
      selected: false,
    },
    {
      header: '模型类型',
      field: 'modelType',
      sortType: '',
      width: 100,
      selected: false,
    },
    {
      header: '创建人',
      field: 'creator',
      sortType: '',
      width: 100,
      selected: false,
    },
    {
      header: '触发时间',
      field: 'lastTriggerTime',
      sortType: '',
      width: 150,
      selected: false,
    },
    {
      header: '创建时间',
      field: 'createTime',
      sortType: '',
      width: 150,
      selected: false,
    },
    {
      header: '修改时间',
      field: 'lastUpdatedTime',
      sortType: '',
      width: 150,
      selected: false,
    },
  ];
  frozenColumns = [{ header: '操作', field: '' }];
  // 添加模型
  public addModelsBtn = [
    {
      label: '关联分析',
      command: () => {
        this.toEdit('correlation');
      },
    },
    {
      label: '安全异常行为分析',
      command: () => {
        this.toEdit('behaviour');
      },
    },
  ];

  // aas权限管理
  aasUriObj = {
    'MODEL-FILTER': '',
    other: '',
    'MODEL-RETROSPECT': '',
  };

  public RefreshListener: any;
  // 模型列表
  public modelLists = [];

  // 实时获取列表的高度
  public tableH: string;

  //
  public tableH1: string;

  // 模型分类列表
  public modelClassLists = [];

  // 专题列表
  public topicsLists = [];
  // 数据源列表
  public dataLists = [];
  // 数据源选中
  public selectDatas = [];
  // 杀伤链列表
  public killChainList = [];

  // 选中的分类
  public selectedLists = [];

  // 选中的杀伤链
  public selectedKillValue = [];

  // 选中的专题
  public selectedTopics = [];

  // 模型列表全选操作按钮
  public allSelected = false;

  //  上传文件加载
  public loading = false;
  // 模型导入
  public modelsImports = {
    show: false,
    isclick: false,
  };

  // 权限用户
  public rolename: any;

  // 全选状态
  public selectAll = {
    cover: false,
    skip: true,
    Rename: false,
    electionCover: false,
    electionSkip: false,
    electionRename: false,
  };

  // 保留状态
  public keepstatus = false;

  public paramsSubject = '';

  // 上传
  public uploadData = {
    disabled: true,
    file: null,
    text: '请选择文件',
    information: null,
    maxHeight: 300,
    tableHeightToScroll: '50px',
    progress: false,
    timer: 0,
  };

  public getWindowHeight = document.documentElement.clientWidth;

  // 树组件  右键菜单
  public menuArray = [
    {
      label: ' 创建新模型分类',
      id: 'creat',
      icon: 'icon-plus',
      command: ($event) => {
        this.messageService.clear();
        if (this.rolename !== '建模员') {
          // this.messageService.add({ key: this.modelType.type, severity: 'warn', summary: '提示', detail: '权限不够,无法操作' });
          return;
        }
        this.editModelClass.addOff = true;
        this.editModelClass.isEdit = false;
      },
    },
    {
      label: ' 编辑此模型分类',
      id: 'edit',
      icon: 'icon-editorial-team',
      command: ($event) => {
        this.messageService.clear();
        if (this.rolename !== '建模员') {
          // this.messageService.add({ key: this.modelType.type, severity: 'warn', summary: '提示', detail: '权限不够,无法操作' });
          return;
        }
        this.editModelClass.isEdit = true;
        this.editModelClass.addOff = true;
      },
    },
    {
      label: ' 删除此模型分类',
      id: 'delete',
      icon: 'icon-delete',
      command: ($event) => {
        this.messageService.clear();
        if (this.rolename !== '建模员') {
          // this.messageService.add({ key: this.modelType.type, severity: 'warn', summary: '提示', detail: '权限不够,无法操作' });
          return;
        }
        this.delTags();
      },
    },
  ];

  // 模型分类编辑部分
  public editModelClass = {
    toEditModel: null,
    toEditData: null,
    addOff: false,
    modelName: '',
    isEdit: false,
  };

  public bathBP = {
    // 批量删除的loading
    delLoading: false,
    stop: {
      toArr: [],
      status: false,
      isLoading: false,
    },
    play: {
      toArr: [],
      status: false,
      isLoading: false,
    },
  };
  // 是安全异常行为分析还是关联分析
  public behaviorDev = 'modelsys';

  public selectedListCheck = [];

  get viewSett() {
    return this.tuiAppService.getAppViewSettById('wilkes', 'wilkes');
  }

  get modelsWs() {
    const loctionUrl = window.document.location.href.split('#')[0].replace(/(http|https)/i, '');
    return loctionUrl + this.viewSett['models-ws'] || '';
  }
  constructor(
    private router: Router,
    private sessionService: SessionService,
    private ws: WebSocketService,
    // private gw: WinLinService,
    private tuiAppService: TuiAppService,
    private routerParams: ActivatedRoute,
    private tuiNavService: TuiNavService,
    private tuiUserService: TuiUserService,
    public runHttpService: ModelsysService,
    private messageService: MessageService,
    private role: IntegrationPermissionService,
    private confirmationService: ConfirmationService,
    private refreshListService: RefreshListService,
    public se: CreatRetrospectService,
  ) {
    this.modelType.type = this.routerParams.data['value']['label'];
    this.runHttpService.type = this.modelType.type;
    const map = {
      'models/correlation': 'CORRELATION',
      'models/behaviour': 'BEHAVIOUR',
    };
    // 判断是从关联分析还是安全异常行为分析连过来的
    let modelType: any = '';
    if (this.routerParams.data['value']) {
      if (map[this.modelType.type]) {
        this.tableHeader.splice(
          this.tableHeader.findIndex((e) => e.header === '模型类型'),
          1,
        );
        modelType = [{ name: 'modelType', value: map[this.modelType.type] }];
        this.searchConditions.parameter.modelType = map[this.modelType.type];
        this.behaviorDev = map[this.modelType.type];
      } else {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        modelType = undefined;
      }
    }
    this.routerParams.queryParams.subscribe((params) => {
      const parameter = this.sessionService.get(this.modelType.type + '_search');
      if (parameter) {
        this.searchConditions.parameter = parameter;
      }
      if (Object.keys(params).length > 0) {
        this.paramsSubject = params.subject;
        this.searchModels([{ name: 'subjectId', value: params.subject }]);
      } else {
        this.searchModels();
      }
    });
    this.rolename = this.role.roleId;
    [
      { type: 'CATEGORY', keyName: 'modelClassLists' },
      { type: 'KILL_CHAIN', keyName: 'killChainList' },
      { type: 'SUBJECT', keyName: 'topicsLists' },
      { type: 'DATA_TYPE', keyName: 'dataLists' },
    ].forEach((e) => {
      const p = {
        groupId: e.type,
        ...(this.behaviorDev == 'modelsys' ? {} : { modelType: this.behaviorDev }),
      };
      this.runHttpService.getListsForTree(p, '').subscribe(
        (data) => {
          this[e.keyName] = this.dealTreeFormat(data);
          if (e.type === 'CATEGORY' && this[e.keyName].length > 0) {
            this[e.keyName][0].expanded = true;
            this.getChildrenForTree({ node: this[e.keyName][0] });
          }
        },
        (error) => {
          this.messageService.add({
            key: this.modelType.type,
            severity: 'error',
            summary: '提示',
            detail: error.error.message,
            isClear: true,
          });
        },
      );
    });
  }

  ngOnInit() {
    this.aasUriObj['MODEL-FILTER'] = this.tuiNavService.getTuiNavByIdentity('subject').uri;
    this.aasUriObj['MODEL-RETROSPECT'] = this.tuiNavService.getTuiNavByIdentity('retrospect').uri;
    this.aasUriObj.other = '/' + Routingname.R + '-' + this.routerParams.data['value']['label'];
    this.criteria(null);
  }

  ngAfterViewInit() {
    window.onresize = () => {
      this.setTableH();
    };
    setTimeout(() => {
      this.setTableH();
    }, 0);
  }

  ngDoCheck() {
    this.role.jurisdiction();
  }
  ngOnDestroy() {
    window.onresize = () => {};
  }

  prepare(model) {
    this.se.createComponent(
      (data) => {
        if (data.status === 'loading') {
          this.runHttpService.loadingOff = true;
        } else {
          this.runHttpService.loadingOff = false;
          this.messageService.add({
            key: 'recall-toast',
            severity: data.status,
            summary: '提示',
            detail: data.message,
          });
        }
      },
      { type: 'singe', info: model },
    );
  }

  setTableH() {
    try {
      const h = document.querySelector('#header-con')['clientHeight'];
      document.querySelector('.mid-rb')['style']['top'] = h + 5 + 'px';
      setTimeout(() => {
        this.tableH = document.querySelector('.mid-rb')['clientHeight'] - 44 + 'px';
      }, 0);
    } catch (error) {}
  }

  // 分页改变
  onPageChange($event) {
    this.searchModels([
      { name: 'pageNo', value: $event.page + 1 },
      { name: 'pageSize', value: $event.rows },
    ]);
  }

  // 模型分类-树结构数据处理
  dealTreeFormat(nodeLists, type?) {
    for (let i = 0; i < nodeLists.length; i++) {
      nodeLists[i].leaf = !nodeLists[i].label;
      nodeLists[i].selectable = !nodeLists[i].label;
      nodeLists[i].children = [];
      nodeLists[i].label = nodeLists[i].name;
      nodeLists[i].expanded = false;
      if (type && this.paramsSubject === nodeLists[i].id) {
        this.selectedTopics = [nodeLists[i]];
      }
    }

    return nodeLists;
  }

  // 模型分类-获取子数据
  getChildrenForTree($event) {
    if ($event.node.children.length > 0) {
      return;
    }
    const groupId = $event.node.groupId.toLocaleUpperCase();
    const p = this.behaviorDev == 'modelsys' ? { groupId } : { groupId, modelType: this.behaviorDev };
    this.runHttpService.getListsForTree(p, $event.node.id).subscribe(
      (data) => {
        $event.node.children = this.dealTreeFormat(data);
        if ($event.node.children.length === 0) {
          $event.node.leaf = true;
        }
      },
      (error) => {
        this.messageService.add({
          key: this.modelType.type,
          severity: 'error',
          summary: '提示',
          detail: error.error.message,
        });
      },
    );
  }

  getOnNodeSelect($event, type) {
    const ty = {
      KILL_CHAIN: { v: 'selectedKillValue', id: 'killChainId' },
      SUBJECT: { v: 'selectedTopics', id: 'subjectId' },
      CATEGORY: { v: 'selectedLists', id: 'categoryId' },
      DATA_TYPE: { v: 'selectDatas', id: 'dataTypeId' },
    };
    const allIds = [];
    if ($event) {
      // 当统计数为0时不进行筛选
      const o = $event['node'];
      if (o.total == 0) {
        const num = this[ty[type].v].findIndex((e) => e.id === o.id);
        if (num !== -1) {
          this[ty[type].v].splice(num, 1);
        }
        return;
      }
    }
    this.criteria(null);
    this[ty[type].v].forEach((item) => {
      allIds.push(item.id);
    });
    this.searchModels([
      { name: ty[type].id, value: allIds.join(',') },
      { name: 'pageNo', value: 1 },
    ]);
  }

  getMenuSelect($event) {
    // if (Number($event.node.levelNum) > 2) {
    //     document.querySelector('.modelsys-tree-meun')['style'].display = 'none';
    // } else {
    //     document.querySelector('.modelsys-tree-meun')['style'].display = 'block';
    // }
    this.editModelClass.toEditModel = $event.node;
    // this.getOnNodeSelect(this.selectedLists[0], 'CATEGORY');
  }

  // 初始化定位分页
  firstNumber(pageNumber, pageSize) {
    if (pageNumber === 1) {
      return pageSize - 1;
    } else {
      return (pageNumber - 1) * pageSize;
    }
  }

  // 查询模型列表   [{name:'', value: ''}]
  searchModels(params?) {
    const p = {};
    this.allSelected = false;
    if (params) {
      params.forEach((item) => {
        this.searchConditions.parameter[item.name] = item.value;
      });
    }
    for (const k in this.searchConditions.parameter) {
      if (this.searchConditions.parameter.hasOwnProperty(k)) {
        p[k] = encodeURIComponent(this.searchConditions.parameter[k]);
      }
    }
    this.runHttpService.loadingOff = true;
    this.runHttpService.getModels(p).subscribe(
      (data) => {
        this.selectedListCheck = [];
        this.bathBP.play.status = false;
        this.bathBP.stop.status = false;
        this.runHttpService.loadingOff = false;
        this.modelLists = data.content;
        this.modelLists.forEach((item) => {
          // 复选框
          item.selected = false;
          // 是否启用转化为布尔值 disabled  停用   enabled  启用
          item.status === 'enabled' ? (item.status = true) : (item.status = false);
          item.isTag = item.tags && item.tags.find((tag) => tag.id === 'customized');
        });
        this.searchConditions.total = data.total;

        // 缓存搜索条件
        this.sessionService.set(this.modelType.type + '_search', this.searchConditions.parameter);
      },
      (error) => {
        this.runHttpService.loadingOff = false;
        this.criteria(null);
        this.messageService.add({
          key: this.modelType.type,
          severity: 'error',
          summary: '提示',
          detail: error.error.message,
        });
      },
    );
  }

  // 搜索条件查询模型列表
  changeConditions($event, type) {
    const is = /(^\s)/.test(this.searchConditions.parameter[type]);
    switch (type) {
      case 'name':
        if (is) {
          this.messageService.clear();
          setTimeout(() => {
            this.searchConditions.parameter.name = '';
          }, 0);
          this.messageService.add({
            key: this.modelType.type,
            severity: 'warn',
            summary: '规则名称',
            detail: '包含特殊字符',
          });
        }
        break;
      case 'source':
        if (this.searchConditions.parameter[type] === null) {
          this.searchConditions.parameter[type] = '';
        }
        break;
      case 'status':
        if (this.searchConditions.parameter[type] === null) {
          this.searchConditions.parameter[type] = '';
        }
        break;
      case 'modelType':
        if (this.searchConditions.parameter[type] === null) {
          this.searchConditions.parameter[type] = '';
        }
        break;
      case 'killChainId':
        this.searchConditions.parameter[type] = this.selectedKillValue.join(',');
        break;
      case 'subjectId':
        this.searchConditions.parameter[type] = this.selectedTopics.join(',');
        break;
    }
    !is && this.searchModels([{ name: 'pageNo', value: 1 }]);
  }
  // 查询 重置
  setOperation(type: any) {
    if (!type) {
      // 清空检索值
      this.searchConditions.parameter.name = '';
      this.searchConditions.parameter.source = '';
      this.searchConditions.parameter.status = '';
      //  去检测是否行为或者关联页面 如果是 则不清空;
      if (this.behaviorDev == 'modelsys') {
        this.searchConditions.parameter.modelType = '';
      }
    }
    this.paginator.pageIndex = 0;
    this.searchModels([{ name: 'pageNo', value: 1 }]);
  }
  setSelectedBoole(v) {
    this.selectedListCheck = [];
    if (v) {
      this.modelLists.forEach((item) => {
        item.selected = true;
        this.selectedListCheck.push(item);
      });
    } else {
      this.modelLists.forEach((item) => {
        item.selected = false;
      });
      this.selectedListCheck = [];
    }
    this.setbathBP(this.selectedListCheck);
  }

  // 单选查询模型列表
  setSelectedRadio($event, model) {
    if ($event) {
      this.selectedListCheck.push(model);
    } else {
      for (let key = 0; key < this.selectedListCheck.length; key++) {
        this.selectedListCheck[key].id === model.id && this.selectedListCheck.splice(key, 1);
      }
      this.allSelected = false;
    }
    this.setbathBP(this.selectedListCheck);
  }

  // 批量删除逻辑
  setbathBP(selectedListCheck) {
    this.bathBP = {
      delLoading: false,
      stop: {
        toArr: [],
        status: false,
        isLoading: false,
      },
      play: {
        toArr: [],
        status: false,
        isLoading: false,
      },
    };
    for (const key in selectedListCheck) {
      !selectedListCheck[key].abnormal && (selectedListCheck[key].status === false ? this.bathBP.play.toArr.push(selectedListCheck[key].no) : this.bathBP.stop.toArr.push(selectedListCheck[key].no));
    }
    this.bathBP.play.status = this.bathBP.play.toArr.length === selectedListCheck.length;
    this.bathBP.stop.status = this.bathBP.stop.toArr.length === selectedListCheck.length;
  }

  // 模型导入
  importModels() {
    this.keepstatus = false;
    this.modelsImports.isclick = true;
    this.ws.createObservableSocket(this.modelsWs).subscribe(
      (data) => {
        this.modelsImports.isclick = false;
        // 当后台提示关闭时关闭连接
        if (data instanceof CloseEvent) {
          this.ws.close();
          this.modelsImports.show = false;
          if (data.reason.length !== 0 && data.reason.indexOf('超时') !== -1) {
            this.confirmationService.confirm({
              key: this.modelType.type,
              message: `${data.reason}`,
              header: '提示',
              icon: '',
              cancelVisible: false,
            });
          }
          return;
        }
        if (data !== 'undefined') {
          const no = JSON.parse(data);
          if (no.label) {
            this.modelsImports.show = true;
          } else {
            this.messageService.add({
              key: this.modelType.type,
              severity: 'warn',
              summary: '提示',
              detail: no.message,
              isClear: true,
            });
          }
        }
      },
      (err) => {
        this.modelsImports.isclick = false;
        this.messageService.add({
          key: this.modelType.type,
          severity: 'error',
          summary: '提示',
          detail: '连接已经创建请稍后再试！',
          isClear: true,
        });
      },
    );
  }

  // 关闭模型导入
  fileUpClose() {
    this.modelsImports.isclick = false;
    this.ws.close();
  }

  // 创建副本
  creatCopy(no) {
    this.confirmationService.confirm({
      key: this.modelType.type,
      message: '确定创建此模型副本吗?',
      header: '提示',
      icon: 'icon-exclamation-triangle',
      accept: () => {
        this.runHttpService.copyModel(no).subscribe(
          (data) => {
            data.status === 'enabled' ? (data.status = true) : (data.status = false);
            this.modelLists.unshift(data);
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'success',
              summary: '提示',
              detail: '创建成功',
            });
          },
          (error) => {
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'error',
              summary: '提示',
              detail: error.error.message,
            });
          },
        );
      },
    });
  }

  // 自定义模型转成内置模型
  modelTotoBuilt(model) {
    this.confirmationService.confirm({
      key: this.modelType.type,
      message: '确定把此模型转成内置模型吗?',
      header: '提示',
      icon: 'icon-exclamation-triangle',
      accept: () => {
        this.runHttpService.toBuilt(model.no).subscribe(
          (data: any) => {
            for (const key in model) {
              for (const key1 in data) {
                key === key1 && (model[key] = data[key1]);
              }
            }
            model.status = data.status === 'enabled' ? true : false;
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'success',
              summary: '提示',
              detail: '转换成功',
            });
          },
          (error) => {
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'error',
              summary: '提示',
              detail: error.error.message,
            });
          },
        );
      },
    });
  }

  // 删除模型
  delModel(no) {
    this.confirmationService.confirm({
      key: this.modelType.type,
      message: '确定删除此模型吗?',
      header: '提示',
      icon: 'icon-exclamation-triangle',
      accept: () => {
        this.bathBP.delLoading = this.runHttpService.loadingOff = true;
        this.runHttpService.delModel(no).subscribe(
          (data) => {
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'success',
              summary: '提示',
              detail: '删除成功',
            });
            this.setOperation(1);
            // this.modelLists.forEach((item, index) => {
            //   if (item.no === no) {
            //     this.modelLists.splice(index, 1);
            //   }
            // });
          },
          (error) => {
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'error',
              summary: '提示',
              detail: error.error.message,
            });
            this.bathBP.delLoading = this.runHttpService.loadingOff = false;
          },
          () => {
            this.bathBP.delLoading = this.runHttpService.loadingOff = false;
          },
        );
      },
    });
  }

  // 批量启用停用
  editEDModelBath(p?) {
    let is = true,
      pArr = [this.behaviorDev === 'modelsys' ? 'all' : this.behaviorDev];
    if (p) {
      is = { open: true, close: false }[p];
    } else if (this.bathBP.play.status) {
      (is = true), (pArr = this.bathBP.play.toArr);
    } else if (this.bathBP.stop.status) {
      (is = false), (pArr = this.bathBP.stop.toArr);
    }
    this.confirmationService.confirm({
      key: this.modelType.type,
      message: `确定${p ? '全部' : '批量'}${is ? '启用' : '停用'}模型吗?`,
      header: '提示',
      icon: 'icon-exclamation-triangle',
      accept: () => {
        this.editEDModel(is, pArr);
      },
    });
  }

  // 批量删除
  delDModelBath(p?) {
    if (p) {
      p = this.behaviorDev === 'modelsys' ? 'all' : this.behaviorDev;
    } else {
      p = this.selectedListCheck.map((e) => e.no).toString();
    }
    this.delModel(p);
  }

  // 模型启用停用  this.setbathBP(this.selectedListCheck);
  editEDModel($event, no, model?) {
    const type = $event ? 'ENABLED' : 'DISABLED';
    const fun = (is) => {
      this.bathBP[$event ? 'play' : 'stop'].isLoading = is;
      this.runHttpService.loadingOff = is;
      if (model) {
        // 单独点击启动时
        model.isLoading = is;
      } else {
        // 当复选框有选中值的时候
        this.selectedListCheck.forEach((e) => {
          e.isLoading = is;
          e.status = $event;
        });
      }
    };
    // 请后成功或者失败后重新赋值
    const fun1 = (arr1, arr2) => {
      arr1.forEach((e) => {
        arr2.forEach((d) => {
          if (e.no === (d.no || d)) {
            e.status = d.status ? (d.status === 'enabled' ? true : false) : !e.status;
          }
        });
      });
    };
    // 请求时为请求中
    fun(true);
    this.runHttpService.EDModel(no.join(','), type).subscribe(
      (data) => {
        let text = $event ? '启用成功' : '停用成功';
        text += `${data.successNum}个`;
        data.errorNum > 0 && (text += `，失败${data.errorNum}个`);
        this.messageService.add({
          key: this.modelType.type,
          severity: 'success',
          summary: '提示',
          detail: text,
          isClear: true,
        });
        fun1(this.modelLists, data.models);
        if (this.selectedListCheck.length > 0) {
          fun1(this.selectedListCheck, data.models);
          this.setbathBP(this.selectedListCheck);
        }
      },
      (error) => {
        fun(false);
        fun1(this.modelLists, no);
        this.messageService.add({
          key: this.modelType.type,
          severity: 'error',
          summary: '提示',
          detail: error.error.message,
          isClear: true,
        });
      },
      () => {
        fun(false);
      },
    );
  }

  initDialog() {
    this.uploadDom.clear();
    this.uploadData = {
      disabled: true,
      file: null,
      text: '请选择文件',
      information: null,
      maxHeight: 300,
      tableHeightToScroll: '50px',
      progress: false,
      timer: 0,
    };
    this.uploadData.maxHeight = document.documentElement.clientHeight - 200 > 600 ? 600 : document.documentElement.clientHeight - 200;
    this.uploadData.tableHeightToScroll = this.uploadData.maxHeight - 230 + 'px';
  }

  // 选择文件
  selectedFile($event) {
    if (this.uploadDom.msgs.length) {
      this.messageService.clear();
      this.messageService.add({
        key: this.modelType.type,
        severity: 'error',
        summary: '提示',
        detail: this.uploadDom.msgs[0].detail,
      });
      return;
    }
    if ($event.files.length) {
      this.uploadData.file = $event.files[0];
      this.uploadData.text = this.uploadData.file.name;
      this.uploadData.disabled = false;
    }
  }

  // 清除已经选择的文件
  clearFile() {
    this.uploadDom.clear();
    this.uploadData.file = null;
    this.uploadData.text = '请选择文件';
    this.uploadData.disabled = true;
  }

  // 上传文件
  uploadFile() {
    this.uploadDom.upload();
  }

  // 开始上传文件
  beginUpload() {
    this.uploadData.progress = true;
    this.uploadData.timer = new Date().getTime();
  }

  // 文件上传成功  treatmentType
  successFile($event) {
    const lastTimer = new Date().getTime() - this.uploadData.timer;
    if (lastTimer < 2000) {
      setTimeout(() => {
        this.uploadData.progress = false;
      }, 2000 - lastTimer);
    } else {
      this.uploadData.progress = false;
    }
    this.uploadData.disabled = true;
    this.uploadData.information = $event.originalEvent.body;
    this.tableH1 = document.querySelector('.disk-import')['clientHeight'] - 44 + 'px';
    if (this.uploadData.information.content.length > 0) {
      for (let i = 0; i < this.uploadData.information.content.length; i++) {
        this.uploadData.information.content[i].overwrite = false;
        this.uploadData.information.content[i].overskip = true;
        this.uploadData.information.content[i].againno = false;
      }
      // if (this.uploadData.information.content.every(item => item.causeType === 'defeated')) {
      //   this.selectAll.skip = true;
      // } else {
      //   this.selectAll.skip = false;
      // }
    }
  }

  uploadError($event) {
    const lastTimer = new Date().getTime() - this.uploadData.timer;
    if (lastTimer < 2000) {
      setTimeout(() => {
        this.uploadData.progress = false;
        this.messageService.clear();
        this.messageService.add({
          key: this.modelType.type,
          severity: 'error',
          summary: '提示',
          detail: $event.error.error.message,
        });
        this.uploadDom.clear();
        this.uploadData = {
          disabled: true,
          file: null,
          text: '请选择文件',
          information: null,
          maxHeight: 300,
          tableHeightToScroll: '50px',
          progress: false,
          timer: 0,
        };
      }, 2000 - lastTimer);
    } else {
      this.uploadData.progress = false;
      this.messageService.clear();
      this.messageService.add({
        key: this.modelType.type,
        severity: 'error',
        summary: '提示',
        detail: $event.error.error.message,
      });
      this.uploadDom.clear();
      this.uploadData = {
        disabled: true,
        file: null,
        text: '请选择文件',
        information: null,
        maxHeight: 300,
        tableHeightToScroll: '50px',
        progress: false,
        timer: 0,
      };
    }
    this.uploadData.disabled = true;
  }

  // 操作
  changeAction(checked, node, type, list) {
    const arr = ['overwrite', 'overskip', 'againno'];
    if (checked) {
      for (let i = 0; i < arr.length; i++) {
        node[arr[i]] = false;
      }
      node[type] = true;
    }
    this.selectAll.Rename = list.every((item) => item.againno === true);
    this.selectAll.skip = list.every((item) => item.overskip === true);
    this.selectAll.cover = list.every((item) => item.overwrite === true);
  }

  // 确认导入
  getFileUploadToAction() {
    this.loading = true;
    const reData = [];
    const arr = ['overwrite', 'overskip', 'againno'];
    let continueGo = true;
    let no = null;
    for (let i = 0; i < this.uploadData.information.content.length; i++) {
      let action = '';
      for (let j = 0; j < arr.length; j++) {
        this.uploadData.information.content[i][arr[j]] && (action = arr[j]);
      }
      reData.push({
        no: this.uploadData.information.content[i].no,
        treatmentType: action,
      });
      if (action === '') {
        this.messageService.clear();
        this.messageService.add({
          key: this.modelType.type,
          severity: 'error',
          summary: '提示',
          detail: '请对列表中所有模型操作完后导入',
        });
        continueGo = false;
        break;
      }
    }
    no = { keepStatus: this.keepstatus, executes: reData };
    if (!continueGo) {
      return;
    }
    this.runHttpService.setActionModel(this.uploadData.information.importId, no).subscribe(
      (data) => {
        this.modelsImports.show = false;
        this.modelsImports.isclick = false;
        this.loading = false;
        this.messageService.clear();
        this.searchModels();
        this.messageService.add({
          key: this.modelType.type,
          severity: 'success',
          summary: '提示',
          detail: `内置成功${data.builtIn}个模型，自定义成功${data.unBuiltIn}个模型，示例成功${data.sample}个模型`,
        });
      },
      (error) => {
        this.messageService.clear();
        this.messageService.add({
          key: this.modelType.type,
          severity: 'error',
          summary: '提示',
          detail: error.error.message,
        });
      },
      () => {
        this.selectAll = {
          cover: false,
          skip: true,
          Rename: false,
          electionCover: false,
          electionSkip: false,
          electionRename: false,
        };
      },
    );
  }

  // 取消导入
  cacleUpload() {
    this.loading = false;
    this.runHttpService.cacleUpload(this.uploadData.information.importId).subscribe(
      (data) => {
        this.modelsImports.show = false;
        this.modelsImports.isclick = false;
        this.messageService.clear();
        this.messageService.add({
          key: this.modelType.type,
          severity: 'success',
          summary: '提示',
          detail: `取消导入成功`,
        });
      },
      (error) => {
        this.messageService.clear();
        this.messageService.add({
          key: this.modelType.type,
          severity: 'error',
          summary: '提示',
          detail: error.error.message,
        });
      },
      () => {
        this.selectAll = {
          cover: false,
          skip: true,
          Rename: false,
          electionCover: false,
          electionSkip: false,
          electionRename: false,
        };
      },
    );
  }

  // m模型导出
  exportsModel() {
    const ps = {
      source: this.searchConditions.parameter.source,
      modelType: this.searchConditions.parameter.modelType,
      status: this.searchConditions.parameter.status,
      name: this.searchConditions.parameter.name,
      categoryId: this.searchConditions.parameter.categoryId.split(','),
      killChainId: this.searchConditions.parameter.killChainId.split(','),
      nos: (this.selectedListCheck || []).map((e) => e.no).toString(),
      dataTypeId: this.searchConditions.parameter.dataTypeId.split(','),
      subjectId: this.searchConditions.parameter.subjectId.split(','),
    };
    let pathP = '';
    // tslint:disable-next-line: forin
    for (const key in ps) {
      if (ps[key]) {
        pathP += `${key}=${ps[key]}`;
        key !== 'subjectId' && (pathP += '&');
      }
    }
    let url = this.runHttpService.uploadUrl.exportModel;
    window.open((url += `?${pathP}` + `&principal=${this.tuiUserService.curUser.principal}`));
  }

  // 新增模型分类
  addToModelClass(type) {
    if (type === 'go') {
      if (!this.editModelClass.isEdit) {
        // 新增
        const parameter = {
          parentId: this.editModelClass.toEditModel.id,
          name: this.editModelClass.modelName,
          groupId: 'CATEGORY',
          levelNum: Number(this.editModelClass.toEditModel.levelNum) + 1 + '',
        };
        this.runHttpService.creatModelClass(parameter).subscribe(
          (data) => {
            data.leaf = !data.label;
            data.children = [];
            data.label = data.name;
            data.expanded = false;
            this.editModelClass.toEditModel.children.unshift(data);
            this.editModelClass.toEditModel.expanded = true;
            this.editModelClass.toEditModel.leaf = !this.editModelClass.toEditModel.leaf ? this.editModelClass.toEditModel.leaf : !this.editModelClass.toEditModel.leaf;
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'success',
              summary: '提示',
              detail: '新增成功！',
            });
          },
          (error) => {
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'error',
              summary: '提示',
              detail: error.error.message,
            });
          },
        );
      } else {
        // 编辑
        if (this.editModelClass.modelName === this.editModelClass.toEditModel.name) {
          this.messageService.clear();
          this.messageService.add({
            key: this.modelType.type,
            severity: 'info',
            summary: '提示',
            detail: '请修改名称后操作！',
          });
          return;
        }
        this.runHttpService.edittags(this.editModelClass.toEditModel.id, { groupId: 'CATEGORY' }, { name: this.editModelClass.modelName }).subscribe(
          (data) => {
            this.editModelClass.toEditModel.name = data.name;
            this.editModelClass.toEditModel.label = data.name;
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'success',
              summary: '提示',
              detail: '修改成功！',
            });
          },
          (error) => {
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'error',
              summary: '提示',
              detail: error.error.message,
            });
          },
        );
      }
    }
    this.editModelClass.addOff = false;
  }

  // 删除模型分类
  delTags() {
    this.confirmationService.confirm({
      key: this.modelType.type,
      message: '确定删除此模型分类吗?',
      header: '提示',
      icon: 'icon-exclamation-triangle',
      accept: () => {
        this.runHttpService.deletags(this.editModelClass.toEditModel.id, { groupId: 'CATEGORY' }).subscribe(
          (data) => {
            this.messageService.clear();
            if (data.status === 'success') {
              if (this.editModelClass.toEditModel.parent) {
                for (let i = 0; i < this.editModelClass.toEditModel.parent.children.length; i++) {
                  this.editModelClass.toEditModel.id === this.editModelClass.toEditModel.parent.children[i].id && this.editModelClass.toEditModel.parent.children.splice(i, 1);
                }
              }
              for (let i = 0; i < this.selectedLists.length; i++) {
                if (this.selectedLists[i].id === this.editModelClass.toEditModel.id) {
                  this.selectedLists.splice(i, 1);
                }
              }
              this.getOnNodeSelect(null, 'CATEGORY');
              this.messageService.add({
                key: this.modelType.type,
                severity: 'success',
                summary: '提示',
                detail: '删除成功！',
              });
            } else {
              this.messageService.add({
                key: this.modelType.type,
                severity: 'error',
                summary: '提示',
                detail: data.message,
              });
            }
          },
          (error) => {
            this.messageService.clear();
            this.messageService.add({
              key: this.modelType.type,
              severity: 'error',
              summary: '提示',
              detail: error.error.message,
            });
          },
        );
      },
    });
  }
  // 全选操作
  changeSelectAll(event: any, type) {
    const list = this.uploadData.information.content;
    const map = {
      cover: 'overwrite',
      skip: 'overskip',
      Rename: 'againno',
    };
    list.forEach((e) => Object.values(map).forEach((v) => (e[v] = v === map[type] ? event : !event)));
    Object.keys(map).forEach((k) => k !== type && (this.selectAll[k] = !event));
  }
  // 排序
  sortTable(item) {
    if (item.field == 'lastTriggerTime') {
      return;
    }
    const ar = ['', 'ASC', 'DESC'];
    item.sortType = ar[ar.indexOf(item.sortType) + 1 > ar.length - 1 ? 0 : ar.indexOf(item.sortType) + 1];
    for (const key in this.tableHeader) {
      if (this.tableHeader[key].field !== item.field) {
        this.tableHeader[key].sortType = '';
      }
    }
    this.searchModels([
      { name: 'sort', value: item.sortType === '' ? '' : item.field },
      { name: 'sortType', value: item.sortType },
    ]);
  }
  // 选中的分类个数
  criteria(ty: any) {
    this.searcConditionsNumber.selectedLists = this.selectedLists.length;
    this.searcConditionsNumber.selectedKillValue = this.selectedKillValue.length;
    this.searcConditionsNumber.selectedTopics = this.selectedTopics.length;
    this.searcConditionsNumber.selectDatas = this.selectDatas.length;
  }
  // 清除检索条件
  conditions(type: any, ty: any) {
    this[type] = [];
    this.getOnNodeSelect(null, ty);
  }
  // 关联分析/安全异常行为分析 调转routerName (relation|ai|behaviors)
  toEdit(routerName, no = 'new') {
    routerName = routerName.toLocaleLowerCase();
    const url = `${Routingname.R}/models/${routerName}`;
    if (routerName === 'behaviour') {
      sessionStorage.setItem('key', 'behaviors');
    }

    this.router.navigateByUrl(`${url}/edit/${no}`);
  }

  toBuiltInEdit(routerName: string, id: string, setting = false) {
    routerName = routerName.toLocaleLowerCase();
    const url = `${Routingname.R}/models/${routerName}`;
    if (routerName === 'behaviour') {
      sessionStorage.setItem('key', 'behaviors');
    }

    this.router.navigateByUrl(`${url}/edit/${setting ? 'setting' : 'built-in'}/${id}`);
  }
}
