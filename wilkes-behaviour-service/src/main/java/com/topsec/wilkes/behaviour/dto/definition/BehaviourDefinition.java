package com.topsec.wilkes.behaviour.dto.definition;

import com.topsec.minsky.domain.Expression;
import com.topsec.wilkes.operator.type.ClueType;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BehaviourDefinition {
  private List<String> dataIds;
  private FieldFilter fieldFilter;
  private KnowledgeFilter knowledgeFilter;
  private String behaviourSubject;
  private String behaviourObject;
  private Measure measure;
  private String periodic;

  // 过滤条件筛选
  @Data
  public static class FieldFilter {
    private ClueType type;
    private String predication;
    private Expression expression;
  }

  // 统计度量
  @Data
  public static class Measure {
    private String function;
    private String filed;
  }

  // 知识情报筛选
  @Data
  public static class KnowledgeFilter {
    @JsonProperty("knowledge_base_url")
    private String knowledgeBaseUrl;
    private ClueType type;
    @JsonProperty("filter_condition")
    private Expression filterCondition;
    private Expression predication;
  }
}
