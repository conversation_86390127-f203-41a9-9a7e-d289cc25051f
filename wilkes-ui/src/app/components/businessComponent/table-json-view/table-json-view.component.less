:host {
  .table-json-view {
    height: calc(100% - 30px);
  }

  .page-con {
    height: 30px;

    ::ng-deep .disabled-prev {
      .ui-paginator-page,
      .ui-paginator-prev {
        color: #a0a0a0;
        color: var(--disabled-text-color, #a0a0a0) !important;
        pointer-events: none;
        cursor: not-allowed !important;
        background-color: #f2f2f2 !important;
        border-color: #dfdfdf !important;
        opacity: 0.9;
      }

      .ui-state-active {
        color: #c62b2f !important;
        color: var(--primary-active-color, #c62b2f) !important;
        cursor: pointer !important;
        background-color: #fff !important;
        border-color: #c62b2f !important;
        border-color: var(--primary-active-color, #c62b2f) !important;
      }
    }
  }

  .json-view {
    .json-include-box {
      margin-left: 20px;

      .item-json-key {
        margin-right: 6px;
        color: #98a4af;
      }

      .item-json-value {
        word-break: break-all;
      }

      .fold {
        display: inline-block;
        width: 13px;
        height: 13px;
        margin-right: 4px;
        line-height: 9px;
        text-align: center;
        vertical-align: text-bottom;
        cursor: pointer;
        border: 1px solid #98a4af;
      }

      .arr-value {
        margin-top: 4px;
        margin-left: 20px;
      }
    }
  }

  .table-view {
    .table-view-item {
      display: flex;
      flex-flow: row wrap;
      justify-content: flex-start;

      & > li {
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        justify-content: flex-start;
        width: 25%;
        border: 1px solid #e3e6e8;

        &:nth-child(n + 5) {
          border-top: none;
        }

        &:not(:nth-child(4n)) {
          border-right: none;
        }

        &:last-child {
          border-right: 1px solid #e3e6e8;
        }

        .tableItem-name {
          display: flex;
          align-items: center;
          width: 154px;
          height: 100%;
          padding: 10px;
          overflow: hidden;
          line-height: 24px;
          text-align: right;
          text-overflow: ellipsis;
          white-space: nowrap;
          background: #f2f4f5;
          border-right: 1px solid #e3e6e8;
        }

        .tableItem-value {
          max-width: calc(100% - 154px);
          padding: 0 10px;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
