# 1. 页面路由
1. 专题管理 /wilkes/subject
2. 模型管理 /wilkes/models
3. 关联分析 /wilkes/models/correlation 
4. 行为分析 /wilkes/models/behaviour
5. 深度分析 /wilkes/models/ai
# 2. route gateway 配置
```shell script
spring.cloud.gateway.routes[1].id=wilkes
spring.cloud.gateway.routes[1].uri=lb://wilkes
spring.cloud.gateway.routes[1].predicates[0]= Path=/TopCIS/wilkes/**
spring.cloud.gateway.routes[1].filters[0]= StripPrefix=1

spring.cloud.gateway.routes[16].id=wilkes-aiUI
spring.cloud.gateway.routes[16].uri=lb://wilkes
spring.cloud.gateway.routes[16].predicates[0]= Path=/TopCIS/wilkes-ai-model/**
spring.cloud.gateway.routes[16].filters[0]= StripPrefix=1


spring.cloud.gateway.routes[3].id=wilkes-ws
spring.cloud.gateway.routes[3].uri=lb:ws://wilkes
#ws://************:22010/wilkes/models/import/lock
#spring.cloud.gateway.routes[9].uri=ws://************:22010/wilkes/
spring.cloud.gateway.routes[3].predicates[0]= Path=/TopCIS/wilkes/models/import/**
spring.cloud.gateway.routes[3].filters[0]= StripPrefix=1

tsm.aas.auth.filter.uris=/wilkes-ai-model
```

# 3. 权限配置
1. 集成页面主页 见 #1
2. 模型关联分析查看编辑
/wilkes/behaviors/edit
/wilkes/rules/edit
/wilkes/ai-models/edit