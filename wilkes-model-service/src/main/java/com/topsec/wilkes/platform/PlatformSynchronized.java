package com.topsec.wilkes.platform;

import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.Status;
import com.topsec.wilkes.dto.ModelDistribute;
import com.topsec.wilkes.dto.ModelDistributeOperation;
import com.topsec.wilkes.dto.platformlevel.UpPlatformCondition;
import com.topsec.wilkes.platform.feign.ProbeSubordinateService;
import com.topsec.wilkes.platform.service.ModelDistributeService;
import com.topsec.wilkes.platform.service.PlatformService;
import com.topsec.wilkes.repository.ModelRepository;
import com.topsec.wilkes.repository.entity.ModelEntity;
import com.topsec.wilkes.platform.service.ModelDistributeOperationService;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Conditional(UpPlatformCondition.class)
public class PlatformSynchronized {

  @Autowired
  ModelDistributeService modelDistributeService;

  @Autowired
  private ModelDistributeOperationService modelDistributeOperationService;

  @Autowired
  private ModelRepository modelRepository;

  @Resource
  private PlatformService platformService;

  @Autowired
  private ProbeSubordinateService probeSubordinateService;

  @Scheduled(fixedDelay = 1 * 60 * 1000L)
  @Transactional
  public synchronized void schedule() {
    List<ModelDistributeOperation> modelDistributeOperationList = modelDistributeOperationService.getAll();
    List<String> modelDistributeOperationFailIdList = Lists.newArrayList();
    for (ModelDistributeOperation modelDistributeOperation : modelDistributeOperationList) {
      if (modelDistributeOperation.getOperationNum() == 0) {  //无法操作了
        modelDistributeOperationService.delete(modelDistributeOperation.getId());
        continue;
      }
      if (modelDistributeOperation.getOperationNum() == -1) {
        modelDistributeOperationFailIdList.add(modelDistributeOperation.getId());
        continue;
      }
      Optional<ModelEntity> modelEntityOptional = modelRepository.findByNo(modelDistributeOperation.getModelNo());
      try {
        switch (modelDistributeOperation.getDistributeType()) {
          case ADD:
            if (modelEntityOptional.isPresent()) {
              ModelEntity modelEntity = modelEntityOptional.get();
              //多次分发，update上次分发
              if (modelDistributeService.getModelDistributesByModelNoAndPlatformId(modelDistributeOperation.getModelNo(), modelDistributeOperation.getPlatformId()).size() > 0) {
                //有值
                Model model = platformService.editModel(modelDistributeOperation.getPlatformId(), modelDistributeOperation.getModelNo(), modelEntity.convertToModel());
                if (!Strings.isNullOrEmpty(model.getNo())) {//更新的情况
                  if (!Strings.isNullOrEmpty(model.getName()) && !model.getName().equals(modelEntity.getName())) {//名称不相等，下级对名称做了重新编号
                    String exceptionMessage = String.format("%s:%s:%s:%s:%s", "名称冲突", modelEntity.getNo(), modelEntity.getName(), model.getNo(), model.getName());
                    modelDistributeOperationService.create(modelDistributeOperation.setOperationNum(-1)
                        .setFailureMessage(exceptionMessage).setModelNo(model.getNo()));
                  } else {
                    modelDistributeOperationService.delete(modelDistributeOperation.getId());  //delete  distribute_operation
                  }
                } else {
                  modelDistributeOperationService.create(modelDistributeOperation.setOperationNum(modelDistributeOperation.getOperationNum() - 1));
                }
                continue;
              }
              Model model = platformService.createModel(modelDistributeOperation.getPlatformId(), modelEntity.convertToModel(), getSelfPlatformInfo());
              if (!Strings.isNullOrEmpty(model.getNo())) {
                if (model.getNo().equals(modelEntity.getNo()) && model.getName().equals(modelEntity.getName())) {//分发成功
                  modelDistributeOperationService.delete(modelDistributeOperation.getId());  //delete  distribute_operation
                } else {
                  String desc = null;
                  if (!model.getNo().equals(modelEntity.getNo())) {
                    desc = "编号冲突";
                  }
                  if (!model.getName().equals(modelEntity.getName())) {
                    if (Strings.isNullOrEmpty(desc)) {
                      desc = "名称冲突";
                    } else {
                      desc = "编号冲突,名称冲突";
                    }
                  }
                  String failMessage = String.format("%s:%s:%s:%s:%s", desc, modelEntity.getNo(), modelEntity.getName(), model.getNo(), model.getName());
                  modelDistributeOperationService.create(modelDistributeOperation.setOperationNum(-1).setFailureMessage(failMessage).setModelNo(model.getNo()));
                }
                modelDistributeService.createModelDistribute(new ModelDistribute(model.getNo(), modelDistributeOperation.getPlatformId()));
              } else {
                modelDistributeOperationService.create(modelDistributeOperation.setOperationNum(modelDistributeOperation.getOperationNum() - 1));
              }
            }
            break;
          case DELETE:
            List<String> noDeletes = new LinkedList<>();
            noDeletes.add(modelDistributeOperation.getModelNo());
            int deleteNum = platformService.deleteModels(modelDistributeOperation.getPlatformId(), noDeletes, getSelfPlatformInfo());
            if (deleteNum != -1) {
//              modelDistributeOperationService.delete(modelDistributeOperation.getId());
              modelDistributeOperationService.deleteByModelNoAndPlatformId(modelDistributeOperation.getModelNo(), modelDistributeOperation.getPlatformId());
              //删除
              modelDistributeService.deleteByModelNoAndPlatformId(modelDistributeOperation.getModelNo(), modelDistributeOperation.getPlatformId());
            } else {
              modelDistributeOperationService.create(modelDistributeOperation.setOperationNum(modelDistributeOperation.getOperationNum() - 1));
            }
            break;
          case ENABLED:
          case DISABLED:
            List<String> nos = new LinkedList<>();
            nos.add(modelDistributeOperation.getModelNo());
            Status status = EnumUtils.getEnum(Status.class, modelDistributeOperation.getDistributeType().toString());
            Map<String, Object> resultMap = platformService.editModelStatus(modelDistributeOperation.getPlatformId(), nos, status);
            if (null != resultMap && Integer.parseInt(resultMap.get("errorNum").toString()) == 0) {//只针对单个  只要错误为0，即可删除
              modelDistributeOperationService.delete(modelDistributeOperation.getId());
            }
            break;
        }
      } catch (Exception e) {
        if (modelEntityOptional.isPresent()) {
          ModelEntity modelEntity = modelEntityOptional.get();
          modelDistributeOperationService.create(modelDistributeOperation.setOperationNum(-1)
              .setFailureMessage(String.format("%s:%s:%s:%s:%s", "分发失败", modelEntity.getNo(), modelEntity.getName(), " ", " "))
              .setDistributeStatus(ModelDistributeOperation.DistributeStatus.FAIL));
        }
        log.error("PlatformSynchronized; reason was:" + e);
      }
    }
    //删除一些
    if (modelDistributeOperationFailIdList.size() >= 10) {
      for (int i = 0; i < modelDistributeOperationFailIdList.size() - 10; i++) {
        modelDistributeOperationService.delete(modelDistributeOperationFailIdList.get(i));
      }
    }
  }

  //获取平台自身的 id
  public String getSelfPlatformInfo() {
    return probeSubordinateService.getSelfInfo().getId();
  }
}
