import { Directive, ViewContainerRef } from '@angular/core';
import { ComponentFactoryResolver, Injectable } from '@angular/core';
import { CreatRetrospectComponent } from './creat-retrospect.component';

@Directive({
  selector: '[creat-recall-container]',
})
export class CreatRetrospectDirective {
  constructor(vc: ViewContainerRef, private se: CreatRetrospectService) {
    this.se.getContainer(vc);
  }
}

@Injectable({
  providedIn: 'root',
})
export class CreatRetrospectService {
  private container: any;
  constructor(private resolver: ComponentFactoryResolver) {}

  createComponent(fn: any, modelInfo?: any) {
    this.container.clear();
    const factory = this.resolver.resolveComponentFactory(CreatRetrospectComponent);
    const componentRef = this.container.createComponent(factory);
    if (modelInfo) {
      componentRef.instance.modelInfo = modelInfo;
    }
    componentRef.instance.onGetSuccess.subscribe((event: any) => {
      fn(event);
    });
  }

  getContainer(container: any) {
    this.container = container;
  }

  clear() {
    this.container.clear();
  }
}
