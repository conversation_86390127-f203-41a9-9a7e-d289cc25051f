<div class="groud-checkbox-con hidden">
  <button tButton type="button" label="设置其他输出信息" class="ui-button-secondary" [disabled]="disabled"  style="float: right;" (click)="btnClick()"></button>
  <div class="hidden sel-con add-top-to-bottom" *ngIf="oldSelects&&oldSelects.length>0">
    <div class="sel-item" *ngFor="let selItem of (allItems | gFilterList:'value':oldSelects); index as i; trackBy: selItemByFn;">
      <label class="item-name">{{selItem.label}}</label>
      <div class="item" *ngIf="!selItem.isCustom; else elseBlock">
        <span class="value_name">设置为</span>
        <div [ngClass]="{'in-error-con': !selItem['selValue']}">
          <t-dropdown [options]="dropdownObg.options | filterList:'bType':fieldsMap[selItem.type]:true:'items':true:2"
            [(ngModel)]="selItem['selValue']" placeholder="请选择字段" [filter]="true" filterBy="label,value,bType"
            appendTo="body" [style]="selStyle.sel" [disabled]="disabled" (onChange)="fieldsChange($event, selItem)"
            [layoutModel]="'group-horizontal'">
          </t-dropdown>
        </div>
        <div [ngClass]="{'in-error-con': !selItem['func']}" *ngIf="type==='manytoone'">
          <span style="margin:0 5px;">的</span>
          <!--allType   string为selItem的两个类型-->
          <t-dropdown [options]="(sourceTypes[selItem.type] || [])[selItem.pType] || []" [(ngModel)]="selItem['func']"
            [disabled]="disabled" placeholder="请选择" [filter]="true" filterBy="label,value" appendTo="body"
            [style]="selStyle.sel" [panelStyle]="selStyle.pel">
          </t-dropdown>
        </div>
      </div>
      <ng-template #elseBlock>
        <div class="text-con">
          <special-fields-search type="textarea" [disabled]="disabled"
            [style]="{'line-height': '22px', 'font-size': '14px', 'color':'rgba(0, 0, 0, 0.65)', 'background:': '#fff', 'padding': '5px 6px', 'border-radius': '2px', 'border': '1px solid #d9d9d9', 'height': '56px', 'width': '99%'}"
            [(value)]="selItem['selValue']" placeholder="支持输入'$'引用变量"
            [SpecialFields]="[{name:'$', options: dropdownObg.options | filterList:'bType':fieldsMap[selItem.type]:true:'items':true:2}]"></special-fields-search>
        </div>
      </ng-template>
      <i class="icon-colse" ngif="disabled" (click)="removeClick(selItem, false)"></i>
    </div>
  </div>
</div>

<t-dialog header="设置其它输出信息" [(visible)]="isShow" [positionTop]="150" [baseZIndex]="980000009" (onHide)="clearClick()">
  <div class="g-c-dialog-con">
    <t-scrollPanel [style]="{width: '100%', height: '100%'}" *ngIf="datatypes.isEmpty">
      <div class="item-con" *ngFor="let ground of checkboxList; trackBy: grounpByFn;">
        <h3 class="title-font file-name">{{ground.gName}}</h3>
        <div class="padding-checkbox hidden">
          <ng-template ngFor let-item [ngForOf]="ground.items" let-i="index" [ngForTrackBy]="itemByFn">
            <div class="item" *ngIf="!item.isState" [title]="item.label">
              <div class="detail-con" [ngStyle]="{'marginRight': item.isRemove ? '10px' : '0'}">
                <t-checkbox *ngIf="!item.isAdd || item.isRemove" name="groud-c-d" [value]="item.value" [label]="item.label"
                  [(ngModel)]="selectes" [disabled]="item.disabled" (onChange)="checkboxChange($event, item)"></t-checkbox>
                <input *ngIf="item.inputHide" type="text" tInputText placeholder="自定义内容" style="width: 100%;" #inputName
                  (blur)="addClick($event,ground.items,i)" (keyDown.enter)="addClick($event,ground.items,i)">
              </div>
              <i *ngIf="item.isAdd&&!item.inputHide" class="icon-open" title="自定义" (click)="showinputHide(item)"></i>
              <i *ngIf="item.isRemove" class="icon-close" title="删除自定义" (click)="removeClick(ground.items, i)"></i>
            </div>
          </ng-template>
        </div>
      </div>
    </t-scrollPanel>
    <t-nodata [noDataType]="'noDataContent'" [noDatatext]="'无数据'" *ngIf="datatypes.empty"></t-nodata>
    <t-loading *ngIf="datatypes.loading" [bgColor]="'transparent'" [zindex]="980000999" [visible]="true"></t-loading>
  </div>
  <t-footer>
    <button type="button" tButton (click)="save()" style="float: right;margin:0;"  label="确定"></button>
    <button type="button" tButton (click)="clearClick()" style="float: right;margin-right: 10px;" label="取消" class="ui-button-secondary"></button>
  </t-footer>
</t-dialog>