package com.topsec.wilkes.common.dto;

import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: dy
 * @time: 2025/4/28 14:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlarmTagNode {
  private String id;
  private String name;
  private List<AlarmTagNode> children = new ArrayList<>();

  public AlarmTagNode(String id, String name) {
    this.id = id;
    this.name = name;
  }
}
