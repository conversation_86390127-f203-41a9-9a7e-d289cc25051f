package com.topsec.wilkes.behaviour.service.impl.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Properties;

public class PropertiesUtil {
  public static String addPropertiesToUrl(String url, Properties properties) {
    StringBuilder newUrl = new StringBuilder(url);
    if (!properties.isEmpty()) {
      if (url.contains("?")) {
        newUrl.append("&");
      } else {
        newUrl.append("?");
      }
      for (String name : properties.stringPropertyNames()) {
        String encode = null;
        try {
          encode = URLEncoder.encode((String) properties.get(name), "UTF-8");
        } catch (UnsupportedEncodingException e) {
          e.printStackTrace();
        }
        newUrl.append(String.format("%s=%s&", name, encode));
      }
      newUrl.deleteCharAt(newUrl.length() - 1);
    }
    return newUrl.toString();
  }
}
