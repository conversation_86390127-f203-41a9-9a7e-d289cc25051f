package com.topsec.wilkes.service;

import static com.topsec.wilkes.util.ModelUtil.executeExtraTest;

import com.google.common.collect.Lists;
import com.topsec.minsky.dto.ProcessMetadata;
import com.topsec.minsky.dto.Result;
import com.topsec.minsky.service.feign.ProcessService;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.common.dto.Status;
import com.topsec.wilkes.dto.ModelTask;
import com.topsec.wilkes.dto.PlatformProcessInfo;
import com.topsec.wilkes.dto.task.ExecuteTask;
import com.topsec.wilkes.dto.task.ExecuteTaskType;
import com.topsec.wilkes.dto.task.StageType;
import com.topsec.wilkes.platform.feign.ProbeService;
import com.topsec.wilkes.repository.UnionProcessRespository;
import com.topsec.wilkes.repository.entity.UnionProcessEntity;
import com.topsec.wilkes.task.ExecuteTaskGenerator;
import com.topsec.wilkes.task.TaskExecuteService;
import com.topsec.wilkes.task.impl.TaskProcessGenerator;
import com.topsec.wilkes.task.impl.factory.ExecuteTaskGeneratorFactory;
import com.topsec.wilkes.task.impl.factory.TaskExecuteServiceFactory;

import java.util.ArrayList;
import java.util.LinkedList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ModelTaskGenerator {

  @Autowired
  private ModelTaskService modelTaskService;

  @Autowired
  private TaskExecuteServiceFactory taskExecuteServiceFactory;

  @Autowired
  private ExecuteTaskGeneratorFactory executeTaskGeneratorFactory;

  @Autowired
  ProcessService processService;

  @Autowired
  UnionProcessRespository unionProcessRespository;

  @Autowired
  private ProbeService probeService;

  @Autowired
  private TaskProcessGenerator taskProcessGenerator;


  public void createModelTask(Model model) throws Exception {
    List<ExecuteTask> executeTasks = getExecuteTaskGenerator(model.getModelType())
        .getExecuteTask(model);
    for (ExecuteTask executeTask : executeTasks) {
      String taskId = getTaskExecuteService(executeTask.getExecuteTaskType())
          .registerTask(executeTask);
      modelTaskService.createModelTask(
          new ModelTask(taskId, model.getId(), executeTask.getStageType(),
              executeTask.getExecuteTaskType()));
      log.info("Register {} task successful!", executeTask.getStageType().name());
    }
  }

  public void editModelTask(Model model) throws Exception {
    List<ModelTask> modelTasks = modelTaskService.getValidModelTasks(model.getId());
    if (Status.ENABLED == model.getStatus() && modelTasks.size() == 0) {
      createModelTask(model);
    }

    Map<StageType, ExecuteTask> executeTasks = getExecuteTaskGenerator(model.getModelType())
        .getExecuteTask(model).stream()
        .collect(Collectors.toMap(ExecuteTask::getStageType, v -> v));

    for (ModelTask modelTask : modelTasks) {
      String oldTaskId = modelTask.getTaskId();
      String newTaskId = getTaskExecuteService(modelTask.getExecuteTaskType())
          .editTask(oldTaskId, executeTasks.get(modelTask.getStageType()));
      if (!oldTaskId.equals(newTaskId)) {
        List<String> allInvalidTaskProcessIds = getProcessMetadatasByModel(modelTask).stream()
            .map(ProcessMetadata::getId).collect(Collectors.toList());
        List<ProcessMetadata> retList = batchDelete(allInvalidTaskProcessIds);
        if (retList == null) {
          log.info("delete failed, cause: response into fallback");
        } else if (retList.size() > 0) {
          log.info("delete task_process:{}", allInvalidTaskProcessIds);
        }
        modelTaskService.deleteModelTaskByTaskId(oldTaskId);
        modelTask.setTaskId(newTaskId);
        modelTaskService.createModelTask(modelTask);
      }
    }
  }

  public List<ProcessMetadata> batchDelete(List<String> ids) {
    List<ProcessMetadata> retList = new ArrayList<>();
    int size = ids.size();
    try {
      if (size > 100) {
        int count = (int) Math.ceil(size / 100.0);
        for (int i = 0; i < count; i++) {
          List<ProcessMetadata> subRetList = processService
              .delete(ids.subList(i * 100, Math.min(i * 100 + 99, size - 1)));
          if (subRetList != null) {
            retList.addAll(subRetList);
          }
        }
      } else {
        retList = processService.delete(ids);
      }
    } catch (Exception e) {
      log.error("delete process failed ,process id:{}", ids);
    }
    List<String> unionPorcessIds = retList.stream()
        .filter(processMetadata -> processMetadata.getNamespace().getName().equals("union"))
        .map(processMetadata -> processMetadata.getId()).collect(Collectors.toList());
    deleteProcess(unionPorcessIds);
    return retList;
  }


  public void deleteProcess(List<String> ids) {
    Map<String, String> distributeMap = unionProcessRespository.findAllById(ids).stream().collect(
        Collectors.toMap(UnionProcessEntity::getParentProcessId,
            UnionProcessEntity::getDownPlatformsMap));
    for (String id : ids) {
      if (!distributeMap.containsKey(id)) {
        continue;
      }
      List<PlatformProcessInfo> platformProcessList = com.alibaba.fastjson.JSONObject
          .parseArray(distributeMap.get(id), PlatformProcessInfo.class);

      platformProcessList.stream().forEach(platformProcessInfo -> {
        probeService.deleteProcess(platformProcessInfo.getPlatformId(),
            Lists.newArrayList(platformProcessInfo.getProrcessId()));
      });
      unionProcessRespository.deleteById(id);
    }

  }

  public List<ProcessMetadata> getProcessMetadatasByModel(ModelTask modelTask) {
    List<ProcessMetadata> processMetadataList = new LinkedList<>();
    String[] processIds = modelTask.getTaskId().split(",");
    for (String processId : processIds) {
      ProcessMetadata processMetadata = processService.getMetadata(processId);
      processMetadataList.add(processMetadata);
    }
    return processMetadataList;
  }

  public void editModelTask(Model model, Status status) throws Exception {
    List<ModelTask> modelTasks = modelTaskService.getValidModelTasks(model.getId());
    model.setStatus(status);
    if (Status.ENABLED == status && modelTasks.size() == 0) {
      createModelTask(model);
      return;
    }
    for (ModelTask modelTask : modelTasks) {
      TaskExecuteService taskExecuteService = getTaskExecuteService(modelTask.getExecuteTaskType());
      if (status == Status.ENABLED) {
        taskExecuteService.startTask(modelTask.getTaskId());
      } else {
        taskExecuteService.stopTask(modelTask.getTaskId());
      }
    }
  }

  public void deleteModelTask(String modelId) {
    List<ModelTask> modelTasks = modelTaskService.getValidModelTasks(modelId);
    for (ModelTask modelTask : modelTasks) {
      TaskExecuteService taskExecuteService = getTaskExecuteService(modelTask.getExecuteTaskType());
      taskExecuteService.deleteTask(modelTask.getTaskId());
    }
    modelTaskService.deleteModelTasks(modelId);
  }

  public Result testModelTask(Model model) throws Exception {
    Result result = Result.failed();
    for (ExecuteTask executeTask : getExecuteTaskGenerator(model.getModelType())
        .getExecuteTask(model)) {
      result = executeExtraTest(model, executeTask);
      if (result.getStatus() == Result.Status.SUCCESS) {
        TaskExecuteService taskExecuteService = getTaskExecuteService(
            executeTask.getExecuteTaskType());
        result = taskExecuteService.testTask(executeTask);
      }
      if (result.getStatus() == Result.Status.FAILED) {
        return result;
      }
    }
    return result;
  }

  private ExecuteTaskGenerator getExecuteTaskGenerator(ModelType modelType) {
    return executeTaskGeneratorFactory.getExecuteTaskGenerator(modelType);
  }

  private TaskExecuteService getTaskExecuteService(ExecuteTaskType executeTaskType) {
    return taskExecuteServiceFactory.getTaskExecuteService(executeTaskType);
  }

}
