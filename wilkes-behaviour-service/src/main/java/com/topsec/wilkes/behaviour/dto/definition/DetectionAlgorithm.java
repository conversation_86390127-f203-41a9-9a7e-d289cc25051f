package com.topsec.wilkes.behaviour.dto.definition;


import com.topsec.common.utils.ToolUtils;
import com.topsec.wilkes.behaviour.dto.task.StorageInfo;
import com.topsec.wilkes.dto.task.info.TaskInfo;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.net.JarURLConnection;
import java.net.URL;
import java.util.Comparator;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.jar.JarEntry;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
public class DetectionAlgorithm {

  private final static Logger LOGGER = LoggerFactory.getLogger(DetectionAlgorithm.class);
  private static final String ALL_ALGORITHM_DEFINITION_LOCATION = "com/topsec/wilkes/behaviour/algorithm/";
  private static final Pattern ALGORITHM_DEFINITION_LOCATION = Pattern.compile("com/topsec/wilkes/behaviour/algorithm/[^/]+\\.json");
  private static List<DetectionAlgorithm> allAlgorithmDefinition;
  private static List<DetectionAlgorithm> publicAlgorithmDefinition;

  private String id;

  private String name;

  private List<Object> params;

  private String desc;

  private int order;

  private String type;

  private boolean isModelOutputSupport;

  private StorageInfo storageInfo;

  private List<TaskInfo> taskInfo;


  public static Map<String, DetectionAlgorithm> getAllAlgorithmMap(boolean isFilter) {

    HashMap<String, DetectionAlgorithm> hashMap = new HashMap<>();
    List<URL> pkgURLs = Lists.newLinkedList();
    try {
      Enumeration<URL> urls = DetectionAlgorithm.class.getClassLoader().getResources(ALL_ALGORITHM_DEFINITION_LOCATION);
      while (urls.hasMoreElements()) {
        pkgURLs.add(urls.nextElement());
      }
    } catch (Exception ex) {
      LOGGER.error("", ex);
      throw new RuntimeException(ex);
    }

    for (URL url : pkgURLs) {
      if (url.toString().startsWith("jar:file")) {
        String jarPath = url.toString().substring(0, url.toString().indexOf("!/") + 2);
        Enumeration<JarEntry> jarEntries = null;
        try {
          URL jarURL = new URL(jarPath);
          JarURLConnection jarCon = (JarURLConnection) jarURL.openConnection();
          jarEntries = jarCon.getJarFile().entries();
        } catch (IOException e) {
          LOGGER.error("open JarURLConnection error!", e);
          throw new RuntimeException(String.format("open JarURLConnection error! for more:%s", e));
        }
        ObjectMapper objectMapper = ToolUtils.OBJECT_MAPPER;
        while (jarEntries.hasMoreElements()) {
          JarEntry entry = jarEntries.nextElement();
          String name = entry.getName();
          if (name.startsWith(ALL_ALGORITHM_DEFINITION_LOCATION) && name.endsWith(".json") && !entry.isDirectory()) {
            try {
              DetectionAlgorithm detectionAlgorithm = objectMapper.readValue(DetectionAlgorithm.class.getClassLoader()
                  .getResourceAsStream(name), DetectionAlgorithm.class);
              if (!isFilter) {
                name = name.substring(name.lastIndexOf("/") + 1).split("\\.")[0];
              }
              hashMap.put(name, detectionAlgorithm);
            } catch (IOException e) {
              LOGGER.error(String.format("Get from %s  to Algorithm error!", name), e);
              throw new RuntimeException(String.format("get Algorithm error! for more:%s", e));
            }
          }
        }
      } else {
        getAlgorithmFromFile(hashMap, url.getPath());
      }
    }

    return hashMap;
  }

  private static void getAlgorithmFromFile(Map<String, DetectionAlgorithm> map, String path) {
    File fileTempDir = new File(path);

    File[] fileList = fileTempDir.listFiles((dir, name) -> name.endsWith(".json"));
    for (File file : fileList) {
      ObjectMapper objectMapper = ToolUtils.OBJECT_MAPPER;
      DetectionAlgorithm Algorithm = null;
      try {
        Algorithm = objectMapper.readValue(file, DetectionAlgorithm.class);
      } catch (IOException e) {
        LOGGER.error("get Algorithm error!", e);
        throw new RuntimeException(String.format("get Algorithm error! for more:%s", e));
      }
      map.put(file.getName(), Algorithm);
    }

    File[] files = fileTempDir.listFiles(file -> file.isDirectory());
    for (File file : files) {
      getAlgorithmFromFile(map, file.getPath());
    }
  }

  /**
   * 获取指定Resource下的所有算子定义.
   *
   * @return
   */
  public static List<DetectionAlgorithm> getAllAlgorithm() {
    if (allAlgorithmDefinition != null) {
      return allAlgorithmDefinition;
    }
    allAlgorithmDefinition = getAllAlgorithmMap(false).entrySet().stream()
        .map(entrySet -> entrySet.getValue()).collect(Collectors.toList());
    allAlgorithmDefinition.sort(Comparator.comparingInt(algorithm -> algorithm.getOrder()));
    return allAlgorithmDefinition;
  }

  public static List<DetectionAlgorithm> getPublicAlgorithm() {
    if (publicAlgorithmDefinition != null) {
      return publicAlgorithmDefinition;
    }
    publicAlgorithmDefinition = getAllAlgorithmMap(true).entrySet().stream()
        .filter(entry -> ALGORITHM_DEFINITION_LOCATION.matcher(entry.getKey()).find())
        .map(entrySet -> entrySet.getValue()).collect(Collectors.toList());
    publicAlgorithmDefinition.sort(Comparator.comparingInt(algorithm -> algorithm.getOrder()));
    return publicAlgorithmDefinition;
  }


  @Override
  public DetectionAlgorithm clone() {
    ObjectMapper objectMapper = ToolUtils.OBJECT_MAPPER;
    try {
      return objectMapper.readValue(objectMapper.writeValueAsString(this), DetectionAlgorithm.class);
    } catch (Exception e) {
      log.error("", e);
      return new DetectionAlgorithm();
    }
  }
}
