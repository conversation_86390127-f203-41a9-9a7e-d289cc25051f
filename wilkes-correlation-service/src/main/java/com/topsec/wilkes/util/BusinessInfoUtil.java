package com.topsec.wilkes.util;

import static com.topsec.common.utils.ToolUtils.OBJECT_MAPPER;
import static com.topsec.wilkes.util.OperatorDefinition.Group.AGGREGATION;
import static com.topsec.wilkes.util.OperatorDefinition.Group.RELATION;


import com.topsec.dm.domain.DataModel;
import com.topsec.dm.domain.FieldInfo;
import com.topsec.dm.domain.intelligence.Intelligence;
import com.topsec.dm.domain.intelligence.IntelligenceField;
import com.topsec.dm.service.IntelligenceService;
import com.topsec.minsky.domain.Operator;
import com.topsec.minsky.domain.Process;
import com.topsec.wilkes.dto.BusinessInfo;
import com.topsec.wilkes.dto.OperatorTree;
import com.topsec.wilkes.operator.util.FormatUtil;
import com.topsec.wilkes.service.OperatorService;
import com.topsec.wilkes.common.dto.ModelType;


import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class BusinessInfoUtil {
  public static Map<String, List<FieldInfo>> extraFieldInfoMap = new HashMap<>();
  private static List<String> oneList = Lists.newArrayList("follow-by-potential-cep", "not-follow-by-cep", "any-one-happen");
  private static List<String> manyList = Lists.newArrayList("");

  static {
    FieldInfo[] extraFields = null;
    try {
      extraFields = OBJECT_MAPPER.readValue(BusinessInfoUtil.class.getResourceAsStream("/extra_business_field.json"), FieldInfo[].class);
    } catch (IOException e) {
      log.error("", e);
    }
    if (extraFields != null) {
      for (FieldInfo extraField : extraFields) {
        if (extraFieldInfoMap.containsKey(extraField.getGroup())) {
          extraFieldInfoMap.get(extraField.getGroup()).add(extraField);
        } else {
          List<FieldInfo> fieldInfos = new LinkedList<>();
          fieldInfos.add(extraField);
          extraFieldInfoMap.put(extraField.getGroup(), fieldInfos);
        }
      }
    }

  }

  @Autowired
  private IntelligenceService intelligenceService;
  @Autowired
  private OperatorService operatorService;

  public BusinessInfo getBusinessInfo(Process process) {
    Map<String, List<FieldInfo>> fieldMap = new LinkedHashMap<>();
    LinkedHashSet<FieldInfo> fieldInfoList = new LinkedHashSet<>();

    BusinessInfo businessInfo = new BusinessInfo();
    BusinessInfo.MappingType mappingType = BusinessInfo.MappingType.ONETOONE;


    List<Operator> sourceOperatorDescriptions = ProcessUtil.findProcessSource(process);
    List<Operator> lastOperatorDescriptions = ProcessUtil.findLastOperatorDescription(process);
    for (Operator sourceOperatorDescription : sourceOperatorDescriptions) {
      fieldInfoList.addAll(getSourceOperatorField(sourceOperatorDescription));
    }
    // 根据算子附加业务字段
    Map<String, Intelligence> intelligenceMap = intelligenceService.getIntelligences("").stream().collect(Collectors.toMap(Intelligence::getId, intelligence -> intelligence));
    process.getOperators().stream()
        .filter(operator -> "knowledge-base-filter".equals(operator.getId()) || "threat-base-filter".equals(operator.getId()))
        .forEach(operator -> {
          fieldInfoList.addAll(getKnowledgeBaseFilterOperatorField(operator, intelligenceMap));
        });
    Map<String, OperatorDefinition.Group> operatorMap = new HashMap<>();
    for (OperatorTree operatorTree : operatorService.getOperatorsByGroup(ModelType.CORRELATION)) {
      List<OperatorDefinition> operatorDefinitionList = OBJECT_MAPPER.convertValue(operatorTree.getChildren(), new TypeReference<List<OperatorDefinition>>() {
      });
      for (OperatorDefinition operatorDefinition : operatorDefinitionList) {
        operatorMap.put(operatorDefinition.getId(), operatorDefinition.getGroup());
      }
    }
    for (Operator lastOperatorDescription : lastOperatorDescriptions) {
      // 判断对应关系
      String operatorDescriptionId = lastOperatorDescription.getId();
      if (!oneList.contains(operatorDescriptionId) && operatorMap.containsKey(operatorDescriptionId)
          && (operatorMap.get(operatorDescriptionId) == RELATION || operatorMap.get(operatorDescriptionId) == AGGREGATION)
          || manyList.contains(operatorDescriptionId)) {
        mappingType = BusinessInfo.MappingType.MANYTOONE;
      }

      // 聚合算子在末尾才附加业务字段
      if ("field-aggregation".equals(operatorDescriptionId)) {
        fieldInfoList.addAll(extraFieldInfoMap.get(operatorDescriptionId));
      }
    }

    fieldMap.put("业务字段", new ArrayList<>(fieldInfoList));
    businessInfo.setMappingType(mappingType);
    businessInfo.setFieldsMap(fieldMap);
    return businessInfo;
  }

  private List<FieldInfo> getSourceOperatorField(Operator sourceOperatorDescription) {
    List<FieldInfo> fieldInfos = new LinkedList<>();
    if (sourceOperatorDescription.getParams().containsKey("url")) {
      List<Object> urlList = (List<Object>) sourceOperatorDescription.getParams().get("url");
      if (urlList != null && urlList.size() > 0) {
        List<DataModel> sourceList = urlList.stream().map(url -> OBJECT_MAPPER.convertValue(url, DataModel.class)).collect(Collectors.toList());
        for (DataModel source : sourceList) {
          if (extraFieldInfoMap.containsKey(source.getId())) {
            List<FieldInfo> fieldParams = extraFieldInfoMap.get(source.getId());
            fieldInfos.addAll(fieldParams);
          }
        }
      }
    }
    return fieldInfos;
  }

  public List<FieldInfo> getKnowledgeBaseFilterOperatorField(
      Operator knowledgeBaseFilterOperatorDescription,
      Map<String, Intelligence> knowledgeBaseContentMap) {
    Map<String, Object> paramMap = knowledgeBaseFilterOperatorDescription.getParams();
    String instanceId = knowledgeBaseFilterOperatorDescription.getInstanceId().replace("-", "");
    List<FieldInfo> fieldInfos = new LinkedList<>();
    String knowledgeBaseId = paramMap.get("knowledge_base_url").toString();
    Object expression = paramMap.get("expression");
    if (Strings.isNullOrEmpty(knowledgeBaseId) || expression == null) {
      return Collections.EMPTY_LIST;
    }

    if (!knowledgeBaseContentMap.containsKey(knowledgeBaseId)) {
      log.warn("The knowledgeBase {} currently does not exist!", knowledgeBaseId);
      return Collections.EMPTY_LIST;
    }

    Map<String, IntelligenceField> columnMap = knowledgeBaseContentMap.get(knowledgeBaseId).getSchemas().stream().collect(Collectors.toMap(c -> c.getColumn(), c -> c));
    List<Map<String, Object>> leafNodeList = (List<Map<String, Object>>) ((Map<String, Object>) ((Map<String, Object>) expression).get("node")).get("children");
    List<String> relation_field = (List<String>) paramMap.get("relation_field");
    for (String field : relation_field) {
      if (columnMap.containsKey(field)) {
        IntelligenceField intelligenceField = columnMap.get(field);
        FieldInfo fieldInfo = new FieldInfo(field + FormatUtil.extendMark + instanceId, intelligenceField.getType(), intelligenceField.getName(), "业务字段");
        fieldInfos.add(fieldInfo);
      }
    }

    Set<String> leftFields = leafNodeList.stream().map(leafNode -> ((Map<String, String>) leafNode.get("leaf_node")).get("left")).collect(Collectors.toSet());
    leftFields.addAll(relation_field);
    for (String field : leftFields) {
      FieldInfo fieldInfo = new FieldInfo(field, "keyword", "业务字段", "业务字段");
      fieldInfos.add(fieldInfo);
    }
    return fieldInfos;
  }
}
