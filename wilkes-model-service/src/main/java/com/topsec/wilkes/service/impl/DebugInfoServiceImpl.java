package com.topsec.wilkes.service.impl;

import com.topsec.minsky.domain.Process;
import com.topsec.minsky.dto.Namespace;
import com.topsec.minsky.dto.NamespaceOptions;
import com.topsec.minsky.service.feign.NamespaceService;
import com.topsec.minsky.service.feign.ProcessService;
import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.common.dto.ModelType;
import com.topsec.wilkes.dto.ModelTask;
import com.topsec.wilkes.service.DebugInfoService;
import com.topsec.wilkes.service.ModelService;
import com.topsec.wilkes.service.ModelTaskService;
import com.topsec.wilkes.util.ExecuteLinuxUtil;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.io.SequenceInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DebugInfoServiceImpl implements DebugInfoService {

  private final String catalinaBase = System.getProperty("catalina.base", "");
  @Autowired
  private ModelService modelService;
  @Autowired
  private ModelTaskService modelTaskService;
  @Autowired
  private NamespaceService namespaceService;
  @Autowired
  private ProcessService processService;
  @Autowired
  private Environment env;

  @Override
  public List<Process> getProcess(String no) {
    Model model = modelService.getModel(no);
    if (model.equals(new Model())) {
      return null;
    }
    return modelTaskService.getProcess(model.getId());
  }

  @Override
  public InputStream getGroupedProcess(String no) {
    Model model = modelService.getModel(no);
    if (model.equals(new Model())) {
      return null;
    }
    String jobId = modelService.getJobId(model.getId());
    String filePath = String.format("%s/work/jobs/%s/process", catalinaBase, jobId);
    return readFile(filePath);
  }

  @Override
  public InputStream getServiceLog() {
    String filePath = String.format("%s/logs/wilkes/service/wilkes.log", catalinaBase);
    String commandStr = " tail -200 " + filePath;
    String logTail = ExecuteLinuxUtil.getExeResult(commandStr);
    return new ByteArrayInputStream(logTail.getBytes(StandardCharsets.UTF_8));
  }

  @Override
  public InputStream getEngineLog() {
    String filePath = String.format("%s/logs/minsky/service/minsky.log", catalinaBase);
    String commandStr = " tail -200 " + filePath;
    String logTail = ExecuteLinuxUtil.getExeResult(commandStr);
    return new ByteArrayInputStream(logTail.getBytes(StandardCharsets.UTF_8));
  }

  @Override
  public InputStream getFlinkConf(String no) {
    String filePath = String.format("%s/work/sessions/%s/conf/flink-conf.yaml", catalinaBase, getSessionName(no));
    return readFile(filePath);
  }

  @Override
  public InputStream getTaskManagerStderr(String no) {
    String filePath = String.format("%s/work/sessions/%s/stderr", catalinaBase, getSessionName(no));
    return readFile(filePath);
  }

  @Override
  public InputStream getTaskManagerStdout(String no) {
    String filePath = String.format("%s/work/sessions/%s/stdout", catalinaBase, getSessionName(no));
    return readFile(filePath);
  }

  @Override
  public Map<String, Object> getSessionStatus(String no) {
    String sessionName = getSessionName(no);
    String jobManagerNum = ExecuteLinuxUtil.getExeResult(String.format("ps -ef|grep flink|grep sessions/%s/|grep cluster|grep -v grep|awk '{print $2}'|wc -l", sessionName));
    String taskManagerNum = ExecuteLinuxUtil.getExeResult(String.format("ps -ef|grep flink|grep sessions/%s/|grep -v grep|grep taskmanager|awk '{print $2}'|wc -l", sessionName));
    Map<String, Object> sessionMap = new HashMap<>();
    sessionMap.put("name", sessionName);
    sessionMap.put("job-manager", jobManagerNum);
    sessionMap.put("task-manager", taskManagerNum);
    return sessionMap;
  }

  @Override
  public InputStream getEnv() {
    String commandStr = "ps -ef|grep wilkes|grep -v ps|grep org.apache.catalina.startup.Bootstrap |awk '{print $2}'";
    String pid = ExecuteLinuxUtil.getExeResult(commandStr);
    String filePath = String.format("/proc/%s/environ", pid);
    return readFile(filePath);
  }

  @Override
  public String getJobListUrl(String no) {
    return String.format("/proxy/%s/jobs/overview", getSessionName(no));
  }

  @Override
  public String getJobDetailUrl(String no, String jid) {
    String jobId;
    if (jid != null) {
      jobId = jid;
    } else {
      Model model = modelService.getModel(no);
      if (model.equals(new Model())) {
        return null;
      }
      jobId = modelService.getJobId(model.getId());
    }
    if (!Strings.isNullOrEmpty(jobId)) {
      return String.format("/proxy/%s/jobs/%s", getSessionName(no), jobId);
    }
    return null;
  }

  @Override
  public String getOfflineUrl(String jid) {
    if (Strings.isNullOrEmpty(jid)) {
      return "/proxy/correlation-offline/jobs/overview";
    } else {
      return String.format("/proxy/correlation-offline/jobs/%s", jid);
    }
  }

  @Override
  public InputStream getConfig() {
    String filePath = String.format("%s/webapps/wilkes/WEB-INF/classes/config/application-update.yml", catalinaBase);
    return readFile(filePath);
  }

  @Override
  public String updateConfig(String config) {
    String filePath = String.format("%s/webapps/wilkes/WEB-INF/classes/config/application-update.yml", catalinaBase);
    String commandStr = String.format("echo \"%s\" > %s", config, filePath);
    return ExecuteLinuxUtil.getExeResult(commandStr);
  }

  @Override
  public String addFlinkConf(String no, String confStr) {
    String serviceData = System.getenv("TOP_DATA_PATH");
    String filePath = String.format("%s/wilkes/temp/flink-conf-add.yaml", serviceData);
    String commandStr = String.format("echo \"%s\" > %s", confStr, filePath);
    ExecuteLinuxUtil.getExeResult(commandStr);
    killSession(getSessionName(no));
    return "ok";
  }

  @Override
  public void updateNamespace(List<String> changeList) {
    boolean needChangeAll = false;
    if (changeList.size() < 1) return;
    Set<String> changeNamespaceSet = new LinkedHashSet<>();
    for (String change : changeList) {
      if (change.startsWith("namespace")) {
        String[] strings = change.split("\\.");
        if (strings.length > 1) {
          changeNamespaceSet.add(strings[1]);
        }
      } else {
        needChangeAll = true;
      }
    }
    for (String namespace : changeNamespaceSet) {
      String prefix = "namespace." + namespace;
      List<Namespace> namespaceList = namespaceService.getByNames(Collections.singletonList(namespace));
      if (namespaceList.size() > 0) {
        Namespace nsDto = namespaceList.get(0);
        NamespaceOptions.JobOptions jobOptions = nsDto.getOptions().getJobOptions();
        jobOptions.setDefaultParallelism(Integer.parseInt(Objects.requireNonNull(env.getProperty(prefix + ".default-parallelism"))));
        jobOptions.setUseKafkaParallelism(Boolean.parseBoolean(env.getProperty(prefix + ".useKafkaParallelism")));
        Map<String, Integer> map = new LinkedHashMap<>();
        map.put("security_log", Integer.parseInt(Objects.requireNonNull(env.getProperty(prefix + ".parallelism.security_log"))));
        map.put("http_log", Integer.parseInt(Objects.requireNonNull(env.getProperty(prefix + ".parallelism.http_log"))));
        map.put("dns_log", Integer.parseInt(Objects.requireNonNull(env.getProperty(prefix + ".parallelism.dns_log"))));
        jobOptions.setParallelism(map);
        namespaceService.update(namespace, nsDto);
        log.info("更新了" + namespace + "的参数");
      }
    }
    //全停还是部分停
    if (needChangeAll) {
      deleteAllProcess();
    } else {
      for (String changeNamespace : changeNamespaceSet) {
        deleteProcessByModelType(ModelType.valueOf(changeNamespace.toUpperCase()));
      }
    }
  }

  @Override
  public InputStream getBanner() {
    InputStream in1 = readFile(String.format("%s/webapps/minsky/WEB-INF/classes/banner.txt", catalinaBase));
    InputStream in2 = readFile(String.format("%s/webapps/wilkes/WEB-INF/classes/banner.txt", catalinaBase));
    // 串流合并
    return new SequenceInputStream(in1, in2);
  }

  private InputStream readFile(String filePath) {
    File file = new File(filePath);
    try {
      return new FileInputStream(file);
    } catch (FileNotFoundException e) {
      log.error("", e);
    }
    return null;
  }

  private String getSessionName(String no) {
    Model model = modelService.getModel(no);
    if (model.equals(new Model())) {
      return null;
    }
    return model.getModelType().toString().toLowerCase();
  }

  private void killSession(String sessionName) {
    String killProcess = String.format("ps -ef | grep flink | grep sessions/%s/ | awk '{print $2}' | xargs kill -9", sessionName);
    ExecuteLinuxUtil.getExeResult(killProcess);
    try {
      Thread.sleep(10000);
    } catch (InterruptedException e) {
      log.error("", e);
    }
  }

  private void deleteProcessByModelType(ModelType modelType) {
    List<Model> modelList = modelService.getModels(modelType, false);
    modelList.forEach(model -> {
      List<String> processIdList = modelTaskService.getValidModelTasks(model.getId()).stream().map(ModelTask::getTaskId).collect(Collectors.toList());
      processService.delete(processIdList);
    });
  }

  private void deleteAllProcess() {
    List<Model> modelList = modelService.getModels();
    modelList.forEach(model -> {
      List<String> processIdList = modelTaskService.getValidModelTasks(model.getId()).stream().map(ModelTask::getTaskId).collect(Collectors.toList());
      processService.delete(processIdList);
    });
  }
}
