package com.topsec.wilkes.runner;

import static com.topsec.wilkes.dto.ActionConstants.GENERATE_EVENT;
import static com.topsec.wilkes.util.FileUtil.getFileSort;
import static com.topsec.wilkes.util.FileUtil.getHashStr;

import com.topsec.common.utils.ToolUtils;
import com.topsec.dm.domain.EventType;
import com.topsec.dm.service.IntelligenceService;
import com.topsec.wilkes.common.dto.*;
import com.topsec.wilkes.repository.ConfigRepository;
import com.topsec.wilkes.repository.entity.ConfigEntity;
import com.topsec.wilkes.service.ModelService;

import com.google.common.base.Strings;
import com.topsec.wilkes.util.TagUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class ModelUpdateRunner {

  private static final String MODEL_UPDATE_RECORD = "model.update.record";
  private final String updateFilePath = Objects.requireNonNull(this.getClass().getClassLoader().getResource("/update/cat")).getPath();
  private final String recordFilePath = updateFilePath + "record.txt";
  @Autowired
  private ModelService modelService;
  @Autowired
  private IntelligenceService intelligenceService;
  @Autowired
  private ConfigRepository configRepository;
  private List<String> oldHashStrList = new LinkedList<>();
  private List<String> newHashStrList = new LinkedList<>();

  @Autowired
  private TagUtil tagUtil;
  private Map<String, String> unExistModels = new HashMap<>();
  private final String catalinaBase = System.getProperty("catalina.base", "");
  private final String unExistModelPath = String.format("%s/%s", catalinaBase, "conf");

  public void run(){
    configRepository.findById(MODEL_UPDATE_RECORD).ifPresent(configEntity -> oldHashStrList.addAll(Arrays.asList(configEntity.getValue().split(","))));
    log.info("------事件类型更新开始------");
    int complete = updateEventType(null);
    if (newHashStrList.size() > 0) {
      oldHashStrList.addAll(newHashStrList);
      configRepository.saveAndFlush(new ConfigEntity(MODEL_UPDATE_RECORD, StringUtils.join(oldHashStrList.toArray(), ",")));
    }
    log.info("------事件类型更新完成，更新模型{}个------", complete);
    log.info("------开始设置事件描述和处置意见默认值------");
    int count = updateDefaultValue(null);
    log.info("------默认值设置完成，更新模型{}个------", count);

    if (unExistModels.size() > 0) {
      unUpdateEventType();
    }
    log.info("------事件类型无法更新模型{}个------", unExistModels.size());
  }

  public void unUpdateEventType() {
    FileWriter fw = null;
    try {
      fw = new FileWriter(unExistModelPath + "/unUpdateEventType.json");
      fw.write(ToolUtils.OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(unExistModels));
      fw.close();
    } catch (IOException e) {
      log.error("事件类型无法更新模型写入文件异常,{}", e);
    }
  }

  public int updateDefaultValue(List<Model> importingModels) {
    int count = 0;
    for (Model model : getModels(importingModels)) {
      if (model.getActions() != null) {
        for (Action action : model.getActions()) {
          if (action.getId().equalsIgnoreCase(GENERATE_EVENT)) {
            Map<String, Object> params = action.getParams();
            Map<String, Mapping> map = action.getMappingMap();
            List<Mapping> fieldParamsList = new LinkedList<>();
            Mapping solutionMap = map.get("solu");
            Mapping descMap = map.get("event_desc");
            Mapping cat2IdMap = map.get("CAT2_ID");
            if (cat2IdMap == null) {
              continue;
            }
            boolean needUpdate = false;
            if (solutionMap == null
                    || solutionMap.getValue() == null
                    || Strings.isNullOrEmpty(solutionMap.getValue().toString().trim())
                    || descMap.getValue() == null
                    || Strings.isNullOrEmpty(descMap.getValue().toString().trim())) {
              needUpdate = true;
            }
            if (needUpdate) {
              String key = cat2IdMap.getValue().toString();
              Map<String, String> updateMap = intelligenceService.getCatInfo(key);
              if (updateMap != null && !updateMap.isEmpty()) {
                if (solutionMap == null) {
                  solutionMap = new Mapping("solu", updateMap.get("SOLUTION"));
                  map.put("solu", solutionMap);
                } else if (solutionMap.getValue() == null || Strings.isNullOrEmpty(solutionMap.getValue().toString().trim())) {
                  solutionMap.setValue(updateMap.get("SOLUTION"));
                }
                if (descMap.getValue() == null || Strings.isNullOrEmpty(descMap.getValue().toString().trim())) {
                  descMap.setValue(updateMap.get("event_desc"));
                }
                fieldParamsList.addAll(map.values());
                params.put("mappings", fieldParamsList);
                action.setParams(params);
                modelService.editModel(model.getNo(), model);
                count++;
                log.info("已补全模型\"{}_{}\"的事件描述和处置意见默认值", model.getNo(), model.getName());
              }
            }
          }
        }
      }
    }
    return count;
  }

  public int updateEventType(List<Model> importingModels) {
    int count = 0;
    List<File> fileList = getFileSort(updateFilePath, "cat|.csv");
    for (File file : fileList) {
      if (recordFilePath.equals(file.getPath())) {
        continue;
      }
      String hashStr = getHashStr(file);
      if (importingModels == null) {
        if (oldHashStrList.contains(hashStr)) continue;
        else newHashStrList.add(hashStr);
      }
      Map<String, EventType> updateEventTypeMap = new LinkedHashMap();
      BufferedReader bufferedReader = null;
      try {
        bufferedReader = new BufferedReader(new FileReader(file));
      } catch (FileNotFoundException e) {
        log.error("", e);
      }
      String line = null;
      while (true) {
        try {
          if ((line = bufferedReader.readLine()) == null) {
            break;
          }
        } catch (IOException e) {
          log.error("", e);
        }
        final String[] split = line.split(",");
        if (Strings.isNullOrEmpty(split[0]) || split[0].equals("old_cat2")) {
          continue;
        }
        String old_cat2 = split[0];
        String cat1 = split[1];
        String cat1_id = split[2];
        String cat2 = split[3];
        String cat2_id = split[4];
        String k_c = split[5];
        String k_c_id = "".equals(split[6]) ? "10" : split[6];
        String attack_list="";
        if (split.length !=7){
          attack_list =split[7];
        }
        EventType eventType = new EventType(cat1_id, cat1);
        eventType.getCat2s().add(new EventType.Cat2(cat2_id, cat2, k_c_id, k_c,attack_list));
        updateEventTypeMap.put(old_cat2, eventType);
      }
      if (!updateEventTypeMap.isEmpty()) {
        count += updateModelsEventType(updateEventTypeMap, importingModels);
      }
    }
    return count;
  }


  private List<Model> getModels(List<Model> importingModels) {
    List<Model> modelList = new LinkedList<>();
    // 导入时仅获取刚导进去的模型
    if (importingModels != null && importingModels.size() > 0) {
      return importingModels;
    }
    for (ModelType modelType : ModelType.values()) {
      if (ModelType.AI.equals(modelType)) continue;
      modelList.addAll(modelService.getModels(modelType, true));
    }
    return modelList;
  }

  private int updateModelsEventType(Map<String, EventType> updateEventTypeMap, List<Model> importingModels) {
    int count = 0;
    clearUpAction(importingModels);
    for (Model model : getModels(importingModels)) {
      if (model.getActions() != null) {
        for (Action action : model.getActions()) {
          if (action.getId().equalsIgnoreCase(GENERATE_EVENT)) {
            Map<String, Object> params = action.getParams();
            Map<String, Mapping> map = action.getMappingMap();
            List<Mapping> fieldParamsList = new LinkedList<>();
            Mapping cat1Map = map.get("cat1");
            Mapping cat2Map = map.get("cat2");
            Mapping cat1_idMap = map.get("CAT1_ID");
            Mapping cat2_idMap = map.get("CAT2_ID");
            Mapping k_cMap = map.get("kill_chain");
            Mapping k_c_idMap = map.get("K_C_ID");
            if (cat2Map == null) {
              continue;
            }
            String key = cat2Map.getValue().toString();
            EventType eventType = updateEventTypeMap.get(key);
            if (eventType != null && needUpdate(eventType, map)) {
              cat1_idMap.setValue(eventType.getCat1_id());
              cat1Map.setValue(eventType.getCat1());
              if (eventType.getCat2s() != null && eventType.getCat2s().size() > 0) {
                EventType.Cat2 cat2 = eventType.getCat2s().get(0);
                cat2_idMap.setValue(cat2.getCat2_id());
                cat2Map.setValue(cat2.getCat2());
                if (k_c_idMap != null
                        && (cat2.getK_c_id().equals(k_c_idMap.getValue()) ^ cat2.getK_c().equals(k_cMap.getValue()))) {
                  k_cMap.setValue(cat2.getK_c());
                  k_c_idMap.setValue(cat2.getK_c_id());
                }
              }
              fieldParamsList.addAll(map.values());
              params.put("mappings", fieldParamsList);
              action.setParams(params);

              // 同步tag
              if (model.getTags() != null) {
                List<Tag> tagList = tagUtil.getTags(model.getActions());
                Map<String, Tag> tagMap = model.getTags().stream().collect(Collectors.toMap(Tag::getId, Function.identity()));
                tagList.forEach(tag -> {
                  if (!tagMap.containsKey(tag.getId())) {
                    tagMap.put(tag.getId(), tag);
                  }
                });
                model.setTags(tagMap.values().stream().collect(Collectors.toList()));
              }

              modelService.editModel(model.getNo(), model);
              count++;
              log.info("已升级模型\"{}_{}\"的事件类型", model.getNo(), model.getName());
            }else{
              unExistModels.put(model.getNo(),model.getName());
            }
          }
        }
      }
    }
    return count;
  }

  private boolean needUpdate(EventType eventType, Map<String, Mapping> map) {
    EventType.Cat2 cat2 = eventType.getCat2s().get(0);
    return !eventType.getCat1().equals(map.get("cat1").getValue())
            || !eventType.getCat1_id().equals(map.get("CAT1_ID").getValue())
            || !cat2.getCat2().equals(map.get("cat2").getValue())
            || !cat2.getCat2_id().equals(map.get("CAT2_ID").getValue())
            || (map.get("K_C_ID") != null
            && (cat2.getK_c_id().equals(map.get("K_C_ID").getValue()) ^ cat2.getK_c().equals(map.get("kill_chain").getValue())));
  }

  private void clearUpAction(List<Model> importingModels) {
    for (Model model : getModels(importingModels)) {
      if (model.getActions() != null) {
        for (Action action : model.getActions()) {
          if (action.getId().equalsIgnoreCase(GENERATE_EVENT)) {
            Map<String, Object> params = action.getParams();
            Map<String, Mapping> map = action.getMappingMap();
            //去重
            if (((List) params.get("mappings")).size() > map.values().size()) {
              params.put("mappings", map.values());
              action.setParams(params);
              modelService.editModel(model.getNo(), model);
            }
          }
        }
      }
    }
  }
}
