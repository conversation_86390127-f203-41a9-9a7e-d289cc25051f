import {
  Component,
  EventEmitter,
  forwardRef,
  Input,
  OnInit,
  Output,
  ViewChild,
} from '@angular/core';
import {
  ControlValueAccessor,
  FormGroup,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';

import { TuiAppService } from '@tui/frame';

import { Util } from 'src/app/utils/util';
import { ComToComService } from '../../../services/com-to-com.service';
@Component({
  selector: 'app-knowledge-from',
  templateUrl: './knowledge-from.component.html',
  styleUrls: ['./knowledge-from.component.less'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => KnowledgeFromComponent),
      multi: true,
    },
  ],
  inputs: ['id', 'item', 'options'],
})
export class KnowledgeFromComponent implements OnInit, ControlValueAccessor {
  @ViewChild('wEdit') wEdit: any;
  @Input() disabled = false;
  @Output() kChange = new EventEmitter<any>();
  @Input() form: FormGroup;

  get isInclude() {
    return this.form.get('type').value === 'include';
  }
  value: any = {
    predication: '',
    expression: null,
    knowledge_base_url: '',
    filter_condition: null,
  };

  relation_field = [{ option: [], value: '' }];
  cover_field = [{ option: [], name: '', value: '', dataType: '' }];
  // 覆盖字段中日志字段的类型
  logFields: any[];
  // get logFields() {
  //   const result = [];
  //   this.item.templateType.option.forEach(({ label, items }) => {
  //     result.push({
  //       label,
  //       items: items.filter((el) => el.bType == 'string'),
  //     });
  //   });
  //   return result;
  // }

  id: string;
  item: any;
  set options(d) {
    d && (this.logFields = d);
  }

  public onModelChange: Function = () => {};
  public onModelTouched: Function = () => {};

  get viewSett() {
    return this.tui.getAppViewSettById('wilkes', 'wilkes');
  }

  get attachField() {
    return this.viewSett?.showAttachField || false;
  }
  constructor(
    private tui: TuiAppService,
    private comToComService: ComToComService
  ) {}

  ngOnInit() {}

  writeValue(v): void {
    if (v) {
      this.value = v;
      this.value.predication = this.value.expression;
      if (this.value.relation_field && this.value.relation_field.length > 0) {
        this.relation_field = this.value.relation_field.map((value) => ({
          option: [],
          value,
        }));
      }

      if (this.value.cover_field && this.value.cover_field.length > 0) {
        this.cover_field = this.value.cover_field.map((field, index) => {
          return {
            option: [],
            value: field.value,
            name: field.name,
            dataType: field.dataType,
          };
        });
      }
    }
  }

  save() {
    this.wEdit.save();
    this.value.expression = this.value.predication;
    this.value.predication = '';
    delete this.value.type;
    this.value.relation_field = [];
    this.relation_field.forEach(
      (e) => e.value && this.value.relation_field.push(e.value)
    );
    this.value.cover_field = [];
    this.cover_field.forEach(
      (e) =>
        e.name &&
        e.value &&
        this.value.cover_field.push({
          value: e.value,
          name: e.name,
          dataType: e.dataType,
        })
    );

    this.comToComService.postData = this.comToComService.postData.filter(
      (e) => !new RegExp(this.id).test(e)
    );
    if (Object.values(this.wEdit.errObg).some((e) => !!e)) {
      this.comToComService.postData.push(
        this.id + ',' + Object.values(this.wEdit.errObg).toString()
      );
    }
    this.onModelChange(this.value);
  }

  registerOnChange(fn: any): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onModelTouched = fn;
  }

  setROptions(e) {
    this.relation_field.forEach((f) => (f.option = Util.copy(e)));
    const mapFunc = (el) => ({
      label: el.label,
      value: '${' + el.value + '}',
      dataType: el.dataType,
    });
    this.cover_field.forEach((f) => (f.option = e.map(mapFunc)));
    this.removeIn();
    this.kChange && this.kChange.emit(this.wEdit.value.kname);
  }

  selChange(e) {
    this.removeIn();
  }

  // 附加字段添加或者删除 is为true是添加 disabled
  fieldOperation(i?) {
    if (i === undefined) {
      this.relation_field.push({
        option: Util.copy(this.relation_field[0].option),
        value: '',
      });
    } else {
      this.relation_field.splice(i, 1);
    }
    this.removeIn();
  }

  // 去除已存在的下拉选项
  removeIn() {
    const that = this;
    const arr = [
      ...(that.wEdit.ruleSets.b.rules || []),
      ...that.relation_field,
    ].map((e) => e.value);
    this.relation_field.forEach((e) => {
      e.option.forEach((ee: any) => {
        ee['disabled'] = !!arr.includes(ee['value']);
      });
    });
  }

  coverFieldOperation(i?) {
    if (i === undefined) {
      this.cover_field.push({
        option: Util.copy(this.cover_field[0].option),
        value: '',
        name: '',
        dataType: '',
      });
    } else {
      this.cover_field.splice(i, 1);
    }
    this.removeCover();
  }

  removeCover(e?, node?) {
    const that = this;
    e && (node.dataType = e.option.dataType || 'all') && (node.name = '');
    const arr = [
      ...(that.wEdit.ruleSets.b.rules || []),
      ...that.cover_field,
    ].map((e) => e.value);
    this.cover_field.forEach((e) => {
      e.option.forEach((ee: any) => {
        ee['disabled'] = !!arr.includes(ee['value']);
      });
    });
  }
}
