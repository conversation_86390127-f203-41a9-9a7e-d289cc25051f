package com.topsec.wilkes.controller;

import com.topsec.wilkes.common.dto.Model;
import com.topsec.wilkes.service.ModelHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/model/history")
public class ModelHistoryController {

  @Autowired
  private ModelHistoryService modelHistoryService;

  @RequestMapping(value = "{no}", method = RequestMethod.GET)
  @ResponseBody
  public List<Model> getModels(@PathVariable("no") String no) {
    return modelHistoryService.getModelHistorys(no);
  }
}
