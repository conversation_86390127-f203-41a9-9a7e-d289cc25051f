<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>wilkes-parent</artifactId>
    <groupId>com.topsec.wilkes</groupId>
    <version>${revision}</version>
  </parent>

  <artifactId>wilkes-ui</artifactId>

  <name>Wilkes UI</name>

  <build>
    <plugins>
<!--      <plugin>-->
<!--        <groupId>org.apache.maven.plugins</groupId>-->
<!--        <artifactId>maven-antrun-plugin</artifactId>-->
<!--        <version>1.8</version>-->
<!--        <executions>-->
<!--          <execution>-->
<!--            <id>ui-package</id>-->
<!--            <phase>prepare-package</phase>-->
<!--            <goals>-->
<!--              <goal>run</goal>-->
<!--            </goals>-->
<!--            <configuration>-->
<!--              <target>-->
<!--                <echo>run build.sh in ${basedir}</echo>-->
<!--                <chmod file="${basedir}/build.sh" perm="ugo+rx"/>-->
<!--                <exec dir="${basedir}" executable="/bin/bash">-->
<!--                  <arg value="build.sh"/>-->
<!--                </exec>-->
<!--              </target>-->
<!--            </configuration>-->
<!--          </execution>-->
<!--        </executions>-->
<!--      </plugin>-->
    </plugins>
    <resources>
      <resource>
        <directory>dist/wilkes</directory>
        <targetPath>META-INF/resources</targetPath>
      </resource>
    </resources>
  </build>

</project>
