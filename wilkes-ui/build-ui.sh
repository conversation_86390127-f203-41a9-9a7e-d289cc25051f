desc=`git describe --tags`
result=$(echo $desc | grep "g")
if [ $result ]
then
    update=`echo $desc | awk -F "-" '{print $(NF-1)}'`
    tag=`echo $desc | awk -F "-" '{print $1}'`-`echo $desc | awk -F "-" '{print $2}'`
else
    update=0
    tag=$desc
fi
echo $update
echo $tag
sed -i 's/\"version\": \".*\",/\"version\": \"'${tag}'.'${update}'\",/g' package.json

npm config delete http-proxy
npm config delete https-proxy
npm config delete proxy
npm config set registry http://10.11.6.81:8989/nexus/content/repositories/npm-all/
delete_node_modules="$1"
if [[ $delete_node_modules == "yes" ]]; then
    echo "delete node_modules"
    rm package-lock.json
    rm -rf node_modules
else
    echo "not delete node_modules"
    rm -rf node_modules/@tui
    rm -rf node_modules/@firmament
fi
npm i
chmod -R 755 node_modules
npm run build
