import { AfterViewInit, Component, OnInit } from '@angular/core';
import { TopHttpService } from '../../service/topHttp.service';
import { ActivatedRoute, Router } from '@angular/router';
import { MessageService } from '@tui/component-library';
import { TuiNavService, TuiAppService } from '@tui/frame';

@Component({
  selector: 'app-topic-list',
  templateUrl: './topic-list.component.html',
  styleUrls: ['./topic-list.component.less'],
})
export class TopicListComponent implements OnInit, AfterViewInit {
  public url = '';
  public noData = true;
  public mainLIsts: any[] = [];
  public isShowForSubject = true;
  public showSpecial: any;
  public getListData: any = {};
  getSkin = 'red';
  public noDataList = { noDataType: 'noDataLoading', noDatatext: '正在加载中' };
  public analysis = [
    {
      name: '关联分析',
      color: '#6f64e4',
      background: '#f1f0f8',
      number: '20',
      icon: '',
    },
    {
      name: '安全异常行为分析',
      color: '#54cbcf',
      background: '#e8fcfd',
      number: '20',
      icon: '',
    },
    {
      name: '深度分析',
      color: '#6cafdc',
      background: '#e6f4fd',
      number: '20',
      icon: '',
    },
  ];

  get viewSett() {
    return this.tuiAppService.getAppViewSettById('wilkes', 'wilkes');
  }

  constructor(public httpService: TopHttpService, private messageService: MessageService, private tuiNavService: TuiNavService, private router: Router, private activatedRoute: ActivatedRoute, private tuiAppService: TuiAppService) {}

  ngOnInit() {
    this.accessToThematicData();
  }

  ngAfterViewInit(): void {
    this.getSkin = localStorage.getItem('bodyTheme') != 'blue-theme' ? 'red' : 'blue';
    this.tuiAppService.tuiAppCfgChanged.addListener('tuiAppCfgChanged', (keyStr: string) => {
      if (Object.is(keyStr, 'skinSett.skin')) {
        this.getSkin = localStorage.getItem('bodyTheme') != 'blue-theme' ? 'red' : 'blue';
      }
    });
  }

  // 专题分析 模型列表  跳转
  toGetRouter(type: number, data?: any) {
    let url;
    if (type === 2) {
      url = this.router.url.replace('subject', 'models');
      this.tuiNavService.jumpTo(url, { subject: data.id });
    } else {
      url = this.viewSett?.subject + '/' + data.id;
      this.tuiNavService.jumpTo(url, null);
    }
  }
  changer() {
    this.noData = true;
    this.noDataList = { noDataType: 'noData', noDatatext: '无数据' };
  }
  accessToThematicData() {
    // 获取专题管理页面获取模型数量接口
    this.httpService.getModelNumber().subscribe((data) => {
      this.getListData = data;
    });
    // 获取专题分析列表
    this.url = this.tuiAppService.tuiApp.serviceUrl;
    this.httpService.getMasterModel({ groupId: 'SUBJECT' }, '').subscribe(
      (data) => {
        this.showSpecial = this.viewSett?.showSpecial;
        data.length === 0 ? this.changer : (this.noData = false);
        this.mainLIsts = data;
        for (const i in this.mainLIsts) {
          this.getsubordinate(this.mainLIsts[i]);
        }
      },
      (error) => {
        this.noDataList = { noDataType: '500', noDatatext: '服务器错误' };
        this.messageService.add({
          severity: 'error',
          summary: '提示',
          detail: error.error.message,
          isClear: true,
        });
      },
    );
    this.viewSett?.isIntegrated === 'false' ? (this.isShowForSubject = false) : (this.isShowForSubject = true);
  }

  getsubordinate(data: any) {
    // /  根据一级专题获取二级专题
    this.httpService.getAbnormal(data.id, 'SUBJECT').subscribe((item) => {
      data.children = item;
    });
  }

  details(item: any) {
    this.httpService.modelId = item.id;
    this.router.navigate([`./details/${item.id}`], {
      relativeTo: this.activatedRoute,
    });
  }
}
