import { Component, EventEmitter, forwardRef, Input, OnInit, Output } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'app-follow-by-predication',
  templateUrl: './follow-by-predication.component.html',
  styleUrls: ['./follow-by-predication.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FollowByPredicationComponent),
      multi: true,
    },
  ],
})
export class FollowByPredicationComponent implements OnInit {
  @Input() disabled = false;
  @Output() change = new EventEmitter<any>();
  value = {
    number_a: 1,
    number_b: 1,
    type_a: 'least',
    type_b: 'least',
  };
  // 关联字段
  relationLMArr = [{ label: '至少', value: 'least' }]; // {label: '最多', value: 'most'}

  public onModelChange: Function = () => {};
  public onModelTouched: Function = () => {};

  constructor() {}

  ngOnInit() {}

  writeValue(v): void {
    if (v) {
      this.value = v;
    }
    setTimeout(() => {
      this.nodeChange();
    }, 0);
  }

  nodeChange() {
    this.change.emit({ N: this.value.number_a, M: this.value.number_b });
    this.onModelChange(this.value);
  }

  registerOnChange(fn: any): void {
    this.onModelChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onModelTouched = fn;
  }
}
