# 智能分析安装文档(@bdapp/wilkes)

## 安装

### 1.本地安装
npm install file:libs/@bdapp/wilkes 或

yarn add file:libs/@bdapp/wilkes

### 2.线上安装或者查询
##### 1）安装
npm install @bdapp/wilkes 或 
yarn add @bdapp/wilkes

##### 2）查询版本
npm view @bdapp/wilkes versions --json

### 3.样式引入
@import "~ng-zorro-antd/ng-zorro-antd.min.css";需要在样式中最上面引入避免影响项目自身的样式

@import "assets/rules/changeAnt";

### 4.ng-zorro-antd全局配置
在项目app.module.ts引入

...

import { NgZorroAntdModule, NZ_I18N, zh_CN } from 'ng-zorro-antd';

import { registerLocaleData } from '@angular/common';

import zh from '@angular/common/locales/zh';

--国际化设置中英文--

registerLocaleData(zh);

--服务中注册--

providers: [{ provide: NZ_I18N, useValue: zh_CN }]

### 5.静态资源的导入

assets文件下粘贴rules文件夹

### 6.路由以及参数配置如下

    {
      "navs": [
      {
        "icon": "icon-intelligent-analysis",
        "label": "智能分析",
        "uri": "wilkes",
        "linkChildEnable": true,
        "dynaChildEnable": true,
        "viewSett":{
            "id": "wilkes",
            "isIntegrated": "true",  集成环境必须true
            "toMagicEyeUrl": "https://172.21.1.238:8050/MagicEye/#/quicksearch", 
            "toMagicEyeUrlA": "http://172.21.2.101:27096/MagicEye/#/alarmManagement",
            "models-ws": "models/import/lock", 集成项目如top
            "rulesparams": { "eventName": "事件类型" },
            "aiComponent": "/wilkes-ai-model",
            "helpDocument": "/wilkes/docs",
            "subject": "/subject",
            "showSpecial": {
              "externalAonnection":false
            }
        },
        "children": [
        {
          "icon": "icon-stackoverflow",
          "label": "专题管理",
          "uri": "subject",
          "primaryLink": false,
          "linkChildEnable": false
        },
        {
          "icon": "icon-model-management-one",
          "label": "模型管理",
          "uri": "models",
          "primaryLink": false,
          "linkChildEnable": false
        },
        {
        "icon": "icon-relation1",
        "label": "关联分析",
        "uri": "models/correlation",
        "primaryLink": false,
        "linkChildEnable": false
        },
        {
        "icon": "icon-behavior",
        "label": "行为分析",
        "uri": "models/behaviour",
        "primaryLink": false,
        "linkChildEnable": false
        },
          {
            "icon": "icon-relation1",
            "label": "关联分析",
            "uri": "rules",
            "primaryLink": false,
            "linkChildEnable": false,
            "invisible": true,
            "children": [
              {
                "icon": "icon-document",
                "label": "关联分析模型",
                "uri": "edit",
                "primaryLink": false,
                "invisible": true,
                "dynaChildEnable": true
              },
              {
                "icon": "icon-ai",
                "label": "管理",
                "uri": "admin",
                "primaryLink": false,
                "invisible": true
              }
            ]
          },
          {
            "icon": "icon-behavior",
            "label": "行为分析",
            "uri": "behaviors",
            "primaryLink": false,
            "linkChildEnable": false,
            "invisible": true,
            "children": [
              {
                "icon": "icon-document",
                "label": "行为分析模型",
                "uri": "edit",
                "primaryLink": false,
                "invisible": true,
                "dynaChildEnable": true
              }
            ]
          },
          {
            "icon": "icon-ai",
            "label": "深度分析",
            "uri": "models/ai",
            "primaryLink": false,
            "linkChildEnable": false,
            "children": [
              {
                "icon": "icon-document",
                "label": "AI模型",
                "uri": "edit",
                "primaryLink": false,
                "invisible": true,
                "dynaChildEnable": true
              }
            ]
          }
        ]
      }
      ],
      "serviceUrl": {
      "rules": "/wilkes"
      }
    }

| 参数名            | 用途 | 默认值 |
| ----------------- | ------- | ------- |
| id                | 框架服务获得参数id必填 | wilkes |
| isIntegrated      | 是否是集成环境，集成环境被指为false,非集成环境为true | false |
| toMagicEyeUrl     | 跳转检索路由或地址，集成环境写入相应的检索路由地址，非集成环境为线上服务器地址 | https://172.21.1.238:8050/MagicEye/#/quicksearch |
| toMagicEyeUrlA    | 跳转告警同上  | http://172.21.2.101:27096/MagicEye/#/alarmManagement |
| rulesparams       | 基础设置中配置  | { "eventName": "事件类型" } |
| aiComponent      | ai模型跳转地址 | /wilkes-ai-model|
| helpDocument      | 帮助文档地址   | /wilkes/docs |
| subject        | 必填   | /subject|
| models-ws      | 必填   | models/import/lock|
| showSpecial          | 必填  | { "externalAonnection":false } |

### 7.国际化文件配置
src\assets\i18n\main中的

en.json

zh_cn.json

分别加入

"process": {
    "rules": {
      "eventName": "安全事件"
    }
  }

### 8. 配置routegateway，其中xxx为产品集成路径

spring.cloud.gateway.routes[1].id=wilkes
spring.cloud.gateway.routes[1].uri=lb://wilkes
spring.cloud.gateway.routes[1].predicates[0]=Path=/{xxx}/wilkes/**
spring.cloud.gateway.routes[1].filters[0]=StripPrefix=1

spring.cloud.gateway.routes[16].id=wilkes-aiUI
spring.cloud.gateway.routes[16].uri=lb://wilkes
spring.cloud.gateway.routes[16].predicates[0]=Path=/{xxx}/wilkes-ai-model/**
spring.cloud.gateway.routes[16].filters[0]=StripPrefix=1

spring.cloud.gateway.routes[3].id=wilkes-ws
spring.cloud.gateway.routes[3].uri=lb:ws://wilkes
#ws://************:22010/wilkes/models/import/lock
#spring.cloud.gateway.routes[9].uri=ws://************:22010/wilkes/
spring.cloud.gateway.routes[3].predicates[0]=Path=/{xxx}/wilkes/models/import/**
spring.cloud.gateway.routes[3].filters[0]=StripPrefix=1

tsm.aas.auth.filter.uris=/wilkes-ai-model
