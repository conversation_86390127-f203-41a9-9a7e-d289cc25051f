package com.topsec.wilkes.service.impl;

import static com.topsec.common.utils.ToolUtils.OBJECT_MAPPER;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.topsec.minsky.domain.Expression;
import com.topsec.minsky.domain.util.ExpressionUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.junit.Test;

/**
 * @description:
 * @author: dy
 * @time: 2025/5/21 10:50
 */
public class CustomWhite {


  @SneakyThrows
  @Test
   public  void test(){
    String test = "{\n" + "      \"node\": {\n" + "        \"operator\": \"and\",\n" + "        \"children\": [\n"
        + "          {\n" + "            \"leaf_node\": {\n"
        + "              \"left\": \"repeat\",\n" + "              \"operator\": \"intEqual\",\n"
        + "              \"right\": \"111\"\n" + "            }\n" + "          },\n"
        + "          {\n" + "            \"leaf_node\": {\n" + "              \"left\": \"name\",\n"
        + "              \"operator\": \"=\",\n" + "              \"right\": \"aa\"\n"
        + "            }\n" + "          },\n" + "          {\n" + "            \"node\": {\n"
        + "              \"operator\": \"and\",\n" + "              \"children\": [\n"
        + "                {\n" + "                  \"leaf_node\": {\n"
        + "                    \"left\": \"pri\",\n" + "                    \"operator\": \"=\",\n"
        + "                    \"right\": \"1\"\n" + "                  }\n" + "                }\n"
        + "              ]\n" + "            }\n" + "          }\n" + "        ]\n" + "      }\n"
        + "    }";
     Expression expression = OBJECT_MAPPER.readValue(test, Expression.class);
    System.out.println(ExpressionUtil.generateSQL(expression, "field-filter"));

  }

  @Test
  public void test1(){

    String test = "{\n" + "      \"node\": {\n" + "        \"operator\": \"and\",\n" + "        \"children\": [\n"
        + "          {\n" + "            \"leaf_node\": {\n"
        + "              \"left\": \"repeat\",\n" + "              \"operator\": \"intEqual\",\n"
        + "              \"right\": \"111\"\n" + "            }\n" + "          },\n"
        + "          {\n" + "            \"leaf_node\": {\n" + "              \"left\": \"name\",\n"
        + "              \"operator\": \"=\",\n" + "              \"right\": \"aa\"\n"
        + "            }\n" + "          },\n" + "          {\n" + "            \"node\": {\n"
        + "              \"operator\": \"and\",\n" + "              \"children\": [\n"
        + "                {\n" + "                  \"leaf_node\": {\n"
        + "                    \"left\": \"pri\",\n" + "                    \"operator\": \"=\",\n"
        + "                    \"right\": \"1\"\n" + "                  }\n" + "                }\n"
        + "              ]\n" + "            }\n" + "          }\n" + "        ]\n" + "      }\n"
        + "    }";

    List<String> customWhiteDsl = Lists.newArrayList(test, test);
    List<String> customWhiteSql = customWhiteDsl.stream().map(dsl -> {
      Expression expression = null;
      try {
        expression = OBJECT_MAPPER.readValue(dsl, Expression.class);
      } catch (JsonProcessingException e) {
      }
      return ExpressionUtil.generateSQL(expression, "field-filter");
    }).collect(Collectors.toList());
    String customWhiteFilterSql = Joiner.on(" or ").join(customWhiteSql);
    System.out.println(customWhiteFilterSql);


  }
}
